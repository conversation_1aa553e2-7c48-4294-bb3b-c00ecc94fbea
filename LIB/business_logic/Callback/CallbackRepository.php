<?php

namespace wws\Callback;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;

class CallbackRepository
{
    const STATUS_GELOESCHT = 'geloescht';
    const STATUS_ERLEDIGT = 'erledigt';
    const STATUS_OFFEN = 'offen';

    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function load(int $callback_id): Callback
    {
        $daten = $this->db->singleQuery("
            SELECT
                callback.callback_id,
                callback.callback_list_id,
                callback.status,
                callback.datum_angelegt,
                callback.datum_erledigt,
                callback.name,
                callback.bemerkung,
                callback.user_id,
                callback.customer_id,
                callback.order_id,
                callback.telefon,
                callback.shop_id
            FROM
                callback
            WHERE
                callback.callback_id = '" . (int)$callback_id . "'
        ");
        if (!$daten) {
            throw new SmartDataEntityNotFoundException('callback not found');
        }

        $callback = new Callback();
        $callback->getSmartDataObj()->loadDaten($daten);

        return $callback;
    }

    public function save(Callback $callback): void
    {
        $callback->save($this->db);
    }
}
