<?php

namespace wws\Callback\EventHandler;

use wws\business_structure\Shop;
use wws\Callback\Event\EventCallbackCreated;
use wws\Mails\Mail;

class SendMailOnNewCallback
{
    public function handle(EventCallbackCreated $event): void
    {
        $mail = new Mail();
        $mail->setShop(Shop::ALLEGO);
        $mail->loadVorlage('intern_callback');
        $mail->setEmpf('<EMAIL>');
        $mail->send();
    }
}
