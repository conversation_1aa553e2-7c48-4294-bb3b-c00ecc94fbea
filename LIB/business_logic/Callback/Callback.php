<?php

namespace wws\Callback;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\InputException;
use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataObj;
use service_loader;
use wws\Callback\Event\EventCallbackCreated;

class Callback implements ProvideSmartDataObj
{
    protected SmartDataObj $daten;

    //
    public function __construct()
    {
        $this->daten = new SmartDataObj($this);
    }

    public function save(db_generic $db)
    {
        $errors = new InputException();
        if (!$this->daten['name']) {
            $errors->add('name', 'Bitte geben Sie einen Namen an.');
        }
        if (!$this->daten['telefon']) {
            $errors->add('telefon', 'Bitte geben Sie eine Telefonnummer an.');
        }
        if (!$this->daten['callback_list_id']) {
            $errors->add('callback_list_id', 'Bitte geben <PERSON>e eine gültige Callbackliste an.');
        }
        $errors->check();

        if ($this->daten->isNew()) {
            $this->setDatumAngelegt(new DateObj());
            if (!$this->getUserId()) {
                $this->setUserId(-1);
            }
        }

        $changes = $this->daten->getChanges();

        if ($changes) {
            $sql = [];
            foreach ($changes as $key => $value) {
                $sql[] = "callback.$key = '" . $db->escape($value) . "'";
            }

            switch ($this->daten->getObjStatus()) {
                case SmartDataObj::STATUS_NEW:
                    $db->query("
                        INSERT INTO
                            callback
                        SET
                            " . implode(',', $sql) . "
                    ");

                    $this->daten->setterDirect('callback_id', $db->insert_id());
                    $this->daten->setSaved();

                    $event = new EventCallbackCreated($this);
                    service_loader::getEventDispatcher()->dispatch($event);
                    break;
                case SmartDataObj::STATUS_UPDATE:
                    $db->query("
                        UPDATE
                            callback
                        SET
                            " . implode(',', $sql) . "
                        WHERE
                            callback.callback_id = '" . $this->getCallbackId() . "'
                    ");
                    $this->daten->setSaved();
                    break;
            }
        }
    }

    //getter
    public function getCallbackId()
    {
        return $this->daten->getter('callback_id');
    }

    public function getStatus()
    {
        return $this->daten->getter('status');
    }

    public function getDatumAngelegt()
    {
        return $this->daten->getter('datum_angelegt');
    }

    public function getDatumErledigt()
    {
        return $this->daten->getter('datum_erledigt');
    }

    public function getName()
    {
        return $this->daten->getter('name');
    }

    public function getBemerkung()
    {
        return $this->daten->getter('bemerkung');
    }

    public function getUserId()
    {
        return $this->daten->getter('user_id');
    }

    public function getCustomerId()
    {
        return $this->daten->getter('customer_id');
    }

    public function getOrderId()
    {
        return $this->daten->getter('order_id');
    }

    public function getTelefon()
    {
        return $this->daten->getter('telefon');
    }

    public function getShopId()
    {
        return $this->daten->getter('shop_id');
    }

    public function getCallbackListId()
    {
        return $this->daten->getter('callback_list_id');
    }

    //setter
    /*public function setCallbackId($value) {
        return $this->callback_id = $value;
    }*/

    public function setStatus($value)
    {
        if ($value == CallbackRepository::STATUS_ERLEDIGT || $value == CallbackRepository::STATUS_GELOESCHT) {
            $this->setDatumErledigt(new DateObj());
        }

        return $this->daten->setter('status', $value);
    }

    public function setDatumAngelegt(DateObj $value)
    {
        return $this->daten->setter('datum_angelegt', $value->db());
    }

    public function setDatumErledigt(DateObj $value)
    {
        return $this->daten->setter('datum_erledigt', $value->db());
    }

    public function setName($value)
    {
        return $this->daten->setter('name', $value);
    }

    public function setBemerkung($value)
    {
        return $this->daten->setter('bemerkung', $value);
    }

    public function setUserId($value)
    {
        return $this->daten->setter('user_id', $value);
    }

    public function setCustomerId($value)
    {
        return $this->daten->setter('customer_id', $value);
    }

    public function setOrderId($value)
    {
        return $this->daten->setter('order_id', $value);
    }

    public function setTelefon($value)
    {
        return $this->daten->setter('telefon', $value);
    }

    public function setShopId($value)
    {
        return $this->daten->setter('shop_id', $value);
    }

    public function setCallbackListId($value)
    {
        return $this->daten->setter('callback_list_id', $value);
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }
}
