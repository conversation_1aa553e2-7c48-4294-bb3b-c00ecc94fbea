<?php

namespace wws\Order\Event;

use bqp\Event\Event;
use wws\Order\Order;

/**
 * Event dient zum beenden von Aufträgen auf 3ten Plattformen. Der Event wird ausgelöst
 * nachdem die Rechnung gelegt wurde und entweder Sendungsnummern hinzugefügt wurden,
 * oder eine frei konfigurierbares Timeout eintritt. Z.B. 12h später, nach 22 Uhr, oder Wochenende etc.
 *
 * Das reduziert den Aufwand für das Beenden. Bisher wurde für jede Anbindung individuelle
 * warte mechanismen implementiert um mit nachträglichen Sendungsnummern umzugehen.
 * Das entfällt durch diesen Event, über den @see OrderCompletionService können die Timeouts gesteuert werden.
 */
class EventOrderCompleteForPlatform extends Event
{
    private Order $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function getOrderId(): int
    {
        return $this->order->getOrderId();
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getMessage(): array
    {
        return ['order_id' => $this->order->getOrderId()];
    }
}
