<?php

namespace wws\Order\Event;

use bqp\Event\Event;
use bqp\Model\EntityChanges;
use wws\Order\Order;

class EventOrderChange extends Event
{
    private Order $order;
    private EntityChanges $entity_changes;

    public function __construct(Order $order, EntityChanges $entity_changes)
    {
        $this->order = $order;
        $this->entity_changes = $entity_changes;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getEntityChanges(): EntityChanges
    {
        return $this->entity_changes;
    }

    public function getMessage(): array
    {
        return ['order_id' => $this->order->getOrderId()];
    }
}
