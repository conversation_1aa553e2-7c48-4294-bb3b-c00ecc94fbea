<?php

namespace wws\Order\Event;

use bqp\Event\Event;

class EventOrderShipmentPriorityChanged extends Event
{
    private int $order_id;
    private int $order_item_id;
    private int $versand_status;

    public function __construct(int $order_id, int $order_item_id, int $versand_status)
    {
        $this->order_id = $order_id;
        $this->order_item_id = $order_item_id;
        $this->versand_status = $versand_status;
    }

    public function getOrderId(): int
    {
        return $this->order_id;
    }

    public function getOrderItemId(): int
    {
        return $this->order_item_id;
    }

    public function getVersandStatus(): int
    {
        return $this->versand_status;
    }
}
