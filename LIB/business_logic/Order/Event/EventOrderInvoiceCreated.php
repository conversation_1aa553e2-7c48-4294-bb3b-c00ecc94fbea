<?php

namespace wws\Order\Event;

use bqp\Event\Event;
use wws\buchhaltung\Invoice\InvoiceDocument;
use wws\Order\Order;

class EventOrderInvoiceCreated extends Event
{
    private InvoiceDocument $beleg;
    private Order $order;

    public function __construct(InvoiceDocument $beleg, Order $order)
    {
        $this->beleg = $beleg;
        $this->order = $order;
    }

    public function getOrderId(): int
    {
        return $this->beleg->getOrderId();
    }

    public function getCustomerId(): int
    {
        return $this->beleg->getCustomerId();
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getMessage(): array
    {
        return ['order_id' => $this->getOrderId(), 'rechnungs_nr' => $this->beleg->getBelegNr()];
    }
}
