<?php

namespace wws\Order\Event;

use bqp\Event\Event;
use wws\Order\Order;

class EventOrderCreated extends Event
{
    private Order $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getMessage(): array
    {
        return ['order_id' => $this->order->getOrderId()];
    }
}
