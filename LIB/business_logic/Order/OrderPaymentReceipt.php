<?php

namespace wws\Order;

use bqp\Date\DateObj;

/**
 * wird nur als Datenstruktur für Zahlungseingänge genutzt
 */
class OrderPaymentReceipt
{
    protected $daten = [
        'mail_payment_receipt' => false,
        'mail_payment_payment_receipt_partial' => false,
        'complete' => true,
        'bemerkung' => ''
    ];


    //setter
    public function setZahlungsId($zahlungs_id)
    {
        $this->daten['zahlungs_id'] = $zahlungs_id;
    }

    public function setAmount($amount)
    {
        $this->daten['amount'] = $amount;
    }

    public function setDatum($datum)
    {
        if (!($datum instanceof DateObj)) {
            $datum = new DateObj($datum);
        }
        $this->daten['datum'] = $datum;
    }

    public function setBemerkung($bemerkung)
    {
        $this->daten['bemerkung'] = $bemerkung;
    }

    public function setOther($key, $value)
    {
        $this->daten[$key] = $value;
    }

    public function setMailPaymentReceipt($value)
    {
        $this->daten['mail_payment_receipt'] = $value;
    }

    public function setMailPaymentReceiptPartial($value)
    {
        $this->daten['mail_payment_payment_receipt_partial'] = $value;
    }

    public function setComplete($complete)
    {
        $this->daten['complete'] = $complete;
    }

    //getter
    public function getZahlungsId()
    {
        return $this->daten['zahlungs_id'] ?? 0;
    }

    public function getAmount()
    {
        return $this->daten['amount'];
    }

    /**
     * @return DateObj
     */
    public function getDatum(): DateObj
    {
        if (isset($this->daten['datum'])) {
            return $this->daten['datum'];
        }

        return new DateObj();
    }

    public function getBemerkung()
    {
        return $this->daten['bemerkung'];
    }

    public function isMailPaymentReceipt()
    {
        return $this->daten['mail_payment_receipt'];
    }

    public function isMailPaymentReceiptPartial()
    {
        return $this->daten['mail_payment_payment_receipt_partial'];
    }

    public function isComplete()
    {
        return $this->daten['complete'];
    }
}
