<?php

namespace wws\Order;

use bqp\Date\DateObj;
use output;

/**
 * Hilfsklasse um ein Bestellung textlich zusammenzufassen. Es ist im System an einigen stellen sinnvoll eine kleine
 * Zusammenfassung des Auftrags zu haben, um den Auftrag selbst nicht aufrufen zu müssen.
 * Das ist derzeit alles sehr nah am "Problem" gestrickt. Das kann eigentlich relativ frei umgestalltet oder angepasst werden.
 *
 */
class OrderSummary
{

    public function forBookkeeping(Order $order): string
    {
        $info = '';
        $info .= '<b>Auftnr:</b> <a href="/ax/customer/orders/?action=search_by_auftnr&auftnr=' . $order->getAuftnr() . '" onclick="popup_large(event)">' . $order->getAuftnr() . '</a><br>';
        $info .= $this->basicCustomerSummary($order);
        $info .= '<b>Bestelldatum:</b> ' . $order->getBestellDatum()->format(DateObj::DE_DATETIME) . '<br>';
        $info .= '<b>Rechnungsbetrag</b> ' . output::formatPrice($order->getRechnungsBetrag()) . '<br>';
        $info .= '<b>Herkunft</b> ' . $order->getOrderOriginId() . '<br>';
        $info .= '<b>Zahlungsart</b> ' . $order->getZahlungsartName();

        return $info;
    }

    public function forSales(Order $order): string
    {
        $info = $this->forBookkeeping($order);
        $info .= '<br><br>';
        $info .= $this->basicItemsSummary($order);

        return $info;
    }

    public function basicCustomerSummary(Order $order): string
    {
        $customer = $order->getCustomer();

        $info = '';
        $info .= '<b>Kundennummer: </b>' . $order->getCustomerNr() . '<br>';
        $info .= '<b>Kunde</b>: ';
        if ($customer->getFirma()) {
            $info .= htmlentities($customer->getFirma()) . ' | ';
        }
        $info .= htmlentities($customer->getVorname() . ' ' . $customer->getName()) . '<br>';
        return $info;
    }

    public function basicItemsSummary(Order $order): string
    {
        $info = '';

        $info .= '<b>Positionen</b><br>';
        foreach ($order->getOrderItems() as $order_item) {
            $info .= $order_item->getAnzahl() . ' x ' . htmlentities($order_item->getProductName()) . " (" . $order_item->getStatusAsHtml() . ")<br>";
        }

        return $info;
    }
}
