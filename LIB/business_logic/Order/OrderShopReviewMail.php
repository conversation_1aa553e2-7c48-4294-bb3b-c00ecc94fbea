<?php

namespace wws\Order;

use bqp\BoolWithReason;
use bqp\db\db_generic;
use wws\business_structure\Shop;
use wws\Lager\WarenausgangLieferschein;
use wws\Mails\Mail;

class OrderShopReviewMail
{
    private int $delay_days = 14;

    private db_generic $db;

    private array $rekla_cache;
    private array $refund_cache;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function setDelayAfterOrderFinish(int $days): void
    {
        $this->delay_days = $days;
    }

    private function preloadData(array $order_ids): void
    {
        $this->rekla_cache = $this->db->query("
            SELECT
                rekla_stamm.order_id
            FROM
                rekla_stamm
            WHERE
                rekla_stamm.order_id IN (" . $this->db->in($order_ids) . ")
        ")->asSingleArray();

        $this->refund_cache = $this->db->query("
            SELECT
                buchhaltung_rueckzahlung.order_id
            FROM
                buchhaltung_rueckzahlung
            WHERE
                buchhaltung_rueckzahlung.order_id IN (" . $this->db->in($order_ids) . ")
        ")->asSingleArray();
    }

    public function run(): void
    {
        $order_ids = $this->getPossibleOrderIds();

        if (!$order_ids) {
            return;
        }

        $this->preloadData($order_ids);

        foreach ($order_ids as $order_id) {
            $order = new Order($order_id);

            $state = $this->checkShouldGetReviewState($order);

            if ($state->isOk()) {
                $this->sendReviewMail($order);
                $order->save();
            } else {
                $order->setBewertungsEmailStatus(OrderConst::BEWERTUNGS_MAIL_SKIPED);
                $order->save();
            }
        }
    }

    private function sendReviewMail(Order $order): void
    {
        $mail = new Mail();
        $mail->setOrder($order);

//        switch ($order->getOrderOriginId()) {
//            case 'amazon':
//                $mail->loadVorlage('bewertung_amazon');
//                break;
//            default:
//                $mail->loadVorlage('bewertung_psm');
//        }

        $mail->loadVorlage('daumenbild');

        if (trim($mail->getMailBody())) {
            $mail->send();
        }

        $order->setBewertungsEmailStatus(OrderConst::BEWERTUNGS_MAIL_SENT);
    }

    public function checkShouldGetReviewState(Order $order): BoolWithReason
    {
        if (in_array($order->getOrderId(), $this->rekla_cache)) {
            return new BoolWithReason(false, 'Reklamation');
        }

        if (in_array($order->getOrderId(), $this->refund_cache)) {
            return new BoolWithReason(false, 'Rückzahlung');
        }

        if ($order->isOrderTag(OrderConst::TAG_ASWO)) {
            return new BoolWithReason(false, 'ASWO');
        }

        if ($order->getZahlungsart() == OrderConst::PAYMENT_VERRECHNUNG) {
            return new BoolWithReason(false, 'Verrechnung');
        }

        if ($order->isOrderTag(OrderConst::TAG_SERVICE)) {
            return new BoolWithReason(false, 'Service');
        }

        if (!$order->getRechnungsNr()) {
            return new BoolWithReason(false, 'fehlende Rechnung');
        }

        //prüfen ob mail nach
        $mails_received = $this->db->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                customer_mail_archive
            WHERE
                customer_mail_archive.customer_id = " . $order->getCustomerId() . " AND
                customer_mail_archive.date_mail > '" . $order->getRechnungsDatum()->db() . "' AND
                customer_mail_archive.email_sender NOT LIKE '%ersatzteilshop%' AND
                customer_mail_archive.email_sender NOT LIKE '%smartgoods%'
        ");

        if ($mails_received > 0) {
            return new BoolWithReason(false, 'Mail nach Versand');
        }

        return new BoolWithReason(true);
    }

    public function getPossibleOrderIds(): array
    {
        //1. orders.added > NOW() - INTERVAL 60 DAY -> aus performance gründen
        return $this->db->query("
            SELECT
                orders.order_id
            FROM
                orders INNER JOIN
                warenausgang_lieferschein ON (orders.order_id = warenausgang_lieferschein.order_id)
            WHERE
                orders.added > NOW() - INTERVAL 60 DAY AND
                orders.shop_id = " . Shop::ERSATZTEILSHOP . " AND
                orders.order_origin_id = '" . OrderConst::ORDER_ORIGIN_SHOPWARE . "' AND
                orders.bewertungs_email = " . OrderConst::BEWERTUNGS_MAIL_OPEN . " AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "' AND
                warenausgang_lieferschein.datum_erledigt BETWEEN (NOW() - INTERVAL " . ($this->delay_days + 10) . " DAY) AND (NOW() - INTERVAL " . $this->delay_days . " DAY)
            GROUP BY
                orders.order_id
        ")->asSingleArray();
    }
}
