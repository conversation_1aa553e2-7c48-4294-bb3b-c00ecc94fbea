<?php

namespace wws\Order;

use bqp\Date\DateObj;
use bqp\Exceptions\DevException;
use bqp\Model\SmartDataObjLegacy;
use bqp\Vat\VatRate;
use bqp\Vat\VatRateSimple;
use config;
use db;
use order_repository;
use wws\Product\ProductRepositoryLegacy;
use wws\Product\ProductWarenkorbTypesRepository;
use wws\Supplier\SupplierRepository;

class OrderItem extends SmartDataObjLegacy
{
    protected $estimated_deliveries = null;

    /**
     * @var Order
     */
    protected $order = null;

    public function __construct(Order $order)
    {
        $this->loadDefaults();
        $this->createInterimsOrderItemId();
        $this->order = $order;
    }

    protected function recalcOpenQuantities()
    {
        $this->setQuantityOpen($this->getQuantity() - $this->getQuantityPlaned() - $this->getQuantityProgress() - $this->getQuantityCompleted());
    }

    public function createInterimsOrderItemId(): void
    {
        //$this->daten['order_item_id'] = 'INTERIM'.rand(100000, 999999);
        $this->daten['order_item_id'] = -rand(100000, 999999);
    }

    private function isImterimsOrderItemId(): bool
    {
        //return strpos($this->getOrderItemId(), 'INTERIM') === 0;
        return $this->daten['order_item_id'] < 0;
    }

    public function loadByArray($daten)
    {
        $daten = array_merge($this->getAsArray(), $daten);
        $this->loadDaten($daten);
    }

    public function loadDefaults()
    {
        $this->setLagerId(config::system('default_lager_id'));
        $this->setUnitCode(config::system('default_unit_code'));
        $this->setGrosMemo('');
        $this->setStornoMsg('');
        $this->setTypValue('');
        $this->setExterneReferenz('');
        $this->setExterneReferenz2('');
        $this->setSupplierId(0);
    }

    public function del(): void
    {
        $this->setObjStatus(self::STATUS_DEL);
    }

    public function getProductId(): int
    {
        return $this->getter('product_id');
    }

    public function getOrderItemId(): int
    {
        return $this->getter('order_item_id');
    }

    public function getModelPk()
    {
        if ($this->isImterimsOrderItemId()) {
            return false;
        }

        return $this->getOrderItemId();
    }

    public function setOrderItemId($value)
    {
        $this->setter('order_item_id', $value);
    }

    public function setOrderId($value)
    {
        return $this->setter('order_id', $value);
    }

    public function getOrderId(): int
    {
        return $this->order->getOrderId();
    }

    public function getLagerId(): int
    {
        return (int)$this->getter('lager_id');
    }

    /**
     * gibt ein Array mit den Daten des Objektes zurück
     * @return array $daten
     * @depracated
     */
    public function getDaten()
    {
        return $this->daten;
    }

    /**
     * gibt den Produktname zurück
     * @return string
     */
    public function getProductName()
    {
        return $this->getter('product_name');
    }

    public function getStatus(): int
    {
        return (int)$this->getter('status');
    }

    public function getPos()
    {
        return $this->getter('pos');
    }

    public function setPos($pos)
    {
        return $this->setter('pos', $pos);
    }

    public function isStorno(): bool
    {
        return $this->getStatus() == OrderConst::STATUS_STORNO;
    }

    public function getStatusAsHtml()
    {
        return order_repository::getStatusAsHtml($this->getter('status'));
    }

    public function setShopId($value)
    {
        return $this->setter('shop_id', $value);
    }

    public function setStatus(int $status): bool
    {
        return $this->setter('status', $status);
    }

    public function setExterneReferenz($externe_referenz)
    {
        return $this->setter('externe_referenz', $externe_referenz);
    }

    public function getExterneReferenz()
    {
        return $this->getter('externe_referenz');
    }

    public function setExterneReferenz2(string $externe_referenz_2): bool
    {
        return $this->setter('externe_referenz_2', $externe_referenz_2);
    }

    public function getExterneReferenz2(): string
    {
        return $this->getter('externe_referenz_2');
    }

    public function getAnzahl(): int
    {
        return $this->getQuantity();
    }

    public function getQuantity(): int
    {
        return (int)$this->getter('quantity');
    }

    public function getQuantityPlaned(): int
    {
        return (int)$this->getter('quantity_planed');
    }

    public function getQuantityProgress(): int
    {
        return (int)$this->getter('quantity_progress');
    }

    public function getQuantityCompleted(): int
    {
        return (int)$this->getter('quantity_completed');
    }

    public function getQuantityOpen(): int
    {
        return (int)$this->getter('quantity_open');
    }

    /**
     * gibt die Quantities als HTML Formatierten String zruück
     * @return string
     */
    public function getQuantitiesAsHtml(): string
    {
        if (!$this->isDeliveryAble()) {
            return '';
        }

        $return = '<span title="geplant/ausgelöst/beendet/offen">';
        $return .= '(' . $this->getQuantityPlaned() . '/' . $this->getQuantityProgress() . '/' . $this->getQuantityCompleted() . '/<b>' . $this->getQuantityOpen() . '</b>)';
        $return .= '</span>';

        return $return;
    }


    public function getUnitCode()
    {
        return $this->getter('unit_code');
    }

    public function setSpedId($value)
    {
        $this->setter('sped_id', $value);
    }

    public function getSpedId()
    {
        return $this->getter('sped_id');
    }

    public function getSupplierId(): int
    {
        return (int)$this->getter('supplier_id');
    }

    public function getStatsSupplierId(): int
    {
        return $this->getter('stats_supplier_id');
    }

    /**
     * @return float
     */
    public function getEkBrutto()
    {
        return $this->getter('ekpreis');
    }

    /**
     * @return float
     * @return mixed|null|string
     */
    public function getEkRevisedBrutto()
    {
        return $this->getter('ek_nnn_revised_brutto');
    }


    public function getEkNetto()
    {
        return round($this->getter('ekpreis') / $this->getVatRate()->getAsFactor(), 2);
    }

    public function getVersandStatus()
    {
        return $this->getter('versand_status');
    }

    public function setLagerId($value)
    {
        $this->setter('lager_id', $value);

        return $this;
    }

    public function setPreis($value)
    {
        return $this->setVkBrutto($value);
    }

    public function setVkBrutto($value)
    {
        $this->setter('preis', $value);
        return $this;
    }

    public function setProductId($value)
    {
        $this->setter('product_id', $value);
        return $this;
    }

    public function setQuantity($value)
    {
        $this->setter('quantity', $value);

        $this->recalcOpenQuantities();

        return $this;
    }

    public function setQuantityPlaned($quantity_planed)
    {
        $status = $this->setter('quantity_planed', $quantity_planed);

        $this->recalcOpenQuantities();

        return $status;
    }

    public function setQuantityProgress($quantity_progress)
    {
        $status = $this->setter('quantity_progress', $quantity_progress);

        $this->recalcOpenQuantities();

        return $status;
    }

    public function setQuantityCompleted($quantity_completed)
    {
        $status = $this->setter('quantity_completed', $quantity_completed);

        $this->recalcOpenQuantities();

        return $status;
    }

    public function setQuantityOpen($quantity_open)
    {
        return $this->setter('quantity_open', $quantity_open);
    }

    public function chgQuantityPlaned($value)
    {
        return $this->setQuantityPlaned($this->getQuantityPlaned() + $value);
    }

    public function chgQuantityProgress($value)
    {
        return $this->setQuantityProgress($this->getQuantityProgress() + $value);
    }

    public function chgQuantityCompleted($value)
    {
        return $this->setQuantityCompleted($this->getQuantityCompleted() + $value);
    }

    public function setUnitCode($unit_code)
    {
        return $this->setter('unit_code', $unit_code);
    }

    public function setProductName($value)
    {
        $this->setter('product_name', $value);
        return $this;
    }

    public function setProductNr($value)
    {
        $this->setter('product_nr', $value);
        return $this;
    }

    public function setMwstSatz($value)
    {
        $this->setter('mwst_satz', $value);
        return $this;
    }

    public function setEkBrutto($ek_brutto): bool
    {
        return $this->setter('ekpreis', round($ek_brutto, 2));
    }

    public function setEkNetto($ek_netto): bool
    {
        return $this->setEkBrutto($ek_netto * $this->getVatRate()->getAsFactor());
    }


    public function setEkRevisedBrutto($ek_revised_brutto): bool
    {
        return $this->setter('ek_revised_brutto', round($ek_revised_brutto, 2));
    }

    public function setEkNnnRevisedBrutto($ek_nnn_revised_brutto): bool
    {
        return $this->setter('ek_nnn_revised_brutto', round($ek_nnn_revised_brutto, 2));
    }

    public function setEkNnnBrutto($ek_nnn_burtto): bool
    {
        return $this->setter('ek_nnn_brutto', round($ek_nnn_burtto, 2));
    }

    public function setEkNnnNetto($ek_nnn_netto): bool
    {
        return $this->setEkNnnBrutto($ek_nnn_netto * $this->getVatRate()->getAsFactor());
    }

    public function getEkNnnBrutto()
    {
        return $this->getter('ek_nnn_brutto');
    }

    public function setUserId($value)
    {
        $this->setter('user_id', $value);
        return $this;
    }

    public function setDateAdded(DateObj $date): bool
    {
        return $this->setter('added', $date->db());
    }

    /**
     * @param DateObj $date
     * @return bool
     * @deprecated
     * @see setDateAdded()
     */
    public function setDatum(DateObj $date): bool
    {
        return $this->setDateAdded($date);
    }

    public function getDateAdded(): DateObj
    {
        return new DateObj($this->getter('added'));
    }

    /**
     * @return DateObj
     * @see getDateAdded()
     * @deprecated
     */
    public function getDatum(): DateObj
    {
        return $this->getDateAdded();
    }

    /**
     * @return DateObj
     */
    public function getDateLast(): DateObj
    {
        return new DateObj($this->getter('last_date'));
    }

    /**
     * @return DateObj
     */
    public function getVersandDatum(): DateObj
    {
        return new DateObj($this->getter('versand_datum'));
    }

    public function setGrosMemo($value)
    {
        return $this->setter('gros_memo', $value);
    }

    public function appendGrosMemo($memo)
    {
        $old_memo = $this->getGrosMemo();
        if ($old_memo) {
            $old_memo .= "\n\n";
        }

        return $this->setGrosMemo($old_memo . "\n\n" . $memo);
    }

    public function getGrosMemo()
    {
        return $this->getter('gros_memo');
    }

    public function setSupplierId(int $supplier_id): bool
    {
        $status = $this->setter('supplier_id', $supplier_id);

        if ($status) {
            $this->setStatsSupplierId($supplier_id);
        }

        return $status;
    }

    public function setStatsSupplierId(int $supplier_id): bool
    {
        return $this->setter('stats_supplier_id', $supplier_id);
    }

    public function setGrosBestellt($status)
    {
        return $this->setter('gros_bestellt', $status);
    }

    public function setGrosLieferscheinBemerkung($bemerkung)
    {
        return $this->setter('gros_lieferschein_bemerkung', $bemerkung);
    }

    public function setStornoMsg($storno_msg)
    {
        return $this->setter('storno_msg', $storno_msg);
    }

    public function setGrosNeuerEk($gros_neuer_ek)
    {
        return $this->setter('gros_neuer_ek', $gros_neuer_ek);
    }

    public function setGrosBestellDatum(DateObj $date): bool
    {
        return $this->setter('gros_bestell_datum', $date->db());
    }

    /**
     * gibt den Mwstsatz zurück
     * @return string
     */
    public function getMwstSatz()
    {
        return $this->getter('mwst_satz');
    }

    public function getVatRate(): VatRate
    {
        return new VatRateSimple($this->getMwstSatz());
    }


    public function getPreis()
    {
        return $this->getter('preis');
    }

    /**
     * @return float
     */
    public function getVkBrutto()
    {
        return $this->getter('preis');
    }

    public function getVkNetto()
    {
        return round($this->getVkBrutto() / $this->getVatRate()->getAsFactor(), 2);
    }

    /**
     * @return float
     */
    public function getVkBruttoSum()
    {
        return $this->getter('preis') * $this->getQuantity();
    }

    /**
     * @return float
     */
    public function getVkNettoSum()
    {
        return round($this->getVkBruttoSum() / $this->getVatRate()->getAsFactor(), 2);
    }

    /**
     * @return float
     */
    public function getPreisSumme()
    {
        return $this->getVkBrutto() * $this->getQuantity();
    }

    public function getVkAutoMwst()
    {
        switch ($this->order->getTaxStatus()) {
            case OrderConst::TAX_STATUS_NORMAL:
                return $this->getPreis();
            case OrderConst::TAX_STATUS_IG_LIEFERUNG:
            case OrderConst::TAX_STATUS_IG_AUSFUHRLIEFERUNG:
            case OrderConst::TAX_STATUS_SOLAR_STEUERFREI:
                return round($this->getPreis() / $this->getVatRate()->getAsFactor(), 2);
            default:
                throw new DevException('tax_status nicht gesetzt');
        }
    }

    public function getVkSummeAutoMwst()
    {
        switch ($this->order->getTaxStatus()) {
            case OrderConst::TAX_STATUS_NORMAL:
                return $this->getPreisSumme();
            case OrderConst::TAX_STATUS_IG_LIEFERUNG:
            case OrderConst::TAX_STATUS_IG_AUSFUHRLIEFERUNG:
            case OrderConst::TAX_STATUS_SOLAR_STEUERFREI:
                return round($this->getPreisSumme() / $this->getVatRate()->getAsFactor(), 2);
            default:
                throw new DevException('tax_status nicht gesetzt');
        }
    }

    public function getProductNr()
    {
        return $this->getter('product_nr');
    }

    /**
     * Setzt die Anlieferungs LagerId (legt fest wohin der großhändler die ware schicken soll/oder woher die Ware kommt)
     * @param int $value
     * @return bool
     * @deprecated
     */
    public function setAnlieferungLagerId($lager_id)
    {
        return $this->setter('anlieferung_lager', $lager_id);
    }


    public function setLiefertermin($liefertermin)
    {
        $liefertermin_date = new DateObj();

        if ($liefertermin == -1) {
            $liefertermin_text = 'leider nicht mehr lieferbar';
        } elseif ($liefertermin == -2) {
            $liefertermin_text = '';
        } else {
            $lieferzeiten = config::gs_lieferzeiten('lieferzeiten');

            if (!isset($lieferzeiten[$liefertermin])) {
                throw new DevException('lieferzeit: nicht implementiert');
            }

            $liefertermin_date->addWerktage($lieferzeiten[$liefertermin]['days']);
            $liefertermin_text = $lieferzeiten[$liefertermin]['text_customer'];
        }

        $this->setter('liefertermin', $liefertermin);

        $this->setLieferterminDate($liefertermin_date, !((bool)$liefertermin_text));

        if ($liefertermin_text) {
            $this->setLieferterminText($liefertermin_text);

            $delivery = new OrderItemEstimatedDelivery();
            $delivery->setEstimatedDeliveryDate($liefertermin_date);
            $delivery->setCustomerInformed(false);
            $delivery->setNote('bestätigte Lieferzeit ' . ($liefertermin_text));

            $this->addEstimatedDelivery($delivery);
        }
    }

    public function setLieferterminText($liefertermin_text)
    {
        $this->setter('liefertermin_text', $liefertermin_text);
    }

    public function getLieferterminText()
    {
        return $this->getter('liefertermin_text');
    }

    public function getLiefertermin()
    {
        return $this->getter('liefertermin');
    }

    public function setLieferterminDate(DateObj $liefertermin_date, $update_estimated_delivery_date = true)
    {
        $status = $this->setter('liefertermin_date', $liefertermin_date->db());

        $extimated_date = $this->getEstimatedDeliveryDate();

        if ($liefertermin_date->format(DateObj::MYSQL_DATE) == $extimated_date->format(DateObj::MYSQL_DATE)) {
            return $status;
        }

        if ($update_estimated_delivery_date) {
            $delivery = new OrderItemEstimatedDelivery();
            $delivery->setEstimatedDeliveryDate($liefertermin_date);
            $delivery->setCustomerInformed(false);
            $delivery->setNote('bestätigte Lieferzeit');

            $this->addEstimatedDelivery($delivery);
        }
    }

    public function setLieferbaranzeigeAndSetEstimatedDeliveryDate(int $lieferbaranzeige): bool
    {
        $status = $this->setLieferbaranzeige($lieferbaranzeige);

        $shop_id = $this->order->getShopId();

        $row = ProductRepositoryLegacy::getLieferbaranzeige($shop_id)[$lieferbaranzeige] ?? [];

        //voraussichtliche lieferzeit
        $delivery_date = new DateObj();
        $delivery_date->addWerktage($row['lieferbar_days'] ?? 0);

        $delivery = new OrderItemEstimatedDelivery();
        $delivery->setEstimatedDeliveryDate($delivery_date);
        $delivery->setCustomerInformed(true);
        $delivery->setNote('hinterlegte Lieferzeit (' . ProductRepositoryLegacy::getLieferbaranzeigeName($shop_id, $lieferbaranzeige) . ')');

        $this->addEstimatedDelivery($delivery);

        return $status;
    }

    public function setLieferbaranzeige(int $lieferbaranzeige): bool
    {
        return $this->setter('lieferbaranzeige', $lieferbaranzeige);
    }

    public function getLieferbaranzeige()
    {
        return $this->getter('lieferbaranzeige');
    }

    public function setTyp($value)
    {
        return $this->setter('typ', $value);
    }

    public function setTypValue($value)
    {
        return $this->setter('typ_value', $value);
    }

    public function getTyp()
    {
        return $this->getWarenkorbType();
    }

    public function getWarenkorbType()
    {
        return $this->daten['typ'];
    }

    public function getTypValue()
    {
        return $this->daten['typ_value'];
    }

    public function isDeliveryAble()
    {
        return (bool)ProductWarenkorbTypesRepository::isDelivery($this->getTyp());
    }

    /**
     * setzt das voraussichtliche Lieferdatum
     * @param DateObj $date
     * @return bool
     */
    public function setEstimatedDeliveryDate(DateObj $date): bool
    {
        return $this->setter('estimated_delivery_date', $date->db());
    }

    /**
     * gibt das voraussichtliche Lieferdatum zurück
     * @return DateObj
     */
    public function getEstimatedDeliveryDate(): DateObj
    {
        return new DateObj($this->getter('estimated_delivery_date'));
    }


    /**
     * setzt das voraussichtliche Lieferdatum was den Kunden mitgeteilt wurde
     * @param DateObj $date
     * @return bool
     */
    public function setEstimatedDeliveryDateCustomer(DateObj $date): bool
    {
        return $this->setter('estimated_delivery_date_customer', $date->db());
    }

    /**
     * gibt das voraussichtliche Lieferdatum zurück das den Kunden mitgeteilt wurde
     * @return DateObj
     */
    public function getEstimatedDeliveryDateCustomer(): DateObj
    {
        return new DateObj($this->getter('estimated_delivery_date_customer'));
    }

    public function addEstimatedDelivery(OrderItemEstimatedDelivery $estimated_delivery): void
    {
        $this->loadEstimatedDesliveries();

        $estimated_delivery->setOrderItem($this);

        $this->estimated_deliveries[] = $estimated_delivery;

        $this->setEstimatedDeliveryDate($estimated_delivery->getEstimatedDeliveryDate());

        if ($estimated_delivery->isCustomerInformed()) {
            $this->setEstimatedDeliveryDateCustomer($estimated_delivery->getEstimatedDeliveryDate());
        }
    }

    /**
     *
     * @return OrderItemEstimatedDelivery[]
     */
    public function getEstimatedDeliveries()
    {
        if ($this->estimated_deliveries === null) {
            $this->loadEstimatedDesliveries();
        }

        return $this->estimated_deliveries;
    }

    protected function loadEstimatedDesliveries()
    {
        $this->estimated_deliveries = [];

        if (!$this->getOrderItemId() || $this->isImterimsOrderItemId()) {
            return;
        }

        $result = db::getInstance()->query("
            SELECT
                order_item_estimated_delivery.delivery_id,
                order_item_estimated_delivery.order_item_id,
                order_item_estimated_delivery.added,
                order_item_estimated_delivery.delivery_date,
                order_item_estimated_delivery.note,
                order_item_estimated_delivery.customer_informed
            FROM
                order_item_estimated_delivery
            WHERE
                order_item_estimated_delivery.order_item_id = '" . $this->getOrderItemId() . "'
        ");

        foreach ($result as $daten) {
            $this->estimated_deliveries[] = new OrderItemEstimatedDelivery($daten);
        }
    }

    public function saveEstimatedDesliveries()
    {
        if (!$this->estimated_deliveries) {
            return;
        }

        $db = db::getInstance();

        foreach ($this->estimated_deliveries as $key => $delivery) {
            $obj = $delivery->getSmartDataObj();

            if ($obj->getObjStatus() == 'loaded') {
                continue;
            }

            if ($obj->getObjStatus() == 'add') {
                $delivery->setAdded(new DateObj());
            }

            $sql = [];
            foreach ($obj->getChanges() as $field => $value) {
                switch ($field) {
                    default:
                        $sql[] = "order_item_estimated_delivery.$field = '" . $db->escape($value) . "'";
                }
            }

            switch ($obj->getObjStatus()) {
                case 'add':

                    $sql[] = "order_item_estimated_delivery.order_item_id = '" . $db->escape($this->getOrderItemId()) . "'";

                    $db->query("
                        INSERT INTO
                            order_item_estimated_delivery
                        SET
                            " . implode(',', $sql) . "
                    ");

                    $delivery->setDeliveryId($db->insert_id());
                    $obj->setSaved();
                    break;
                case 'update':
                    $db->query("
                        UPDATE
                            order_item_estimated_delivery
                        SET
                            " . implode(',', $sql) . "
                        WHERE
                            order_item_estimated_delivery.delivery_id = '" . $delivery->getDeliveryId() . "'
                    ");
                    $obj->setSaved();
                    break;
                case 'del':
                    $db->query("
                        DELETE FROM
                            order_item_estimated_delivery
                        WHERE
                            order_item_estimated_delivery.delivery_id = '" . $delivery->getDeliveryId() . "'
                    ");

                    unset($this->estimated_deliveries[$key]);
                    break;
            }
        }
    }

    /**
     * setzt den Status einer bestellposition sofort um
     * @param $order_item_id
     * @param $status
     */
    public static function setStatusInstant($order_item_id, $status)
    {
        $order = order_repository::getOrderByOrderItemId($order_item_id);

        $order_item = $order->getOrderItemByOrderItemId($order_item_id);
        $order_item->setStatus($status);

        $order->save();
    }

    public function isDelivery()
    {
        return ProductWarenkorbTypesRepository::isDelivery($this->getTyp());
    }

    public function getSupplierName()
    {
        return SupplierRepository::getSupplierName($this->getSupplierId());
    }
}
