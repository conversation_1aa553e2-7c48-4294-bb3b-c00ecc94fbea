<?php

namespace wws\Order;

class OrderConst
{
    const STATUS_ANGEBOT_OFFEN = 210;
    const STATUS_ANGEBOT_STORNIERT = 220;

    const STATUS_STORNO = 100;
    const STATUS_BEENDET = 80;

    const STATUS_ZUSTELLUNG_BEENDET = 75;
    const STATUS_ZUSTELLUNG_AUSGELOEST = 74;

    const STATUS_WARTE_AUSLIEFERUNG = 40;

    const STATUS_ARTIKEL_BESTELLT = 45;
    const STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND = 46;

    const STATUS_AB_LEERFELD = 20;
    const STATUS_WARTE_EINKAUF = 25;
    const STATUS_WARTE_ZAHLUNG = 37;
    const STATUS_AUSLIEFERUNG_VERSCHOBEN = 51;
    const STATUS_ABHOLBEREIT_GROSSIST = 47;
    const STATUS_ABSAGE = 28;
    const STATUS_GROSS_BESTAETIGT = 30;

    const STATUS_PARK = 99;
    const STATUS_NEU_SHOP = 1;
    const STATUS_EBAY_ZAHLUNGSABWICKLUNG = 3;

    const STATUS_60 = 60; //wird teilweise als "schwelle" verwednet, ob unterwegs oder nicht
    const STATUS_10 = 10;
    const STATUS_0 = 0;

    const STATUS_WARTE_AUF_KLAERUNG = 29;

    //depreacted
    const STATUS_AUFTRAGSANNAHME = 2;
    const STATUS_ARTIKEL_ANGEFRAGT = 41;

    public const TAX_STATUS_NORMAL = 1; //Steuern normal berechnen
    public const TAX_STATUS_IG_LIEFERUNG = 2; //Steuerfreie IG-Lieferung
    public const TAX_STATUS_IG_AUSFUHRLIEFERUNG = 3; //Steuerfreie Ausfuhrlieferung
    public const TAX_STATUS_SOLAR_STEUERFREI = 4; //

    public const ORDER_ORIGIN_SHOP = 'shop';
    public const ORDER_ORIGIN_AMAZON = 'amazon';
    public const ORDER_ORIGIN_CROWDFOX = 'cfox';
    public const ORDER_ORIGIN_EBAY = 'ebay';
    public const ORDER_ORIGIN_MEINPAKET = 'meinpaket';
    public const ORDER_ORIGIN_RAKUTEN = 'rakuten';
    public const ORDER_ORIGIN_C24 = 'c24';
    public const ORDER_ORIGIN_KAUFLAND = 'kaufland';
    public const ORDER_ORIGIN_IDEALO_DIREKT = 'idealodi';
    public const ORDER_ORIGIN_SHOPWARE = 'shopware';
    public const ORDER_ORIGIN_PHONE = 'tel';
    public const ORDER_ORIGIN_REWE = 'rewe';
    public const ORDER_ORIGIN_LADEN = 'laden';
    public const ORDER_ORIGIN_GALAXUS = 'galaxus';

    public const ORDER_TYPE_NORMAL = 'normal';
    public const ORDER_TYPE_SERVICE = 'service';
    public const ORDER_TYPE_EK_BESTELLUNG = 'ek_bestellung';
    public const ORDER_TYPE_OFFER = 'offer';
    public const ORDER_TYPE_RETURN = 'return'; //Auftragstyp um Bestand zu Bewegen,  ohne dass das als Kauf bewertet wird. (Z.B. Ware an Lieferanten retournieren, Eigenbedarf, etc)

    public const WARENKORB_TYP_VERSAND = 'versand';
    public const WARENKORB_TYP_ASWO = 'aswo';
    public const WARENKORB_TYP_VERSANDAUTO = 'versandauto';
    public const WARENKORB_TYP_PRODUKT = 'produkt';
    public const WARENKORB_TYP_LEISTUNG = 'leistung';
    public const WARENKORB_TYP_GUTSCHEIN = 'gutschein';

    public const BEWERTUNGS_MAIL_OPEN = 0;
    public const BEWERTUNGS_MAIL_SENT = 1;
    public const BEWERTUNGS_MAIL_SUPPRESSED = 2;
    public const BEWERTUNGS_MAIL_SKIPED = 3;

    public const PAYMENT_VORKASSE = 3;
    public const PAYMENT_KREDITKARTE = 1;
    public const PAYMENT_NACHNAHME = 2;
    public const PAYMENT_BAR = 9;
    public const PAYMENT_RECHNUNG = 4;
    public const PAYMENT_VERSCHIEDENE = 18;
    public const PAYMENT_VERRECHNUNG = 19;
    public const PAYMENT_GUTSCHEIN = 20;
    public const PAYMENT_GUTSCHEIN_EP = 21;
    public const PAYMENT_FINANZIERUNG = 14;
    public const PAYMENT_FINANZIERUNG_HANSEATIC = 14;
    public const PAYMENT_FINANZIERUNG_COMMERZ = 24;
    public const PAYMENT_KASSE = 22;
    public const PAYMENT_EC = 11;
    public const PAYMENT_PAYPAL = 15;
    public const PAYMENT_RECHNUNG_DOMNOWSKI = 13;
    public const PAYMENT_SOFORTUEBERWEISUNG = 23;
    public const PAYMENT_RECHNUNG_BILLSAFE = 25;
    public const PAYMENT_AMAZON = 16;
    public const PAYMENT_BARZAHLEN = 26;
    public const PAYMENT_MEINPAKET = 27;
    public const PAYMENT_ALLYOUNEED = 27;
    public const PAYMENT_BILLPAY_RECHUNG = 29;
    public const PAYMENT_BILLPAY_RATENKAUF = 30;
    public const PAYMENT_BILLPAY_LASTSCHRIFT = 31;
    public const PAYMENT_CROWDFOX = 32;
    public const PAYMENT_CHECK24 = 28;
    public const PAYMENT_RAKUTEN = 34;
    public const PAYMENT_KAUFLAND = 35;
    public const PAYMENT_EBAY = 36;
    public const PAYMENT_TEILZAHLUNG = 18;
    public const PAYMENT_ECOM_IDEALO = 37;
    public const PAYMENT_ECOM_UNZER = 39; //achtung bei K11 ist das PAYMENT_KREDITKARTE und PAYMENT_SOFORTUEBERWEISUNG
    public const PAYMENT_ECOM_GALAXUS = 50;
    public const PAYMENT_DIREKTUEBERWEISUNG = 40;

    public const PAYMENT_STATUS_NEUTRAL = 0;

    public const PAYMENT_STATUS_LEGACY_0 = 0;
    public const PAYMENT_STATUS_LEGACY_1 = 1;

    //@todo zahlungstatuse hier entfernen -> in das jweilige Modul der Zahlungsart schieben
    public const PAYMENT_STATUS_KREDITKARTE_FAILED = -1;
    public const PAYMENT_STATUS_KREDITKARTE_OPEN = 0;
    public const PAYMENT_STATUS_KREDITKARTE_RESERVED = 1;
    public const PAYMENT_STATUS_KREDITKARTE_BOOKED = 2;

    public const PAYMENT_STATUS_UNZER_OPEN = 0;
    public const PAYMENT_STATUS_UNZER_BOOKED = 1;

    public const PAYMENT_STATUS_PAYPAL_OPEN = 0;
    public const PAYMENT_STATUS_PAYPAL_BOOKED = 1;

    public const PAYMENT_STATUS_EBAY_OPEN = 0;
    public const PAYMENT_STATUS_EBAY_BOOKED = 1;

    public const PAYMENT_STATUS_VORKASSE_OPEN = 0;
    public const PAYMENT_STATUS_VORKASSE_RECEIVED = 1;

    public const PAYMENT_STATUS_FINANZIERUNG_OHNE_ANTRAG = 0;
    public const PAYMENT_STATUS_FINANZIERUNG_VORLAEUFIG_GENEHMIGT = 1;
    public const PAYMENT_STATUS_FINANZIERUNG_GENEHMIGT = 2;

    public const PAYMENT_STATUS_SOFORTUEBERWEISUNG_OPEN = 0;
    public const PAYMENT_STATUS_SOFORTUEBERWEISUNG_RECEIVED = 1;

    public const PAYMENT_STATUS_DIREKTUEBERWEISUNG_OPEN = 0;
    public const PAYMENT_STATUS_DIREKTUEBERWEISUNG_RECEIVED = 1;

    public const PAYMENT_STATUS_BARZAHLEN_FREIGEGEBEN = 1;

    public const PAYMENT_STATUS_RAKUTEN_OPEN = 0;
    public const PAYMENT_STATUS_RAKUTEN_FREIGEGEBEN = 1;

    public const PAYMENT_STATUS_BILLPAY_FREIGEGEBEN = 1;

    public const PAYMENT_STATUS_KAUFLAND_OK = 1;
    public const PAYMENT_STATUS_AMAZON_OK = 1;
    public const PAYMENT_STATUS_CHECK24_OK = 1;
    public const PAYMENT_STATUS_CROWDFOX_OK = 1;
    public const PAYMENT_STATUS_IDEALO_OK = 1;
    public const PAYMENT_STATUS_GALAXUS_OK = 1;

    public const STORNO_REASON_MISSING_PAYMENT = 1;

    public const TAG_ASWO_FAV = 'aswofav';
    public const TAG_KREMPL_BESCHAFFUNG = 'kbeschaff';
    public const TAG_OUTLAND = 'aus';
    public const TAG_ASWO_EX = 'aswoex';
    public const TAG_PRIORITY = 'priority';
    public const TAG_RECHNUNGKORREKTUR = 'rechungskorrektur';
    public const TAG_EBAY_FEE = 'ebay_fee';
    public const TAG_AMAZON = 'amz';
    public const TAG_AB_LEERFELD_AUTOMATIC_EXECUTED = 'leerfeldautoexe';
    public const TAG_HIGHLIGHT = 'highlight';
    public const TAG_ABHOLUNG = 'abh';
    public const TAG_UMGEBUNG = 'umg';
    public const TAG_PAYPAL_EXPRESS = 'paypal_express';
    public const TAG_ABHOLUNG_KUNDE_INFORMIERT = 'abhi';
    public const TAG_ISLAND = 'ins';
    public const TAG_UNREAD_MAIL = 'unreadMail';
    public const TAG_AB_LEERFELD_AUTOMATIC_SUCCESS = 'leerfeldauto';
    public const TAG_SERVICE = 'service';
    public const TAG_AMEX = 'amex';
    public const TAG_EBAY = 'ebay';
    public const TAG_CHECK24 = 'c24';
    public const TAG_B_WARE = 'bware';
    public const TAG_EBAY_CANCELLED = 'ebay_cancelled';
    public const TAG_FAKE = 'fake';
    public const TAG_AMAZON_FBA = 'fba';
    public const TAG_EBAYPLUS = 'ebayplus';
    public const TAG_ASWO = 'aswo';
    public const TAG_ABHOLUNG_BEREITGESTELLT = 'abhb';
    public const TAG_FAKE_VERDACHT = 'fakever';
    public const TAG_WARN = 'warn';
}
