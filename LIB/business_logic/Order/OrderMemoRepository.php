<?php

namespace wws\Order;

use bqp\db\db_generic;
use bqp\FileAttachment\FileAttachment;
use bqp\storage\storage_mount_manager;
use bqp\Utils\FileUtils;
use service_loader;
use wws\Customer\Customer;
use wws\Customer\CustomerMemo;

class OrderMemoRepository
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * @param Order $order
     * @param OrderMemo $order_memo
     * @return int
     */
    public function saveOrderMemo(Order $order, OrderMemo $order_memo): int
    {
        $attachments = '';
        if ($order_memo->getAttachments()) {
            $attachments = serialize($order_memo->getAttachments());
        }

        $this->db->query("
            INSERT INTO
                order_memo
            SET
                order_memo.order_id = '" . $order->getOrderId() . "',
                order_memo.user_id = '" . $order_memo->getUserId() . "',
                order_memo.memo_status = 1,
                order_memo.datum = NOW(),
                order_memo.memo_type = '" . $this->db->escape($order_memo->getMemoType()) . "',
                order_memo.bemerkung = '" . $this->db->escape($order_memo->getText()) . "',
                order_memo.attachments = '" . $this->db->escape($attachments) . "'
        ");

        $order_memo_id = $this->db->insert_id();

        $order_memo->setOrderMemoId($order_memo_id);

        if ($order_memo->isAutoMemo()) {
            $this->addCustomerMemo($order, $order_memo);
        }

        return $order_memo_id;
    }

    private function addCustomerMemo(Order $order, OrderMemo $order_memo): void
    {
        $customer = new Customer($order->getCustomerId());

        $memo = new CustomerMemo('(' . $order->getAuftnr() . ') ' . $order_memo->getText());
        $memo->setOrderMemoId($order_memo->getOrderMemoId());
        $customer->addMemo($memo);
        $customer->save();
    }

    public function getOrderMemosAsArray(int $order_id): array
    {
        $result = $this->db->query("
            SELECT
                order_memo.order_memo_id,
                DATE_FORMAT(order_memo.datum,'%d.%m.%Y %H:%i:%s') AS datum,
                order_memo.memo_type,
                order_memo.bemerkung,

                CONCAT(user_accounts.firstname,' ',user_accounts.surname) AS bearbeiter
            FROM
                order_memo INNER JOIN
                user_accounts ON (order_memo.user_id = user_accounts.user_id)
            WHERE
                order_memo.order_id = '" . $order_id . "' AND
                order_memo.memo_status = 1
            ORDER BY
                order_memo.datum ASC
        ");

        $result->addMappingArray('memo_type', OrderMemo::$memo_types, 'typ_name');

        return $result->asArray();
    }

    public function deleteOrderMemoByMemoId(int $order_memo_id, int $order_id = null): void
    {
        $where = '';
        if ($order_id !== null) {
            $where = " AND order_memo.order_id = '" . (int)$order_id . "'";
        }

        $this->db->query("
            UPDATE
                order_memo
            SET
                order_memo.memo_status = 0
            WHERE
                order_memo.order_memo_id = '" . $order_memo_id . "'
                $where
        ");
    }

    public function getMemoTypeNames(): array
    {
        return OrderMemo::$memo_types;
    }

    public function unserializeOrderMemoAttachments(string $raw): array
    {
        if (!$raw) {
            return [];
        }

        return unserialize($raw, ['allowed_classes' => false]);
    }

    public function getFilesystem(): storage_mount_manager
    {
        $factory = service_loader::getStorageFactory();
        $filesystem = $factory->get('wws');

        return $filesystem;
    }


    public function saveAttachment(int $order_id, FileAttachment $attachment, string $content): void
    {
        $path = $attachment->getStorageUrl();

        if (!$path) {
            $filename = FileUtils::normalizeFilename($attachment->getFilename());

            $path = 'wws://orders/anlagen/' . $order_id . '_' . FileUtils::appendBeforExtenstion($filename, md5($content));
        }

        if (!$attachment->getFilesize()) {
            $attachment->setFilesize(strlen($content));
        }

        $attachment->setStorageUrl($path);

        $storage = $this->getFilesystem();

        if ($storage->has($path)) {
            //muss nix getan werden -> der hash ist auf dem content
        } else {
            $storage->write($path, $content);
        }
    }

    public function editDirect(int $order_memo_id, int $order_id, string $memo_type, string $bemerkung): void
    {
        $this->db->query("
            UPDATE
                order_memo
            SET
                order_memo.order_id = '$order_id',
                order_memo.memo_type = '" . $this->db->escape($memo_type) . "',
                order_memo.bemerkung = '" . $this->db->escape($bemerkung) . "'
            WHERE
                order_memo.order_memo_id = $order_memo_id AND
                order_memo.order_id = $order_id
        ");
    }
}
