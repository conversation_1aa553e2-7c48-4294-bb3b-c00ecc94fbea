<?php

namespace wws\Order;

use bqp\db\db_generic;
use service_loader;
use wws\Country\CountryConst;

class OrderTags
{
    private db_generic $db;
    private ?array $order_tags = null;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }


    public function getOrderTags(): array
    {
        if ($this->order_tags === null) {
            $this->order_tags = $this->db->query("
                SELECT
                    einst_order_tags.order_tag,
                    einst_order_tags.image,
                    einst_order_tags.name,
                    einst_order_tags.show_global,
                    einst_order_tags.link
                FROM
                    einst_order_tags
            ")->asArray('order_tag');

            foreach ($this->order_tags as $key => $tag) {
                $this->order_tags[$key]['show_global'] = (bool)$this->order_tags[$key]['show_global'];
                $text = $tag['order_tag'];
                if ($tag['name']) {
                    $text .= ': ' . $tag['name'];
                }
                $this->order_tags[$key]['text'] = $text;
            }
        }

        return $this->order_tags;
    }

    /**
     * Prüft ob der übergebene Tag eine Flagge ist
     * @param string $tag
     * @return bool
     */
    public static function isCountryFlag(string $tag): bool
    {
        return str_starts_with($tag, 'F_');
    }

    /**
     * Gibt den Flaggen Tag für die $country_id zurück falls existent
     * @param int $country_id
     * @return null|string
     */
    public static function getCountryFlagTagForCountry(int $country_id): ?string
    {
        if ($country_id === CountryConst::COUNTRY_ID_AT) {
            return 'F_AT';
        }

        return null;
    }

    /**
     * @param string|array $order_tags
     * @param ?int $order_id
     * @param bool $show_all
     * @return string
     */
    public static function toHtml($order_tags, ?int $order_id, bool $show_all = false): string
    {
        $tags = [];
        if ($order_tags) {
            if (is_string($order_tags)) {
                $tags = self::extendOrderTags(explode(',', $order_tags));
            } elseif (is_array($order_tags)) {
                $tags = self::extendOrderTags($order_tags);
            }
        }

        $return = '';

        foreach ($tags as $tag) {
            if (!$show_all && !$tag['show_global']) {
                continue;
            }

            $print_link = false;
            if ($order_id && isset($tag['link']) && $tag['link']) {
                $print_link = true;
            }

            if ($print_link) {
                $link = sprintf($tag['link'], $order_id);
                $return .= sprintf('<a onclick="popup_large(event)" href="%s" target="_blank">', $link);
            }

            if (isset($tag['image']) && $tag['image']) {
                $return .= '<img src="/res/images/' . $tag['image'] . '" alt="' . $tag['name'] . '" title="' . $tag['name'] . '" class="order_tag_img"> ';
            } else {
                if ($return) {
                    $return .= ' / ';
                }
                $return .= '<span class="order_tag_text">' . $tag['name'] . '</span>';
            }

            if ($print_link) {
                $return .= '</a>';
            }
        }

        if ($return) {
            $return = '<nobr>' . $return . '</nobr>';
        }

        return $return;
    }

    /**
     * @param array $row
     * @return string
     */
    public static function tableHelperOrderTagsSmall(array $row): string
    {
        $order_id = $row['order_id'] ?? null;

        return self::toHtml($row['order_tags'], $order_id, false);
    }

    /**
     * @param array $row
     * @return string
     */
    public static function tableHelperOrderTags(array $row): string
    {
        $order_id = $row['order_id'] ?? null;

        return self::toHtml($row['order_tags'], $order_id, true);
    }

    /**
     * @param array $tags
     * @return array
     */
    public static function extendOrderTags(array $tags): array
    {
        //wenn flagge, dann ausland entfernen
        $tkey = null;
        $flag = false;
        foreach ($tags as $key => $tag) {
            if ($tag === OrderConst::TAG_OUTLAND) {
                $tkey = $key;
                continue;
            }

            if (self::isCountryFlag($tag)) {
                $flag = true;
            }
        }

        if ($flag && $tkey !== null) {
            unset($tags[$tkey]);
        }

        return array_map([OrderTags::class, 'extendOrderTag'], $tags);
    }

    /**
     * @param string $tag
     * @return array
     */
    public static function extendOrderTag(string $tag): array
    {
        $instance = service_loader::get(OrderTags::class);

        $tags = $instance->getOrderTags();

        $config = $tags[$tag] ?? [
            'image' => false,
            'name' => $tag,
            'show_global' => 0
        ];

        $config['id'] = $tag;

        if (self::isCountryFlag($tag)) {
            $config = [
                'image' => 'tags/' . strtolower($tag) . '.png',
                'name' => $tag,
                'show_global' => 1
            ];
        }

        return $config;
    }

    /**
     * @param string $order_tags
     * @param string $tag
     * @return bool
     */
    public static function is(string $order_tags, string $tag): bool
    {
        return in_array($tag, explode(',', $order_tags));
    }
}
