<?php

namespace wws\Order;

use bqp\FileAttachment\FileAttachment;
use env;

class OrderMemo
{
    public const SONSTIGES = 'sonstiges';
    public const EINKAUF = 'einkauf';
    public const VERSAND = 'versand';
    public const VERKAUF = 'verkauf';
    public const BUCHHALTUNG = 'buchhaltung';
    public const JURA = 'jura';
    public const REKLA = 'rekla';
    public const ANRUF = 'anruf';

    public static $memo_types = [
        OrderMemo::SONSTIGES => 'Sonstiges',
        OrderMemo::EINKAUF => 'Einkauf',
        OrderMemo::VERSAND => 'Versand',
        OrderMemo::BUCHHALTUNG => 'Buchhaltung',
        OrderMemo::JURA => 'Rechtliches',
        OrderMemo::REKLA => 'Reklamation',
        OrderMemo::VERKAUF => 'Verkauf',
        OrderMemo::ANRUF => 'Anruf'
    ];

    public const USER_SYSTEM = -1;

    private $order_memo_id = null;
    private $text;
    private $user_id;
    private $memo_type;
    private $autoMemo = true;
    private $attachments = [];

    /**
     * neuer Memofeld eintrag
     *
     * @param string $text Eintrag
     * @param string $memo_type Typ des Eintrags (Klassen Constanten nutzen)
     */
    public function __construct(string $text = '', string $memo_type = self::SONSTIGES, int $user_id = 0)
    {
        $this->text = $text;
        $this->memo_type = $memo_type;

        if ($user_id) {
            $this->user_id = $user_id;
        } else {
            $this->user_id = env::getUserId();
        }
    }

    /**
     * Setzt den Text des Eintrags.
     *
     * @param string $text Eintrag
     */
    public function setText(string $text): void
    {
        $this->text = $text;
    }

    /**
     * fügt Text an
     * @param string $text
     */
    public function addText(string $text): void
    {
        $this->text .= $text;
    }

    /**
     * fügt Text als neue Zeile hinzu
     * @param string $text
     */
    public function addLine(string $text): void
    {
        if ($this->text) {
            $this->text .= "\n";
        }

        $this->text .= $text;
    }

    public function setMemoType(string $memo_type): void
    {
        $this->memo_type = $memo_type;
    }

    /**
     * Überschreibt den aktuellen Benutzer
     *
     * @param int $user_id User Id des verfassers
     */
    public function setUserId(int $user_id): void
    {
        $this->user_id = $user_id;
    }

    /**
     * Legt fest ob der Eintrag automatisch den Kundenmemo hinzugefügt werden soll
     *
     * @param bool $value
     */
    public function setAutoMemo(bool $value): void
    {
        $this->autoMemo = $value;
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function getMemoType(): string
    {
        return $this->memo_type;
    }

    public function getUserId(): int
    {
        return $this->user_id;
    }

    public function isAutoMemo(): bool
    {
        return $this->autoMemo;
    }

    /**
     * @param array $attachment
     * @see $this->addFileAttachment()
     * @deprecated
     */
    public function addAttachment(array $attachment): void
    {
        $this->attachments[] = $attachment;
    }

    public function addFileAttachment(FileAttachment $attachment): void
    {
        $old = [
            'file' => $attachment->getStorageUrl(),
            'name' => $attachment->getFilename(),
            'typ' => $attachment->getExtension()
        ];

        $this->addAttachment($old);
    }

    public function setAttachments(array $attachments): void
    {
        $this->attachments = $attachments;
    }

    /**
     * @return array
     */
    public function getAttachments(): array
    {
        return $this->attachments;
    }

    public function setOrderMemoId(int $order_memo_id): void
    {
        $this->order_memo_id = $order_memo_id;
    }

    public function getOrderMemoId(): ?int
    {
        return $this->order_memo_id;
    }

    public function hasContent(): bool
    {
        return !empty($this->text) || !empty($this->attachments);
    }
}
