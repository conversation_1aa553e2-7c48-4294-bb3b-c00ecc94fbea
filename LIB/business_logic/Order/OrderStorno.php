<?php

namespace wws\Order;

use bqp\Date\DateObj;
use bqp\Exceptions\InputException;
use bqp\Json;
use bqp\Model\SmartDataObj;
use db;
use ErrorException;
use order_repository;
use service_loader;

class OrderStorno
{
    public const STORNO_ART_AUFTRAG = 'auftrag';
    public const STORNO_ART_ARTIKEL = 'artikel';

    public const STORNO_STATUS_ENDED = 5;
    public const STORNO_STATUS_GROSSIST_WIEDER = 4;
    public const STORNO_STATUS_WIEDERBELEGE = 1;
    public const STORNO_STATUS_ALTERNATIVE = 2;
    public const STORNO_STATUS_STORNIEREN = 3;
    public const STORNO_STATUS_GROSSIST = 0;

    protected SmartDataObj $daten;


    public function __construct(int $storno_id = null)
    {
        $this->daten = new SmartDataObj($this);

        if ($storno_id !== null) {
            $this->load($storno_id);
        } else {
            $this->loadDefaults();
        }
    }

    public function load(int $storno_id): void
    {
        $daten = db::getInstance()->singleQuery("
            SELECT
                orders_storno.storno_id,
                orders_storno.order_item_ids,
                orders_storno.old_status,    
                orders_storno.storno_reason_id,
                orders_storno.storno_grund,
                orders_storno.datum,
                orders_storno.storno_status,
                orders_storno.storno_art,
                orders_storno.alternativen,
                orders_storno.storno_party, 
                orders_storno.alternativtext,
                orders_storno.order_id
            FROM
                orders_storno
            WHERE
                orders_storno.storno_id = '" . (int)$storno_id . "'
        ");

        $this->daten->loadDaten($daten);
    }

    public function loadDefaults(): void
    {
        $this->setStornoStatus(self::STORNO_STATUS_GROSSIST);
        $this->setAlternativtext('');
    }

    public function save(): int
    {
        $this->validate();

        if (!$this->daten->isChange()) {
            return $this->getStornoId();
        }

        if ($this->daten->getObjStatus() == SmartDataObj::STATUS_NEW) {
            $this->setStornoStatus(self::STORNO_STATUS_GROSSIST_WIEDER);
            $this->setDatum(new DateObj());
        }

        $changes = $this->daten->getChanges(SmartDataObj::CHANGES_NEW_VALUES);

        $db = db::getInstance();

        $sql = [];
        foreach ($changes as $key => $value) {
            $sql[] = "orders_storno.$key = '" . $db->escape($value) . "'";
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db->query("
                    INSERT INTO
                        orders_storno
                    SET
                        " . implode(',', $sql) . "
                ");

                $this->daten->setterDirect('storno_id', $db->insert_id());
                $this->daten->setSaved();

                break;
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                    UPDATE
                        orders_storno
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        orders_storno.storno_id = '" . $this->getStornoId() . "'
                    LIMIT
                        1
                ");

                $this->daten->setSaved();
                break;
        }

        return $this->getStornoId();
    }

    /**
     * storniert den Auftrag/die Artikel und speichert das storno
     * @return int $storno_id
     */
    public function doStorno(): int
    {
        $this->validate();

        $order = service_loader::get(OrderRepository::class)->loadCached($this->getOrderId());

        $product_names = [];
        $statuse = [];

        foreach ($order->getOrderItems() as $order_item) {
            if (!$this->isOrderItemId($order_item->getOrderItemId())) {
                continue;
            }

            $statuse[$order_item->getOrderItemId()] = $order_item->getStatus();
            $product_names[] = $order_item->getProductName();

            $order_item->setStatus(OrderConst::STATUS_STORNO);
        }

        $this->setOldStatus($statuse);

        $grund = '';

        switch ($this->getStornoArt()) {
            case 'auftrag':
                $grund = 'Auftrag ' . $order->getAuftnr() . ' storniert. Grund: ' . $this->getStornoReason() . '';
                break;
            case 'artikel':
                $grund = 'Artikel "' . implode('", "', $product_names) . '" aus Auftrag ' . $order->getAuftnr() . ' storniert. Grund: ' . $this->getStornoReason() . '';
                break;
        }

        if ($this->getBemerkung()) {
            $grund .= "\nBemerkung: " . $this->getBemerkung();
        }

        $order_memo = new OrderMemo($grund, OrderMemo::VERKAUF);
        $order->addOrderMemo($order_memo);

        $order->save();

        return $this->save();
    }


    public function isStornoDone(): bool
    {
        return $this->getStornoStatus() > 0;
    }

    public function validate(): void
    {
        $input = new InputException();

        if (!$this->getOrderId()) {
            $input->add('order_id', 'Wählen Sie eine Bestellung aus.');
        }
        if (!$this->getOrderItemIds()) {
            $input->add('order_item_ids', 'Wählen Sie mindestens eine Position zum stornieren aus.');
        }
        if (!$this->getStornoReasonId()) {
            $input->add('storno_reason_id', 'Bitte wählen Sie ein Stornogrund aus.');
        }

        $input->check();
    }


    protected function isFullStorno(): bool
    {
        $order = $this->getOrder();

        foreach ($order->getOrderItems() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            if (!$this->isOrderItemId($order_item->getOrderItemId())) {
                return false;
            }
        }

        return true;
    }

    protected function autoSetStornoArt(): void
    {
        if (!$this->getOrderId()) {
            return;
        }

        $this->setStornoArt($this->isFullStorno() ? self::STORNO_ART_AUFTRAG : self::STORNO_ART_ARTIKEL);
    }

    public function setOrderItemIds(array $order_item_ids): bool
    {
        $changed = $this->daten->setter('order_item_ids', implode(',', $order_item_ids));

        if ($changed) {
            $this->autoSetStornoArt();
        }

        return $changed;
    }

    public function addOrderItemId(int $order_item_id): void
    {
        $order_item_ids = $this->getOrderItemIds();

        $order_item_ids[] = $order_item_id;

        $order_item_ids = array_unique($order_item_ids);

        $this->setOrderItemIds($order_item_ids);
    }


    /**
     * [order_item_id => status, order_item_id => status, ...]
     *
     * @param array $old_status
     * @return bool
     */
    public function setOldStatus(array $old_status): bool
    {
        return $this->daten->setter('old_status', self::serializeOldStatus($old_status));
    }

    public function setStornoReasonId(int $storno_reason_id): bool
    {
        $changed = $this->daten->setter('storno_reason_id', $storno_reason_id);

        if ($changed) {
            $this->setStornoParty(order_repository::getStornoPartyByReasonId($storno_reason_id));
        }

        return $changed;
    }

    public function setBemerkung(string $bemerkung): bool
    {
        return $this->daten->setter('storno_grund', $bemerkung);
    }

    public function setDatum(DateObj $datum): bool
    {
        return $this->daten->setter('datum', $datum->db());
    }

    public function setStornoStatus(int $storno_status): bool
    {
        return $this->daten->setter('storno_status', $storno_status);
    }

    public function setStornoArt(string $storno_art): bool
    {
        return $this->daten->setter('storno_art', $storno_art);
    }

    public function setAlternativen(string $alternativen): bool
    {
        return $this->daten->setter('alternativen', $alternativen);
    }

    public function setStornoParty(string $storno_party): bool
    {
        return $this->daten->setter('storno_party', $storno_party);
    }

    public function setAlternativtext(string $alternativtext): bool
    {
        return $this->daten->setter('alternativtext', $alternativtext);
    }

    public function setOrderId(int $order_id): bool
    {
        $changed = $this->daten->setter('order_id', $order_id);

        if ($changed) {
            $this->autoSetStornoArt();
        }

        return $changed;
    }

    public function getStornoId(): int
    {
        return (int)$this->daten->getter('storno_id');
    }

    public function getOrderItemIds(): array
    {
        $order_item_ids = $this->daten->getter('order_item_ids');

        if ($order_item_ids) {
            return explode(',', $order_item_ids);
        }

        return [];
    }

    public function isOrderItemId(int $order_item_id): bool
    {
        $order_item_ids = $this->getOrderItemIds();

        return in_array($order_item_id, $order_item_ids);
    }

    public function getOldStatus(): string
    {
        return $this->daten->getter('old_status');
    }

    public function getStornoReasonId(): int
    {
        return (int)$this->daten->getter('storno_reason_id');
    }

    /**
     * gibt dne Grund des Stornos als Text zurück
     * @return string
     */
    public function getStornoReason(): string
    {
        return order_repository::getStornoReason($this->getStornoReasonId());
    }

    public function getBemerkung(): string
    {
        return $this->daten->getter('storno_grund');
    }

    public function getDatum(): DateObj
    {
        return new DateObj($this->daten->getter('datum'));
    }

    public function getStornoStatus(): int
    {
        return $this->daten->getter('storno_status');
    }

    public function getStornoArt(): string
    {
        return $this->daten->getter('storno_art');
    }

    public function getAlternativen(): string
    {
        return $this->daten->getter('alternativen');
    }

    public function getStornoParty(): string
    {
        return $this->daten->getter('storno_party');
    }

    public function getAlternativtext(): string
    {
        return $this->daten->getter('alternativtext');
    }

    public function getOrderId(): int
    {
        return $this->daten->getter('order_id');
    }

    public function getOrder(): Order
    {
        return service_loader::get(OrderRepository::class)->loadCached($this->getOrderId());
    }


    public function getMailTemplate(): string
    {
        $mail_templates = db::getInstance()->singleQuery("
            SELECT
                einst_storno_reasons.mail_id_order_storno,
                einst_storno_reasons.mail_id_order_item_storno
            FROM
                einst_storno_reasons
            WHERE
                einst_storno_reasons.storno_reason_id = " . $this->getStornoReasonId() . "
        ");

        if ($this->getStornoArt() == self::STORNO_ART_AUFTRAG) {
            $mail_id = $mail_templates['mail_id_order_storno'];
        } else {
            $mail_id = $mail_templates['mail_id_order_item_storno'];
        }

        return $mail_id;
    }

    public static function serializeOldStatus(array $old_status): string
    {
        foreach ($old_status as $order_item_id => $status) {
            $old_status[$order_item_id] = (int)$status;
        }

        return Json::encode($old_status);
    }

    public static function deserializeOldStatus(string $old_status): array
    {
        //@todo remove... kommt nach der migration nicht mehr vor
        if (!Json::looksLikeJson($old_status)) {
            try {
                $status = unserialize($old_status);
            } catch (ErrorException $e) {
                $status = [];
            }

            return $status;
        }

        return Json::decode($old_status);
    }
}
