<?php

namespace wws\Order;

use bqp\Date\DateObj;
use bqp\Model\SmartDataObj;

class OrderItemEstimatedDelivery
{
    protected SmartDataObj $daten;

    protected $order_item = null;

    public function __construct($daten = null)
    {
        $this->daten = new SmartDataObj($this);
        if ($daten !== null) {
            $this->daten->loadDaten($daten);
        }
    }

    public function setDeliveryId($delivery_id): bool
    {
        return $this->daten->setter('delivery_id', $delivery_id);
    }

    public function setOrderItem(OrderItem $order_item): void
    {
        $this->order_item = $order_item;
    }

    public function getEstimatedDeliveryDate(): DateObj
    {
        return new DateObj($this->daten['delivery_date']);
    }

    public function isCustomerInformed(): bool
    {
        return (bool)$this->daten['customer_informed'];
    }

    public function getNote(): string
    {
        return (string)$this->daten['note'];
    }

    public function setEstimatedDeliveryDate(DateObj $date): bool
    {
        return $this->daten->setter('delivery_date', $date->db('date'));
    }

    public function setNote($note): bool
    {
        return $this->daten->setter('note', $note);
    }

    public function setCustomerInformed($value): bool
    {
        return $this->daten->setter('customer_informed', $value ? 1 : 0);
    }

    public function setAdded(DateObj $date): bool
    {
        return $this->daten->setter('added', $date->db());
    }

    public function getAdded(): DateObj
    {
        return new DateObj($this->daten['added']);
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }
}
