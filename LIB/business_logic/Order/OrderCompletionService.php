<?php

namespace wws\Order;

use bqp\Date\DateObj;
use bqp\Event\AsyncEvent;
use bqp\Event\EventDispatcher;
use bqp\Event\EventListenerFactory;
use wws\Order\Event\EventOrderCompleteForPlatform;
use wws\Tracking\TrackingLegacy;

/**
 * Bestimmt ob eine Bestellung komplett ist und feuert ggf. Events.
 * Aktuell ist nur ein Event implementiert EventOrderCompleteForPlatform... @see EventOrderCompleteForPlatform
 */
abstract class OrderCompletionService
{
    private EventListenerFactory $event_listener_factory;
    private EventDispatcher $event_dispatcher;

    public function __construct(
        EventListenerFactory $event_listener_factory,
        EventDispatcher $event_dispatcher
    ) {
        $this->event_listener_factory = $event_listener_factory;
        $this->event_dispatcher = $event_dispatcher;
    }

    public function run(): void
    {
        $listener = $this->event_listener_factory->getAsyncListenerForSubscriber(OrderCompletionService::class);

        foreach ($listener->listen() as $event) {
            if ($this->processOrderCompleteForPlatform($event)) {
                $listener->remove($event);
            }
        }
    }

    public function processOrderCompleteForPlatform(AsyncEvent $async_event): bool
    {
        $order = new Order($async_event->getMessageValue('order_id'));

        //Service- und Rechnungskorrektur-Aufträge immer skippen. Die Meldung an die Platform ist sehr wahrscheinlich über den ursprünglichen Auftrag erfolgt.
        //Erneute Meldungen führen u.A. bei Amazon, ebay, idealo, kaufland zu Problemen.
        //Sonderbehandlungen für bestimmte Plattformen können bei Bedarf hier mit abgebildet werden. (zwar nicht schön, aber spezifische logik ist auch schon in waitingTimeExceeded() implementiert)
        if ($order->isOrderTag(OrderConst::TAG_SERVICE) || $order->isOrderTag(OrderConst::TAG_RECHNUNGKORREKTUR)) {
            return true;
        }

        $state = false;

        if ($this->waitingTimeExceeded($order, $async_event->getEventDate())) {
            $state = true;
        } else {
            $tracking_dtos = TrackingLegacy::getTrackingIdsByOrderIdIncludeNonTrackableCarriers($order->getOrderId());

            if ($tracking_dtos) {
                $state = true;
            }
        }

        if ($state) {
            $event = new EventOrderCompleteForPlatform($order);

            $this->event_dispatcher->dispatch($event);
        }

        return $state;
    }


    abstract protected function waitingTimeExceeded(Order $order, DateObj $date): bool;


    public function isOrderAssociatedWithSupplier(Order $order, int $supplier_id): bool
    {
        $is_relevant = false;

        foreach ($order->getOrderItems() as $order_item) {
            if ($order_item->getSupplierId() === $supplier_id) {
                $is_relevant = true;
                break;
            }
        }

        if (!$is_relevant) {
            return false;
        }

        //prüfen ob direkt sped? -> muss für den use case nicht

        return true;
    }
}
