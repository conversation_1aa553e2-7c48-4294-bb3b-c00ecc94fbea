<?php

namespace wws\Order;

class OrderAcl
{
    private $user_id = null;

    /**
     * @var Order
     */
    private $order;

    private $reason = '';

    public function setUserId($user_id)
    {
        $this->user_id = $user_id;
    }

    public function setOrder(Order $order)
    {
        $this->order = $order;
    }

    public function getReason()
    {
        return $this->reason;
    }

    public function canChangeZahlungsId($zahlungs_id)
    {
        if ($this->order->hasInvoice()) {
            $this->reason = 'Rechnung bereits gelegt.';
            return false;
        }

        return true;
    }
}
