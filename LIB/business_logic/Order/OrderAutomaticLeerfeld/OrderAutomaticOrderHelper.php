<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use db;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;
use wws\Product\ProductConst;
use wws\Product\ProductWarenkorbTypesRepository;
use wws\ProductStock\ProductStockConst;
use wws\Supplier\SupplierRepository;
use wws\Supplier\SuppliersConst;

/**
 * Class OrderAutomaticOrderHelper
 *
 * Klasse die Standardprüfungen, Auswertungen, Aktionen abbildet, die diversen Handler immer wieder benötigen
 *
 * @package wws\Order\Automatic
 */
class OrderAutomaticOrderHelper
{
    private db_generic $db;

    public function __construct()
    {
        $this->db = db::getInstance();
    }

    /**
     * @param Order $order
     * @return null|OrderItem
     */
    public function getStdVersandItem(Order $order): ?OrderItem
    {
        foreach ($order->getOrderItems() as $order_item) {
            if ($order_item->getTyp() === OrderConst::WARENKORB_TYP_VERSANDAUTO and $order_item->getTypValue() === 'versand') {
                return $order_item;
            }
        }

        return null;
    }


    /**
     * @param Order $order
     * @return bool
     */
    public function isErsatzteilOrder(Order $order): bool
    {
        $order_items = $order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_PRODUKT);

        if (count($order_items) === 0) {
            return false;
        }

        $product_ids = array_map(function (OrderItem $order_item) {
            return $order_item->getProductId();
        }, $order_items);

        $product_types = $this->db->query("
            SELECT
                product.product_type
            FROM
                product
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ")
            GROUP BY
                product.product_type
        ")->asSingleArray();

        if (count($product_types) !== 1) {
            return false;
        }

        return $product_types[0] === ProductConst::PRODUCT_TYPE_XET;
    }


    /**
     * @param OrderItem[] $order_items
     * @return int
     */
    public function getUniqueSupplierIdOrderItems(array $order_items): ?int
    {
        $supplier_id = null;

        foreach ($order_items as $order_item) {
            if ($supplier_id === null) {
                $supplier_id = $order_item->getSupplierId();
            }

            if ($supplier_id != $order_item->getSupplierId()) {
                throw new FatalException('Auftrag ist derzeit über mehrere Grossisten eingestellt.');
            }
        }

        return $supplier_id;
    }


    /**
     * @param OrderAutomaticStatusHandler $status
     * @param Order $order
     * @param float|null $fulfillment_shipment_costs
     */
    public function checkGrossMarginWithDropshipping(OrderAutomaticStatusHandler $status, Order $order, ?float $fulfillment_shipment_costs): void
    {
        $versand_item = $this->getStdVersandItem($order);

        $rule = $status->add('Standard-Versandkosten Position vorhanden');

        if ($versand_item === null) {
            $rule->fail('Nicht gefunden.');
        }

        if ($fulfillment_shipment_costs !== null) {
            $diff = ($versand_item->getEkNetto() - $fulfillment_shipment_costs) * $versand_item->getVatRate()->getAsFactor();
        } else {
            $diff = 0;
        }

        $marge = $order->getVkBrutto() - $order->getEkNnnBrutto();

        $marge = $marge + $diff;

        $rule = $status->add('Marge mit Fulfillment-Versand.');

        if ($marge < 0) {
            $rule->fail('Negative Marge');
        }
    }

    public function checkDropshippingCosts(OrderAutomaticStatusHandler $status, ?float $fulfillment_shipment_costs): void
    {
        $rule = $status->add('Fulfillment-Versandkosten');

        if ($fulfillment_shipment_costs === null) {
            $rule->fail('konnten nicht bestimmt werden');
        }
    }

    public function setFulfillmentShippingCosts(Order $order, ?float $fulfillment_shipment_costs): void
    {
        $versand_item = $this->getStdVersandItem($order);

        if (!$versand_item) {
            throw new FatalException('Keine Position für die Standard-Versandkosten hinterlegt.');
        }

        $versand_item->setEkNetto($fulfillment_shipment_costs);
        $versand_item->setEkNnnNetto($fulfillment_shipment_costs);
    }

    /**
     * @param OrderItem[] $order_items
     */
    public function setCurrentEks(array $order_items): void
    {
        foreach ($order_items as $order_item) {
            $this->setCurrentEk($order_item);
        }
    }

    public function setCurrentEk(OrderItem $order_item): void
    {
        if ($order_item->getWarenkorbType() !== OrderConst::WARENKORB_TYP_PRODUKT) {
            return;
        }

        $row = $this->db->singleQuery("
            SELECT
                IF(product_ek.ek_option = 'snp', product_ek.ek_snp, product_ek.ek_rnp) AS ek_netto,
                product_ek.ek_netto AS ek_nnn_netto
            FROM
                product_ek
            WHERE
                product_ek.product_id = '" . $order_item->getProductId() . "' AND
                product_ek.supplier_id = '" . $order_item->getSupplierId() . "'
        ");

        if (!$row) {
            //@todo exception oder nicht exception?!
            return;
        }

        $order_item->setEkNetto($row['ek_netto']);
        $order_item->setEkNnnNetto($row['ek_nnn_netto']);
    }

    /**
     * Setzt die $lager_id für alle physischen Positionen in diesem Auftrag
     *
     * @param Order $order
     * @param int $lager_id
     * @return void
     */
    public function setLagerId(Order $order, int $lager_id): void
    {
        foreach ($order->getOrderItems() as $order_item) {
            if (ProductWarenkorbTypesRepository::isDelivery($order_item->getTyp())) {
                $order_item->setLagerId($lager_id);
            } else {
                $order_item->setLagerId(ProductStockConst::LAGER_ID_LAGER);
                $order_item->setSupplierId(SuppliersConst::SUPPLIER_ID_ALLEGO);
            }
        }
    }

    /**
     * Setzt den Auftrag auf OrderConst::STATUS_GROSS_BESTAETIGT und setzt den Liefertermin.
     * Das führt nix direkt aus, aber beim Speichern der Bestellung wird das Events auslösen.
     *
     * @param Order $order
     * @return void
     */
    public function confirmOrder(Order $order): void
    {
        foreach ($order->getOrderItems() as $order_item) {
            $order_item->setStatus(OrderConst::STATUS_GROSS_BESTAETIGT);

            $liefertermin = SupplierRepository::getLieferbaranzeigeToLieferzeit($order_item->getLieferbaranzeige());
            $order_item->setLiefertermin($liefertermin);
        }
    }
}
