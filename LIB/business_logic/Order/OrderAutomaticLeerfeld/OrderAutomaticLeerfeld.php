<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use bqp\db\db_generic;
use bqp\extern\Aswo\AswoOrderAutomatic;
use bqp\extern\krempl\KremplOrderAutomaticLeerfeld;
use config;
use debug;
use env;
use ReflectionClass;
use ReflectionException;
use service_loader;
use system_protokoll;
use Throwable;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Product\ProductConst;
use wws\ProductStock\ProductStockConst;
use wws\Supplier\Supplier;
use wws\Supplier\SupplierRepository;
use wws\Supplier\SuppliersConst;

class OrderAutomaticLeerfeld
{
    protected array $reasons = [];

    protected OrderAutomaticStatusHandler $status;
    protected OrderAutomaticOrderHelper $helper;

    private db_generic $db;
    private bool $simulate = false;
    private OrderAutomaticCommonRules $rules;
    private system_protokoll $log;

    public function __construct(db_generic $db)
    {
        $this->db = $db;

        $this->helper = new OrderAutomaticOrderHelper();

        $this->rules = new OrderAutomaticCommonRules($db);

        $this->log = system_protokoll::getInstance('order_leerfeld_auto');
    }


    public function run(): void
    {
        //Alle Aufträge die auf STATUS_AB_LEERFELD stehen, aber noch nicht durch die automatik geschickt wurden (TAG_AB_LEERFELD_AUTOMATIC_EXECUTED)
        //warum polling? -> damit ist sichergestellt, dass aufträge die erneut im leerfeld landen, nicht automatisch neu bearbeitet werden

        $allowed_states = [OrderConst::STATUS_AB_LEERFELD, OrderConst::STATUS_WARTE_AUSLIEFERUNG];

        $order_ids = $this->db->query("
            SELECT
                orders.order_id
            FROM
                (
                    SELECT
                        DISTINCT order_item.order_id
                    FROM
                        order_item
                    WHERE
                        order_item.status = " . OrderConst::STATUS_AB_LEERFELD . "
                ) AS relevant_orders INNER JOIN
                orders ON (orders.order_id = relevant_orders.order_id) INNER JOIN
                order_item ON (orders.order_id = order_item.order_id)
            WHERE
                order_item.status < " . OrderConst::STATUS_STORNO . " AND
                FIND_IN_SET('" . $this->db->escape(OrderConst::TAG_AB_LEERFELD_AUTOMATIC_EXECUTED) . "', orders.order_tags) = 0
            GROUP BY
                orders.order_id
            HAVING
                MIN(order_item.status) = " . OrderConst::STATUS_AB_LEERFELD . " AND
                MAX(order_item.status) IN (" . $this->db->in($allowed_states) . ")
        ")->asSingleArray();

        $order_ids = $this->delayOrderFilter($order_ids);

        foreach ($order_ids as $order_id) {
            $order = new Order($order_id);
            try {
                $this->processOrder($order);
            } catch (Throwable $e) {
                //eventuell auch schon als EXECUTED markieren... bin mir nicht ganz sicher ob es gut ist, wenn die da ständig in ein Fehler laufen
                //gefangen werden muss aber, damit nicht die ganze automatik blockiert
                debug::dump($order->getAuftnr());
                debug::dumpException($e);
            }
        }
    }

    public function delayOrderFilter(array $order_ids): array
    {
//        if (\env::isK11()) {
//            $euras_delay = \service_loader::get(EurasOrderAutomaticLeerfeldDelay::class);
//            $order_ids = $euras_delay->filterDelayedOrderIds($order_ids);
//        }

        return $order_ids;
    }


    /**
     * legt fest ob nur simuliert werden soll
     * @param bool $simulate
     */
    public function setSimulate(bool $simulate): void
    {
        $this->simulate = $simulate;
    }

    public function getStatusHandler(): OrderAutomaticStatusHandler
    {
        return $this->status;
    }

    private function initStatus(): void
    {
        $this->status = new OrderAutomaticStatusHandler();
        if ($this->simulate) {
            $this->status->setSimulate($this->simulate);
        }
    }

    public function processOrder(Order $order): void
    {
        /*
         * Ggf. muss hier nochmal das Konzept überarbeitet werden. Die aktuelle Struktur macht es schwer "Lieferanten"
         * zu switchen (jedenfalls nicht sauber und flexibel). Ein besserer Ansatz wäre, den Auftrag durch alle Automatiken
         * (oder vorselektierte Automatiken) bewerten zu lassen und anhand dieser Bewertung zu entscheiden.
         * Das aktuelle Konzept sieht vor, dass der Auftrag "richtig" eingestellt ankommt und entscheidet, ob er automatisch
         * weiter "geschoben" werden soll.
         */

        //Auftrag manipulieren
        if (!$this->simulate) {
            if ($this->helper->isErsatzteilOrder($order)) {
                KremplOrderAutomaticLeerfeld::kremplBshGrossistSetterAutomatic($order);
            }

            //für lager automatik prüfen, ob eventuell das lager geändert werden "muss"
            //wenn ein auftrag mit dem k11 lager auf gros "lager ecom" reinkommt, ist das bei ecom auf lager -> lager umschalten, (und vice versa)
            if (env::isEcom()) {
                $lager_automatic_switcher = new LagerOrderAutomaticLagerSwitcher([
                    ['supplier_id' => SuppliersConst::SUPPLIER_ID_LAGER_ECOM, 'lager_id' => ProductStockConst::LAGER_ID_LAGER],
                    ['supplier_id' => SuppliersConst::SUPPLIER_ID_LAGER_K11, 'lager_id' => ProductStockConst::LAGER_ID_K11]
                ]);
            }

            if (env::isK11()) {
                $lager_automatic_switcher = new LagerOrderAutomaticLagerSwitcher([
                    ['supplier_id' => SuppliersConst::SUPPLIER_ID_LAGER_ECOM, 'lager_id' => ProductStockConst::LAGER_ID_LAGER],
                    ['supplier_id' => SuppliersConst::SUPPLIER_ID_LAGER_K11, 'lager_id' => ProductStockConst::LAGER_ID_K11],
                    ['supplier_id' => SuppliersConst::SUPPLIER_ID_EURAS, 'lager_id' => ProductStockConst::LAGER_ID_SONSTIGE],
                ]);
            }

            $lager_automatic_switcher->process($order);
        }

        //
        $this->initStatus();

        $order_items = $order->getOrderItemsByWarenkorbTypes([OrderConst::WARENKORB_TYP_PRODUKT, OrderConst::WARENKORB_TYP_ASWO]);

        $this->helper->setCurrentEks($order_items);
        $this->rules->setContext($this->status, $order, $order_items);

        try {
            $this->rules->checkOrderTagsNotSetted([OrderConst::TAG_FAKE, OrderConst::TAG_FAKE_VERDACHT]);
            $this->rules->checkStatusAbLeerfeld();
            $this->rules->checkEkDataExists();
            $this->rules->checkGsLieferzeiten();
            $this->rules->checkOrderIncludesDeliverableOrderItems();
            $this->rules->checkOrderIsWithSelfPickup();
            $this->rules->checkOrderItemsInOurStock();
            $this->rules->checkOnlyOneSupplierId(true);
            $this->rules->checkDropshippingBlock();
            $this->rules->checkCustTopicRelevance();
            $this->rules->checkQuantity(100, 20);

            if (env::isEcom()) {
                $this->rules->checkServiceOrder();
                $this->rules->checkPhoneOrder();
                $this->rules->checkTotalOrderAmount(2500);
            } else {
                $this->rules->checkTotalOrderAmount(1500);
                $this->rules->checkBlockedProductTags([ProductConst::TAG_DUMMY]);
            }

            $rule = $this->status->add('Keine Bemerkung für Grossisten angegeben');

            foreach ($order_items as $order_item) {
                if ($order_item->getGrosMemo()) {
                    $rule->fail('Es ist eine Bemerkung für den Grossisten angegeben. ' . $order_item->getProductName());
                }
            }

            $supplier_id = $this->helper->getUniqueSupplierIdOrderItems($order_items);

            $automatic = $this->getOrderAutomatic($supplier_id);

            if (!($automatic instanceof AswoOrderAutomatic)) {
                //Prüfen ob der Bezug gültig ist... -> eigentlich ist das allgemein, aber keine Regel ohne Ausnahme.
                //Euras hat derzeit bei K11 kein Bezug, damit das nicht in die Kalkulation einfließt.
                //Sobald das über die EK Automatik abgebildet ist, kann diese Bedingung nach oben ohne das IF.
                $this->rules->checkEkIsValid();
            }

            $automatic->checkSpecificRules($order, $order_items);
            $this->canProcess();

            $automatic->handleOrder($order, $order_items);

            $order->addOrderTag(OrderConst::TAG_AB_LEERFELD_AUTOMATIC_SUCCESS);

            $this->log->wok('Auftrag weitergeschaltet ' . $order->getAuftnr() . ' (' . get_class($automatic) . ')', '', $order->getOrderId());
        } catch (OrderAutomaticRuleAbortException $e) {
            if (!$this->simulate) {
                $reasons = $this->status->getErrorReasons();
                $this->log->winfo('Auftrag nicht automatisch weitergeschaltet ' . $order->getAuftnr(), implode("\n", $reasons), $order->getOrderId());
            }
        }

        if ($this->simulate) {
            return;
        }

        $order->addOrderTag(OrderConst::TAG_AB_LEERFELD_AUTOMATIC_EXECUTED);
        $order->save();
    }

    /**
     * @param int|null $supplier_id
     * @return OrderAutomaticLeerfeldHandler
     * @throws OrderAutomaticRuleAbortException
     */
    private function getOrderAutomatic(?int $supplier_id): OrderAutomaticLeerfeldHandler
    {
        $rule = $this->status->add('Leerfeld-Automatik für Lieferant');

        $supplier = new Supplier($supplier_id);
        $order_automatic_leerfeld_class = $supplier->getOrderAutomaticLeerfeldClass();

        try {
            $reflection = new ReflectionClass($order_automatic_leerfeld_class);
            if (!$reflection->isInstantiable()) {
                throw new ReflectionException();
            }
        } catch (ReflectionException $e) {
            $rule->fail(SupplierRepository::getSupplierName($supplier_id) . ' (' . $supplier_id . ')');
            throw new OrderAutomaticRuleAbortException();
        }

        $inject = compact('supplier_id');

        $config_key = $supplier->getOrderApiConfigKey();
        if ($config_key) {
            $inject['config'] = config::get($config_key);
        }

        $automatic = service_loader::getDiContainer()->make($order_automatic_leerfeld_class, $inject);

        $rule->success($order_automatic_leerfeld_class);

        if (method_exists($automatic, 'setOrderAutomaticOrderHelper')) {
            $automatic->setOrderAutomaticOrderHelper($this->helper);
        }

        if (method_exists($automatic, 'setCommonRules')) {
            $automatic->setCommonRules($this->rules);
        }

        $automatic->setStatus($this->status);

        return $automatic;
    }

    private function canProcess(): void
    {
        if ($this->status->isSimulate()) {
            $this->status->add('Simulationsmodus')->success('Die Automatik befindet sich Simulationsmodus. Im Normalmodus
                können je nach Automatik weitere Prüfungen stattfinden, insbesondere Verfügbarkeit und Marge inkl. der
                Fulfillmentkosten.');

            throw new OrderAutomaticRuleAbortException();
        }

        if (!$this->status->isOk()) {
            throw new OrderAutomaticRuleAbortException();
        }
    }
}
