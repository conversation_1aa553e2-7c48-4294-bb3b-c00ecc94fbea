<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use wws\Order\Order;
use wws\Order\OrderItem;

interface OrderAutomaticLeerfeldHandler
{
    public function setStatus(OrderAutomaticStatusHandler $status): void;


    /**
     * @param Order $order
     * @param array $order_items
     * @thorws OrderAutomaticRuleAbortException
     */
    public function checkSpecificRules(Order $order, array $order_items): void;


    /**
     * @param Order $order
     * @param OrderItem[] $order_items
     * @thorws OrderAutomaticRuleAbortException
     */
    public function handleOrder(Order $order, array $order_items): void;
}
