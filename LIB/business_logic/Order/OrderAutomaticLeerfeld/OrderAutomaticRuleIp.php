<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use bqp\db\db_generic;
use db;
use wws\Order\Order;
use wws\Order\OrderConst;

class OrderAutomaticRuleIp
{
    private db_generic $db;

    public function __construct()
    {
        $this->db = db::getInstance();
    }

    public function checkOrder(Order $order): OrderAutomaticRuleIpResult
    {
        if ($order->getOrderOriginId() !== OrderConst::ORDER_ORIGIN_SHOP) {
            return new OrderAutomaticRuleIpResult();
        }

        $ip = $order->getIp();

        if (!$this->isValidIp($ip)) {
            return new OrderAutomaticRuleIpResult();
        }

        $date = $order->getBestellDatum();

        //$time_frames = [3, 6, 48];
        $time_frames = [48];

        $current_count = 1;

        foreach ($time_frames as $time_frame) {
            $date_start = $date->clone()->subSimple('hours', $time_frame);
            $date_end = $date->clone()->addSimple('hours', $time_frame);

            $result = $this->db->query("
                SELECT
                    orders.order_id,
                    orders.ip,
                    orders.added,
                    orders.auftnr,
                    orders.order_amount_gross,
                    customers.customer_nr,
                    customers.customer_id,
                    customers.firma,
                    customers.name,
                    customers.vorname,
                    customers.email,
                    customers.telefon
                FROM
                    orders INNER JOIN
                    customers ON (orders.customer_id = customers.customer_id)
                WHERE
                    orders.added BETWEEN '" . $date_start->format('Y-m-d H:i:s') . "' AND  '" . $date_end->format('Y-m-d H:i:s') . "' AND
                    orders.ip = '" . $this->db->escape($ip) . "'
            ");

            if ($result->count() === $current_count) {
                break;
            }

            $current_count = $result->count();
        }

        return new OrderAutomaticRuleIpResult($result->asArray(), $order);
    }

    private function isValidIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4);
    }
}
