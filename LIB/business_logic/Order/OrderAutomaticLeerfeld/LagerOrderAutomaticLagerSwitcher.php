<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;

/**
 * Class LagerOrderAutomaticEkSwitcher
 * @package wws\Order\Automatic
 *
 * Hilfsklasse um ggf. den Auftrag auf ein anderes Lager mit freien Bestand zu setzen.
 */
class LagerOrderAutomaticLagerSwitcher
{
    /**
     * [][supplier_id => ?, $lager_id => ?]
     *
     * @var array
     */
    private array $gros_lager_map = [];

    public function __construct(array $gros_lager_map)
    {
        $this->gros_lager_map = $gros_lager_map;
    }

    public function process(Order $order): void
    {
        if (!$this->checkOrder($order)) {
            return;
        }

        $order_item = $this->getOrderItem($order);

        if (!$order_item) {
            return;
        }

        $lager_id = $this->getLagerIdForSupplierId($order_item->getSupplierId());

        $order_item->setLagerId($lager_id);

        $order->save();
    }

    public function checkOrder(Order $order): bool
    {
        if ($order->getMaxStatus() > OrderConst::STATUS_WARTE_AUSLIEFERUNG) {
            return false;
        }

        $order_item = $this->getOrderItem($order);

        if ($order_item === null) {
            return false;
        }

        $lager_id = $this->getLagerIdForSupplierId($order_item->getSupplierId());

        if (!$lager_id) {
            return false;
        }

        if ($lager_id === $order_item->getLagerId()) { //nix zu tun
            return false;
        }

        return true;
    }

    private function getOrderItem(Order $order): ?OrderItem
    {
        $order_items = $order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_PRODUKT);

        if (count($order_items) !== 1) {
            return null;
        }
        return current($order_items);
    }

    private function getLagerIdForSupplierId(int $supplier_id): ?int
    {
        foreach ($this->gros_lager_map as $entry) {
            if ($entry['supplier_id'] === $supplier_id) {
                return $entry['lager_id'];
            }
        }

        return null;
    }
}
