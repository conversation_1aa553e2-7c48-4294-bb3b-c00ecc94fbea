<?php

namespace wws\Order\OrderAutomaticLeerfeld;

class OrderAutomaticStatusHandlerRule
{
    private array $messages = [];
    private OrderAutomaticStatusHandler $order_automatic_status_handler;
    private string $rule_description;

    public function __construct(OrderAutomaticStatusHandler $order_automatic_status_handler, string $rule_description)
    {
        $this->order_automatic_status_handler = $order_automatic_status_handler;
        $this->rule_description = $rule_description;
    }

    /**
     * @throws OrderAutomaticRuleAbortException
     */
    public function fail(string $message = ''): void
    {
        $this->messages[] = [
            'rule_description' => $this->getRuleDescription(),
            'message' => $message,
            'result' => $this->order_automatic_status_handler::RULE_RESULT_FAIL
        ];

        if (!$this->order_automatic_status_handler->isSimulate()) {
            $this->order_automatic_status_handler->triggerOrderAutomaticRuleAbortException();
        }
    }

    public function success(string $message = ''): void
    {
        $this->messages[] = [
            'rule_description' => $this->getRuleDescription(),
            'message' => $message,
            'result' => $this->order_automatic_status_handler::RULE_RESULT_SUCCESS
        ];
    }

    public function getMessages(): array
    {
        return $this->messages;
    }

    /**
     * @return string
     */
    public function getRuleDescription(): string
    {
        return $this->rule_description;
    }
}
