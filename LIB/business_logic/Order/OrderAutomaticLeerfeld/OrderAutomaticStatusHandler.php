<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use bqp\Exceptions\DevException;

class OrderAutomaticStatusHandler
{
    public const RULE_RESULT_FAIL = 'fail';
    public const RULE_RESULT_SUCCESS = 'success';
    private bool $simulate = false;

    /**
     * @var OrderAutomaticStatusHandlerRule[]
     */
    private array $rules = [];
    private array $messages = [];


    private function unfoldRules(): void
    {
        foreach ($this->rules as $key => $rule) {
            $messages = $rule->getMessages();

            if (!$messages) {
                $this->messages[] = ['rule_description' => $rule->getRuleDescription(), 'message' => '', 'result' => OrderAutomaticStatusHandler::RULE_RESULT_SUCCESS];
            } else {
                $this->messages = array_merge($this->messages, $messages);
            }

            unset($this->rules[$key]);
        }
    }

    /**
     * @throws OrderAutomaticRuleAbortException
     */
    public function triggerOrderAutomaticRuleAbortException(): void
    {
        $this->unfoldRules();

        throw new OrderAutomaticRuleAbortException();
    }


    public function getErrorReasons(): array
    {
        $this->unfoldRules();

        $result = [];
        foreach ($this->messages as $message) {
            if ($message['result'] !== self::RULE_RESULT_FAIL) {
                continue;
            }

            if ($message['message']) {
                $result[] = $message['rule_description'] . ': ' . $message['message'];
            } else {
                $result[] = $message['rule_description'];
            }
        }

        return $result;
    }

    public function getMessages(): array
    {
        return $this->messages;
    }

    public function isOk(): bool
    {
        $this->unfoldRules();

        return count($this->getErrorReasons()) === 0;
    }

    public function isError(): bool
    {
        return !$this->isOk();
    }

    public function isErrorByRuleDescription(string $rule_description): bool
    {
        $this->unfoldRules();

        foreach ($this->messages as $message) {
            if ($message['result'] !== self::RULE_RESULT_FAIL) {
                continue;
            }

            if ($message['rule_description'] === $rule_description) {
                return true;
            }
        }
        return false;
    }

    public function __invoke(string $rule_name): void
    {
        throw new DevException('deprecated');
    }

    public function setSimulate(bool $status): void
    {
        $this->simulate = $status;
    }

    public function isSimulate(): bool
    {
        return $this->simulate;
    }

    /**
     * @throws OrderAutomaticRuleAbortException
     */
    public function add(string $rule_description): OrderAutomaticStatusHandlerRule
    {
        $rule = new OrderAutomaticStatusHandlerRule($this, $rule_description);
        $this->rules[] = $rule;

        return $rule;
    }
}
