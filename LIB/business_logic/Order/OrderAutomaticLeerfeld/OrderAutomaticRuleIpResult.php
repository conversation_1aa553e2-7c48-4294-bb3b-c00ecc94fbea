<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use bqp\table\DataSource\TableObjectDataSourceArray;
use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_callback;
use bqp\table\TableObject;
use bqp\table\TableObjectInline;
use wws\Order\Order;

class OrderAutomaticRuleIpResult
{
    private array $result;
    private ?Order $order;
    private string $ip = '';

    public function __construct(array $result = [], ?Order $order = null)
    {
        foreach ($result as $key => $row) {
            $this->ip = $row['ip'];

            $row['is_this_order'] = false;
            $row['is_this_customer'] = false;

            if ($order && $order->getOrderId() == $row['order_id']) {
                $row['is_this_order'] = true;
            }

            if ($order && $order->getCustomerId() == $row['customer_id']) {
                $row['is_this_customer'] = true;
            }

            $result[$key] = $row;
        }

        $this->result = $result;
        $this->order = $order;
    }

    public function isMatch(): bool
    {
        foreach ($this->result as $row) {
            if (!$row['is_this_customer']) {
                return true;
            }
        }

        return false;
    }

    public function getAsTextSimple(): string
    {
        if (!$this->isMatch()) {
            return 'Keine Treffer';
        }


        $auftnrs = [];

        foreach ($this->result as $row) {
            if (!$row['is_this_order']) {
                $auftnrs[] = $row['auftnr'];
            }
        }

        return implode(', ', $auftnrs);
    }

    public function getAsTableObject(): TableObject
    {
        $src = new TableObjectDataSourceArray($this->result);

        $table = new TableObjectInline($src);
        $table->setCaption('Bestellungen mit gleicher IP-Adresse (' . $this->ip . ')');


        $field = new table_object_field_callback('info', '');
        $field->setCallback(function (array $row) {
            if ($row['is_this_order']) {
                return 'Diese Bestellung';
            }

            if ($row['is_this_customer']) {
                return 'Dieser Kunde';
            }

            return '';
        });
        $table->addField($field);

        $table->addRowFormaterHighlight('is_this_customer', true);
        $table->addRowFormaterHighlight('is_this_order', true);

        $table->addFieldByTableConfig('orders.added', 'added');
        $table->addFieldByTableConfig('orders.auftnr', 'auftnr');
        $table->addField(new table_object_field('customer_nr', 'Kundennummer'));
        $table->addFieldByTableConfig('MAKRO.customers.customer_name_firma', 'customer_name_firma');

        $table->addField(new table_object_field('email', 'Email'));
        $table->addField(new table_object_field('telefon', 'Telefon'));

        return $table;
    }
}
