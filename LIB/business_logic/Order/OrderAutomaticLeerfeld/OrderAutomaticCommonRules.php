<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use bqp\Address\Address;
use bqp\Address\AddressFittingException;
use bqp\Date\DateObj;
use bqp\Date\DateRange;
use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use config;
use output;
use service_loader;
use wws\MarketViewWwsConnector\MarketViewAvailabilityServiceConnector;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;
use wws\Product\ProductEk;
use wws\Product\ProductExtraServices;
use wws\Shipment\RateCalculator\RateCalculatorRequest;
use wws\Shipment\RateCalculator\ShipmentGroups\ShipmentGroupServiceStandard;
use wws\Shipment\ShipmentRepository;
use wws\Shipment\Spedition;
use wws\Supplier\SupplierRepository;
use wws\Supplier\SuppliersConst;

class OrderAutomaticCommonRules
{
    private db_generic $db;
    private array $suppliers;

    private OrderAutomaticStatusHandler $status;
    private Order $order;

    /**
     * @var OrderItem[]
     */
    private array $order_items;

    private ?Address $delivery_address = null;

    private ?array $ek_data = null;

    private bool $is_off_work_periode;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
        $this->loadSuppliers();

        $this->is_off_work_periode = self::isOffWorkPeriode(new DateObj());
    }

    /**
     * Gibt true zurück, wenn wir "im" Wochenende oder Feiertag sind.
     *
     * Achtung: wird mittlerweile auch noch extern verwendet, bei anpassungen des zeitplans die consumer prüfen!
     *
     * @param DateObj $date
     * @return bool
     * @throws FatalException
     */
    public static function isOffWorkPeriode(DateObj $date): bool
    {
        if ($date->getMonth() === 11 && $date->getYear() === 2024) {
            $special_time_frame = new DateRange(new DateObj('2024-11-19 16:00:00'), new DateObj('2024-11-23 09:00:00'));

            if ($special_time_frame->isInRange($date)) {
                return true;
            }
        }

        //Wochenende oder Feiertag
        if (!$date->isWorkingday()) {
            return true;
        }

        //nach 16 uhr
        if ($date->format('G') >= 16) {
            $date = $date->clone()->addSimple('day', 1);
            return !$date->isWorkingday();
        }

        //vor 8 uhr
        if ($date->format('G') < 8) {
            $date = $date->clone()->subSimple('day', 1);
            return !$date->isWorkingday();
        }

        return false;
    }


    private function loadSuppliers(): void
    {
        $this->suppliers = $this->db->query("
            SELECT
                supplier.supplier_id,
                supplier.supplier_name,
                supplier.gros_type,
                supplier.lager_id
            FROM
                supplier
        ")->asArray('supplier_id');
    }

    public function setContext(OrderAutomaticStatusHandler $status, Order $order, ?array $order_items = null): void
    {
        $this->status = $status;
        $this->order = $order;

        if ($order_items !== null) {
            $this->order_items = $order_items;
        } else {
            $this->order_items = $order->getOrderItems();
        }

        $this->delivery_address = null;
        $this->ek_data = null;
    }


    private function getEkData(): array
    {
        if ($this->ek_data === null) {
            $tuples = [];
            foreach ($this->order_items as $order_item) {
                $tuples[] = [$order_item->getProductId(), $order_item->getSupplierId()];
            }

            $ek_data_raw = $this->db->query("
                SELECT
                    product_ek.product_id,
                    product_ek.supplier_id,
                    product_ek.ek_netto,
                    product_ek.ek_fulfill_versand,
                    product_ek.ek_fulfill_versand_source,
                    product_ek.dropshipping,
                    product_ek.ek_option,
                    product_ek.ek_is_valid,
                    product_ek.vpe_zwang,
                    product_ek.mvsrc_availability_id
                FROM
                    product_ek
                WHERE
                    (product_ek.product_id, product_ek.supplier_id) IN (" . $this->db->inTuple($tuples) . ")
            ")->asArray('product_id');

            $this->ek_data = [];

            foreach ($this->order_items as $order_item) {
                if (!isset($ek_data_raw[$order_item->getProductId()])) {
                    throw new OrderAutomaticEkDataNotFoundException('ek entry not found (order_item_id:' . $order_item->getOrderItemId() . ')');
                }

                $ek_data_entry = $ek_data_raw[$order_item->getProductId()];
                $ek_data_entry['order_item'] = $order_item;

                $this->ek_data[] = $ek_data_entry;
            }
        }

        return $this->ek_data;
    }

    private function getDeliveryAddress(): Address
    {
        if (!$this->delivery_address) {
            $this->delivery_address = $this->order->getLieferAddress();
        }

        return $this->delivery_address;
    }

    /**
     * Prüft das der Auftrag nicht per Briefversand abgewickelt wird.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkNoLetter(): void
    {
        $rule = $this->status->add('Kein Versand per Brief');

        if ($this->order->getSpedId() === ShipmentRepository::SPED_POST) {
            $rule->fail();
        }
    }

    /**
     * Prüft das der Auftrag nur eine gewisse Anzahl Positionen und Einheiten enthält.
     *
     * @param int $max_items
     * @param int $max_positions
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkQuantity(int $max_items, int $max_positions = 1): void
    {
        $rule_quantity = $this->status->add('Maximal ' . $max_items . ' Stück in einer Position.');
        $rule_positions = $this->status->add('Maximal ' . $max_positions . ' Auftragspositionen.');

        foreach ($this->order_items as $order_item) {
            if ($order_item->getAnzahl() > $max_items) {
                $rule_quantity->fail($order_item->getAnzahl() . ' x ' . $order_item->getProductName());
            }
        }

        if (count($this->order_items) > $max_positions) {
            $rule_positions->fail(count($this->order_items));
        }
    }

    /**
     * Prüft, ob das Lieferland erlaubt ist.
     *
     * @param array $allowed_country_ids
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkDeliveryCountry(array $allowed_country_ids): void
    {
        $rule = $this->status->add('Lieferadresse im Inland.');

        $address = $this->getDeliveryAddress();

        if (!in_array($address->getCountryId(), $allowed_country_ids)) {
            $rule->fail($address->getCountry()->getCountryName());
        }
    }

    /**
     * Prüft, ob die Lieferadresse mit dem übergebenen AddressFitter kompatibel ist
     *
     * @param string $address_fitting_class
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkAddressFitting(string $address_fitting_class): void
    {
        $address = $this->getDeliveryAddress();

        $rule = $this->status->add('Lieferadresse');

        try {
            $fitting = new $address_fitting_class($address);
            $fitting->execute();
        } catch (AddressFittingException $e) {
            $rule->fail($e->getMessage());
        }
    }

    public function checkNoDhlService(): void
    {
        $address = $this->getDeliveryAddress();

        $rule = $this->status->add('Keine DHL Packstation/Postfiliale');

        if ($address->isDhlService()) {
            $rule->fail();
        }
    }

    /**
     * Prüft das der Auftrag nicht per Nachnahme läuft.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkNotPaymentMethod(array $zahlungsart_ids): void
    {
        $rule = $this->status->add('Gewählte Zahlungsart wird unterstützt.');

        if (in_array($this->order->getZahlungsart(), $zahlungsart_ids)) {
            $rule->fail();
        }
    }

    public function checkSpedCapability(int $sped_id): void
    {
        $sped = new Spedition($sped_id);
        if (!$sped->getAbilityPackstation()) {
            $this->checkNoDhlService();
        }

        $this->checkNotPaymentMethod($sped->getAbilityZahlungsIdsBlacklist());
        $this->checkShipmentGroupId($sped->getAbilityShipmentGroupIds());

        $rule = $this->status->add('Gewählte Serviceleistungen werden unterstützt.');
        $unsupported_services = array_diff(ProductExtraServices::getOrderShippingExtraService($this->order), $sped->getAbilityExtraServices());
        if ($unsupported_services) {
            $rule->fail();
        }

        if ($sped->getAddressFitter()) {
            $this->checkAddressFitting($sped->getAddressFitter());
        }
    }

    public function checkShipmentGroupId(array $shipment_group_ids): void
    {
        $rule = $this->status->add('Gewählte Versandart wird unterstützt.');

        $address = $this->getDeliveryAddress();

        $request = new RateCalculatorRequest();
        $request->setVersandKundeOpt($this->order->getVersandKundeOpt());
        $request->setCountryId($address->getCountryId());
        $request->setPLZ($address->getPlz());
        $request->setZahlungsId($this->order->getZahlungsart());
        $request->setOrderOriginId($this->order->getOrderOriginId());

        $shipment_group_service = service_loader::getDiContainer()->get(ShipmentGroupServiceStandard::class);
        $shipment_group_id = $shipment_group_service->determineShipmentGroupId($request);

        if (!in_array($shipment_group_id, $shipment_group_ids)) {
            $rule->fail();
        }
    }

    /**
     * Prüft ob der Status des Auftrags AB-Leerfeld ist
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkStatusAbLeerfeld(): void
    {
        $rule = $this->status->add('Auftrag hat Status AB-Leerfeld');

        if ($this->order->getMinStatus() !== OrderConst::STATUS_AB_LEERFELD) {
            $rule->fail();
        }
    }

    /**
     * Prüft, ob in der Bestellung nicht nur Leistungen enthalten sind.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkOrderIncludesDeliverableOrderItems(): void
    {
        $rule = $this->status->add('Auftrag enthält physische Produkte.');

        //@todo -> ist quatsch wenn wir über order der context gesetzt wird!
        if (count($this->order_items) === 0) {
            $rule->fail();
        }
    }

    /**
     * Prüft ob der Kunde Selbstabholung gewählt hat
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkOrderIsWithSelfPickup(): void
    {
        $rule = $this->status->add('Auftrag nicht per Selbstabholung');

        if ($this->order->getVersandKundeOpt() === 'abh') {
            $rule->fail();
        }
    }

    /**
     * Prüft, ob die Auftragssumme <= $total_amount ist.
     *
     * @param float $total_amount
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkTotalOrderAmount(float $total_amount): void
    {
        $rule = $this->status->add('Rechnungsbetrag ist maximal ' . output::formatPrice($total_amount));

        if ($this->order->getRechnungsBetrag() > $total_amount) {
            $rule->fail($this->order->getRechnungsBetrag());
        }
    }

    /**
     * Prüft, ob die OrderItems eventuell in einen unserer Läger vorhanden sind und nicht über dieses "bezogen" werden.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkOrderItemsInOurStock(): void
    {
        if (!$this->order_items) {
            return;
        }

        //Artikel in unserem Lager verfügbar. (bzw. im Lager der Auftragsposition)
        $rule = $this->status->add('Auftrag mit Positionen in unserem Lager, aber auf anderen Lieferanten eingestellt.');

        foreach ($this->order_items as $order_item) {
            $supplier = $this->suppliers[$order_item->getSupplierId()];

            //der aktuelle Grossist ist eins unserer Lager (damit spielt der Bestand keine Rolle,
            //solange nicht im Nachhinein der Lieferant geändert wird, z.Z. nicht der Fall)
            if ($supplier['gros_type'] === SuppliersConst::TYPE_LAGER) {
                continue;
            }

            //das eingestellte lager und lieferant gehören zusammen -> bewusst
            if ($supplier['lager_id'] == $order_item->getLagerId()) {
                continue;
            }

            //der Auftrag hat einen Versandstatus, aber der Lieferant und Lager gehören nicht zusammen -> dann ist sehr
            //wahrscheinlich, dass das unser Lager ist.
            if ($order_item->getVersandStatus() == 1) {
                $rule->fail($order_item->getProductName());
            }
        }
    }


    /**
     * @param OrderItem[] $order_items
     * @return bool
     */
    public function isOnlyOneSupplierId(array $order_items): bool
    {
        $supplier_id = null;

        foreach ($order_items as $order_item) {
            if ($supplier_id === null) {
                $supplier_id = $order_item->getSupplierId();
            }

            if ($supplier_id !== $order_item->getSupplierId()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Prüft, ob die $order_items auf eine einzelne Grossisten stehen.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkOnlyOneSupplierId(bool $fail_instant = false): void
    {
        $rule = $this->status->add('Auftrag wird nicht über mehrere Lieferanten abgewickelt');

        if (!$this->isOnlyOneSupplierId($this->order_items)) {
            $rule->fail();

            if ($fail_instant) {
                $this->status->triggerOrderAutomaticRuleAbortException();
            }
        }
    }


    /**
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkIsOffWorkPeriode(): void
    {
        $rule = $this->status->add('Zeitfenster: Wochenende und Feiertagen (inkl. ab 16 Uhr am Vortag und bis 8 Uhr am Folgetag)');

        if (!$this->is_off_work_periode) {
            $rule->fail();
        }
    }

    private function getOrderProductTypes(): array
    {
        $order_items = $this->order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_PRODUKT);

        $product_ids = [];
        foreach ($order_items as $order_item) {
            $product_ids[] = $order_item->getProductId();
        }

        if (!$product_ids) {
            return [];
        }

        $product_types = $this->db->singleQuery("
            SELECT
                DISTINCT product.product_type
            FROM
                product
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ")
        ");

        return $product_types;
    }

    /**
     * @param array $allowed_product_types
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkProductTypes(array $allowed_product_types): void
    {
        $rule = $this->status->add('erlaubte Warengruppe/n');

        $product_types = $this->getOrderProductTypes();

        if (!$product_types) {
            $rule->fail('Es wurde keine passenden Positionen gefunden');
        }

        foreach ($product_types as $product_type) {
            if (!in_array($product_type, $allowed_product_types)) {
                $rule->fail('Die Bestellung enthält Positionen mit nicht freigegebenen Warengruppen. (' . $product_type . ')');
            }
        }
    }

    public function checkDisallowedProductTypes(array $disallowed_product_types): void
    {
        $rule = $this->status->add('erlaubte Warengruppe/n');

        $product_types = $this->getOrderProductTypes();

        if (!$product_types) {
            $rule->fail('Es wurde keine passenden Positionen gefunden');
        }

        foreach ($product_types as $product_type) {
            if (in_array($product_type, $disallowed_product_types)) {
                $rule->fail('Die Bestellung enthält Positionen mit nicht gesperrten Warengruppen. (' . $product_type . ')');
            }
        }
    }

    /**
     * Prüft, ob die Bestellung keine Zusatzleistung enthält, die den Versand beeinflusst.
     * Z.B. Trageservice, Türanschlagwechsel etc.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkShippingExtraServices(): void
    {
        $rule = $this->status->add('keine Versandabhängigen Service-Leistungen');

        if (ProductExtraServices::getOrderShippingExtraService($this->order)) {
            $rule->fail('Der Auftrag enthält Versandabhängig Service-Leistungen.');
        }
    }


    /**
     * Prüft, das keine Position auf ein SNP steht.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkNoSnpPrices(): void
    {
        $rule = $this->status->add('Auftragsposition steht auf Listen-EK.');

        $ek_data = $this->getEkData();

        foreach ($ek_data as $row) {
            if ($row['ek_option'] === 'snp') {
                $rule->fail($row['order_item']->getProductName());
            }
        }
    }

    public function checkNoManualFulfillmentShippingCosts(): void
    {
        $rule = $this->status->add('Fulfillment Versandkosten nicht manuell festgelegt');

        $ek_data = $this->getEkData();

        foreach ($ek_data as $row) {
            if ($row['ek_fulfill_versand_source'] === ProductEk::EK_FULFILL_SOURCE_MANUAL) {
                $rule->fail($row['order_item']->getProductName());
            }
        }
    }

    public function checkAvailability(): void
    {
        $ek_data = $this->getEkData();

        $availability_service = service_loader::getDiContainer()->get(MarketViewAvailabilityServiceConnector::class);

        $rule = $this->status->add('Verfügbarkeit der Auftragsposition');

        foreach ($ek_data as $item) {
            if (!$availability_service->isDeliverable($item['mvsrc_availability_id'])) {
                $rule->fail($item['order_item']->getProductName());
            }
        }
    }

    /**
     * Prüft, ob das eine reguläre Bestellung ist.
     *
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkServiceOrder(): void
    {
        $rule = $this->status->add('Auftragstyp "Normal"');

        if ($this->order->getOrderType() !== OrderConst::ORDER_TYPE_NORMAL) {
            $rule->fail($this->order->getOrderType());
        }
    }

    public function checkOrderTagsNotSetted(array $order_tags): void
    {
        $rule = $this->status->add('Order-Tags nicht gesetzt: ' . implode(', ', $order_tags));

        foreach ($this->order->getOrderTags() as $order_tag) {
            if (in_array($order_tag, $order_tags)) {
                $rule->fail('Order-Tag ' . $order_tag . ' gesetzt.');
            }
        }
    }

    public function checkDropshippingBlock(): void
    {
        $rule = $this->status->add('Dropshipping nicht gesperrt');

        $ek_data = $this->getEkData();

        foreach ($ek_data as $item) {
            if ($item['dropshipping'] < 0) {
                $rule->fail($item['order_item']->getProductName());
            }
        }
    }

    public function checkVorkasse(): void
    {
        $rule = $this->status->add('Keine offene Vorkasse');

        if ($this->order->getZahlungsart() !== OrderConst::PAYMENT_VORKASSE) {
            return;
        }

        if ($this->order->getZahlungsartStatus() === OrderConst::PAYMENT_STATUS_VORKASSE_RECEIVED) {
            return;
        }

        $rule->fail();
    }


    public function checkPhoneOrder(): void
    {
        $rule = $this->status->add('Bestellung nicht per Telefon aufgenommen');

        if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_PHONE) {
            $rule->fail();
        }
    }


    public function checkCustTopicRelevance(): void
    {
        $rule = $this->status->add('Kunde hat relevante Bemerkung angegeben');

        if ($this->order->getKundenBemerkungRelevanz() === 1) {
            $rule->fail();
        }
    }


    /**
     * Prüft, ob eine Adresse wie eine Testadresse aussieht.
     *
     * @param Address $address
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkTestAddress(Address $address): void
    {
        $address_string = $address->format();

        $rule = $this->status->add('Adresse enthält kein Muster/Test.');

        //test(?!r) -> "test" was nicht auf r endet. Kommt ab und zu bei Straßen vor: "...testraße" (r deckt dann auch str. ab und co)
        if (preg_match('~(muster|test(?!r))~ui', $address_string)) {
            $rule->fail();
        }
    }

    /**
     * Prüft, ob für alle Positionen in der Bestellung für den jeweiligen Lieferanten Angebotsdaten im Produktstamm vorliegen.
     *
     * @return void
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkEkDataExists(): void
    {
        $rule = $this->status->add('Angebotsdaten für alle Positionen gepflegt.');

        try {
            $this->getEkData();
        } catch (OrderAutomaticEkDataNotFoundException $e) {
            $rule->fail();
        }
    }

    public function checkEkIsValid(): void
    {
        $rule = $this->status->add('Angebot hat Bezug/gültigen EK.');

        $data = $this->getEkData();

        foreach ($data as $row) {
            if (!$row['ek_is_valid']) {
                $rule->fail($row['order_item']->getOrderItemId());
            }
        }
    }

    /**
     * Prüft, ob wir die "Verfügbarkeitsanzeige", mit der der Kunde bestellt hat, auf eine gültige Lieferzeit des Lieferanten
     * übersetzten können. Das wird derzeit beim Umschalten der Aufträge benötigt. Ansonsten gibt es eine Exception.
     *
     * Die Regel macht an sich eigentlich wenig sinn. Derzeit betrifft das nur "Liefertermin unbekannt". Fragwürdig warum
     * das überhaupt passiert, ist aber "ok". Vom Ergebnis ändern sich letztendlich nichts, außer das es sauber im Workflow
     * ist und nicht runden als Exception dreht.
     *
     * @return void
     * @throws OrderAutomaticRuleAbortException
     */
    public function checkGsLieferzeiten(): void
    {
        $rule = $this->status->add('Mapping: Verfügbarkeitsanzeige zu Lieferzeit des Lieferanten');

        $lieferzeiten = config::gs_lieferzeiten('lieferzeiten');

        foreach ($this->order_items as $order_item) {
            $liefertermin = SupplierRepository::getLieferbaranzeigeToLieferzeit($order_item->getLieferbaranzeige());

            if (!array_key_exists($liefertermin, $lieferzeiten)) {
                $rule->fail($order_item->getProductName() . ' (' . $order_item->getLieferbaranzeige() . ')');
            }
        }
    }

    private function getProductTags(): array
    {
        $order_items = $this->order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_PRODUKT);

        $product_ids = [];
        foreach ($order_items as $order_item) {
            $product_ids[] = $order_item->getProductId();
        }

        if (!$product_ids) {
            return [];
        }

        $product_tags = $this->db->singleQuery("
            SELECT
                DISTINCT product_tags.product_tag
            FROM
                product_tags
            WHERE
                product_tags.product_id IN (" . $this->db->in($product_ids) . ")
        ");

        return $product_tags;
    }

    public function checkBlockedProductTags(array $blocked_product_tags): void
    {
        $rule = $this->status->add('Gesperrte Produkt-Tags: ' . implode(', ', $blocked_product_tags));

        $product_tags = $this->getProductTags();

        foreach ($product_tags as $product_tag) {
            if (in_array($product_tag, $blocked_product_tags)) {
                $rule->fail('Die Bestellung enthält gesperrte Produkt-Tags. (' . $product_tag . ')');
            }
        }
    }
}
