<?php

namespace wws\Order\OrderAutomaticLeerfeld;

use bqp\Config\ConfigContainer;
use bqp\db\db_generic;
use config;
use env;
use wws\Country\CountryConst;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;
use wws\Order\OrderMemo;
use wws\Product\ProductConst;

class LagerOrderAutomaticLeerfeld implements OrderAutomaticLeerfeldHandler
{
    private OrderAutomaticOrderHelper $helper;
    private OrderAutomaticStatusHandler $status;
    private OrderAutomaticCommonRules $rules;

    private db_generic $db;
    private int $lager_id;

    public function __construct(db_generic $db, ConfigContainer $config)
    {
        $this->lager_id = $config->getInt('lager_id');
        $this->db = $db;
    }

    public function setCommonRules(OrderAutomaticCommonRules $rules): void
    {
        $this->rules = $rules;
    }

    public function setStatus(OrderAutomaticStatusHandler $status): void
    {
        $this->status = $status;
    }

    public function setOrderAutomaticOrderHelper(OrderAutomaticOrderHelper $helper): void
    {
        $this->helper = $helper;
    }

    /**
     * @param Order $order
     * @param OrderItem[] $order_items
     */
    public function checkSpecificRules(Order $order, array $order_items): void
    {
        $rule = $this->status->add('Position steht auf Lager ' . $this->lager_id);

        foreach ($order_items as $order_item) {
            if ($order_item->getLagerId() != $this->lager_id) {
                $rule->fail('Die Auftragspositin ' . $order_item->getProductName() . ' steht auf einem anderen Lager.');
            }
        }

        if (env::isEcom()) {
            $this->rules->checkQuantity(3, 1);
            $this->rules->checkDeliveryCountry([CountryConst::DE]);
        }

        if (env::isK11()) {
            $this->rules->checkDeliveryCountry([CountryConst::DE, CountryConst::AT]);
        }

        $this->rules->checkNotPaymentMethod([OrderConst::PAYMENT_NACHNAHME]);
        $this->rules->checkVorkasse();

        //$order->getRechnungsBetrag()
        //prüfung marge einbinden

        $rule = $this->status->add('Marge des Auftrags ist Positiv');

        if (($order->getVkBrutto() - $order->getEkBrutto() < 0) && $order->getOrderId() === $order->getOrgOrderId()) {
            $rule->fail();
        }

        // Das muss über die Datenbank gemacht werden, ansonsten bekomm ich derzeit den Bestand nicht sauber berechnet.
        // (Als Workaround könnten die changes der order genommen werden, und bei relevanten änderungen die Bestände
        // korrektur rechnen. Das ist aber auch nicht trivial, die Frei-Berechnung bassiert derzeit auf einer neu
        // Kalkulation, hier bräuchte es eine Delta -> das wären getrennte Code Pfade die in sync gehalten werden müssen)
        $result = $this->db->query("
            SELECT
                order_item.product_id,
                order_item.quantity,
                product_lager_detail.lager_min,
                product_lager_detail.lager_frei,
                product.auto_order_processing,
                product.product_name,
                product.product_type
            FROM
                orders INNER JOIN
                order_item ON (orders.order_id = order_item.order_id) INNER JOIN
                product ON (order_item.product_id = product.product_id) INNER JOIN
                product_lager_detail ON (order_item.product_id = product_lager_detail.product_id AND order_item.lager_id = product_lager_detail.lager_id) INNER JOIN
                product_warenkorb_types ON (order_item.typ = product_warenkorb_types.product_warenkorb_typ)
            WHERE
                orders.order_id = '" . $order->getOrderId() . "' AND
                product_warenkorb_types.delivery = 1
        ");

        $rule_stock_automatic = $this->status->add('Position für Lagerautomatik freigegeben');
        $rule_stock_min_threshold = $this->status->add('Mindest-Bestand für Position nicht unterschritten');
        foreach ($result as $daten) {
            if (env::isEcom()) {
                if ($daten['product_type'] === ProductConst::PRODUCT_TYPE_XET) {
                    $daten['auto_order_processing'] = true;
                    $daten['lager_min'] = 0;
                }
            }

            if (!$daten['auto_order_processing']) {
                $rule_stock_automatic->fail($daten['product_name']);
            }

            if ($daten['lager_frei'] < $daten['lager_min']) {
                $rule_stock_min_threshold->fail('Der Mindest-Bestand (' . $daten['lager_min'] . ' Stück) für "' . $daten['product_name'] . '" ist unterschritten.');
            }
        }
    }

    /**
     * @param Order $order
     * @param OrderItem[] $order_items
     */
    public function handleOrder(Order $order, array $order_items): void
    {
        //auftrag weiterschalten...
        foreach ($order->getOrderItems() as $order_item) {
            $order_item->setLiefertermin(config::gs_lieferzeiten('sofort'));
        }

        $memo = new OrderMemo('Lager Automatik', OrderMemo::EINKAUF);
        $memo->setAutoMemo(false);

        $order->addOrderMemo($memo);

        $order->sendZugangsbestaetigung();
        $order->auft_go();
        $order->save();
    }
}
