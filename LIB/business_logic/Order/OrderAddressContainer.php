<?php

namespace wws\Order;

use bqp\Address\Address;
use bqp\Exceptions\DevException;
use db;

class OrderAddressContainer
{
    protected Order $order;

    /**
     * @var Address[]
     */
    protected array $addresses = [];

    /**
     * @var Address[]
     */
    protected array $addresses_org = [];

    protected bool $addresses_loaded = false;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function isLoaded(): bool
    {
        return $this->addresses_loaded;
    }

    public function init(bool $override = false): void
    {
        if (!$this->order->getOrderId() || $override) {
            if ($this->isComplete() && !$override) {
                return;
            }

            $address = $this->order->getCustomer()->getAddress();

            $this->setAddress($address);

            $this->addresses_loaded = true;
        }
    }

    public function isComplete(): bool
    {
        return (
                isset($this->addresses[Order::ADDRESS_TYPE_RECHUNG]) &&
                isset($this->addresses[Order::ADDRESS_TYPE_LIEFER])
            ) || isset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG]);
    }

    public function setAddress(Address $address, $type = Order::ADDRESS_TYPE_LIEFERRECHUNG): void
    {
        $this->load();

        //schauen was zu tun
        switch ($type) {
            case Order::ADDRESS_TYPE_LIEFERRECHUNG:
                unset($this->addresses[Order::ADDRESS_TYPE_LIEFER]);
                unset($this->addresses[Order::ADDRESS_TYPE_RECHUNG]);

                break;
            case Order::ADDRESS_TYPE_LIEFER:
                if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
                    $this->addresses[Order::ADDRESS_TYPE_RECHUNG] = $this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG];
                    unset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG]);
                }
                break;
            case Order::ADDRESS_TYPE_RECHUNG:
                if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
                    $this->addresses[Order::ADDRESS_TYPE_LIEFER] = $this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG];
                    unset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG]);
                }
                break;
        }

        $this->addresses[$type] = $address;
    }

    public function save(): void
    {
        if (!$this->addresses_loaded) {
            return;
        }

        $this->reorg();

        $db = db::getInstance();

        $order_id = $this->order->getOrderId();

        foreach ($this->addresses as $type => $address) {
            $sql = '';
            $sql .= "order_addresses.anrede = '" . $db->escape($address->getAnrede()) . "',";
            $sql .= "order_addresses.firma = '" . $db->escape($address->getFirma()) . "',";
            $sql .= "order_addresses.name = '" . $db->escape($address->getName()) . "',";
            $sql .= "order_addresses.vorname = '" . $db->escape($address->getVorname()) . "',";
            $sql .= "order_addresses.adresse1 = '" . $db->escape($address->getAdresse1()) . "',";
            $sql .= "order_addresses.adresse2 = '" . $db->escape($address->getAdresse2()) . "',";
            $sql .= "order_addresses.plz = '" . $db->escape($address->getPlz()) . "',";
            $sql .= "order_addresses.ort = '" . $db->escape($address->getOrt()) . "',";
            $sql .= "order_addresses.country_id = '" . $address->getCountryId() . "',";
            $sql .= "order_addresses.telefon = '" . $db->escape($address->getTel1()) . "',";
            $sql .= "order_addresses.hash = '" . $db->escape($address->getHash()) . "'";


            $update = isset($this->addresses_org[$type]);

            if ($update) {
                $db->query("
                    UPDATE
                        order_addresses
                    SET
                        $sql
                    WHERE
                        order_addresses.order_id = '" . $order_id . "' AND
                        order_addresses.address_type = '" . $type . "'
                ");
            } else {
                $db->query("
                    INSERT INTO
                        order_addresses
                    SET
                        $sql,
                        order_addresses.order_id = '" . $order_id . "',
                        order_addresses.address_type = '" . $type . "'
                ");
            }
        }

        foreach ($this->addresses_org as $type => $address) {
            if (isset($this->addresses[$type])) {
                continue;
            }

            $db->query("
                DELETE FROM
                    order_addresses
                WHERE
                    order_addresses.order_id = '" . $order_id . "' AND
                    order_addresses.address_type = '" . $type . "'
            ");
        }

        $this->addresses_org = [];
        foreach ($this->addresses as $type => $address) {
            $this->addresses_org[$type] = clone $address;
        }
    }

    private function reorg(): void
    {
        if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
            unset($this->addresses[Order::ADDRESS_TYPE_LIEFER]);
            unset($this->addresses[Order::ADDRESS_TYPE_RECHUNG]);
            return;
        }

        if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFER]) && !isset($this->addresses[Order::ADDRESS_TYPE_RECHUNG])) {
            $this->setAddress($this->addresses[Order::ADDRESS_TYPE_LIEFER]);
        }

        if (isset($this->addresses[Order::ADDRESS_TYPE_RECHUNG]) && !isset($this->addresses[Order::ADDRESS_TYPE_LIEFER])) {
            $this->setAddress($this->addresses[Order::ADDRESS_TYPE_RECHUNG]);
        }

        if ($this->addresses[Order::ADDRESS_TYPE_LIEFER]->getHash() == $this->addresses[Order::ADDRESS_TYPE_RECHUNG]->getHash()) {
            $this->setAddress($this->addresses[Order::ADDRESS_TYPE_RECHUNG]);
        }
    }

    public function getAllChanges(): array
    {
        if (!$this->addresses) {
            return [];
        }

        $this->reorg();

        $changes = [];

        if (isset($this->addresses_org[Order::ADDRESS_TYPE_RECHUNG])) {
            $rech_org = $this->addresses_org[Order::ADDRESS_TYPE_RECHUNG];
        } elseif (isset($this->addresses_org[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
            $rech_org = $this->addresses_org[Order::ADDRESS_TYPE_LIEFERRECHUNG];
        } else {
            $rech_org = new Address();
        }

        $rech = $this->getRechungAddress();

        $diff = $rech_org->compare($rech);

        foreach ($diff as $key => $values) {
            $changes[] = [
                'type' => 'order_addresses',
                'type_id' => 1,
                'field' => $key,
                'old_value' => $values[0],
                'new_value' => $values[1]
            ];
        }


        if (isset($this->addresses_org[Order::ADDRESS_TYPE_LIEFER])) {
            $liefer_org = $this->addresses_org[Order::ADDRESS_TYPE_LIEFER];
        } elseif (isset($this->addresses_org[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
            $liefer_org = $this->addresses_org[Order::ADDRESS_TYPE_LIEFERRECHUNG];
        } else {
            $liefer_org = new Address();
        }

        $liefer = $this->getLieferAddress();

        $diff = $liefer_org->compare($liefer);

        foreach ($diff as $key => $values) {
            $changes[] = [
                'type' => 'order_addresses',
                'type_id' => 2,
                'field' => $key,
                'old_value' => $values[0],
                'new_value' => $values[1]
            ];
        }

        return $changes;
    }

    public function load(): void
    {
        if ($this->addresses_loaded) {
            return;
        }

        $this->addresses_loaded = true;

        if (!$this->order->getOrderId()) {
            $this->init();
            return;
        }

        $result = db::getInstance()->query("
            SELECT
                order_addresses.order_id,
                order_addresses.address_type,
                order_addresses.anrede,
                order_addresses.firma,
                order_addresses.name,
                order_addresses.vorname,
                order_addresses.adresse1,
                order_addresses.adresse2,
                order_addresses.plz,
                order_addresses.ort,
                order_addresses.country_id,
                order_addresses.telefon
            FROM
                order_addresses
            WHERE
                order_addresses.order_id = '" . $this->order->getOrderId() . "'
        ");

        foreach ($result as $daten) {
            $this->addresses[$daten['address_type']] = new Address($daten);
            $this->addresses_org[$daten['address_type']] = new Address($daten);
        }
    }

    /**
     * @return Address
     */
    public function getLieferAddress(): Address
    {
        $this->load();
        $this->reorg();

        if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
            return $this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG];
        }

        if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFER])) {
            return $this->addresses[Order::ADDRESS_TYPE_LIEFER];
        }

        throw new DevException('kann nicht passieren');
    }

    /**
     * @return Address
     * @throws DevException
     */
    public function getRechungAddress(): Address
    {
        $this->load();
        $this->reorg();

        if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
            return $this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG];
        }

        if (isset($this->addresses[Order::ADDRESS_TYPE_RECHUNG])) {
            return $this->addresses[Order::ADDRESS_TYPE_RECHUNG];
        }

        throw new DevException('kann nicht passieren');
    }

    public function isAbweichendeLieferadresse(): bool
    {
        $this->load();
        $this->reorg();

        if (isset($this->addresses[Order::ADDRESS_TYPE_LIEFERRECHUNG])) {
            return false;
        }

        return true;
    }
}
