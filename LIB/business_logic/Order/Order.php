<?php

namespace wws\Order;

use BadMethodCallException;
use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\Event\EventDispatcher;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Exceptions\InputException;
use bqp\Model\EntityChanges;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use bqp\Model\SmartDataObjLegacy;
use bqp\Vat\VatRateConst;
use config;
use db;
use env;
use Exception;
use exception_payment;
use InvalidArgumentException;
use League\Flysystem\FileNotFoundException;
use order_repository;
use payment_repository;
use service_loader;
use SplObjectStorage;
use wws\buchhaltung\Invoice\InvoiceRepository;
use wws\buchhaltung\Invoice\Rechnung;
use wws\business_structure\Shop;
use wws\Customer\Customer;
use wws\Customer\CustomerRepository;
use wws\Lager\WarenausgangLieferschein;
use wws\Mails\Mail;
use wws\Order\Actions\OrderActions;
use wws\Order\Actions\OrderPaymentAction;
use wws\Order\Event\EventOrderBeforeSave;
use wws\Order\Event\EventOrderChange;
use wws\Order\Event\EventOrderCreated;
use wws\Order\Event\EventOrderNewShop;
use wws\Order\Event\EventOrderStatusChange;
use wws\payment\concardis\ConcardisFactory;
use wws\Product\Exception\ProductNotFoundException;
use wws\Product\ProductConst;
use wws\Product\ProductLager;
use wws\Shipment\RateCalculator\RateCalculator;
use wws\Shipment\RateCalculator\RateCalculatorProduct;
use wws\Shipment\RateCalculator\RateCalculatorRequest;
use wws\Shipment\RateTypes\ShipmentTypeRateCalculator;
use wws\Shipment\ShipmentRepository;

class Order extends SmartDataObjLegacy
{
    const ADDRESS_TYPE_RECHUNG = 'rechnung';
    const ADDRESS_TYPE_LIEFER = 'liefer';
    const ADDRESS_TYPE_LIEFERRECHUNG = 'lieferrechnung';

    private static $instances = [];

    /**
     * @var OrderItem[]
     */
    private array $order_items = [];

    private OrderAddressContainer $order_address_container;

    /**
     * @var OrderMemo[]
     */
    private array $order_memo_save_queue = [];
    private ?Customer $customer;

    public function __construct(?int $order_id = null)
    {
        if ($order_id !== null) {
            $this->load($order_id);
        } else {
            $this->loadDefaults();
        }

        $this->order_address_container = new OrderAddressContainer($this);
    }

    // <editor-fold defaultstate="collapsed" desc="Laden und Speichern">
    private function loadDefaults(): void
    {
        $this->regenerateVersion();
        $this->setOrderType(OrderConst::ORDER_TYPE_NORMAL);
        $this->setInvoiceAutoCreate(1);
        $this->refreshVorkassenFrist();
        $this->setTaxStatus(OrderConst::TAX_STATUS_NORMAL);
        $this->setVersandKundeOpt('nor');
        $this->setVersandMemo('');
        $this->setOrderOriginId(OrderConst::ORDER_ORIGIN_SHOP);
        $this->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_NEUTRAL);
        $this->setInvoiceNotice('');
        $this->setFristBemerkung('');
        $this->setKundenBemerkung('');
        $this->setPaymentUrl('');
        $this->setPaymentReferenz('');
        $this->setPaymentReferenz2('');
        $this->setPaymentReferenz3('');
        $this->setKundeBeleg('');
        $this->setKundeZeichen('');
        $this->setShopReferenz('');
        $this->setShopReferenz2('');
        $this->setFristBemerkung('');
        $this->setUserId(0);
        $this->setIp('');
        $this->setBestellDatum(new DateObj());
        $this->setKundenBemerkungRelevanz(0);
        $this->setSpedId(ShipmentRepository::SPED_UNBEKANNT);
        $this->setOrderCampaignId('');
    }

    private function load(int $order_id): void
    {
        $db = db::getInstance();

        //order daten auslesen
        $daten = $db->singleQuery("
                    SELECT
                        orders.order_id,
                        orders.org_order_id,
                        orders.order_version,
                        orders.customer_id,
                        orders.shop_id,
                        orders.ip,
                        orders.order_aktiv,
                        orders.auftnr,

                        orders.order_origin_id,
                        orders.order_campaign_id,
                        orders.payment_url,
                        orders.tax_status,
                        orders.tax_status_manual,
                        orders.vorkassen_frist,
                        orders.vorkassen_betrag,

                        orders.cust_topic_relevanz,
                        orders.cust_topic,

                        orders.fix_price,
                        orders.order_amount_net,
                        orders.order_amount_gross,
                        orders.payment_method,
                        orders.payment_method_order_id,
                        orders.payment_referenz,
                        orders.payment_referenz_2,
                        orders.payment_referenz_3,

                        orders.kunde_zeichen,
                        orders.kunde_beleg,

                        orders.zahlungs_id,
                        orders.zahlungs_status,
                        orders.zahlungs_hinweis,
                        orders.is_payed,
                        orders.shop_referenz,
                        orders.shop_referenz_2,

                        orders.frist_ende,
                        orders.frist_bemerkung,
                        orders.frist_gesetzt,

                        orders.bewertungs_email,
                        orders.order_type,

                        orders.auslieferung_ab,
                        orders.versand_memo,
                        orders.versandkosten,
                        orders.rechnungs_id,
                        orders.added AS bestell_datum,
                        orders.invoice_notice,
                        orders.invoice_autocreate,

                        orders.order_tags,
                        orders.gelangensbestaetigung,
                        orders.device_id,

                        order_item.versand_kunde_opt,
                        order_item.user_id,
                        order_item.zahlungsart,
                        order_item.bestaedigungs_mail,

                        order_item.lager_id,
                        order_item.sped_id,

                        buchhaltung_rechnung.rechnungs_nr,
                        buchhaltung_rechnung.rechnungs_datum,

                        customers.ustidnr,
                        customers.ustidnr_status
                    FROM
                        orders INNER JOIN
                        order_item ON (orders.order_id = order_item.order_id) LEFT JOIN
                        buchhaltung_rechnung ON (orders.rechnungs_id = buchhaltung_rechnung.rechnungs_id) INNER JOIN
                        customers ON (orders.customer_id = customers.customer_id)
                    WHERE
                        orders.order_id = '$order_id'
                    GROUP BY
                        orders.order_id
                ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException('Auftrag nicht gefunden! (' . $order_id . ')');
        }

        $this->loadDaten($daten);

        $result = $db->query("
                    SELECT
                        order_item.order_item_id,
                        order_item.order_id,
                        order_item.pos,
                        order_item.externe_referenz,
                        order_item.externe_referenz_2,
                        order_item.quantity,
                        order_item.quantity_planed,
                        order_item.quantity_progress,
                        order_item.quantity_completed,
                        order_item.quantity_open,
                        order_item.unit_code,
                        order_item.typ,
                        order_item.typ_value,
                        order_item.product_nr,
                        order_item.product_id,
                        order_item.product,
                        order_item.product AS product_name,
                        order_item.preis,
                        order_item.ekpreis,
                        order_item.ek_revised_brutto,
                        order_item.ek_nnn_brutto,
                        order_item.ek_nnn_revised_brutto,
                        order_item.mwst_satz,
                        order_item.liefertermin_text,
                        order_item.liefertermin,
                        order_item.liefertermin_date,
                        order_item.lieferbaranzeige,
                        order_item.status,
                        order_item.anlieferung_lager,
                        order_item.sped_id,
                        order_item.lager_id,
                        order_item.shop_id,
                        order_item.added,
                        order_item.user_id,
                        order_item.gros_memo,
                        order_item.supplier_id,
                        order_item.stats_supplier_id,
                        order_item.estimated_delivery_date,
                        order_item.estimated_delivery_date_customer,
                        order_item.versand_status,
                        order_item.versand_datum,
                        order_item.last_date,
                        order_item.gros_bestellt,
                        order_item.gros_lieferschein_bemerkung,
                        order_item.storno_msg,
                        order_item.gros_neuer_ek,
                        order_item.gros_bestell_datum
                    FROM
                        order_item
                    WHERE
                        order_item.order_id = '$daten[order_id]'
                ");

        foreach ($result as $daten) {
            $order_item = new OrderItem($this);
            $order_item->loadByArray($daten);

            $this->order_items[] = $order_item;
        }


        //mitigation zwecks des tabellen designs und zwei unklaren konzepten...
        //die "virtuellen" felder lager_id und sped_id werden hier ggf. nochmal "korrigiert".
        //unter umständen kann es sein das diese Daten aus einer stornierten Position übernommen wurden.
        $order_items = $this->getOrderItemsSorted();
        $primary_order_item = null;

        foreach ($order_items as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            $primary_order_item = $order_item;
            break;
        }

        if (!$primary_order_item) {
            $primary_order_item = $order_items[0];
        }

        $this->setterDirect('lager_id', $primary_order_item->getLagerId());
        $this->setterDirect('sped_id', $primary_order_item->getSpedId());
        //
    }

    /**
     * @return EntityChanges
     */
    public function getAllChanges(): EntityChanges
    {
        $entity_changes = new EntityChanges('orders');

        $entity_changes->addBySmartDataObj($this, 'order', 0);

        foreach ($this->order_items as $order_item) {
            if ($order_item->getObjStatus() == self::STATUS_DEL) {
                $entity_changes->addRemove('item', $order_item, $order_item->getProductId());
                continue;
            }

            if ($order_item->getObjStatus() == self::STATUS_NEW) {
                $entity_changes->addNew('item', $order_item);
            }

            $entity_changes->addBySmartDataObj($order_item, 'item', $order_item);
        }

        $changes = $this->order_address_container->getAllChanges();
        foreach ($changes as $change) {
            $entity_changes->addByArray($change);
        }

        return $entity_changes;
    }

    public function getErrors(): InputException
    {
        $errors = new InputException();

        if (!$this->getZahlungsart()) {
            $errors->add('zahlungsart', 'Wählen Sie eine Zahlungsart.');
        }
        if (!$this->getOrderItems()) {
            $errors->add('order_items', 'Fügen Sie mindestens eine Position dem Auftrag hinzu.');
        }
        if (!$this->getCustomerId()) {
            $errors->add('customer_id', 'Der Auftrag ist keinen Kunden zugeordnet.');
        }

        if ($this->order_address_container->isLoaded()) {
            if ($this->isAbweichendeLieferadresse()) {
                $address = $this->getAbweichendeLieferAddress();
                $address_errors = $address->validate();

                if (!$address->isValid()) {
                    $errors->add('liefer_address',
                        'Geben Sie eine gültige Lieferadresse an: ' . implode(',', $address_errors)
                    );
                }

                $address = $this->getRechnungAddress();
                $address_errors = $address->validate();

                if (!$address->isValid()) {
                    $errors->add(
                        'rechung_address',
                        'Geben Sie eine gültige Rechnungsadresse an: ' . implode(',', $address_errors)
                    );
                }
            } else {
                $address = $this->getRechnungAddress();
                $address_errors = $address->validate();

                if (!$address->isValid()) {
                    $errors->add(
                        'rechung_address',
                        'Geben Sie eine gültige Rechnungs/Lieferadresse an: ' . implode(',', $address_errors)
                    );
                }
            }
        }

        return $errors;
    }

    /**
     * @throws InputException
     */
    public function validate(): void
    {
        $this->getErrors()->check();
    }

    public function save(): int
    {
        $this->setOrderAktiv($this->calcOrderAktiv());
        $this->reviseEks();
        $this->recalcOrderAmounts();

        $this->order_address_container->init();

        OrderActions::calcOrderTags($this);

        $event = new EventOrderBeforeSave($this);
        service_loader::getEventDispatcher()->dispatch($event);

        $changes = $this->getAllChanges();

        if ($changes->isChange()) {
            $old_version = $this->getOrderVersion();
            $this->regenerateVersion();
            $changes->add('order_version', $this->getOrderVersion(), $old_version, 'order', 0);
        }

        $is_new = false;

        //durch das verfuschte DB design muss das speichern unterschiedlich gehandhabt werden
        switch ($this->getObjStatus()) {
            case self::STATUS_UPDATE:
                $this->saveUpdate();
                break;
            case self::STATUS_NEW:
                $this->saveNew();
                $is_new = true;
                break;
        }

        $this->order_address_container->save();

        $this->saveOrderMemos();
        $this->triggerCalcVersandPrioIfNecessary($changes);

        $changes->setEntityId($this->getOrderId());
        $changes->save();

        $event_service = service_loader::getDiContainer()->get(EventDispatcher::class);

        if ($is_new) {
            $event_service->dispatch(new EventOrderCreated($this));

            if ($this->getMaxStatus() === OrderConst::STATUS_NEU_SHOP) {
                $event_service->dispatch(new EventOrderNewShop($this));
            }
        }

        $event_service->dispatch(new EventOrderChange($this, $changes));
        if ($changes->isChange('item.status')) {
            $event_service->dispatch(new EventOrderStatusChange($this));
        }

        return $this->getOrderId();
    }

    private function saveOrderMemos(): void
    {
        if (!$this->order_memo_save_queue) {
            return;
        }

        $order_memo_repository = service_loader::getDiContainer()->get(OrderMemoRepository::class);

        foreach ($this->order_memo_save_queue as $memo) {
            $order_memo_repository->saveOrderMemo($this, $memo);
        }

        $this->order_memo_save_queue = [];
    }

    private function triggerCalcVersandPrioIfNecessary(EntityChanges $changes): void
    {
        $calc = false;
        if ($changes->isChange(['product_id', 'quantity', 'shop_id', 'lager_id'])) {
            $calc = true;
        } else {
            $status_changes = $changes->getChanges('item.status');

            foreach ($status_changes as $status_change) {
                if ($status_change['old_value'] > OrderConst::STATUS_60 && $status_change['new_value'] < OrderConst::STATUS_60) {
                    $calc = true;
                }
                if ($status_change['old_value'] < OrderConst::STATUS_60 && $status_change['new_value'] > OrderConst::STATUS_60) {
                    $calc = true;
                }
            }
        }

        if ($calc) {
            $lager_ids = [];
            foreach ($changes->getChanges('lager_id') as $change) {
                $lager_ids[$change['old_value']] = $change['old_value'];
                $lager_ids[$change['new_value']] = $change['new_value'];
            }

            $this->calcVersandPrio($lager_ids);
        }
    }

    /**
     * legt eine neu Bestellung an
     */
    private function saveNew(): void
    {
        $db = db::getInstance();

        //prüfen ob daten gesetzt
        if (!$this->getCustomerId()) {
            throw new FatalException('$customer_id nicht gesetzt');
        }
        if (!$this->getShopId()) {
            throw new FatalException('$shop_id nicht gesetzt');
        }
        if (!$this->getOrderItems()) {
            throw new FatalException('keine OrderItems gesetzt');
        }

        if (!$this->getBestellDatum()->isValid()) {
            $this->setBestellDatum(new DateObj());
        }

        if (!$this->getAuftnr()) {
            $this->setAuftnr(order_repository::createAuftnr($this->getShopId()));
        }

        if (!$this->getShopReferenz()) {
            $this->setShopReferenz(substr(md5(uniqid('E', true)), 0, 24));
        }

        $sql = $this->getChangesAsSql();

        //orders eintrag setzen
        $db->query("
            INSERT INTO
                orders
            SET
                " . implode(',', $sql['orders'])
        );

        $this->setOrderId($db->insert_id());

        $db->query("
            UPDATE
                orders
            SET
                orders.org_order_id = '" . $this->getOrderId() . "'
            WHERE
                orders.order_id = '" . $this->getOrderId() . "'
        ");

        //
        foreach ($this->getOrderItems() as $order_item) {
            $order_item->setOrderId($this->getOrderId());
            $order_item->setShopId($this->getShopId());
        }

        $this->saveOrderItems();

        //warenkorb_all updaten
        if ($sql['warenkorb_all']) {
            $db->query("
                UPDATE
                    order_item
                SET
                    " . implode(',', $sql['warenkorb_all']) . "
                WHERE
                    order_item.order_id = '{$this->getOrderId()}'
            ");
        }

        $this->setSaved();
    }

    /**
     * updated eine bestehende Bestellung
     */
    private function saveUpdate(): void
    {
        $db = db::getInstance();


        $sql = $this->getChangesAsSql();

        if ($sql['orders']) {
            $db->query("
                UPDATE
                    orders
                SET
                    " . implode(',', $sql['orders']) . "
                WHERE
                    orders.order_id = '{$this->getOrderId()}'
            ");
        }

        foreach ($this->getOrderItems() as $order_item) {
            $order_item->setOrderId($this->getOrderId());
        }
        $this->saveOrderItems();

        if ($sql['warenkorb_all']) {
            $db->query("
                UPDATE
                    order_item
                SET
                    " . implode(',', $sql['warenkorb_all']) . "
                WHERE
                    order_item.order_id = '{$this->getOrderId()}'
            ");
        }

        $this->setSaved();
    }

    /**
     * gibt ein Array mit den änderung als SQL String zurück
     * @return array
     */
    private function getChangesAsSql(): array
    {
        $daten = [
            'orders' => [],
            'warenkorb_all' => []
        ];

        if (!$this->isChange()) {
            return $daten;
        }

        $db = db::getInstance();

        foreach ($this->getChanges() as $field => $value) {
            switch ($field) {
                case 'sped_id':
                    $daten['warenkorb_all'][] = "order_item.sped_id = '" . $db->escape($value) . "'";
                    break;
                case 'kunde_beleg':
                case 'kunde_zeichen':
                case 'payment_method':
                case 'payment_referenz':
                case 'payment_referenz_2':
                case 'payment_referenz_3':
                case 'zahlungs_hinweis':
                case 'is_payed':
                case 'shop_referenz':
                case 'shop_referenz_2':
                case 'vorkassen_frist':
                case 'ip':
                case 'frist_ende':
                case 'frist_bemerkung':
                case 'frist_gesetzt':
                case 'bewertungs_email':
                case 'tax_status':
                case 'tax_status_manual':
                case 'vorkassen_betrag':
                case 'order_type':
                case 'versand_memo':
                case 'versandkosten':
                case 'order_aktiv':
                case 'invoice_notice':
                case 'invoice_autocreate':
                case 'cust_topic_relevanz':
                case 'cust_topic':
                case 'order_tags':
                case 'gelangensbestaetigung':
                case 'device_id':
                case 'rechnungs_id':
                case 'fix_price':
                case 'order_version':
                case 'order_origin_id':
                case 'order_campaign_id':
                case 'zahlungs_status':
                case 'order_amount_net':
                case 'order_amount_gross':
                case 'payment_url':
                case 'customer_id':
                    $daten['orders'][] = "orders.$field = '" . $db->escape($value) . "'";
                    break;
                case 'auslieferung_ab': //nullable values
                    $daten['orders'][] = "orders.$field = " . $db->quote($value);
                    break;
                case 'shop_id':
                    $daten['orders'][] = "orders.shop_id = '" . $db->escape($value) . "'";
                    $daten['warenkorb_all'][] = "order_item.shop_id = '" . $db->escape($value) . "'";
                    break;
                case 'order_id':
                    $daten['orders'][] = "orders.order_id = '" . $db->escape($value) . "'";
                    $daten['warenkorb_all'][] = "order_item.order_id = '" . $db->escape($value) . "'";
                    break;
                case 'auftnr':
                    $daten['orders'][] = "orders.auftnr = '" . $db->escape($value) . "'";
                    $daten['warenkorb_all'][] = "order_item.auftnr = '" . $db->escape($value) . "'";
                    break;
                case 'bestell_datum':
                    $daten['orders'][] = "orders.added = '" . $db->escape($value) . "'";
                    break;
                case 'payment_method_order_id':
                    $daten['orders'][] = "orders.payment_method_order_id = '" . $db->escape($value) . "'";
                    break;
                case 'zahlungsart':
                case 'zahlungs_id':
                    $daten['orders']['zahlungs_id'] = "orders.zahlungs_id = '" . $db->escape($value) . "'";
                    $daten['warenkorb_all']['zahlungs_id'] = "order_item.zahlungsart = '" . $db->escape($value) . "'";
                    break;
                case 'ustidnr':
                case 'ustidnr_status':
                case 'rechnungs_nr':
                case 'rechnungs_datum':
                case 'org_order_id':
                    break;
                default:
                    $daten['warenkorb_all'][] = "order_item.$field = '" . $db->escape($value) . "'";
            }
        }

        return $daten;
    }

    /**
     * DRECK!!! Wird gebruacht beim Einfügen eines neuen order_items um die Fehler im Datenbankdesign auszugleichen
     * -> die Felder gehören alle in orders
     * @return array
     */
    protected function getWarenkorbAllAsSql(): array
    {
        $db = db::getInstance();

        $sql = [];

        foreach ($this->daten as $field => $value) {
            switch ($field) {
                case 'sped_id':
                    $sql[] = "order_item.sped_id = '" . $db->escape($value) . "'";
                    break;
                case 'zahlungsart':
                case 'zahlungs_id':
                    $sql[] = "order_item.zahlungsart = '" . $db->escape($value) . "'";
                    break;
                case 'user_id':
                    $sql[] = "order_item.user_id = '" . $db->escape($value) . "'";
                    break;
                case 'lager_id':
                    $sql[] = "order_item.lager_id = '" . $db->escape($value) . "'";
                    break;
                case 'versand_kunde_opt':
                    $sql[] = "order_item.versand_kunde_opt = '" . $db->escape($value) . "'";
                    break;
                case 'bestaedigungs_mail':
                    $sql[] = "order_item.bestaedigungs_mail = " . $db->quote($value);
                    break;
            }
        }

        return $sql;
    }

    private function saveOrderItems(): void
    {
        $db = db::getInstance();

        foreach ($this->order_items as $order_item) {
            if ($order_item->getObjStatus() === $order_item::STATUS_LOADED) {
                continue;
            }

            if ($order_item->getObjStatus() === $order_item::STATUS_NEW) {
                $order_item->setter('zahlungsart', $this->getZahlungsart());
            }


            $change_status = false;
            $sql = [];
            foreach ($order_item->getChanges() as $field => $value) {
                switch ($field) {
                    case 'status':
                        $change_status = true;
                        $sql[] = "order_item.status = '" . $db->escape($value) . "'";
                        break;
                    case 'product':
                    case 'product_name':
                        $sql['product'] = "order_item.product = '" . $db->escape($value) . "'";
                        break;
                    case 'order_item_id':
                        break;
                    default:
                        $sql[] = "order_item.$field = '" . $db->escape($value) . "'";
                }
            }

            $sql[] = "order_item.auftnr = '" . $db->escape($this->getAuftnr()) . "'";
            $sql[] = "order_item.order_id = '" . $this->getOrderId() . "'";
            $sql[] = "order_item.last_date = NOW()";

            switch ($order_item->getObjStatus()) {
                case $order_item::STATUS_NEW:
                    //dreck, aber durch das DB Design nötig
                    $sql = array_merge($sql, $this->getWarenkorbAllAsSql());

                    //quick n dirty: doppelte felder killen
                    $sql_new = [];

                    foreach ($sql as $string) {
                        preg_match('~^(.*?)( |)=~', $string, $temp);

                        if (!isset($sql_new[$temp[1]])) {
                            $sql_new[$temp[1]] = $string;
                        }
                    }
                    $sql = $sql_new;


                    $db->query("
                            INSERT INTO
                                order_item
                            SET
                                " . implode(',', $sql)
                    );

                    $order_item_id = $db->insert_id();
                    $order_item->setOrderItemId($order_item_id);

                    //versandprio
                    $db->query("
                            UPDATE
                                order_item
                            SET
                                order_item.versand_prioritaet = order_item.order_item_id
                            WHERE
                                order_item.order_item_id = " . $order_item->getOrderItemId() . "
                        ");
                    $order_item->setterDirect('versand_prioritaet', $order_item->getOrderItemId());

                    $order_item->setSaved();
                    break;
                case $order_item::STATUS_UPDATE:
                    $db->query("
                            UPDATE
                                order_item
                            SET
                                " . implode(',', $sql) . "
                            WHERE
                                order_item.order_item_id = '{$order_item->getOrderItemId()}'
                        ");

                    $order_item->setSaved();

                    break;
                case $order_item::STATUS_DEL:
                    $db->query("
                            DELETE FROM
                                order_item
                            WHERE
                                order_item.order_item_id = '{$order_item->getOrderItemId()}'
                        ");

                    foreach ($this->order_items as $key => $obj) {
                        if ($obj === $order_item) {
                            unset($this->order_items[$key]);
                            break;
                        }
                    }

                    ProductLager::calcOrdersForProduct($order_item->getProductId(), $order_item->getLagerId());

                    $change_status = false;
                    break;
            }

            $order_item->saveEstimatedDesliveries();

            if ($change_status) {
                $user_id = env::getUserId();

                $db->query("
                    INSERT INTO
                        protokoll_status
                    SET
                        protokoll_status.order_item_id = '" . $order_item->getOrderItemId() . "',
                        protokoll_status.status = '" . $order_item->getStatus() . "',
                        protokoll_status.datum = NOW(),
                        protokoll_status.user_id = '" . $user_id . "',
                        protokoll_status.url = '" . $db->escape($_SERVER['REQUEST_URI'] ?? '') . "'
                ");
            }
        }
    }

    public function regenerateVersion(): void
    {
        $this->setter('order_version', rand(100000, 999990));
    }

    public function getOrderVersion(): int
    {
        return $this->getter('order_version');
    }

    // </editor-fold>

    public function isDifferentSped(): bool
    {
        $sped = false;
        foreach ($this->getOrderItems() as $order_item) {
            if (!$order_item->isDeliveryAble()) {
                continue;
            }
            if ($order_item->isStorno()) {
                continue;
            }

            if ($sped === false) {
                $sped = $order_item->getSpedId();
            }
            if ($sped != $order_item->getSpedId()) {
                return true;
            }
        }

        return false;
    }

    public function autoDetermineSped(): void
    {
        $calculator = $this->getShipmentCalculator();
        $this->setSpedId($calculator->determineSpedId($this));
    }

    //getter/setter
    public function getAuftnr(): string
    {
        return $this->getter('auftnr') ?? '';
    }

    public function setAuftnr(string $auftnr): bool
    {
        return $this->setter('auftnr', $auftnr);
    }

    public function getSpedId(): int
    {
        //if($this->isDifferentSped()) throw new FatalException('Auftrag läuft über verschiedene Speditionen.');
        return (int)$this->getter('sped_id');
    }

    /**
     * ermittelt den Lieferanten der Bestellung, wenn die Bestellung über mehrere Lieferanten verteilt ist wird eine excaption gewurfen
     * @return int $supplier_id
     * @throws FatalException
     */
    public function determineSupplierId(): int
    {
        $supplier_id = 0;

        foreach ($this->getOrderItems() as $order_item) {
            if (!$order_item->isDeliveryAble()) {
                continue;
            }
            if ($order_item->isStorno()) {
                continue;
            }

            if (!$supplier_id) {
                $supplier_id = $order_item->getSupplierId();
            }

            if ($supplier_id != $order_item->getSupplierId()) {
                throw new FatalException('Auftrag läuft über verschiedene Lieferanten. (' . $this->getOrderId() . ')');
            }
        }

        return $supplier_id;
    }

    public function getWarenwert(): float
    {
        $summe = 0;
        foreach ($this->getOrderItemsCustomer() as $order_item) {
            $summe += $order_item->getPreisSumme();
        }

        return $summe;
    }

    /**
     * gibt den Netto Einkaufspreis für die Produkte zurück (keine Leistungen/Versand/Wertgarantie usw...)
     */
    public function getEkNettoForProducts(): float
    {
        $summe = 0;
        foreach ($this->getOrderItems() as $order_item) {
            if (!$order_item->isDeliveryAble()) {
                continue;
            }
            if ($order_item->isStorno()) {
                continue;
            }

            $summe += $order_item->getEkNetto() * $order_item->getQuantity();
        }

        return $summe;
    }

    public function getEkBrutto(): float
    {
        $summe = 0;
        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            $summe += $order_item->getEkBrutto() * $order_item->getQuantity();
        }

        return $summe;
    }

    public function getEkNnnBrutto(): float
    {
        $summe = 0;
        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            $summe += $order_item->getEkNnnBrutto() * $order_item->getQuantity();
        }

        return $summe;
    }

    public function getVkBrutto(): float
    {
        $summe = 0;
        foreach ($this->getOrderItemsCustomer() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            $summe += $order_item->getVkBrutto() * $order_item->getQuantity();
        }

        return $summe;
    }

    public function getVkNetto(): float
    {
        $summe = 0;
        foreach ($this->getOrderItemsCustomer() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            $summe += $order_item->getVkNetto() * $order_item->getQuantity();
        }

        return $summe;
    }

    public function getRechnungsBetrag(): float
    {
        $summe = 0;
        foreach ($this->getOrderItemsCustomer() as $order_item) {
            $summe += $order_item->getVkSummeAutoMwst();
        }

        return $summe;
    }

    public function getRechnungsBetragBrutto(): float
    {
        $summe = 0;
        foreach ($this->getOrderItemsCustomer() as $order_item) {
            $summe += $order_item->getPreisSumme();
        }

        return $summe;
    }

    public function getRechnungsBetragNetto(): float
    {
        $summe = 0;

        foreach ($this->getOrderItemsCustomer() as $order_item) {
            $summe += $order_item->getPreisSumme() / $order_item->getVatRate()->getAsFactor();
        }

        return $summe;
    }


    public function getTax(): float
    {
        return round($this->getRechnungsBetrag() - $this->getRechnungsBetragNetto(), 2);
    }

    public function getNachnahmeBetrag(): float
    {
        return $this->getRechnungsBetrag();
    }

    public function getZahlungsart(): int
    {
        return (int)$this->daten['zahlungsart'];
    }

    /**
     * Gibt den Namen der Zahlungsart zurück
     * @return string
     */
    public function getZahlungsartName()
    {
        return payment_repository::getZahlungsart($this->getZahlungsart());
    }

    public function getZahlungsartStatus(): int
    {
        return (int)$this->getter('zahlungs_status');
    }

    public function getBestellDatum(): DateObj
    {
        return new DateObj($this->getter('bestell_datum') ?: false);
    }

    public function setBestellDatum(DateObj $date): bool
    {
        return $this->setter('bestell_datum', $date->db());
    }


    /**
     * @return Address
     */
    public function getLieferAddress(): Address
    {
        return $this->order_address_container->getLieferAddress();
    }

    /**
     * @return Address
     */
    public function getRechnungAddress(): Address
    {
        return $this->order_address_container->getRechungAddress();
    }

    public function setAddress(Address $address, $type = Order::ADDRESS_TYPE_LIEFERRECHUNG): void
    {
        $this->order_address_container->setAddress($address, $type);
    }

    /**
     * prüft ob eine abweichende Lieferadresse für den Auftrag hinterlegt ist
     * @return boolean
     */
    public function isAbweichendeLieferadresse(): bool
    {
        return $this->order_address_container->isAbweichendeLieferadresse();
    }

    /**
     * Gibt die Abweichende Lieferaddresse als Address Objekt zurück
     * @return Address
     */
    public function getAbweichendeLieferAddress(): Address
    {
        if ($this->isAbweichendeLieferadresse()) {
            return $this->getLieferAddress();
        }

        return new Address();
    }

    public function removeAbweichendeLieferadresse(): void
    {
        $address = $this->order_address_container->getRechungAddress();
        $this->order_address_container->setAddress($address);
    }

    public function isAddressLoaded(): bool
    {
        return $this->order_address_container->isLoaded();
    }


    public function getCustomerId(): int
    {
        return (int)$this->getter('customer_id');
    }

    public function getCustomerNr(): string
    {
        return $this->getCustomer()->getCustomerNr();
    }

    public function getCustomer(): Customer
    {
        if (!isset($this->customer)) {
            $this->customer = new Customer($this->getCustomerId());
        }

        return $this->customer;
    }

    public function getShop(): Shop
    {
        return Shop::getInstance($this->getShopId());
    }

    public function getShopId(): int
    {
        return (int)$this->getter('shop_id');
    }

    /**
     * gibt das Zeichen des Kunden zurück
     * @return string
     */
    public function getKundeZeichen(): string
    {
        return $this->getter('kunde_zeichen');
    }

    public function getCustomerZeichen(): string
    {
        return $this->getter('kunde_zeichen');
    }


    /**
     * gibt die Belegkenzeichnung des Kunden zurück
     * @return string
     */
    public function getKundeBeleg(): string
    {
        return $this->getter('kunde_beleg');
    }

    /**
     * @return string
     */
    public function getCustomerBeleg(): string
    {
        return $this->getter('kunde_beleg');
    }

    /**
     * prüft ob alle lieferbaren OrderItems auf einen Lieferschein stehen -> damit ausgeliefert sind
     * @return bool
     */
    public function isCompleteDeliverd(): bool
    {
        $order_complete = true;

        foreach ($this->getOrderItems() as $order_item) {
            if (!$order_item->isDeliveryAble()) {
                continue;
            }
            if ($order_item->isStorno()) {
                continue;
            }

            if ($order_item->getQuantityOpen() > 0) {
                $order_complete = false;
                break;
            }
        }

        return $order_complete;
    }

    /**
     * gibt zurück wie die Mwst behandelt wird
     * @return int
     */
    public function getTaxStatus(): int
    {
        return (int)$this->getter('tax_status');
    }

    /**
     * gibt die Referenz zur Zahlung zurück (abhänging von Zahlungsart)
     * @return string
     */
    public function getPaymentReferenz(): string
    {
        return $this->getter('payment_referenz');
    }

    /**
     * gibt die Referenz zur Zahlung zurück (abhänging von Zahlungsart)
     * @return string
     */
    public function getPaymentReferenz2(): string
    {
        return $this->getter('payment_referenz_2');
    }

    /**
     * gibt die Referenz zur Zahlung zurück (abhänging von Zahlungsart)
     * @return string
     */
    public function getPaymentReferenz3(): string
    {
        return $this->getter('payment_referenz_3');
    }

    /**
     * gibt das Datum der letzten Lieferung zurück
     * return \bqp\Date\DateObj
     */
    public function getDeliveryDate(): DateObj
    {
        $datum = db::getInstance()->fieldQuery("
            SELECT
                warenausgang_lieferschein.datum
            FROM
                warenausgang_lieferschein
            WHERE
                warenausgang_lieferschein.order_id = '" . $this->getOrderId() . "' AND
                warenausgang_lieferschein.status != '" . WarenausgangLieferschein::STATUS_STORNIERT . "'
            ORDER BY
                warenausgang_lieferschein.datum
        ");

        return new DateObj($datum);
    }


    /**
     * gibt ein Array mit den Order Items zurück
     * @return OrderItem[]
     */
    public function getOrderItems(): array
    {
        $items = [];

        foreach ($this->order_items as $item) {
            if ($item->getObjStatus() === SmartDataObj::STATUS_DEL) {
                continue;
            }

            $items[] = $item;
        }

        return $items;
    }

    /**
     * gibt ein Array mit den Order Items zurück
     * ACHTUNG: kann auch stornierte Positionen enthalten
     * @return OrderItem[]
     */
    public function getOrderItemsSorted(): array
    {
        $items = [];

        foreach ($this->order_items as $item) {
            if ($item->getObjStatus() == SmartDataObj::STATUS_DEL) {
                continue;
            }

            $items[] = $item;
        }

        usort($items, function (OrderItem $a, OrderItem $b) {
            if ($a->getPos() === $b->getPos()) {
                return 0;
            }

            return $a->getPos() > $b->getPos() ? 1 : -1;
        });

        return $items;
    }

    /**
     * gibt alle order_items eines bestimmten types zurück
     *
     * @param string $type
     * @return OrderItem[]
     */
    public function getOrderItemsByWarenkorbTyp(string $type): array
    {
        return $this->getOrderItemsByWarenkorbTypes([$type]);
    }

    /**
     * @param array $types
     * @return OrderItem[]
     */
    public function getOrderItemsByWarenkorbTypes(array $types): array
    {
        $order_items = $this->getOrderItems();

        foreach ($order_items as $key => $order_item) {
            if ($order_item->isStorno() || !in_array($order_item->getWarenkorbType(), $types)) {
                unset($order_items[$key]);
            }
        }

        return $order_items;
    }

    /**
     * gibt ein Array mit den Order Items zurück, vorsortiert und gefiltert für den kunden
     * -keine stornierten positionen
     * -keine versteckten positionen
     * @return OrderItem[]
     */
    public function getOrderItemsCustomer(): array
    {
        $order_items = $this->getOrderItemsSorted();

        foreach ($order_items as $key => $order_item) {
            if ($order_item->isStorno()) {
                unset($order_items[$key]);
            }
        }

        return $order_items;
    }

    /**
     * @param int $order_item_id
     * @return OrderItem
     * @throws SmartDataEntityNotFoundException
     */
    public function getOrderItemByOrderItemId(int $order_item_id): OrderItem
    {
        foreach ($this->order_items as $order_item) {
            if ($order_item->getOrderItemId() == $order_item_id) {
                return $order_item;
            }
        }

        throw new SmartDataEntityNotFoundException('$order_item_id not found (' . $order_item_id . ')');
    }


    /**
     * Sucht ein Warenkorb Item anhand der $product_id
     * @param int $product_id
     * @return OrderItem|null
     */
    public function getOrderItemByProductId(int $product_id): ?OrderItem
    {
        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->getProductId() == $product_id) {
                return $order_item;
            }
        }

        return null;
    }

    /**
     * enternt ein order_item anhand der $order_item_id
     * @param int $order_item_id
     */
    public function deleteOrderItemByOrderItemId(int $order_item_id): void
    {
        foreach ($this->order_items as $key => $order_item) {
            if ($order_item->getOrderItemId() !== $order_item_id) {
                continue;
            }

            if ($order_item->getObjStatus() === SmartDataObj::STATUS_NEW) {
                unset($this->order_items[$key]);
            } else {
                $order_item->del();
            }
        }
    }

    public function addOrderItem(OrderItem $order_item): void
    {
        $this->setSpedId($order_item->getSpedId());

        $this->order_items[] = $order_item;
    }

    /**
     * Fügt ein Produkt der Bestellung hinzu und gibt das order_item zurück
     *
     * @param int $product_id
     * @param int|null $ek_group_id
     * @return OrderItem
     * @throws ProductNotFoundException
     */
    public function createOrderItem(int $product_id, ?int $ek_group_id = null): OrderItem
    {
        if (!$this->getShopId()) {
            throw new DevException('$shop_id muss zu erst gesetzt werden');
        }

        if (!$ek_group_id) {
            $ek_group_id = ProductConst::EK_GROUP_ID_FAV;
        }

        $db = db::getInstance();

        $row = $db->singleQuery("
            SELECT
                product.product_id,
                product.product_nr,
                product.product_name,
                product.product_warenkorb_typ,
                product.condition_id,

                product_shop.mwst_satz,
                product_shop.vk_brutto,
                product_shop.lieferbaranzeige,

                product_ek.supplier_id, 
                IF(product_ek.ek_option = 'snp', product_ek.ek_snp, product_ek.ek_rnp) AS ek_netto,
                product_ek.ek_netto AS ek_nnn_netto
            FROM
                product INNER JOIN
                product_shop ON (product.product_id = product_shop.product_id AND product_shop.shop_id = '" . $this->getShopId() . "') INNER JOIN
                product_ek_active ON (product.product_id = product_ek_active.product_id AND product_ek_active.ek_group_id = " . $ek_group_id . ") INNER JOIN
                product_ek ON (product_ek.ek_id = product_ek_active.ek_id)
            WHERE
                product.product_id = " . $product_id . "
        ");

        if (!$row) {
            throw new ProductNotFoundException($product_id);
        }

        $order_item = new OrderItem($this);
        $order_item->setPos($this->getMaxPos() + 1);
        $order_item->setShopId($this->getShopId());
        $order_item->setProductId($row['product_id']);
        $order_item->setProductNr($row['product_nr']);
        $order_item->setProductName($row['product_name']);
        $order_item->setPreis($row['vk_brutto']);
        $order_item->setMwstSatz($row['mwst_satz']);
        $order_item->setEkNetto($row['ek_netto']);
        $order_item->setEkNnnNetto($row['ek_nnn_netto']);
        $order_item->setSupplierId($row['supplier_id']);
        $order_item->setSpedId($this->getSpedId());
        $order_item->setQuantity(1);
        $order_item->setDateAdded(new DateObj());
        $order_item->setStatus(OrderConst::STATUS_NEU_SHOP);

        $order_item->setLieferbaranzeigeAndSetEstimatedDeliveryDate($row['lieferbaranzeige']);
        $order_item->setTyp($row['product_warenkorb_typ']);

        $order_item->setLagerId($this->_getDefaultLagerId());

        if ($row['condition_id'] != ProductConst::CONDITION_NEW) {
            $this->addOrderTag(OrderConst::TAG_B_WARE);
        }

        global $_user;
        if ($_user) {
            $order_item->setUserId($_user->getUserId());
        }

        $this->addOrderItem($order_item);

        return $order_item;
    }

    public function _getDefaultLagerId(): int
    {
        $shop_id = $this->getShopId();

        return config::getLegacy('shop_' . $shop_id, 'default_lager_id');
    }

    public function getMaxPos(): int
    {
        $pos = 0;
        foreach ($this->getOrderItems() as $order_item) {
            $pos = max($pos, $order_item->getPos());
        }

        return $pos;
    }

    public function getOrderId(): int
    {
        return (int)$this->getter('order_id');
    }


    public function getOrgOrderId(): int
    {
        return (int)$this->getter('org_order_id');
    }

    /**
     * Gibt die Zahlungsmethode zurück
     * @return string (complete, deposit, by_delivery)
     * @deprecated
     */
    public function getPaymentMethod()
    {
        return $this->getter('payment_method');
    }

    /**
     * @deprecated
     */
    public function getPaymentMethodOrderId()
    {
        return $this->getter('payment_method_order_id');
    }

    /**
     * gibt die ShopReferenznummer zurück
     * @return string
     */
    public function getShopReferenz(): string
    {
        return $this->getter('shop_referenz');
    }

    /**
     * @return string
     */
    public function getShopReferenz2(): string
    {
        return $this->getter('shop_referenz_2');
    }

    /**
     * gibt eine eventuell vorhandene Lieferfrist für diesen Auftrag zurück
     * @return DateObj
     */
    public function getFristEnde(): DateObj
    {
        return new DateObj($this->getter('frist_ende'));
    }

    /**
     * gibt die Bemerkung zu der Lieferfrist zurück
     * @return string
     */
    public function getFristBemerkung(): string
    {
        return $this->getter('frist_bemerkung');
    }

    /**
     * gibt das Datum zurück wann die Lieferfrist gesetzt wurde
     * @return DateObj
     */
    public function getFristGesetzt(): DateObj
    {
        return new DateObj($this->getter('frist_gesetzt'));
    }


    /**
     * gibt die $user_id des erstellerst/annehmenden zurück
     * @return int $user_id
     */
    public function getUserId(): int
    {
        return $this->getter('user_id');
    }

    public function setUserId(int $user_id): bool
    {
        return $this->setter('user_id', $user_id);
    }

    /**
     * gibt die Vorkassenfrist als \bqp\Date\DateObj zurück
     * @return DateObj
     */
    public function getVorkassenFrist(): DateObj
    {
        return new DateObj($this->getter('vorkassen_frist'));
    }

    /**
     * gibt den typ der bestellung zurück
     * @return string
     * @see order_repository::getOrderTypes()
     */
    public function getOrderType(): string
    {
        return $this->getter('order_type');
    }

    /**
     * gibt falls vorhanden ein Datum zurück ab wann die Ware versand werden soll
     * @return DateObj
     */
    public function getAuslieferungAb(): DateObj
    {
        return new DateObj($this->getter('auslieferung_ab'));
    }

    /**
     * gibt die IP zurück mit welcher der Auftrag erstellt wurde
     * @return string $ip
     */
    public function getIp(): string
    {
        return $this->getter('ip');
    }

    /**
     * gibt das Bemerkungsfeld für die Logistik zurück
     * @return string
     */
    public function getVersandMemo(): string
    {
        return $this->getter('versand_memo');
    }

    /**
     * gibt die Kundenberkung zum Auftrag zurück
     * @return string
     */
    public function getKundenBemerkung(): string
    {
        return $this->getter('cust_topic');
    }

    /**
     * gibt zurück ob die Bemerkung des Kunden relevant ist
     * 0 = null
     * 1 = ja
     * -1 = nein
     */
    public function getKundenBemerkungRelevanz(): int
    {
        return (int)$this->getter('cust_topic_relevanz');
    }

    public function setKundenBemerkungRelevanz(int $value): bool
    {
        return $this->setter('cust_topic_relevanz', $value);
    }


    public function getOrderTags(): array
    {
        if (!$this->getter('order_tags')) {
            return [];
        }

        return explode(',', $this->getter('order_tags'));
    }

    public function setOrderTags(array $tags): bool
    {
        return $this->setter('order_tags', implode(',', $tags));
    }

    public function isOrderTag(string $tag): bool
    {
        return in_array($tag, $this->getOrderTags(), true);
    }

    public function addOrderTag(string $tag): bool
    {
        if (!$tag) {
            return false;
        }
        if ($this->isOrderTag($tag)) {
            return false;
        }

        $tags = $this->getOrderTags();
        $tags[] = $tag;

        return $this->setOrderTags($tags);
    }

    public function removeOrderTag(string $tag): bool
    {
        $order_tags = $this->getOrderTags();

        foreach ($order_tags as $key => $order_tag) {
            if ($tag === $order_tag) {
                unset($order_tags[$key]);
                $this->setOrderTags($order_tags);
                return true;
            }
        }

        return false;
    }

    /**
     * gibt die $lager_id des Versandlager zurück
     * @return int $lager_id
     *
     * @depracated lager_id ist keine Eigenschaft der Bestellung sondern der Positionenn. Achtung: die Positionen können unterschiedliche lager_ids haben.
     */
    public function getLagerId()
    {
        return $this->getter('lager_id');
    }

    /**
     * gibt die Versandart/Versandkategorie für den Auftrag zurück (nor, abh, exp)
     * wird für die berrechnung der Versandkosten benötigt
     * @return string $versand_kunden_opt
     */
    public function getVersandKundeOpt(): string
    {
        return $this->getter('versand_kunde_opt');
    }

    public function getOrderOriginId(): string
    {
        return $this->getter('order_origin_id');
    }

    public function getUstIdnr()
    {
        return $this->getter('ustidnr');
    }

    public function getUstIdnrStatus()
    {
        return $this->getter('ustidnr_status');
    }


    public function getBestaedigungsMailDate(): DateObj
    {
        return new DateObj($this->getter('bestaedigungs_mail'));
    }

    public function setBestaedigungsMailDate(DateObj $date): bool
    {
        return $this->setter('bestaedigungs_mail', $date->db('date'));
    }

    /**
     * gibt zurück ob für diesen Auftrag automatisch eine Rechnung erzeugt werden soll
     * @return bool
     */
    public function isInvoiceAutoCreate(): bool
    {
        return (bool)$this->getter('invoice_autocreate');
    }

    public function setInvoiceAutoCreate(bool $value): bool
    {
        return $this->setter('invoice_autocreate', $value ? '1' : '0');
    }


    /**
     * Setzt die Zahlungsmethode (complete, deposit, by_delivery)
     * @param string $value
     * @return bool
     * @deprecated
     */
    public function setPaymentMethod($value): bool
    {
        return $this->setter('payment_method', $value);
    }

    /**
     * @param $value
     * @return bool
     * @deprecated
     */
    public function setPaymentMethodOrderId($value): bool
    {
        return $this->setter('payment_method_order_id', $value);
    }


    /**
     * setzt die Zahlungsart für den Auftrag
     * @param int $value
     * @return bool
     */
    public function setZahlungsart(int $value): bool
    {
        return $this->setter('zahlungsart', $value);
    }

    public function setZahlungsartStatus(int $zahlungsart_status): bool
    {
        return $this->setter('zahlungs_status', $zahlungsart_status);
    }


    public function setGelangensbestaetigung($status)
    {
        $this->setter('gelangensbestaetigung', $status);
    }

    /**
     * setzt die $customer_id
     * @param int $customer_id
     * @return bool
     */
    public function setCustomerId(int $customer_id): bool
    {
        return $this->setter('customer_id', $customer_id);
    }

    /**
     * setzt die $customer_id und standard einstellungen für den kunden
     * @param int $customer_id
     */
    public function prefillByCustomerId(int $customer_id): void
    {
        $this->customer = null;

        $this->setCustomerId($customer_id);

        $shop_id = CustomerRepository::getShopIdByCustomerId($customer_id);

        $this->setShopId($shop_id);

        $this->order_address_container->init(true);
    }

    /**
     * setzt den kunden und übernimmt die standard einstellungen für den kunden
     * @param Customer $customer
     */
    public function prefillByCustomer(Customer $customer): void
    {
        $this->setCustomerId($customer->getCustomerId());
        $this->setShopId($customer->getShopId());
        $this->customer = $customer;

        $this->order_address_container->init(true);
    }

    /**
     * setzt die $shop_id
     * @param int $shop_id
     * @return bool
     */
    public function setShopId(int $shop_id): bool
    {
        return $this->setter('shop_id', $shop_id);
    }

    /**
     * setzt die sped_id für alle warenkorb items
     * @param int $sped_id
     * @return bool
     */
    public function setSpedId(int $sped_id): bool
    {
        foreach ($this->getOrderItems() as $order_item) {
            $order_item->setSpedId($sped_id);
        }

        return $this->setter('sped_id', $sped_id);
    }

    /**
     * setzt die $order_id
     * wird nur intern aufgerufen beim erstellen einer bestellung
     * @param int $order_id
     */
    private function setOrderId(int $order_id): void
    {
        $this->setter('order_id', $order_id);
    }

    /**
     * setzt den Status ob die Bestellung noch aktiv ist oder abgeschloßen
     * @param bool $status
     * @return bool
     */
    public function setOrderAktiv($status): bool
    {
        return $this->setter('order_aktiv', (int)$status);
    }

    /**
     * setzt das Zeichen des Kunden
     * @param string $value
     * @return bool
     */
    public function setKundeZeichen(string $value): bool
    {
        return $this->setter('kunde_zeichen', $value);
    }

    /**
     * setzt das Belegzeichen des Kunden
     * @param string $value
     * @return bool
     */
    public function setKundeBeleg(string $value): bool
    {
        return $this->setter('kunde_beleg', $value);
    }

    /**
     * setzt die IP, mit der der Auftrag angelegt wurde
     * @param string $ip
     * @return bool
     */
    public function setIp(string $ip): bool
    {
        return $this->setter('ip', $ip);
    }

    /**
     * setzt die Bemerkung des Kunden zur Bestellung
     * @param string $bemerkung
     * @return bool
     */
    public function setKundenBemerkung(string $bemerkung): bool
    {
        return $this->setter('cust_topic', trim($bemerkung));
    }


    public function setVorkassenFrist(DateObj $date): bool
    {
        return $this->setter('vorkassen_frist', $date->db('date'));
    }


    /**
     * setzt die shop_referenz (Referenznummer die im Shop vergeben wird bevor der Kunde die bestellung
     * abgeschickt hat, wird als referenz für z.B. Paypal, Finanzierung und co genutzt)
     * @param string $shop_referenz
     * @return bool
     */
    public function setShopReferenz(string $shop_referenz): bool
    {
        return $this->setter('shop_referenz', $shop_referenz);
    }

    public function setShopReferenz2(string $shop_referenz_2): bool
    {
        return $this->setter('shop_referenz_2', $shop_referenz_2);
    }

    public function setPayed(bool $value): bool
    {
        $status = $this->setter('is_payed', $value ? 1 : 0);

        if ($status) {
            $this->addOrderMemo(new OrderMemo('Auftrag ' . $this->getAuftnr() . ' als bezahlt markiert.', OrderMemo::BUCHHALTUNG));
        }

        return $status;
    }

    /**
     * setzt eine Lieferfrist für diesen Auftrag
     * @param DateObj $frist_ende
     * @return bool
     */
    public function setFristEnde(DateObj $frist_ende): bool
    {
        $status = $this->setter('frist_ende', $frist_ende->db());

        if ($status) {
            $this->setFristGesetzt(new DateObj());
        }

        return $status;
    }

    /**
     * setzt eine Bemerkung zur Lieferfrist
     * @param string $bemerkung
     * @return bool
     */
    public function setFristBemerkung($bemerkung): bool
    {
        return $this->setter('frist_bemerkung', $bemerkung);
    }

    /**
     * setzt das Datum wann die Fristgesetzt wurde
     * @param DateObj $datum
     * @return bool
     */
    public function setFristGesetzt(DateObj $datum): bool
    {
        return $this->setter('frist_gesetzt', $datum->db());
    }

    /**
     * @return int
     */
    public function getBewertungsEmailStatus(): int
    {
        return $this->getter('bewertungs_email');
    }

    /**
     * 0 - Ausgangszustand
     * 1 - Bewertungsemail wurde gesendet
     * 2 - Bewertungsemail wurde unterdrückt
     * @param int $value
     * @return boolean
     */
    public function setBewertungsEmailStatus(int $value): bool
    {
        return $this->setter('bewertungs_email', $value);
    }

    /**
     * setzt wie die Mwst des Auftrags bestimmt wird
     * @param int $tax_status
     * @return boolean
     * @see OrderConst::TAX_STATUS_*
     */
    public function setTaxStatus($tax_status): bool
    {
        return $this->setter('tax_status', $tax_status);
    }

    public function setTaxStatusManual($status): bool
    {
        return $this->setter('tax_status_manual', $status ? 1 : 0);
    }

    public function isTaxStatusManual(): bool
    {
        return (bool)$this->getter('tax_status_manual');
    }

    /**
     * Wird genutzt um den Kunden bei Vorkasse zu informieren, er hat zu wenig gezahlt und wieviel noch offen ist.
     * Vom Konzept her fragwürdig.
     *
     * setzt den Bezahlten Vorkassenbetrag
     * @param float $value
     * @return boolean
     */
    public function setVorkassenBetrag($value): bool
    {
        return $this->setter('vorkassen_betrag', $value);
    }

    /**
     * gibt an wieviel der Kunde shcon Bezahlt hat
     * @return float
     */
    public function getVorkassenBetrag()
    {
        return $this->getter('vorkassen_betrag');
    }

    /**
     * setzt den Typ der Bestellung
     * @param string $order_type
     * @return boolean
     * @see order_repository::getOrderTypes()
     */
    public function setOrderType(string $order_type): bool
    {
        return $this->setter('order_type', $order_type);
    }

    /**
     * setzt ein Datum ab wann die Ware versand werden soll
     * @param DateObj $date
     * @return bool
     */
    public function setAuslieferungAb(DateObj $date): bool
    {
        return $this->setter('auslieferung_ab', $date->db('date'));
    }

    /**
     * setzt das Bemerkugnsfeld für die Logistik
     * @param string $memo
     * @return boolean
     */
    public function setVersandMemo($memo): bool
    {
        return $this->setter('versand_memo', $memo);
    }

    /**
     * setzt das Versandlager
     * @param int $lager_id
     * @return bool
     */
    public function setLagerId(int $lager_id): bool
    {
        foreach ($this->getOrderItems() as $order_item) {
            $order_item->setLagerId($lager_id);
        }

        return $this->setter('lager_id', $lager_id);
    }

    /**
     * setzt die Versandart/Versandkategorie für den Auftrag (nor, abh, exp)
     * wird für die berrechnung der Versandkosten benötigt
     * @param string $versand_kunde_opt
     * @return boolean
     */
    public function setVersandKundeOpt($versand_kunde_opt): bool
    {
        return $this->setter('versand_kunde_opt', $versand_kunde_opt);
    }

    public function setOrderOriginId(string $order_origin_id): bool
    {
        return $this->setter('order_origin_id', $order_origin_id);
    }

    /**
     * Achtung: wird per Cron direkt in der Datenbank nachgepflegt.
     *
     * @param string $order_campaign_id
     * @return bool
     */
    public function setOrderCampaignId(string $order_campaign_id): bool
    {
        return $this->setter('order_campaign_id', $order_campaign_id);
    }

    public function getOrderCampaignId(): string
    {
        return (string)$this->getter('order_campaign_id');
    }

    public function setPaymentReferenz($referenz): bool
    {
        return $this->setter('payment_referenz', $referenz);
    }

    public function setPaymentReferenz2($referenz): bool
    {
        return $this->setter('payment_referenz_2', $referenz);
    }

    public function setPaymentReferenz3(string $referenz): bool
    {
        return $this->setter('payment_referenz_3', $referenz);
    }

    public function setPaymentUrl(string $payment_url): bool
    {
        return $this->setter('payment_url', $payment_url);
    }

    public function getPaymentUrl(): string
    {
        return $this->getter('payment_url');
    }

    /**
     * gibt den zusatz Infotext für die Rechnung zurück
     * @return string
     */
    public function getInvoiceNotice(): string
    {
        return $this->getter('invoice_notice');
    }

    /**
     * setzt den Zusatztext für die Rechnung. (Text wird am Ende der Rechnung angezeigt)
     * @param string $invoice_notice
     * @return bool
     */
    public function setInvoiceNotice(string $invoice_notice): bool
    {
        return $this->setter('invoice_notice', $invoice_notice);
    }

    public function getCurrency(): string
    {
        return 'EUR';
    }

    /**
     * @param int $device_id
     * @return bool
     */
    public function setDeviceId(int $device_id): bool
    {
        return $this->setter('device_id', $device_id);
    }

    /**
     * @return int
     */
    public function getDeviceId(): int
    {
        return (int)$this->getter('device_id');
    }

    //methoden
    public function getShipmentCalculator(): RateCalculator
    {
        $address = $this->getLieferAddress();

        $request = new RateCalculatorRequest();
        $request->setVersandKundeOpt($this->getVersandKundeOpt());
        $request->setCountryId($address->getCountryId());
        $request->setPLZ($address->getPlz());
        $request->setZahlungsId($this->getZahlungsart());
        $request->setOrderOriginId($this->getOrderOriginId());

        foreach ($this->getOrderItems() as $order_item) {
            //if(!$order_item->isDeliveryAble()) continue;
            if ($order_item->getTyp() === OrderConst::WARENKORB_TYP_VERSANDAUTO) {
                continue;
            }
            if ($order_item->isStorno()) {
                continue;
            }

            $request->addProduct(RateCalculatorProduct::createByOrderItem($order_item));
        }

        $calculator = new ShipmentTypeRateCalculator();
        $calculator->setShopId($this->getShopId());
        $calculator->setRateCalculatorRequest($request);

        return $calculator;
    }

    /**
     * berechnet die Versandkosten für den Auftrag neu
     */
    public function recalcVersand(): void
    {
        $calculator = $this->getShipmentCalculator();

        $shipment_positions = $calculator->getShipmentPositions();

        $autoitems = array_keys(ShipmentRepository::getVersandkostenarten());

        $versandarten_to_remove = ShipmentRepository::getVersandkostenarten();
        foreach ($shipment_positions as $shipment_position_type => $NULL) {
            unset($versandarten_to_remove[$shipment_position_type]);
        }

        foreach ($autoitems as $shipment_position_type) {
            if (!isset($shipment_positions[$shipment_position_type])) {
                continue;
            }
            $shipment_position = $shipment_positions[$shipment_position_type];
            unset($shipment_positions[$shipment_position_type]);

            $order_item = $this->getVersandItem($shipment_position_type);

            $order_item->setProductName($shipment_position->getText());
            $order_item->setQuantity($shipment_position->getQuantity());
            $order_item->setVkBrutto($shipment_position->getVkBrutto());
            $order_item->setEkBrutto($shipment_position->getEkBrutto());
            $order_item->setEkNnnBrutto($shipment_position->getEkBrutto());
            $order_item->setMwstSatz($shipment_position->getVatRateValue());
        }

        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->getTyp() !== OrderConst::WARENKORB_TYP_VERSANDAUTO) {
                continue;
            }

            if (!isset($versandarten_to_remove[$order_item->getTypValue()])) {
                continue;
            }

            $this->deleteOrderItemByOrderItemId($order_item->getOrderItemId());
        }
    }

    /**
     * return order_item
     * @param string $shipment_position_type (entweder ShipmentRepositoryy:: oder auch Freitext möglich)
     * @return OrderItem
     * @throws Exception
     */
    public function getVersandItem(string $shipment_position_type): OrderItem
    {
        if (!trim($shipment_position_type)) {
            throw new BadMethodCallException('$shipment_position_type must definied');
        }

        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->getTyp() === OrderConst::WARENKORB_TYP_VERSANDAUTO and $order_item->getTypValue() === $shipment_position_type) {
                return $order_item;
            }

            if ($order_item->getTyp() === OrderConst::WARENKORB_TYP_VERSAND and $order_item->getTypValue() === $shipment_position_type) {
                return $order_item;
            }
        }

        $status = $this->getMinStatus();

        $order_item = $this->createOrderItem($this->getFreeVersandProductId());
        $order_item->setTypValue($shipment_position_type);
        $order_item->setTyp(OrderConst::WARENKORB_TYP_VERSANDAUTO);
        $order_item->setStatus($status);

        return $order_item;
    }

    private function getFreeVersandProductId(): int
    {
        static $product_ids = null;

        if ($product_ids === null) {
            $product_ids = db::getInstance()->query("
                SELECT
                    product.product_id
                FROM
                    product
                WHERE
                    product.product_warenkorb_typ = '" . OrderConst::WARENKORB_TYP_VERSAND . "'
            ")->asSingleArray();
        }

        foreach ($product_ids as $product_id) {
            if ($this->getOrderItemByProductId($product_id)) {
                continue;
            }

            return $product_id;
        }

        throw new Exception('no product dummys for shipping');
    }

    public function getOrderSummeDetail(): array
    {
        $mwst_details = [];
        $summe_brutto = 0;
        $summe_netto = 0;
        foreach ($this->getOrderItemsCustomer() as $order_item) {
            $summe_brutto += $order_item->getPreisSumme();
            $netto = $order_item->getPreisSumme() / $order_item->getVatRate()->getAsFactor();
            $summe_netto += $netto;

            if (!array_key_exists($order_item->getMwstSatz(), $mwst_details)) {
                $mwst_details[$order_item->getMwstSatz()] = 0;
            }

            $mwst_details[$order_item->getMwstSatz()] += $order_item->getPreisSumme() - $netto;
        }

        switch ($this->getTaxStatus()) {
            case OrderConst::TAX_STATUS_NORMAL:
                $mwst = $this->getTaxStatus() == OrderConst::TAX_STATUS_NORMAL;
                $mwst_text = '';
                break;
            case OrderConst::TAX_STATUS_IG_LIEFERUNG:
            case OrderConst::TAX_STATUS_IG_AUSFUHRLIEFERUNG:
            case OrderConst::TAX_STATUS_SOLAR_STEUERFREI:
                $mwst = false;
                $mwst_text = order_repository::getTaxStatusInternalName($this->getTaxStatus());
                $mwst_details = [VatRateConst::UST_0_INT => 0];
                break;
            default:
                throw new InvalidArgumentException('tax status is unknown (' . $this->getTaxStatus() . ')');
        }

        return [
            'mwst' => $mwst,
            'mwst_text' => $mwst_text,
            'mwst_details' => $mwst_details,
            'brutto' => $summe_brutto,
            'netto' => $summe_netto
        ];
    }

    /**
     * erzeugt die Kundenrechnung falls nocht nicht passiert
     *
     * Anmerkung: Speicher die Bestellung
     */
    public function createInvoice(): void
    {
        if (!env::useModule('invoice')) {
            return;
        }

        if ($this->hasInvoice()) {
            return;
        }

        if ($this->getShopId() === Shop::ERSATZTEILSHOP) {
            //aufträge nach 2021-04-30 23:59:59
            if ($this->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
                return;
            }
        }

        Rechnung::createInvoice($this);
    }

    /**
     * prüft ob eine Rechnung für den Auftrag existiert
     *
     * @return bool
     */
    public function hasInvoice(): bool
    {
        return (bool)$this->getter('rechnungs_id');
    }

    public function setRechnungsId(int $rechnungs_id): bool
    {
        return $this->setter('rechnungs_id', $rechnungs_id);
    }

    /**
     * @todo -> warum ist das Rechnungszeurgs hier mit drin?
     */
    public function getRechnungsNr()
    {
        if (!$this->getter('rechnungs_nr')) {
            return false;
        }

        return $this->getter('rechnungs_nr');
    }

    /**
     * @todo -> weg damit. Die Consumer auf eine Liste mit Rechnungen umstellen.
     *
     */
    public function getRechnungsDatum(): DateObj
    {
        return new DateObj($this->daten['rechnungs_datum']);
    }

    /**
     * sendet die Rechnung an den Kunden
     * @param string $mail_template Mailvorlage
     * @throws FileNotFoundException
     * @throws Exception
     */
    public function mailInvoice(string $mail_template = null): void
    {
        if (!$this->hasInvoice()) {
            return;
        }

        $mail_profile = new OrderMailProfile();
        $mail_profile->setOrder($this);

        if (!$mail_profile->isMail($mail_profile::MAIL_TYPE_INVOICE)) {
            return;
        }

        if ($mail_template === null) {
            $mail_template = $mail_profile->getInvoiceMailTemplate();
        }

        $mail = new Mail();
        $mail->setSignatureType('system');
        $mail->setOrder($this);

        if (!$mail->loadVorlage($mail_template)) {
            return;
        }

        $invoice_repository = service_loader::getDiContainer()->get(InvoiceRepository::class);

        $mail->addAttachmentString($invoice_repository->getFirstInvoiceAsPdf($this->getOrderId()), 'Rechnung ' . $this->getAuftnr() . '.pdf');

        if ($this->getOrderOriginId() === OrderConst::ORDER_ORIGIN_CROWDFOX) {
            $mail->setEmpf('<EMAIL>');
        }
        $mail->send();
    }

    /**
     * bucht falls nötig die Kreditkarte ab
     * @throws exception_payment
     *
     * @deperected raus damit
     */
    public function chargeCreditcard(): bool
    {
        if ($this->getZahlungsartStatus() == OrderConst::PAYMENT_STATUS_KREDITKARTE_BOOKED) {
            return true;
        }

        $concardis = ConcardisFactory::create($this->getShopId());
        $concardis->abbuchenByOrder($this);

        $payment = new OrderPaymentReceipt();
        $payment->setAmount($this->getRechnungsBetrag());
        $payment->setZahlungsId(OrderConst::PAYMENT_KREDITKARTE);
        $payment->setComplete(true);
        $payment->setMailPaymentReceipt(false);

        $this->addPaymentReceipt($payment);

        return true;
    }


    public function sendZugangsbestaetigung($user_id = false)
    {
        if ($this->getZahlungsart() != OrderConst::PAYMENT_VORKASSE) {
            return null;
        }
        if ($this->getBestaedigungsMailDate()->isValid()) {
            return null;
        }

        $mail = new Mail();
        $mail->setOrder($this);

        if ($user_id) {
            $mail->setUserId();
        }

        if ($this->daten['zahlungsart'] == OrderConst::PAYMENT_VORKASSE) {
            $mail->loadVorlage('zugangsbestaetigung_vork');
        } else {
            $mail->loadVorlage('zugangsbestaetigung');
        }

        $mail->parse();

        $this->addOrderMemo(new OrderMemo("Zugangsbestätigung (" . $this->getAuftnr() . ") geschickt", OrderMemo::VERKAUF));

        $this->setBestaedigungsMailDate(new DateObj());

        $delivery = new OrderItemEstimatedDelivery();
        $delivery->setEstimatedDeliveryDate($this->getEstimatedDeliveryDate());
        $delivery->setCustomerInformed(true);
        $delivery->setNote('Zugangsbestätigung');

        $this->addEstimatedDeliveryDate($delivery);

        return $mail->send();
    }

    public function addOrderMemo(OrderMemo $memo): void
    {
        $this->order_memo_save_queue[] = $memo;
    }

    /**
     * setzt den Status aller nicht stornieren Positionen.
     * @param int $status
     * @return bool
     */
    public function setStatus(int $status): bool
    {
        $change = false;
        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            $change = $order_item->setStatus($status) || $change;
        }

        return $change;
    }

    /**
     * gibt den kleinsten Status alle Warenkorbitems zurück
     * @return int
     */
    public function getMinStatus(): int
    {
        $status = 1000;
        foreach ($this->getOrderItems() as $order_item) {
            $status = min($order_item->getStatus(), $status);
        }
        return $status;
    }

    public function getMaxStatus(): int
    {
        $status = 0;
        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            $status = max($order_item->getStatus(), $status);
        }
        return $status;
    }

    public function hasStatus(int $status): bool
    {
        foreach ($this->order_items as $order_item) {
            if ($order_item->getStatus() === $status) {
                return true;
            }
        }

        return false;
    }

    public function isStorno(): bool
    {
        if (!$this->getOrderItems()) {
            return false;
        }

        foreach ($this->getOrderItems() as $order_item) {
            if (!$order_item->isStorno()) {
                return false;
            }
        }
        return true;
    }

    public function isOrderAktiv(): bool
    {
        return (bool)$this->getter('order_aktiv');
    }

    public function isPayed(): bool
    {
        return (bool)$this->getter('is_payed');
    }
    //

    /**
     * ermittelt ob die Bestellung noch aktiv ist oder abgeschloßen
     * @return bool
     */
    private function calcOrderAktiv(): bool
    {
        if ($this->getOrderType() === OrderConst::ORDER_TYPE_EK_BESTELLUNG) {
            return false;
        }

        return !in_array($this->getMinStatus(), [OrderConst::STATUS_ZUSTELLUNG_BEENDET, OrderConst::STATUS_BEENDET, OrderConst::STATUS_STORNO, OrderConst::STATUS_ANGEBOT_STORNIERT]);
    }

    public function getEstimatedDeliveryDate(): ?DateObj
    {
        $date = null;

        foreach ($this->getOrderItems() as $order_item) {
            $new_date = $order_item->getEstimatedDeliveryDate();

            if ($date === null) {
                $date = $new_date;
            }

            if ($new_date->isAfter($date)) {
                $date = $new_date;
            }
        }

        return $date;
    }

    public function getEstimatedDeliveryDateCustomer(): DateObj
    {
        $date = null;

        foreach ($this->getOrderItems() as $order_item) {
            $new_date = $order_item->getEstimatedDeliveryDateCustomer();

            if ($date === null) {
                $date = $new_date;
            }

            if ($new_date->isAfter($date)) {
                $date = $new_date;
            }
        }

        return $date;
    }

    public function addEstimatedDeliveryDate(OrderItemEstimatedDelivery $delivery): void
    {
        foreach ($this->getOrderItems() as $order_item) {
            $order_item->addEstimatedDelivery(clone $delivery);
        }
    }

    public function setEstimatedDeliveryDateCustomer(DateObj $date): void
    {
        foreach ($this->getOrderItems() as $order_item) {
            $order_item->setEstimatedDeliveryDateCustomer($date);
        }
    }

    public function reviseEks(): void
    {
        $ek_revise_sum = 0;
        $ek_nnn_revise_sum = 0;

        $vk_sums = new SplObjectStorage();
        $vk_total_sum = 0;

        foreach ($this->getOrderItems() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            if (in_array($order_item->getTyp(), [OrderConst::WARENKORB_TYP_PRODUKT, OrderConst::WARENKORB_TYP_ASWO])) {
                $vk_sums[$order_item] = $order_item->getVkBruttoSum();
                $vk_total_sum += $order_item->getVkBruttoSum();

                continue;
            }

            $ek_revise_sum += ($order_item->getVkBrutto() - $order_item->getEkBrutto()) * $order_item->getQuantity();
            $ek_nnn_revise_sum += ($order_item->getVkBrutto() - $order_item->getEkNnnBrutto()) * $order_item->getQuantity();
            $order_item->setEkRevisedBrutto($order_item->getVkBrutto());
        }

        if (!$vk_total_sum) {
            return;
        }

        foreach ($vk_sums as $order_item) {
            $percent = $vk_sums[$order_item] / $vk_total_sum;

            $order_item->setEkRevisedBrutto(round($order_item->getEkBrutto() - ($ek_revise_sum * $percent) / ($order_item->getQuantity()), 2));
            $order_item->setEkNnnRevisedBrutto(round($order_item->getEkNnnBrutto() - ($ek_revise_sum * $percent) / ($order_item->getQuantity()), 2));
        }
    }

    public function recalcOrderAmounts(): void
    {
        $order_amount_net = $this->getVkNetto();
        $order_amount_gross = $this->getVkBrutto();

        $this->setOrderAmountNet(round($order_amount_net, 2));
        $this->setOrderAmountGross(round($order_amount_gross, 2));
    }

    //auftrags statuse

    public function auft_go($order_item_id = null): void
    {
        $status = $this->getStatusForWarteAuslieferung();

        //bei Vorkasse frist setzen
        if ($this->getZahlungsart() == OrderConst::PAYMENT_VORKASSE && $status == OrderConst::STATUS_WARTE_ZAHLUNG) {
            $this->refreshVorkassenFrist();
        }

        if ($order_item_id) {
            $order_item = $this->getOrderItemByOrderItemId($order_item_id);
            $order_item->setStatus($status);
        } else {
            $this->setStatus($status);
        }
    }

    public function refreshVorkassenFrist(): void
    {
        $date = new DateObj();
        $date->addSimple('days', 16);
        $this->setVorkassenFrist($date);
    }

    /**
     * gibt Waret auf Zahlung oder Warte auf Ausliefeurng zurück, je nachdem
     * @return int
     */
    public function getStatusForWarteAuslieferung(): int
    {
        switch ($this->getZahlungsart()) {
            case OrderConst::PAYMENT_RAKUTEN:
            case OrderConst::PAYMENT_VORKASSE:
            case OrderConst::PAYMENT_FINANZIERUNG_HANSEATIC:
            case OrderConst::PAYMENT_KREDITKARTE:
            case OrderConst::PAYMENT_PAYPAL:
            case OrderConst::PAYMENT_SOFORTUEBERWEISUNG:
            case OrderConst::PAYMENT_DIREKTUEBERWEISUNG:
            case OrderConst::PAYMENT_BARZAHLEN:
            case OrderConst::PAYMENT_ECOM_UNZER:
            case OrderConst::PAYMENT_EBAY:
                if ($this->getZahlungsartStatus() != OrderConst::PAYMENT_STATUS_LEGACY_0) {
                    $status = OrderConst::STATUS_WARTE_AUSLIEFERUNG;
                } else {
                    $status = OrderConst::STATUS_WARTE_ZAHLUNG;
                }
                break;
            case OrderConst::PAYMENT_FINANZIERUNG_COMMERZ:
                if ($this->getZahlungsartStatus() == OrderConst::PAYMENT_STATUS_FINANZIERUNG_GENEHMIGT) {
                    $status = OrderConst::STATUS_WARTE_AUSLIEFERUNG;
                } else {
                    $status = OrderConst::STATUS_WARTE_ZAHLUNG;
                }
                break;
            default:
                $status = OrderConst::STATUS_WARTE_AUSLIEFERUNG;
        }

        return $status;
    }


    /**
     * gibt zurück ob die Bestellung bezhalungstechnisch ok ist
     * -> finanzierungsantrag
     * -> kreditkarte reserviert
     * -> zahlungsbestätigung von sü, paypal
     * -> zahlungsfreigabe rakuten
     *
     *
     * @return bool
     */
    public function isPayable(): bool
    {
        if (in_array($this->getZahlungsart(), [OrderConst::PAYMENT_NACHNAHME, OrderConst::PAYMENT_VORKASSE])) {
            return true;
        }

        return $this->getZahlungsartStatus() >= OrderConst::PAYMENT_STATUS_LEGACY_1;
    }


    public function addPaymentReceipt(OrderPaymentReceipt $payment): void
    {
        $action = new OrderPaymentAction();
        $action->addPaymentReceiptToOrder($payment, $this);
    }

    /**
     * berechnet die Versand Priorität für alle Produkte diese Auftrags neu
     * @param null|array $lager_ids
     */
    public function calcVersandPrio(?array $lager_ids = null): void
    {
        $lager_ids_explicit = $lager_ids;

        foreach ($this->getOrderItems() as $order_item) {
            if (!$order_item->isDeliveryAble()) {
                continue;
            }

            $lager_ids = $lager_ids_explicit;
            if (!$lager_ids) {
                $lager_ids = [$order_item->getLagerId()];
            }

            ProductLager::calcOrdersForProduct($order_item->getProductId(), $lager_ids);
        }
    }

    public function isOrderVersandPrio(): bool
    {
        $versand_status_negativ = db::getInstance()->fieldQuery("
                SELECT
                    COUNT(*)
                FROM
                    order_item INNER JOIN
                    product_warenkorb_types ON (order_item.typ = product_warenkorb_types.product_warenkorb_typ)
                WHERE
                    order_item.order_id = '" . $this->getOrderId() . "' AND
                    order_item.versand_status = 0 AND
                    product_warenkorb_types.delivery = 1
            ");

        return $versand_status_negativ == 0;
    }

    private function setOrderAmountNet(float $order_amount_net): bool
    {
        return $this->setter('order_amount_net', $order_amount_net);
    }

    private function setOrderAmountGross(float $order_amount_gross): bool
    {
        return $this->setter('order_amount_gross', $order_amount_gross);
    }
}
