<?php

namespace wws\Order;

use bqp\Date\DateObj;
use wws\Supplier\SuppliersConst;

class OrderCompletionServiceEcom extends OrderCompletionService
{
    protected function waitingTimeExceeded(Order $order, DateObj $date): bool
    {
        // ->"Rate verspäteter Lieferungen" vs "Rate gültiger Sendungsnummern"
        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
            if ($this->isOrderAssociatedWithSupplier($order, SuppliersConst::SUPPLIER_ID_GAEFGEN)) {
                $threshold = $date->clone()->setTime(20, 00);
                $threshold->addSimple('hour', 48);

                return !$threshold->isFuture();
            }

            if ($this->isOrderAssociatedWithSupplier($order, SuppliersConst::SUPPLIER_ID_UNI_FULFILL)) {
                $threshold = $date->clone()->setTime(20, 00);
                $threshold->addSimple('hour', 24);

                return !$threshold->isFuture();
            }

            if ($this->isOrderAssociatedWithSupplier($order, SuppliersConst::SUPPLIER_ID_EBERHARD)) {
                $threshold = $date->clone()->setTime(20, 00);
                $threshold->addSimple('hour', 24);

                return !$threshold->isFuture();
            }

            if ($this->isOrderAssociatedWithSupplier($order, SuppliersConst::SUPPLIER_ID_ZAJADACZ)) {
                $threshold = $date->clone()->setTime(20, 00);
                $threshold->addSimple('hour', 24);

                return !$threshold->isFuture();
            }
        }

        if (
            $order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON ||
            $order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_EBAY
        ) {
            $threshold = $date->clone()->setTime(22, 30);

            return !$threshold->isFuture();
        }

        //fallback nach 24h
        return !$date->addSimple('hours', 24)->isFuture();
    }
}
