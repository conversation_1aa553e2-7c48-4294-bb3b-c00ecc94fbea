<?php

namespace wws\Order;

use bqp\Date\DateObj;

class OrderCompletionServiceK11 extends OrderCompletionService
{
    protected function waitingTimeExceeded(Order $order, DateObj $date): bool
    {
        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_EBAY) {
            //bei uns im Lager klappt es mit dem Sendungsabschluss manchmal nicht...
            $threshold = $date->clone()->setTime(16, 30);
            $threshold->addSimple('hour', 24);

            return !$threshold->isFuture();
        }

        //fallback nach 24h
        return !$date->addSimple('hours', 24)->isFuture();
    }
}
