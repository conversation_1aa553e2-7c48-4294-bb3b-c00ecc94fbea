<?php

namespace wws\Order\Table;

use bqp\Actionbar\ActionbarLink;
use bqp\Actionbar\ActionbarLinkInline;
use bqp\db\db_generic;
use bqp\Dropdown\Dropdown;
use bqp\table\DataSource\TableObjectDataSourceWws;
use bqp\table\ExtraRowRollup;
use bqp\table\field\table_object_field_callback;
use bqp\table\field\table_object_field_checkbox;
use bqp\table\field\table_object_field_currency;
use bqp\table\Form\TableObjectFormSimpleButtons;
use bqp\table\TableObjectPersistent;
use wws\BankAccount\BankTransaction;
use wws\buchhaltung\AccountingConsts;
use wws\Order\OrderConst;

class TableBuilderBankTransactions
{
    private int $customer_id;

    private db_generic $db;

    private bool $is_show_fees = false;

    private bool $extra_rows = true;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function buildTransactions(?string $filter_id = null): TableObjectPersistent
    {

        $order_ids = $this->db->query("
            SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.customer_id = '" . $this->customer_id . "'
        ")->asSingleArray();

        $where = '';
        if ($order_ids) {
            $where = ' OR buchhaltung_bank_transactions.order_id IN (' . $this->db->in($order_ids) . ')';
        }

        $no_fees = '';
        if (!$this->is_show_fees) {
            $no_fees = " AND buchhaltung_bank_transactions.transaction_type != '" . BankTransaction::TRANSACTION_TYPE_FEE . "'";
        }

        $sql = "
            SELECT
                buchhaltung_bank_transactions.transaction_id,
                buchhaltung_bank_transactions.transaction_type,
                buchhaltung_bank_accounts.account_name,
                buchhaltung_bank_transactions.transaction_type_extern,
                buchhaltung_bank_transactions.transaction_status,
                buchhaltung_bank_transactions.transaction_date,
                buchhaltung_bank_transactions.extern_transaction_id,
                MAKRO.buchhaltung_bank_transactions.amount,
                buchhaltung_bank_transactions.sender_name,
                buchhaltung_bank_transactions.text_reference_1,
                buchhaltung_bank_transactions.text_reference_2,
                buchhaltung_bank_transactions.settlement_id,
                orders.auftnr,
                buchhaltung_bank_transactions.person_konto_nr
            FROM
                buchhaltung_bank_transactions INNER JOIN
                buchhaltung_bank_accounts ON (buchhaltung_bank_transactions.account_id = buchhaltung_bank_accounts.account_id) LEFT JOIN
                orders ON (buchhaltung_bank_transactions.order_id = orders.order_id)
            WHERE
                (
                    buchhaltung_bank_transactions.customer_id = '" . $this->customer_id . "'
                    $where
                )
                $no_fees
            ORDER BY
                buchhaltung_bank_transactions.transaction_date
        ";

        $ds = new TableObjectDataSourceWws($this->db, $sql);

        $table = new TableObjectPersistent($ds, $filter_id);
        $table->setExportEnabled(true);
        $table->setCaption('Zahlungsbewegungen');
        $table->setId('table_object_transactions');

        if ($this->extra_rows) {
            $table->getFieldByKey('amount')->setRollupComplete('SUM');
        }

        $table->setRowFormater(function (array $row) {
            if ($row['transaction_type'] !== 'debitor') {
                return ['class' => 'del'];
            }
        });

        if ($this->extra_rows) {

            $extra_row = new ExtraRowRollup();
            $extra_row->setTitle('Gebühren');
            $extra_row->setFilter(function (array $row) {
                return $row['transaction_type'] === BankTransaction::TRANSACTION_TYPE_FEE;
            });
            $extra_row->addRollupField('amount', 'sum', 0);
            $extra_row->addRollupField('currency_code', function () {
                return 'EUR';
            }, 'EUR');

            $table->addExtraRow($extra_row);

            $extra_row = new ExtraRowRollup();
            $extra_row->setTitle('Zahlungen');
            $extra_row->setFilter(function (array $row) {
                return $row['transaction_type'] === 'debitor' || $row['transaction_type'] === 'unknown';
            });
            $extra_row->addRollupField('amount', 'sum', 0);
            $extra_row->addRollupField('currency_code', function () {
                return 'EUR';
            }, 'EUR');

            $extra_row->setClass('rollup_complete');

            $table->addExtraRow($extra_row);
        }

        return $table;
    }

    public function buildTransactionsWithInvoice(?string $filter_id = null): TableObjectPersistent
    {

        $order_ids = $this->db->query("
            SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.customer_id = '" . $this->customer_id . "'
        ")->asSingleArray();

        $where = '';

        if ($order_ids) {
            $where = ' OR buchhaltung_bank_transactions.order_id IN (' . $this->db->in($order_ids) . ')';
        }

        $no_fees = '';

        if (!$this->is_show_fees) {
            $no_fees = " AND buchhaltung_bank_transactions.transaction_type != '" . BankTransaction::TRANSACTION_TYPE_FEE . "'";
        }

        $sql = "
                SELECT
                    buchhaltung_bank_transactions.transaction_id,
                    buchhaltung_bank_transactions.transaction_type,
                    buchhaltung_bank_accounts.account_id,
                    buchhaltung_bank_accounts.account_name,
                    buchhaltung_bank_transactions.transaction_type_extern,
                    buchhaltung_bank_transactions.transaction_status,
                    buchhaltung_bank_transactions.transaction_date,
                    buchhaltung_bank_transactions.extern_transaction_id,
                    null AS soll,
                    buchhaltung_bank_transactions.amount AS haben,
                    buchhaltung_bank_transactions.currency_code,
                    null AS difference,
                    buchhaltung_bank_transactions.sender_name,
                    buchhaltung_bank_transactions.text_reference_1,
                    buchhaltung_bank_transactions.text_reference_2,
                    buchhaltung_bank_transactions.settlement_id,
                    orders.auftnr,
                    buchhaltung_bank_transactions.person_konto_nr
                FROM
                    buchhaltung_bank_transactions INNER JOIN
                    buchhaltung_bank_accounts ON (buchhaltung_bank_transactions.account_id = buchhaltung_bank_accounts.account_id) LEFT JOIN
                    orders ON (buchhaltung_bank_transactions.order_id = orders.order_id)
                WHERE
                    (
                        buchhaltung_bank_transactions.customer_id = '" . $this->customer_id . "'
                        $where
                    )
                    $no_fees
            UNION
                (
                    SELECT
                        buchhaltung_rechnung.rechnungs_id,
                        '-' AS transaction_type,
                        '' AS account_id,
                        buchhaltung_rechnung.rechnungs_type,
                        '-' AS transaction_type_extern,
                        '-' AS transaction_status,
                        buchhaltung_rechnung.rechnungs_datum,
                        buchhaltung_rechnung.rechnungs_nr,
                        IF(buchhaltung_rechnung.tax_status = " . OrderConst::TAX_STATUS_NORMAL . ", buchhaltung_rechnung.rechnungs_betrag_brutto, buchhaltung_rechnung.rechnungs_betrag_netto) AS soll,
                        null AS haben,
                         'EUR',
                        null AS difference,
                        '-' AS sender_name,
                        '-' AS text_reference_1,
                        '-' AS text_reference_2,
                        '-' AS settlement_id,
                        orders.auftnr,
                        '-' AS person_konto_nr
                    FROM
                        orders INNER JOIN
                        buchhaltung_rechnung ON (buchhaltung_rechnung.order_id = orders.order_id)
                    WHERE
                        orders.customer_id = $this->customer_id
                    GROUP BY
                        buchhaltung_rechnung.rechnungs_id
                )
            ORDER BY
                transaction_date
        ";

        $ds = new TableObjectDataSourceWws($this->db, $sql);

        $table = new TableObjectPersistent($ds, $filter_id);
        $table->setEntriesPerPageDefault(2500); //ansonsten passt das mit den händigen rollups nicht...
        //$table->addPersistentField('customer_id');
        $table->setExportEnabled(true);
        $table->setCaption('Zahlungsbewegungen mit Belegen');
        $table->setId('table_object_transactions_with_invoice');

        $table->removeFieldByKey('account_id');

        $field = $table->getFieldByKey('haben');
        $field->setNullToZero(false);
        $field->setName('Haben');
        $field->setTemplate('<nobr class="value_haben">__VALUE__</nobr>');

        $field = $table->getFieldByKey('soll')->convert('currency');
        $field->setNullToZero(false);
        $field->setName('Soll');
        $field->setTemplate('<nobr class="value_soll">__VALUE__</nobr>');

        $field = $table->getFieldByKey('difference')->convert(table_object_field_currency::class);
        $field->setNullToZero(false);
        $field->setName('Differenz');
        $field->setHighlight(true);
        $field->setTemplate('<nobr>__VALUE__</nobr>');

        $table->getFieldByKey('soll')->setRollupComplete('SUM');
        $table->getFieldByKey('haben')->setRollupComplete('SUM');

        $table->removeFieldByKey('currency_code');

        $field = $table->getFieldByKey('transaction_id')->convert(table_object_field_callback::class);
        $field->setCallback(function (array $row) {
            if ($row['transaction_type'] !== '-') {
                return '<a href="/ax/buchhaltung/konto_transaction/transaction/?transaction_id=' . $row['transaction_id'] . '" target="inline">' . $row['transaction_id'] . '</a>';
            }
        });

        $extra_row = new ExtraRowRollup();
        $extra_row->addStaticContent('soll', '<div style="text-align: right; white-space: nowrap;" id="dynamic_soll_sum"></div>');
        $extra_row->addStaticContent('haben', '<div style="text-align: right; white-space: nowrap;" id="dynamic_haben_sum"></div>');
        $extra_row->addStaticContent('difference', '<div style="text-align: right; white-space: nowrap;" id="dynamic_difference"></div>');
        $extra_row->setClass('');
        $table->addExtraRow($extra_row);

        $extra_row = new ExtraRowRollup();
        $extra_row->setTitle('OPOS Ausgleichsbuchungen');
        $extra_row->setFilter(function (array $row) {
            return $row['account_id'] == AccountingConsts::BANK_ACCOUNT_OPOS;
        });
        $extra_row->addRollupField('haben', 'sum', 0);
        $extra_row->addRollupField('currency_code', function () {
            return 'EUR';
        }, 'EUR');

        $table->addExtraRow($extra_row);

        $extra_row = new ExtraRowRollup();
        $extra_row->setTitle('Gebühren');
        $extra_row->setFilter(function (array $row) {
            return $row['transaction_type'] === BankTransaction::TRANSACTION_TYPE_FEE;
        });
        $extra_row->addRollupField('haben', 'sum', 0);
        $extra_row->addRollupField('currency_code', function () {
            return 'EUR';
        }, 'EUR');

        $table->addExtraRow($extra_row);

        $extra_row = new ExtraRowRollup();
        $extra_row->setTitle('<b>Zahlungen/Rechnungen</b>');
        $extra_row->setFilter(function (array $row) {
            return $row['transaction_type'] === BankTransaction::TRANSACTION_TYPE_DEBITOR || $row['transaction_type'] === BankTransaction::TRANSACTION_TYPE_UNKNOWN || $row['account_name'] === 'invoice' || $row['account_name'] === 'gutschrift';
        });
        $extra_row->addRollupField('haben', 'sum', 0);
        $extra_row->addRollupField('soll', 'sum', 0);
        $extra_row->addRollupField('currency_code', function () {
            return 'EUR';
        }, 'EUR');
        $extra_row->addRollupField('difference', function (array $row, string $field, float $sum) {
            return $row['haben'] - $row['soll'] + $sum;
        }, 0);

        $extra_row->setClass('rollup_complete green');
        $table->addExtraRow($extra_row);

        $table->addRowFormaterHighlight('transaction_id', $_REQUEST['transaction_id'] ?? null);
        $table->addRowFormater(function (array $row) {
            if ($row['account_id'] == AccountingConsts::BANK_ACCOUNT_OPOS) {
                return ['class' => 'warn2'];
            }
            return null;
        });
        $table->addRowFormater(function (array $row) {
            if ($row['transaction_type'] === BankTransaction::TRANSACTION_TYPE_FEE) {
                return ['class' => 'del'];
            }
            return null;
        });


        $checkbox = new table_object_field_checkbox('sum', '');
        $checkbox->setClass('dynamic_sum_checkbox');
        $checkbox->hightlightTableRowOnChecked();

        $table->addField($checkbox);
        $table->loadRowFormaterCheckbox();

        $actions = new table_object_field_callback('action', 'Aktion');
        $actions->setAlign($actions::ALIGN_CENTER);
        $actions->setCallback(function (array $row) {
            $dropdown = new Dropdown();

            if ($row['transaction_type'] === '-') {
                $dropdown->addLink(new ActionbarLink('Beleg anzeigen', 'javascript:invoiceByBelegId(' . $row['transaction_id'] . ')'));
                $dropdown->addLink(new ActionbarLink('Ausgleichsbuchung erstellen', '/ax/buchhaltung/opos_transactions/transaction/?wfid=opos_customer&action=transaction_balancing_by_customer&customer_id=' . $this->customer_id . '&auftnr=' . $row['auftnr'] . '&amount=' . ($row['soll'])));
            } elseif ($row['account_id'] == AccountingConsts::BANK_ACCOUNT_OPOS) {
                $dropdown->addLink(new ActionbarLinkInline('Bearbeiten', '/ax/buchhaltung/opos_transactions/transaction/?wfid=opos_customer&transaction_id=' . $row['transaction_id']));
                $dropdown->addLink(new ActionbarLinkInline('Löschen', '/ax/buchhaltung/opos_transactions/transaction/?wfid=opos_customer&transaction_id=' . $row['transaction_id']));
            } else {
                $link = new ActionbarLink('Anzeigen', '/ax/buchhaltung/konto_transaction/transaction/?wfid=opos_customer&transaction_id=' . $row['transaction_id']);
                $link->popup_large();
                $dropdown->addLink($link);
                $dropdown->addLink(new ActionbarLinkInline('Ausgleichsbuchung erstellen', '/ax/buchhaltung/opos_transactions/transaction/?wfid=opos_customer&action=transaction_balancing&associated_transaction_id=' . $row['transaction_id']));
            }
            return $dropdown->render();
        });

        $table->addField($actions);


        $form = new TableObjectFormSimpleButtons();
        $form->addParameter('customer_id', $this->customer_id);
        $form->addButton('Ausgleichsbuchung erstellen', 'action_transaction_balancing_by_customer');
        $form->setAction('/ax/buchhaltung/opos_transactions/transaction/');
        $form->setMethod('GET');

        $table->setTableForm($form);

        return $table;

    }

    public function setCustomerId(int $customer_id): void
    {
        $this->customer_id = $customer_id;
    }

    public function showFees(bool $is_show_fees): void
    {
        $this->is_show_fees = $is_show_fees;
    }

    public function showRollup(bool $extra_rows): void
    {
        $this->extra_rows = $extra_rows;
    }
}