<?php

namespace wws\Order\Table;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\db\db_result_set;
use bqp\db\SqlDatePeriods;
use bqp\db\SqlUtils;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\table\DataSource\TableObjectDataSourceArray;
use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_callback;
use bqp\table\field\table_object_field_date;
use bqp\table\field\table_object_field_multi;
use bqp\table\TableObject;
use bqp\table\TableObjectPersistent;
use bqp\table\TableObjectUrlPersistent;
use cached_query;
use env;
use order_repository;
use output;
use persistent_container;
use service_loader;
use wws\business_structure\Shop;
use wws\Country\CountryRepository;
use wws\Order\Form\FormElementOrderTagSearch;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Preissuchmaschinen\PreissuchmaschinenRepository;
use wws\Product\ProductConst;
use wws\Product\ProductRepositoryLegacy;
use wws\Product\ProductTypes\ProductTypeRepository;
use wws\Supplier\SuppliersConst;

class TableBuilderTimelineOrderStats
{
    private string $order_view_group = 'order_origin';
    private string $order_view = 'order_count';
    private string $period_group = SqlDatePeriods::PERIOD_DAY;
    private DateObj $date_from;
    private DateObj $date_till;
    private db_generic $db;
    private int $shop_id = Shop::ALLEGO;
    private int $cat_tree_id;
    private bool $show_margins = true;

    private int $brand_id;

    public const DETAIL_VIEW_FULL = 'full';
    public const DETAIL_VIEW_ONLY_PRODUCT = 'only_product';
    public const DETAIL_VIEW_PRODUCT_GROUPED = 'product_grouped';
    public const DETAIL_VIEW_BRAND_GROUPED = 'brand_grouped';

    private $order_view_groups = [
        'none' => 'keine',
        'order_origin' => 'Bestellherkunft',
        'product_type' => 'Produktgruppe',
        'zahlungs_id' => 'Zahlungsart',
        'order_campaign' => 'Kampagne/Preissuchmaschine',
        'product_age_absolute' => 'Produkt-Alter (Bezug ist das aktuell Datum)',
        'product_age_absolute_1' => 'Produkt-Alter 1 Monat (Bezug ist das aktuell Datum)',
        'product_age_absolute_6' => 'Produkt-Alter 6 Monate (Bezug ist das aktuell Datum)',
        'product_age_relative' => 'Produkt-Alter (Bezug ist das Bestelldatum)',
        'product_age_relative_1' => 'Produkt-Alter 1 Monat (Bezug ist das Bestelldatum)',
        'product_age_relative_6' => 'Produkt-Alter 6 Monate (Bezug ist das Bestelldatum)',
        'vk_prices__50' => 'Auftragssumme (10€/20€/30€/40€/50€/75€/100€/Rest)',
        'vk_prices__100' => 'Auftragssumme (10€/20€/40€/60€/80€/100€/Rest)',
        'vk_prices__200' => 'Auftragssumme (25€/50€/100€/200€/500€/Rest)',
        'vk_prices__1000' => 'Auftragssumme (100€/250€/500€/750€/1000€/Rest)',
        'ek_prices__50' => 'Summe Einkaufspreis (brutto) (10€/20€/30€/40€/50€/75€/100€/Rest)',
        'ek_prices__100' => 'Summe Einkaufspreis (brutto) (10€/20€/40€/60€/80€/100€/Rest)',
        'ek_prices__200' => 'Summe Einkaufspreis (brutto) (25€/50€/100€/200€/500€/Rest)',
        'ek_prices__1000' => 'Summe Einkaufspreis (brutto) (100€/250€/500€/750€/1000€/Rest)',
        'ek_prices__k11' => 'Summe Einkaufspreis (brutto) (K11 Gruppierung nach ERSATZ4)',
        'ek_prices__k11_new' => 'Summe Einkaufspreis (brutto) (K11 Gruppierung neu)',
        'lieferbaranzeige' => 'Lieferbaranzeige',
        'country__delivery_address' => 'Lieferland',
        'country__invoice_address' => 'Rechnungsland',
        'cats__0' => 'Hauptkategorien',
    ];

    private $order_views = [
        'order_count' => 'Anzahl Bestellungen',
        'order_count_percentage' => 'Anzahl Bestellungen (Prozentual)',
        'vk_brutto' => 'Umsatz',
        'vk_burtto_percentage' => 'Umsatz (Prozentual)',
        'vk_brutto_avg' => '⌀ Umsatz',
        'ertrag_brutto' => 'Ertrag',
        'ertrag_revised_brutto' => 'Ertrag korrigiert',
        'ertrag_brutto_avg' => '⌀ Ertrag',
        'ertrag_revised_brutto_avg' => ' ⌀ Ertrag korrigiert',
        'ertrag_percent' => '% Ertrag',
        'ertrag_revised_percent' => '% Ertrag korrigiert',
        'item_count' => 'Anzahl Produkte (bestellte Einheiten)',
        'item_count_avg' => '⌀ Anzahl Produkte (bestellte Einheiten) pro Bestellung',
        'combined_count_price_margin_pro' => 'Kombiniert: Anzahl Bestellungen/Umsatz/Ertrag in % korrigiert',
        'combined_count_price_margin_abs' => 'Kombiniert: Anzahl Bestellungen/Umsatz/Ertrag in € korrigiert',
        'combined_detail' => 'Kombiniert: detailliert (nur ohne Gruppierung sinnvoll!)'
    ];

    private $period_groupes = [
        SqlDatePeriods::PERIOD_DAY => 'Tag',
        SqlDatePeriods::PERIOD_WEEK => 'Woche',
        SqlDatePeriods::PERIOD_MONTH => 'Monat'
    ];

    private $gros_groups = [
        'grossisten__gros' => [
            'key' => 'grossisten__gros',
            'label' => 'Lieferanten: Großhändler',
            'map' => [
                ['name' => 'EP (Gruppe)', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_EP, SuppliersConst::SUPPLIER_ID_EP_LAGER, SuppliersConst::SUPPLIER_ID_EP_FULFILLMENT]],
                ['name' => 'Uni (Gruppe)', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_UNI_FULFILL, SuppliersConst::SUPPLIER_ID_UNI_ESCHBORN, SuppliersConst::SUPPLIER_ID_UNI]],
                ['name' => 'Gautzsch (Gruppe)', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_GAUTZSCH_MUENSTER, SuppliersConst::SUPPLIER_ID_GAUTZSCH_PLEISSA_FULFILL, SuppliersConst::SUPPLIER_ID_GAUTZSCH_PLEISSA, SuppliersConst::SUPPLIER_ID_GAUTZSCH_PLEISSA_BAU_FULFILL]],
                ['name' => 'Gäfgen', 'supplier_id' => SuppliersConst::SUPPLIER_ID_GAEFGEN],
                ['name' => 'EGH', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_EGH, SuppliersConst::SUPPLIER_ID_EGH_EXTERN, SuppliersConst::SUPPLIER_ID_EGH_PFULLINGEN]],
                ['name' => 'EKBI', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_EK_SERVICEGROUP, SuppliersConst::SUPPLIER_ID_EK_SERVICE_GROUP_BLOCKLAGER, SuppliersConst::SUPPLIER_ID_EKBI_MANDATENLOGISTIK]],
                ['name' => 'Rexel', 'supplier_id' => SuppliersConst::SUPPLIER_ID_REXEL],
                [
                    'name' => 'Sonepar (Gruppe)',
                    'supplier_ids' => [SuppliersConst::SUPPLIER_ID_SONEPAR_NORD, SuppliersConst::SUPPLIER_ID_SONEPAR_SUED, SuppliersConst::SUPPLIER_ID_SONEPAR_SUED_LANGWEID, SuppliersConst::SUPPLIER_ID_DEG_GARBSEN, SuppliersConst::SUPPLIER_ID_SONEPAR_NORD_WERDER]
                ],
                ['name' => 'Brömmelhaupt', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_BROEMMELHAUPT]],
                ['name' => 'Zajadacz', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_ZAJADACZ]],
                ['name' => 'FEGA', 'supplier_id' => SuppliersConst::SUPPLIER_ID_FEGA],
                ['name' => 'Eberhard', 'supplier_id' => SuppliersConst::SUPPLIER_ID_EBERHARD]
            ]
        ],
        'grossisten__dealer' => [
            'key' => 'grossisten__dealer',
            'label' => 'Lieferanten: Dealer',
            'map' => [
                ['name' => 'Manolya', 'supplier_id' => SuppliersConst::SUPPLIER_ID_MANOLYA],
                ['name' => 'DGH', 'supplier_id' => SuppliersConst::SUPPLIER_ID_DGH],
                ['name' => 'Maxcom', 'supplier_id' => SuppliersConst::SUPPLIER_ID_MAXCOM],
                ['name' => 'Krempl WW', 'supplier_id' => SuppliersConst::SUPPLIER_ID_KREMPL_WW],
                ['name' => 'Mioga', 'supplier_id' => SuppliersConst::SUPPLIER_ID_MIOGA],
                ['name' => 'Sonnenschein', 'supplier_ids' => [SuppliersConst::SUPPLIER_ID_SONNENSCHEIN_LIEBHER, SuppliersConst::SUPPLIER_ID_SONNENSCHEIN]],
            ]
        ],
        'grossisten__top10' => [
            'key' => 'grossisten__top10',
            'label' => 'Lieferanten: Top 10 (letzte 60 Tage)',
            'map' => 'top10'
        ],
        'grossisten__top20' => [
            'key' => 'grossisten__top20',
            'label' => 'Lieferanten: Top 20 (letzte 60 Tage)',
            'map' => 'top20'
        ],
        'grossisten__top10_filter' => [
            'key' => 'grossisten__top10_filter',
            'label' => 'Lieferanten: Top 10',
            'map' => 'top10_filter'
        ],
        'grossisten__top20_filter' => [
            'key' => 'grossisten__top20_filter',
            'label' => 'Lieferanten: Top 20',
            'map' => 'top20_filter'
        ]
    ];


    private $brands_groups = [
        'brands__top10_filter' => [
            'key' => 'brands__top10_filter',
            'label' => 'Marken: Top 10',
            'map' => 'top10_filter'
        ],
        'brands__top20_filter' => [
            'key' => 'brands__top20_filter',
            'label' => 'Marken: Top 20',
            'map' => 'top20_filter'
        ],
        'brands__top50_filter' => [
            'key' => 'brands__top50_filter',
            'label' => 'Marken: Top 50',
            'map' => 'top50_filter'
        ]
    ];

    //filter
    public array $order_origin_ids = [];
    public array $product_types = [];
    public string $product_name = '';
    public array $product_ids = [];
    public string $product_tag = '';
    public array $order_tags = [];
    /**
     * @var int[]
     */
    private array $supplier_ids;
    private bool $order_tag_search_switch = false;
    private bool $current_time_of_day = false;

    private string $base_sql_order_filter;

    public function __construct(db_generic $db)
    {
        $this->db = $db;

        $this->refreshBaseSqlOrderFilter();

        if (env::isEcom()) {
            $this->order_view_groups = array_merge($this->order_view_groups, [
                'cats__' . ProductConst::CAT_ID_SPARPARTS => 'Kategorien - Ersatzteil',
                'cats__692' => 'Kategorien - Haushaltsgeräte',
                'cats__24' => 'Kategorien - kreative Küche',
                'cats__100' => 'Kategorien - Garten',
                'cats__693' => 'Kategorien - Heimwerken',
                'cats__540' => 'Kategorien - Elektroinstallation',
                'cats6__0' => 'Kategorien - Elektro24',
            ]);
        }
        if (env::isK11()) {
            $this->order_view_groups = array_merge($this->order_view_groups, [
                'cats__' . ProductConst::CAT_ID_K11_SHOP_ROOT => 'Kategorien - Shopware Wurzel',
                'cats__1804' => 'Kategorien - Shopware Küche',
                'cats__1805' => 'Kategorien - Shopware Haushalt',
                'cats6__0' => 'Kategorien - ASWO Hauptkategorien',
                'cats6__2907' => 'Kategorien - ASWO - Elektrische Bauteile',
                'cats6__3062' => 'Kategorien - ASWO - Elektromechanische Bauteile',
                'cats6__3547' => 'Kategorien - ASWO - mechanische Bauteile',
            ]);
        }

        foreach ($this->gros_groups as $gros_group) {
            $this->order_view_groups[$gros_group['key']] = $gros_group['label'];
        }

        foreach ($this->brands_groups as $brand_group) {
            $this->order_view_groups[$brand_group['key']] = $brand_group['label'];
        }
    }

    public function setShopId(int $shop_id): void
    {
        $this->shop_id = $shop_id;

        $this->refreshBaseSqlOrderFilter();
    }

    public function setCatTreeId(int $cat_tree_id): void
    {
        $this->cat_tree_id = $cat_tree_id;
    }

    public function refreshBaseSqlOrderFilter(): void
    {
        $this->base_sql_order_filter = "
            order_item.status < " . OrderConst::STATUS_STORNO . " AND
            orders.order_type IN (" . $this->db->in(order_repository::getOrderTypesForStats()) . ") AND
            orders.shop_id = '" . $this->shop_id . "'
        ";
    }


    public function setShowMargins(bool $status): void
    {
        $this->show_margins = $status;
    }

    public function setOrderViewGroup(string $order_view_group): void
    {
        $this->order_view_group = $order_view_group;
    }

    public function setPeriod(DateObj $date_from, DateObj $date_till): void
    {
        $this->date_from = $date_from;
        $this->date_till = $date_till;
    }

    public function setPeriodByPoint(string $period): void
    {
        if (!$period) {
            return;
        }

        $date_range = SqlDatePeriods::convertPeriodGroupValueToDateRange($this->period_group, $period);
        $this->setPeriod($date_range->getDateBegin(), $date_range->getDateEnd());
    }


    public function setOrderView(string $order_view): void
    {
        $this->order_view = $order_view;
    }

    /**
     * @param string $period_group
     */
    public function setPeriodGroup(string $period_group): void
    {
        $this->period_group = $period_group;
    }

    /**
     * @return string[]
     */
    public function getPeriodGroupes(): array
    {
        return $this->period_groupes;
    }

    /**
     * @return string[]
     */
    public function getOrderViewGroups(): array
    {
        $order_view_groups = $this->order_view_groups;

        if (!$this->show_margins) {
            unset(
                $order_view_groups['ertrag_brutto'],
                $order_view_groups['ertrag_revised_brutto'],
                $order_view_groups['ertrag_brutto_avg'],
                $order_view_groups['ertrag_revised_brutto_avg'],
                $order_view_groups['ertrag_percent'],
                $order_view_groups['ertrag_revised_percent']
            );
        }

        return $order_view_groups;
    }

    /**
     * @return string[]
     */
    public function getOrderViews(): array
    {
        return $this->order_views;
    }

    public function setFilterOrderOriginIds(array $order_origin_ids): void
    {
        $this->order_origin_ids = $order_origin_ids;
    }

    public function setFilterProductTypes(array $product_types): void
    {
        $this->product_types = $product_types;
    }

    public function setFilterProductName(string $product_name): void
    {
        $this->product_name = $product_name;
    }

    public function setFilterProductIds(array $product_ids): void
    {
        $this->product_ids = $product_ids;
    }

    public function setFilterProductTag(string $product_tag): void
    {
        $this->product_tag = $product_tag;
    }

    public function setFilterOrderTags(array $order_tags): void
    {
        $this->order_tags = $order_tags;
    }

    public function setFilterOrderTagSearchSwitch(bool $order_tag_search_switch): void
    {
        $this->order_tag_search_switch = $order_tag_search_switch;
    }

    public function setFilterCurrentTimeOfDay(bool $current_time_of_day): void
    {
        $this->current_time_of_day = $current_time_of_day;
    }

    /**
     * @param int[] $supplier_ids
     */
    public function setFilterSupplierIds(array $supplier_ids): void
    {
        $this->supplier_ids = $supplier_ids;
    }

    public function setFilterBrandId(int $brand_id): void
    {
        $this->brand_id = $brand_id;
    }

    private function getFilterAsSqlWhere(): string
    {
        $filter_where = '';

        if ($this->order_origin_ids) {
            $filter_where .= ' AND orders.order_origin_id IN (' . $this->db->makeIn($this->order_origin_ids) . ') ';
        }

        if ($this->supplier_ids) {
            $filter_where .= ' AND order_item.stats_supplier_id IN (' . $this->db->makeIn($this->supplier_ids) . ') ';
        }

        if ($this->product_types) {
            $filter_where .= ' AND product.product_type IN (' . $this->db->makeIn($this->product_types) . ') ';
        }

        if ($this->brand_id) {
            $filter_where .= ' AND product.brand_id = ' . $this->brand_id;
        }

        if ($this->product_name) {
            if (strpos($this->product_name, '|')) {
                $product_names = explode('|', $this->product_name);

                $t = '0';
                foreach ($product_names as $product_name) {
                    $t .= ' OR product.product_name LIKE "' . SqlUtils::searchString($product_name) . '"';
                }
                $filter_where .= ' AND (' . $t . ')';
            } else {
                $filter_where .= ' AND product.product_name LIKE "' . SqlUtils::searchString($this->product_name) . '"';
            }
        }

        if ($this->product_ids) {
            $filter_where .= " AND product.product_id IN (" . $this->db->in($this->product_ids) . ")";
        }

        if ($this->product_tag) {
            $filter_where .= " AND FIND_IN_SET('" . $this->db->escape($this->product_tag) . "', product.product_tags)";
        }

        if ($this->order_tags) {
            $filter_where .= " AND ";
            $filter_where .= FormElementOrderTagSearch::buildSql($this->db, $this->order_tags, $this->order_tag_search_switch);
        }

        if ($this->current_time_of_day) {
            $filter_where .= " AND DATE_FORMAT(order_item.added, '%H%i') <= DATE_FORMAT(NOW(), '%H%i')";
        }

        return $filter_where;
    }

    private function pivotAddTotal(): string
    {
        $select = "SUM(order_item.quantity) AS total_item_count,\n";
        $select .= "COUNT(DISTINCT orders.order_id) AS total_order_count,\n";
        $select .= "SUM(order_item.preis*order_item.quantity) AS total_vk_brutto,\n";
        $select .= "SUM(order_item.ekpreis*order_item.quantity) AS total_ek_brutto,\n";
        $select .= "SUM(order_item.ek_revised_brutto*order_item.quantity) AS total_ek_revised_brutto,\n";

        return $select;
    }

    private function pivotAddGroup(string $sql_condition, string $field): string
    {
        $select = "SUM(IF(" . $sql_condition . ", order_item.quantity, 0)) AS {$field}_item_count,\n";
        $select .= "COUNT(DISTINCT IF(" . $sql_condition . ", orders.order_id, NULL)) AS {$field}_order_count,\n";
        $select .= "SUM(IF(" . $sql_condition . ", order_item.preis*order_item.quantity, 0)) AS {$field}_vk_brutto,\n";
        $select .= "SUM(IF(" . $sql_condition . ", order_item.ekpreis*order_item.quantity, 0)) AS {$field}_ek_brutto,\n";
        $select .= "SUM(IF(" . $sql_condition . ", order_item.ek_revised_brutto*order_item.quantity, 0)) AS {$field}_ek_revised_brutto,\n";

        return $select;
    }

    /**
     * @return array{result: db_result_set, groups: array }
     * @throws FatalException
     */
    public function getTimelineData(): array
    {
        $period_sql = " BETWEEN '" . $this->date_from->db('begin') . "' AND '" . $this->date_till->db('end') . "'";

        $period_group_sql = SqlDatePeriods::getSqlDateFormat($this->period_group);

        $groups = [];

        $filter_where = $this->getFilterAsSqlWhere();

        switch ($this->getOrderViewGroupPrefix($this->order_view_group)) {
            case 'none':
                $groups = ['total' => 'Total'];

                $select = $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')
				";

                break;
            case 'order_origin':
                $select = '';

                $order_origins = order_repository::getOrderOriginNamesForStats();
                foreach ($order_origins as $field => $name) {
                    $groups[$field] = $name;
                    $sql_condition = "orders.order_origin_id = '$field'";
                    $select .= $this->pivotAddGroup($sql_condition, $field);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";
                break;
            case 'product_type':
                $select = '';

                $product_types = service_loader::get(ProductTypeRepository::class)->getProductTypeNames();

                if ($this->product_types) { //wenn der filter nach product typen drin ist, dann nur diese gruppen darstellen.
                    $product_types_org = $product_types;
                    $product_types = [];

                    foreach ($this->product_types as $product_type) {
                        $product_types[$product_type] = $product_types_org[$product_type];
                    }
                }

                foreach ($product_types as $field => $name) {
                    $groups[$field] = $name;

                    $sql_condition = "product.product_type = '$field'";
                    $select .= $this->pivotAddGroup($sql_condition, $field);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";

                break;
            case 'zahlungs_id':
                $select = '';

                //$zahlungsarten = payment_repository::getAllZahlungsarten();

                $zahlungsarten = $this->db->query("
					SELECT
						einst_zahlungsarten.zahlungs_id,
						einst_zahlungsarten.beschreibung
					FROM
						einst_zahlungsarten
					WHERE
						einst_zahlungsarten.show_in_stats = 1 	
				")->asSingleArray('zahlungs_id');

                foreach ($zahlungsarten as $field => $name) {
                    $field_key = 'z' . $field;

                    $groups[$field_key] = $name;

                    $sql_condition = "orders.zahlungs_id = '$field'";
                    $select .= $this->pivotAddGroup($sql_condition, $field_key);
                }

                //alle andere zahlungsarten
                $groups['other'] = 'Sonstige';
                $sql_condition = "orders.zahlungs_id NOT IN (" . $this->db->makeIn(array_keys($zahlungsarten)) . ")";
                $select .= $this->pivotAddGroup($sql_condition, 'other');

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";

                break;
            case 'order_campaign':
                $select = '';

                $order_campaigns = $this->getOrderCampaigns();

                foreach ($order_campaigns as $field => $name) {
                    $groups[$field] = $name;

                    //@todo den part im idealfall gleich mit
                    if ($field === 'other') {
                        $sql_condition = "orders.order_campaign_id NOT IN (" . $this->db->makeIn(array_keys($order_campaigns)) . ", '')";
                    } elseif ($field === 'without') {
                        $sql_condition = "(orders.order_campaign_id IS NULL OR orders.order_campaign_id = '')";
                    } else {
                        $sql_condition = "orders.order_campaign_id = '$field'";
                    }

                    $select .= $this->pivotAddGroup($sql_condition, $field);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";
                break;
            case 'cats':
                $select = '';

                $cat_groups = $this->getCats($this->cat_tree_id, $this->order_view_group);

                foreach ($cat_groups as $key => $cat_group) {
                    $groups[$key] = $cat_group['name'];
                    $select .= $this->pivotAddGroup($cat_group['sql_condition'], $key);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id) INNER JOIN
						product_cat ON (product.cat_id = product_cat.cat_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";
                break;
            case 'cats6':
                $select = '';

                $cat_groups = $this->getCats(ProductConst::CAT_TREE_ID_ELEKTRO24, $this->order_view_group);

                foreach ($cat_groups as $key => $cat_group) {
                    $groups[$key] = $cat_group['name'];
                    $select .= $this->pivotAddGroup($cat_group['sql_condition'], $key);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id) INNER JOIN
						product_cat ON (product.cat_id_6 = product_cat.cat_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";
                break;
            case 'product_age_absolute':
            case 'product_age_absolute_1':
            case 'product_age_absolute_6':
            case 'product_age_relative':
            case 'product_age_relative_1':
            case 'product_age_relative_6':
                $select = '';


                $age_groups = $this->getProductAgeGroupsByOrderViewGroup($this->order_view_group);

                foreach ($age_groups as $field => $price_group) {
                    $groups[$field] = $price_group['text'];

                    $condition = strpos($this->order_view_group, 'product_age_absolute') !== false ? $price_group['sql_absolute'] : $price_group['sql_relative'];
                    $sql_condition = "product.date_create " . $condition;
                    $select .= $this->pivotAddGroup($sql_condition, $field);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";

                break;
            case 'vk_prices':
            case 'ek_prices':
                $select = '';

                $price_groups = $this->getProductPriceGroupsByOrderViewGroup($this->order_view_group);

                foreach ($price_groups as $field => $price_group) {
                    $groups[$field] = $price_group['text'];

                    $condition = $price_group['sql'];

                    $sql_condition = "order_sums.preis " . $condition;
                    $select .= $this->pivotAddGroup($sql_condition, $field);
                }

                $select .= $this->pivotAddTotal();

                $sql_price_field = 'order_item.preis';
                if ($this->getOrderViewGroupPrefix($this->order_view_group) === 'ek_prices') {
                    $sql_price_field = 'order_item.ek_nnn_brutto';
                }

                $stats_sql = "
                    SELECT
                        DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
                        $select
                        1
                    FROM
                        order_item INNER JOIN
                        orders ON (order_item.order_id = orders.order_id) INNER JOIN
                        product ON (order_item.product_id = product.product_id) INNER JOIN
                        (
                            SELECT
                                orders.order_id,
                                SUM($sql_price_field * order_item.quantity) AS preis
                            FROM
                                orders INNER JOIN
                                order_item ON (orders.order_id = order_item.order_id)
                            WHERE
                                order_item.added $period_sql AND
                                " . $this->base_sql_order_filter . "
                            GROUP BY
                                orders.order_id
                        ) AS order_sums ON (orders.order_id = order_sums.order_id)
                    WHERE
                        order_item.added $period_sql AND
                        " . $this->base_sql_order_filter . "
                        $filter_where
                    GROUP BY
                        DATE_FORMAT(order_item.added, '$period_group_sql')
				";

                break;
            case 'grossisten':
                $select = '';

                $map = $this->getGrosGroupMap($this->order_view_group);

                foreach ($map as $field => $gros_entry) {
                    $groups[$field] = $gros_entry['name'];
                    $select .= $this->pivotAddGroup($gros_entry['sql_condition'], $field);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id) INNER JOIN
						(
							SELECT
								orders.order_id,
								SUM(order_item.preis * order_item.quantity) AS preis
							FROM
								orders INNER JOIN
								order_item ON (orders.order_id = order_item.order_id)
							WHERE
								order_item.added $period_sql AND
								" . $this->base_sql_order_filter . "
							GROUP BY
								orders.order_id
						) AS order_sums ON (orders.order_id = order_sums.order_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')
				";

                break;
            case 'brands':
                $select = '';

                $map = $this->getBrandsGroupMap($this->order_view_group);

                foreach ($map as $field => $gros_entry) {
                    $groups[$field] = $gros_entry['name'];
                    $select .= $this->pivotAddGroup($gros_entry['sql_condition'], $field);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id) INNER JOIN
						(
							SELECT
								orders.order_id,
								SUM(order_item.preis * order_item.quantity) AS preis
							FROM
								orders INNER JOIN
								order_item ON (orders.order_id = order_item.order_id)
							WHERE
								order_item.added $period_sql AND
								" . $this->base_sql_order_filter . "
							GROUP BY
								orders.order_id
						) AS order_sums ON (orders.order_id = order_sums.order_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')
				";

                break;
            case 'lieferbaranzeige':
                $select = '';

                $lieferbaranzeigen = ProductRepositoryLegacy::getLieferbaranzeigeNames(Shop::ALLEGO);
                foreach ($lieferbaranzeigen as $lieferbar_id => $lieferbar_text) {
                    $field = 'lieferbaranzeige_' . $lieferbar_id;

                    $groups[$field] = $lieferbar_text;
                    $sql_condition = "order_item.lieferbaranzeige = '$lieferbar_id'";
                    $select .= $this->pivotAddGroup($sql_condition, $field);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
					SELECT
						DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
						$select
						1
					FROM
						order_item INNER JOIN
						orders ON (order_item.order_id = orders.order_id) INNER JOIN
						product ON (order_item.product_id = product.product_id)
					WHERE
						order_item.added $period_sql AND
						" . $this->base_sql_order_filter . "
						$filter_where
					GROUP BY
						DATE_FORMAT(order_item.added, '$period_group_sql')	
				";
                break;
            case 'country':
                $select = '';

                $country_groups = $this->getCountries($this->order_view_group);

                foreach ($country_groups as $key => $cat_group) {
                    $groups[$key] = $cat_group['name'];
                    $select .= $this->pivotAddGroup($cat_group['sql_condition'], $key);
                }

                $select .= $this->pivotAddTotal();

                $stats_sql = "
                    SELECT
                        DATE_FORMAT(order_item.added, '$period_group_sql') AS datum,
                        $select
                        1
                    FROM
                        order_item INNER JOIN
                        orders ON (order_item.order_id = orders.order_id) INNER JOIN
                        order_addresses ON (orders.order_id = order_addresses.order_id AND order_addresses.address_type IN (" . $this->db->in($this->getCountryAddressTypesByOrderViewGroup($this->order_view_group)) . ")) INNER JOIN
						product ON (order_item.product_id = product.product_id)
                    WHERE
                        order_item.added $period_sql AND
                        " . $this->base_sql_order_filter . "
                        $filter_where
                    GROUP BY
                        DATE_FORMAT(order_item.added, '$period_group_sql')
                ";

                break;
            default:
                throw new DevException('unknown order_view_group');
        }

        //*nerv* mysql hat hier irgendwie ein problem mit der selektivität. über die orders joggeln ist billiger als index scan über order_item.added?!
        //->alternativ wäre order.added möglich, wäre aber funktional etwas anders...
        $stats_sql = str_replace('order_item INNER JOIN', 'order_item STRAIGHT_JOIN', $stats_sql);

        $sql = "
            SELECT
                stats.*,
                DATE_FORMAT(system_date_helper.datum, '$period_group_sql') AS datum,
                annotation.annotation_count,
                annotation.annotation_text
            FROM
                system_date_helper LEFT JOIN
                (
                    $stats_sql
                ) AS stats ON (stats.datum = DATE_FORMAT(system_date_helper.datum, '$period_group_sql')) LEFT JOIN
                (
                    SELECT
                        DATE_FORMAT(statistik_annotation.annotation_date, '$period_group_sql') AS datum,
                        COUNT(*) AS annotation_count,
                        GROUP_CONCAT(CONCAT(statistik_annotation.annotation_date,': ', statistik_annotation.annotation) SEPARATOR '\n') AS annotation_text
                    FROM
                        statistik_annotation INNER JOIN
                        user_accounts ON (statistik_annotation.user_id = user_accounts.user_id)
                    WHERE
                        statistik_annotation.annotation_date $period_sql AND
                        user_accounts.shop_id = " . $this->shop_id . "
                    GROUP BY
                        DATE_FORMAT(statistik_annotation.annotation_date, '$period_group_sql')
                ) AS annotation ON (stats.datum = annotation.datum)
            WHERE
                system_date_helper.datum $period_sql
            GROUP BY
                DATE_FORMAT(system_date_helper.datum, '$period_group_sql')
            ORDER BY
                DATE_FORMAT(system_date_helper.datum, '$period_group_sql')
		";

        $this->db->query('SET SESSION group_concat_max_len = 10000'); //die Annotations können in summe lang werden

        return ['result' => $this->db->query($sql), 'groups' => $groups];
    }

    public function getTimelineTable(persistent_container $persistent_container): TableObject
    {
        $timeline_data = $this->getTimelineData();

        $result = $timeline_data['result'];
        $groups = $timeline_data['groups'];

        $datasource = new TableObjectDataSourceArray($result->asArray());

        $table = new TableObjectPersistent($datasource, $persistent_container);
        $table->setCaption('Verkäufe (Brutto)');

        $table->setExportEnabled($this->show_margins);

        if ($this->period_group === 'day') {
            $table->setRowFormater(function ($daten) {
                $date = new DateObj($daten['datum']);

                if ($date->isSaturday() || $date->isSunday()) {
                    return ['class' => 'light_blue'];
                }

                if ($date->isPublicHoliday()) {
                    return ['class' => 'red'];
                }

                return null;
            });
        }

        if ($this->period_group === SqlDatePeriods::PERIOD_DAY) {
            $field = new table_object_field_date('datum', 'Datum');
            $table->addField($field);
        } else {
            $field = new table_object_field('datum', 'Zeitraum');
            $table->addField($field);
        }

        $field = new table_object_field_callback('annotations', '');
        $field->setAlign($field::ALIGN_CENTER);
        $field->setCallback(function ($daten) {
            if (!$daten['annotation_count']) {
                return '';
            }

            return '<i class="fa fa-comment" title="' . htmlentities($daten['annotation_text']) . '"></i>';
        });

        $table->addField($field);

        foreach ($groups as $group_id => $group_name) {
            switch ($this->order_view) {
                case 'combined_count_price_margin_pro':
                    $field = new table_object_field_multi($group_id . 'multi', $group_name);
                    $field->rollup_hierarchical_keys = false;

                    $sub_field = $this->createTableField($group_id, 'Anzahl', 'order_count', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'VK (Brutto)', 'vk_brutto', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'Ertrag', 'ertrag_revised_percent', $table);
                    $sub_field->setClass('green');
                    $field->addField($sub_field);

                    $table->addField($field);
                    break;
                case 'combined_count_price_margin_abs':
                    $field = new table_object_field_multi($group_id . 'multi', $group_name);
                    $field->rollup_hierarchical_keys = false;

                    $sub_field = $this->createTableField($group_id, 'Anzahl', 'order_count', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'VK (Brutto)', 'vk_brutto', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'Ertrag', 'ertrag_revised_brutto', $table);
                    $sub_field->setClass('green');
                    $field->addField($sub_field);

                    $table->addField($field);
                    break;
                case 'combined_detail':
                    $field = new table_object_field_multi($group_id . 'multi', $group_name);
                    $field->rollup_hierarchical_keys = false;

                    $sub_field = $this->createTableField($group_id, 'Bestellungen', 'order_count', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'Einheiten', 'item_count', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'Einheiten pro Bestellung', 'item_count_avg', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'Umsatz', 'vk_brutto', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, '⌀ Umsatz', 'vk_brutto_avg', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, 'Ertrag korrigiert', 'ertrag_revised_brutto', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, '⌀ Ertrag korrigiert', 'ertrag_revised_brutto_avg', $table);
                    $field->addField($sub_field);

                    $sub_field = $this->createTableField($group_id, '% Ertrag korrigiert', 'ertrag_revised_percent', $table);
                    $field->addField($sub_field);

                    $table->addField($field);
                    break;
                default:
                    $field = $this->createTableField($group_id, $group_name, $this->order_view, $table);
                    $table->addField($field);
            }
        }

        if (!isset($groups['total'])) {
            $field = $this->createTableField('total', 'Bestellungen', 'order_count', $table);
            $table->addField($field);

            $field = $this->createTableField('total', 'Total', 'vk_brutto', $table);
            if ($this->period_group === SqlDatePeriods::PERIOD_DAY) {
                $field->setTemplate('<b>__value__</b>&nbsp; <a href="/av/statistiken/statistik_action.php?von={{$datum}}&bis={{$datum}}&sta_art=verkaufe_pro_kat&zeitraum=added"
                                            target="zpop" onclick="openpop(1200,800,\'yes\')"><img src="/res/images/icons/chart_pie.png" /></a>');
            } else {
                $field->setTemplate('<b>__value__</b>');
            }
            $table->addField($field);
        }

        return $table;
    }


    private function createTableField(string $group_id, string $group_name, string $order_view, TableObject $table): table_object_field
    {

        $csv_modus = $table->isExportOngoing();

        $field = new table_object_field_callback($group_id . "_" . $order_view, $group_name);
        $field->setAlign($field::ALIGN_RIGHT);
        $field->setCallback(function ($daten) use ($group_id, $order_view, $csv_modus) {
            return $this->tableFieldgroupRenderer($group_id, $order_view, $daten, $csv_modus);
        });

        $field->setRollup(function ($result) use ($group_id, $order_view) {
            $order_count = 0;
            $total_order_count = 0;
            $item_count = 0;
            $total_item_count = 0;
            $vk_brutto = 0;
            $total_vk_brutto = 0;
            $ek_brutto = 0;
            $ek_revised_brutto = 0;

            foreach ($result as $daten) {
                $order_count += $daten[$group_id . '_order_count'];
                $total_order_count += $daten['total_order_count'];
                $item_count += $daten[$group_id . '_item_count'];
                $total_item_count += $daten['total_item_count'];
                $vk_brutto += $daten[$group_id . '_vk_brutto'];
                $total_vk_brutto += $daten['total_vk_brutto'];
                $ek_brutto += $daten[$group_id . '_ek_brutto'];
                $ek_revised_brutto += $daten[$group_id . '_ek_revised_brutto'];
            }

            if ($order_count == 0) {
                return '';
            }
            if ($ek_revised_brutto == 0) { //kann null sein, wenn die spalte nur leistungen enthält
                $ek_revised_brutto = 0.01;
            }
            $ertrag_brutto = $vk_brutto - $ek_brutto;
            $ertrag_revised_brutto = $vk_brutto - $ek_revised_brutto;
            $vk_brutto_avg = output::formatPrice($vk_brutto / $order_count);
            $ertrag_brutto_avg = output::formatPrice($ertrag_brutto / $order_count);
            $ertrag_revised_brutto_avg = output::formatPrice($ertrag_revised_brutto / $order_count);
            $ertrag_percent = round(($vk_brutto / $ek_brutto - 1) * 100, 1) . '%';
            $ertrag_revised_percent = round(($vk_brutto / $ek_revised_brutto - 1) * 100, 1) . '%';

            $ertrag_brutto = output::formatPrice($ertrag_brutto);
            $ertrag_revised_brutto = output::formatPrice($ertrag_revised_brutto);
            $vk_brutto_formated = output::formatPrice($vk_brutto);

            switch ($order_view) {
                case 'order_count':
                    $sum = $order_count;
                    break;
                case 'order_count_percentage':
                    $sum = round($order_count / $total_order_count * 100, 1) . '%';
                    break;
                case 'vk_brutto':
                    $sum = $vk_brutto_formated;
                    break;
                case 'vk_burtto_percentage':
                    $sum = round($vk_brutto / $total_vk_brutto * 100, 1) . '%';
                    break;
                case 'ertrag_brutto':
                    $sum = $ertrag_brutto;
                    break;
                case 'ertrag_revised_brutto':
                    $sum = $ertrag_revised_brutto;
                    break;
                case 'vk_brutto_avg':
                    $sum = $vk_brutto_avg;
                    break;
                case 'ertrag_brutto_avg':
                    $sum = $ertrag_brutto_avg;
                    break;
                case 'ertrag_revised_brutto_avg':
                    $sum = $ertrag_revised_brutto_avg;
                    break;
                case 'ertrag_percent':
                    $sum = $ertrag_percent;
                    break;
                case 'ertrag_revised_percent':
                    $sum = $ertrag_revised_percent;
                    break;
                case 'item_count':
                    $sum = $item_count;
                    break;
                case 'item_count_avg':
                    $sum = round($item_count / $order_count, 1);
                    break;
                default:
                    throw new DevException('unknown $order_view (' . $order_view . ')');
            }

            $title = "Bestellungen: " . $order_count . "\n";
            $title .= "Umsatz: " . $vk_brutto_formated . " ⌀ " . $vk_brutto_avg . "\n";

            if ($this->show_margins) {
                $title .= "Ertrag: " . $ertrag_brutto . " ⌀ " . $ertrag_brutto_avg . " " . $ertrag_percent . "\n";
                $title .= "Ertrag korrigiert: " . $ertrag_revised_brutto . " ⌀ " . $ertrag_revised_brutto_avg . " " . $ertrag_revised_percent . "\n";
            }

            return '<a href="/ax/statistiken/orders/detail/?order_view_group=' . $group_id . '" style="color: black; white-space: nowrap;" onclick="openpop(1200,800,\'yes\')" target="zpop" title="' . $title . '">' . $sum . '</a>';
        });

        return $field;
    }

    private function tableFieldgroupRenderer(string $group_id, string $order_view, array $row, bool $csv_modus): string
    {
        switch ($order_view) {
            case 'order_count':
                $value = $row[$group_id . '_order_count'];
                if (!$value) {
                    $value = '';
                }
                break;
            case 'order_count_percentage':
                if (!$row[$group_id . '_order_count']) {
                    $value = '';
                } else {
                    $value = round($row[$group_id . '_order_count'] / $row['total_order_count'] * 100, 1) . '%';
                }
                break;
            case 'vk_brutto':
                $value = $row[$group_id . '_vk_brutto'];
                if ($value) {
                    $value = output::formatPrice($value);
                } else {
                    $value = '';
                }

                break;
            case 'vk_burtto_percentage':
                if (!$row[$group_id . '_vk_brutto']) {
                    $value = '';
                } else {
                    $value = round($row[$group_id . '_vk_brutto'] / $row['total_vk_brutto'] * 100, 1) . '%';
                }

                break;
            case 'ertrag_brutto':
                $value = $row[$group_id . '_vk_brutto'] - $row[$group_id . '_ek_brutto'];
                if ($value) {
                    $value = output::formatPrice($value);
                } else {
                    $value = '';
                }

                break;
            case 'ertrag_revised_brutto':
                $value = $row[$group_id . '_vk_brutto'] - $row[$group_id . '_ek_revised_brutto'];
                if ($value) {
                    $value = output::formatPrice($value);
                } else {
                    $value = '';
                }
                break;
            case 'vk_brutto_avg':
                if ($row[$group_id . '_order_count']) {
                    $value = output::formatPrice($row[$group_id . '_vk_brutto'] / $row[$group_id . '_order_count']);
                } else {
                    $value = '';
                }
                break;
            case 'ertrag_brutto_avg':
                $value = $row[$group_id . '_vk_brutto'] - $row[$group_id . '_ek_brutto'];
                if ($row[$group_id . '_order_count']) {
                    $value = output::formatPrice($value / $row[$group_id . '_order_count']);
                } else {
                    $value = '';
                }
                break;
            case 'ertrag_revised_brutto_avg':
                $value = $row[$group_id . '_vk_brutto'] - $row[$group_id . '_ek_revised_brutto'];
                if ($row[$group_id . '_order_count']) {
                    $value = output::formatPrice($value / $row[$group_id . '_order_count']);
                } else {
                    $value = '';
                }
                break;
            case 'ertrag_percent':
                if ($row[$group_id . '_order_count']) {
                    $vk = $row[$group_id . '_vk_brutto'];
                    $ek = $row[$group_id . '_ek_brutto'];
                    $value = round(($vk / $ek - 1) * 100, 1) . '%';
                } else {
                    $value = '';
                }

                break;
            case 'ertrag_revised_percent':
                if ($row[$group_id . '_order_count']) {
                    $vk = $row[$group_id . '_vk_brutto'];
                    $ek = $row[$group_id . '_ek_revised_brutto'];
                    if ($ek == 0) {
                        $value = '';
                    } else {
                        $value = round(($vk / $ek - 1) * 100, 1) . '%';
                    }
                } else {
                    $value = '';
                }

                break;
            case 'item_count':
                $value = $row[$group_id . '_item_count'];
                if (!$value) {
                    $value = '';
                }
                break;
            case 'item_count_avg':
                $value = '';
                if ($row[$group_id . '_order_count']) {
                    $value = round($row[$group_id . '_item_count'] / $row[$group_id . '_order_count'], 1);
                }
                break;
            default:
                throw new DevException('unknown $order_view');
        }

        if ($csv_modus) {
            return $value;
        }

        $title = '';
        if ($row[$group_id . '_order_count']) {
            $count = $row[$group_id . '_order_count'];
            $title .= "Bestellungen: " . $count . "\n";

            $umsatz = $row[$group_id . '_vk_brutto'];
            $title .= "Umsatz: " . output::formatPrice($umsatz) . " ⌀ " . output::formatPrice($umsatz / $count) . "\n";

            if ($this->show_margins) {
                $ertrag = $row[$group_id . '_vk_brutto'] - $row[$group_id . '_ek_brutto'];
                if ($row[$group_id . '_ek_brutto']) {
                    $ertrag_percent = round(($row[$group_id . '_vk_brutto'] / $row[$group_id . '_ek_brutto'] - 1) * 100, 1) . '%';
                } else {
                    $ertrag_percent = '';
                }
                $title .= "Ertrag: " . output::formatPrice($ertrag) . " ⌀ " . output::formatPrice($ertrag / $count) . " " . $ertrag_percent . "\n";

                $ertrag = $row[$group_id . '_vk_brutto'] - $row[$group_id . '_ek_revised_brutto'];
                if ($row[$group_id . '_ek_revised_brutto']) {
                    $ertrag_percent = round(($row[$group_id . '_vk_brutto'] / $row[$group_id . '_ek_revised_brutto'] - 1) * 100, 1) . '%';
                } else {
                    $ertrag_percent = '';
                }

                $title .= "Ertrag korrigiert: " . output::formatPrice($ertrag) . " ⌀ " . output::formatPrice($ertrag / $count) . " " . $ertrag_percent;
            }
        }

        return '<a href="/ax/statistiken/orders/detail/?period=' . $row['datum'] . '&order_view_group=' . $group_id . '" onclick="openpop(1200,800,\'yes\')" target="zpop" title="' . $title . '" style="color: black; white-space: nowrap;">' . $value . '</a>';
    }


    public function getDetailTable(string $act_order_view_group, string $detail_view, ?persistent_container $persistent_container = null): TableObject
    {
        $period_sql = " BETWEEN '" . $this->date_from->db('begin') . "' AND '" . $this->date_till->db('end') . "'";

        $cat_id_field = 'product.cat_id';

        $filter_where = $this->getFilterAsSqlWhere();
        $join = '';

        //table objekt lädt bei einem full rollup alles in den Speicher -> bei größeren Zeitraumen verhindern
        $is_long_period = $this->date_till->diff($this->date_from)->days > 90;

        if ($act_order_view_group !== 'total') {
            switch ($this->getOrderViewGroupPrefix($this->order_view_group)) {
                case 'order_origin':
                    $filter_where .= " AND orders.order_origin_id = '" . $this->db->escape($act_order_view_group) . "' ";
                    break;
                case 'product_type':
                    $filter_where .= " AND product.product_type = '" . $this->db->escape($act_order_view_group) . "' ";
                    break;
                case 'order_campaign':
                    if ($act_order_view_group === 'without') {
                        $filter_where .= " AND (orders.order_campaign_id IS NULL OR orders.order_campaign_id = '')";
                    } elseif ($act_order_view_group === 'other') {
                        $filter_where .= " AND orders.order_campaign_id NOT IN (" . $this->db->in(array_keys($this->getOrderCampaigns())) . ", '') ";
                    } else {
                        $filter_where .= " AND orders.order_campaign_id = '" . $this->db->escape($act_order_view_group) . "' ";
                    }
                    break;
                case 'zahlungs_id':
                    $zahlungs_id = (int)trim($act_order_view_group, 'z');

                    $filter_where .= " AND orders.zahlungs_id = '" . $zahlungs_id . "' ";
                    break;
                case 'product_age_relative':
                case 'product_age_relative_1':
                case 'product_age_relative_6':
                    $age_groups = $this->getProductAgeGroupsByOrderViewGroup($act_order_view_group);
                    if (!isset($age_groups[$act_order_view_group])) {
                        throw new FatalException('unknown age group');
                    }

                    $condition = $age_groups[$act_order_view_group]['sql_relative'];

                    $filter_where .= " AND product.date_create " . $condition;
                    break;
                case 'product_age_absolute':
                case 'product_age_absolute_1':
                case 'product_age_absolute_6':
                    $age_groups = $this->getProductAgeGroupsByOrderViewGroup($act_order_view_group);
                    if (!isset($age_groups[$act_order_view_group])) {
                        throw new FatalException('unknown age group');
                    }

                    $condition = $age_groups[$act_order_view_group]['sql_absolute'];

                    $filter_where .= " AND product.date_create " . $condition;
                    break;
                case 'vk_prices':
                case 'ek_prices':
                    $price_groups = $this->getProductPriceGroupsByOrderViewGroup($this->order_view_group);
                    if (!isset($price_groups[$act_order_view_group])) {
                        throw new FatalException('unknown age group');
                    }

                    $condition = $price_groups[$act_order_view_group]['sql'];

                    $filter_where .= " AND order_sums.preis " . $condition;

                    $sql_price_field = 'order_item.preis';
                    if ($this->getOrderViewGroupPrefix($this->order_view_group) === 'ek_prices') {
                        $sql_price_field = 'order_item.ek_nnn_brutto';
                    }

                    $join = "
						INNER JOIN (
							SELECT
								orders.order_id,
								SUM(" . $sql_price_field . " * order_item.quantity) AS preis
							FROM
								orders INNER JOIN
								order_item ON (orders.order_id = order_item.order_id)
							WHERE
								order_item.added $period_sql AND
								" . $this->base_sql_order_filter . "
							GROUP BY
								orders.order_id
						) AS order_sums ON (orders.order_id = order_sums.order_id)
					";
                    break;
                case 'grossisten':
                    $entry = $this->getGrosGroupMap($this->order_view_group)[$act_order_view_group];
                    $filter_where .= " AND " . $entry['sql_condition'];
                    break;
                case 'brands':
                    $entry = $this->getBrandsGroupMap($this->order_view_group)[$act_order_view_group];
                    $filter_where .= " AND " . $entry['sql_condition'];
                    break;
                case 'cats':
                    $entry = $this->getCats($this->cat_tree_id, $this->order_view_group)[$act_order_view_group];
                    $filter_where .= " AND " . $entry['sql_condition'];
                    break;
                case 'cats6':
                    $entry = $this->getCats(ProductConst::CAT_TREE_ID_ELEKTRO24, $this->order_view_group)[$act_order_view_group];
                    $filter_where .= " AND " . $entry['sql_condition'];
                    $cat_id_field = 'product.cat_id_6';
                    break;
                case 'lieferbaranzeige':
                    $lieferbaranzeige = (int)str_replace('lieferbaranzeige_', '', $act_order_view_group);
                    $filter_where .= " AND order_item.lieferbaranzeige = " . $lieferbaranzeige;
                    break;
                case 'country':
                    $country_id = (int)str_replace('country_id_', '', $act_order_view_group);
                    $filter_where .= " AND order_addresses.country_id = " . $country_id . " AND order_addresses.address_type IN (" . $this->db->in($this->getCountryAddressTypesByOrderViewGroup($this->order_view_group)) . ")";
                    $join = " INNER JOIN order_addresses ON (orders.order_id = order_addresses.order_id) ";
                    break;
                default:
                    throw new DevException('unknwown group type');
            }
        }

        switch ($detail_view) {
            case self::DETAIL_VIEW_PRODUCT_GROUPED:
                $sql = "
                    SELECT
                            product.product_id,
                            product.product_type,
                            product_cat.cat_name,
                            product.product_name,
                            MAKRO.product.product_tags,
                            MAKRO.order_item.group_sum_quantity,
                            MAKRO.order_item.group_sum_ek_revised_sum,
                            MAKRO.order_item.group_sum_preis_sum,
                            MAKRO.order_item.group_sum_ertrag_revised_sum
                    FROM
                        order_item INNER JOIN
                        orders ON (orders.order_id = order_item.order_id) INNER JOIN
                        customers ON (orders.customer_id = customers.customer_id) LEFT JOIN
                        product ON (product.product_id = order_item.product_id) LEFT JOIN
                        product_cat ON ($cat_id_field = product_cat.cat_id)
                        $join
                    WHERE
                        order_item.added $period_sql AND
                        " . $this->base_sql_order_filter . "
                        $filter_where
                    GROUP BY
                        product.product_id
				";
                break;
            case self::DETAIL_VIEW_BRAND_GROUPED:
                $sql = "
                    SELECT
                            product_brand.brand_name,
                            MAKRO.order_item.group_sum_quantity,
                            MAKRO.order_item.group_sum_order_count,
                            MAKRO.order_item.group_sum_ek_revised_sum,
                            MAKRO.order_item.group_sum_preis_sum,
                            MAKRO.order_item.group_sum_ertrag_revised_sum
                    FROM
                        order_item INNER JOIN
                        orders ON (orders.order_id = order_item.order_id) INNER JOIN
                        customers ON (orders.customer_id = customers.customer_id) LEFT JOIN
                        product ON (product.product_id = order_item.product_id) LEFT JOIN
                        product_cat ON ($cat_id_field = product_cat.cat_id) LEFT JOIN
                        product_brand ON (product.brand_id = product_brand.brand_id)
                        $join
                    WHERE
                        order_item.added $period_sql AND
                        " . $this->base_sql_order_filter . "
                        $filter_where
                    GROUP BY
                        product_brand.brand_id
                    ORDER BY
                        SUM(order_item.quantity) DESC
                ";

                break;
            case self::DETAIL_VIEW_FULL:
            case self::DETAIL_VIEW_ONLY_PRODUCT:
                $sql = "SELECT
                    orders.order_id,
					orders.auftnr,
					orders.order_tags,
					customers.customer_id,
					MAKRO.customers.customer_name_or_firma,
					product_cat.cat_name,
					order_item.quantity,
					product.product_type,
					order_item.product,
					MAKRO.product.product_tags,
					order_item.product_id,
		
					order_item.zahlungsart,
					supplier.supplier_name,
				";

                if ($detail_view === self::DETAIL_VIEW_FULL) {
                    $sql .= "
						MAKRO.order_item.ekpreis_sum,
						MAKRO.order_item.preis_sum,
						MAKRO.order_item.ertrag_sum,
					";
                } else {
                    $sql .= "
						MAKRO.order_item.ek_revised_sum,
						MAKRO.order_item.preis_sum,
						MAKRO.order_item.ertrag_revised_sum,
					";
                }

                $sql .= "
					order_item.lieferbaranzeige,
					order_item.status,
		
					MAKRO.product_lager_meta.lager_bestand_1
				FROM
					order_item INNER JOIN
					orders ON (orders.order_id = order_item.order_id) INNER JOIN
					customers ON (orders.customer_id = customers.customer_id) LEFT JOIN
					product_lager_meta ON (order_item.product_id = product_lager_meta.product_id) LEFT JOIN
					supplier ON (order_item.supplier_id = supplier.supplier_id) LEFT JOIN
					product ON (product.product_id = order_item.product_id) LEFT JOIN
					product_cat ON ($cat_id_field = product_cat.cat_id)
					$join
				WHERE
					order_item.added $period_sql AND
					" . $this->base_sql_order_filter . "
					$filter_where
				";

                if ($detail_view !== self::DETAIL_VIEW_FULL) {
                    $sql .= " AND order_item.typ = '" . OrderConst::WARENKORB_TYP_PRODUKT . "'";
                }
                break;
            default:
                throw new DevException('unknown view');
        }

        if ($persistent_container) {
            $table = new TableObjectPersistent($sql, $persistent_container);
        } else {
            $table = new TableObjectUrlPersistent($sql);
        }

        $table->setCaption('Verkäufe');

        $table->removeFieldByKey('order_id');
        $table->removeFieldByKey('product_id');
        $table->removeFieldByKey('customer_id');

        $table->setEntriesPerPageDefault(1000);

        if (!$is_long_period) {
            if ($detail_view === self::DETAIL_VIEW_FULL) {
                $table->getFieldByKey('preis_sum')->setRollup('SUM');
                $table->getFieldByKey('ertrag_sum')->setRollup('SUM');
            }
        }

        if ($detail_view === self::DETAIL_VIEW_PRODUCT_GROUPED || $detail_view === self::DETAIL_VIEW_BRAND_GROUPED) {
            $table->getFieldByKey('group_sum_quantity')->setRollup('SUM');
            $table->getFieldByKey('group_sum_ek_revised_sum')->setRollup('SUM');
            $table->getFieldByKey('group_sum_preis_sum')->setRollup('SUM');
            $table->getFieldByKey('group_sum_ertrag_revised_sum')->setRollup('SUM');

            $table->setExportEnabled(true);
        }

        $table->isNoneResult();

        return $table;
    }


    private function getOrderCampaigns(): array
    {
        $psms = PreissuchmaschinenRepository::getPsmNamesWithTrackingCodeShowInStats();

        $psms['other'] = 'Sonstige';
        $psms['without'] = 'Ohne';

        return $psms;
    }

    private function getProductPriceGroupsByOrderViewGroup(string $order_view_group): array
    {
        $grouping = $this->getOrderViewGroupSuffix($order_view_group);

        $maps = [
            '50' => [10, 20, 30, 40, 50, 75, 100],
            '100' => [10, 20, 40, 60, 80, 100],
            '200' => [25, 50, 100, 200, 500],
            '1000' => [100, 250, 500, 750, 1000],
            'k11' => [4, 6, 9, 11, 13, 16, 56], //angelehnt an ERSATZ4
            'k11_new' => [3, 5, 7, 9, 11, 14, 17, 21, 25, 31, 41, 67]
        ];

        foreach ($maps['k11'] as $key => $value) {
            $maps['k11'][$key] = round($value * 1.19);
        }
        foreach ($maps['k11_new'] as $key => $value) {
            $maps['k11_new'][$key] = round($value * 1.19);
        }

        $map = $maps[$grouping];

        $last = 0;

        $price_groups = [];

        foreach ($map as $value) {
            if (!$last) {
                $price_groups[$value] = [
                    'text' => 'bis ' . $value . '€',
                    'sql' => '<= ' . $value
                ];
            } else {
                $price_groups[$value] = [
                    'text' => $last . '€ bis ' . $value . '€',
                    'sql' => 'BETWEEN ' . $last . '.01 AND ' . $value
                ];
            }

            $last = $value;
        }

        $price_groups['ab' . $last] = [
            'text' => 'ab ' . $last . '€',
            'sql' => ' > ' . $last
        ];

        return $price_groups;
    }

    private function getProductAgeGroupsByOrderViewGroup(string $order_view_group): array
    {
        if (preg_match('~_([0-9])$~', $order_view_group, $match)) {
            $step_size = $match[1];
            $steps = 24;
            if ($step_size == 6) {
                $steps = 12;
            }

            return $this->getProductAgeGroupsFine($step_size, $steps);
        }

        return $this->getProductAgeGroups();
    }

    private function getProductAgeGroups(): array
    {
        $age_groups = [];

        $age_groups['month_1'] = [
            'text' => '0 bis 1 Monat',
            'sql_absolute' => "BETWEEN DATE_SUB(NOW(), INTERVAL 1 MONTH) AND NOW()",
            'sql_relative' => "BETWEEN DATE_SUB(orders.added, INTERVAL 1 MONTH) AND orders.added"
        ];

        $age_groups['month_3'] = [
            'text' => '1 bis 3 Monate',
            'sql_absolute' => "BETWEEN DATE_SUB(NOW(), INTERVAL 3 MONTH) AND DATE_SUB(NOW(), INTERVAL 1 MONTH)",
            'sql_relative' => "BETWEEN DATE_SUB(orders.added, INTERVAL 3 MONTH) AND DATE_SUB(orders.added, INTERVAL 1 MONTH)",
        ];

        $age_groups['month_6'] = [
            'text' => '3 bis 6 Monate',
            'sql_absolute' => "BETWEEN DATE_SUB(NOW(), INTERVAL 6 MONTH) AND DATE_SUB(NOW(), INTERVAL 3 MONTH)",
            'sql_relative' => "BETWEEN DATE_SUB(orders.added, INTERVAL 6 MONTH) AND DATE_SUB(orders.added, INTERVAL 3 MONTH)",
        ];

        $age_groups['month_12'] = [
            'text' => '6 bis 12 Monate',
            'sql_absolute' => "BETWEEN DATE_SUB(NOW(), INTERVAL 12 MONTH) AND DATE_SUB(NOW(), INTERVAL 6 MONTH)",
            'sql_relative' => "BETWEEN DATE_SUB(orders.added, INTERVAL 12 MONTH) AND DATE_SUB(orders.added, INTERVAL 6 MONTH)",
        ];

        $age_groups['month_24'] = [
            'text' => '1 bis 2 Jahre',
            'sql_absolute' => "BETWEEN DATE_SUB(NOW(), INTERVAL 24 MONTH) AND DATE_SUB(NOW(), INTERVAL 12 MONTH)",
            'sql_relative' => "BETWEEN DATE_SUB(orders.added, INTERVAL 24 MONTH) AND DATE_SUB(orders.added, INTERVAL 12 MONTH)",
        ];

        $age_groups['month_36'] = [
            'text' => '2 bis 3 Jahre',
            'sql_absolute' => "BETWEEN DATE_SUB(NOW(), INTERVAL 36 MONTH) AND DATE_SUB(NOW(), INTERVAL 24 MONTH)",
            'sql_relative' => "BETWEEN DATE_SUB(orders.added, INTERVAL 36 MONTH) AND DATE_SUB(orders.added, INTERVAL 24 MONTH)"
        ];

        $age_groups['month_1200'] = [
            'text' => 'älter als 3 Jahre',
            'sql_absolute' => "< DATE_SUB(NOW(), INTERVAL 36 MONTH)",
            'sql_relative' => "< DATE_SUB(orders.added, INTERVAL 36 MONTH)",
        ];

        return $age_groups;
    }

    private function getProductAgeGroupsFine(int $step_size = 1, int $steps = 24): array
    {
        $to = null;
        $age_groups = [];

        for ($i = 0; $i <= $steps * $step_size; $i += $step_size) {
            $to = $i + $step_size;
            $age_groups['month_' . $to] = [
                'text' => $to . ' Mo.',
                'sql_absolute' => "BETWEEN DATE_SUB(NOW(), INTERVAL " . $to . " MONTH) AND DATE_SUB(NOW(), INTERVAL " . $i . " MONTH)",
                'sql_relative' => "BETWEEN DATE_SUB(orders.added, INTERVAL " . $to . " MONTH) AND DATE_SUB(orders.added, INTERVAL " . $i . " MONTH)"
            ];
        }

        $age_groups['month_older'] = [
            'text' => '&gt; ' . $to . ' Mo.',
            'sql_absolute' => "< DATE_SUB(NOW(), INTERVAL " . $to . " MONTH)",
            'sql_relative' => "< DATE_SUB(orders.added, INTERVAL " . $to . " MONTH)",
        ];

        return $age_groups;
    }

    private function getOrderViewGroupPrefix(string $order_view_group): string
    {
        $order_view_group_prefix = $order_view_group;

        if (($pos = strpos($order_view_group_prefix, '__')) !== false) {
            $order_view_group_prefix = substr($order_view_group_prefix, 0, $pos);
        }

        return $order_view_group_prefix;
    }

    private function getOrderViewGroupSuffix(string $order_view_group): string
    {
        return substr($order_view_group, strpos($order_view_group, '__') + 2);
    }

    private function getGrosGroupMap(string $order_view_group): array
    {
        $gros_group = $this->gros_groups[$order_view_group];

        $map = [];

        $period_sql = " BETWEEN '" . $this->date_from->db('begin') . "' AND '" . $this->date_till->db('end') . "'";

        $filter_where = '';

        if ($this->order_origin_ids) {
            $filter_where .= ' AND orders.order_origin_id IN (' . $this->db->makeIn($this->order_origin_ids) . ') ';
        }

        if ($this->product_types) {
            $filter_where .= ' AND product.product_type IN (' . $this->db->makeIn($this->product_types) . ') ';
        }

        $included_supplier_ids = [];

        if (is_array($gros_group['map'])) {
            foreach ($gros_group['map'] as $entry) {
                $key = preg_replace('~[^a-z0-9]~', '', strtolower($entry['name']));
                $supplier_ids = $entry['supplier_ids'] ?? [$entry['supplier_id']];

                $sql_condition = 'order_item.stats_supplier_id IN (' . $this->db->in($supplier_ids) . ')';
                $map[$key] = ['name' => $entry['name'], 'sql_condition' => $sql_condition];

                $included_supplier_ids = array_merge($included_supplier_ids, $supplier_ids);
            }
        } else {
            $limit = 10;
            if ($gros_group['map'] === 'top20' || $gros_group['map'] === 'top20_filter') {
                $limit = 20;
            }

            if (str_contains($gros_group['map'], 'filter')) {
                $cached_query = new cached_query("
                    SELECT
                        order_item.stats_supplier_id
                    FROM
                        order_item INNER JOIN
                        orders ON (order_item.order_id = orders.order_id) INNER JOIN
                        product ON (product.product_id = order_item.product_id)
                    WHERE
                        order_item.status < " . OrderConst::STATUS_STORNO . " AND
                        order_item.added $period_sql AND
                        product.product_type != '" . ProductConst::PRODUCT_TYPE_LEISTUNG . "'
                        $filter_where
                    GROUP BY
                        order_item.stats_supplier_id     
                    ORDER BY
                        COUNT(*) DESC
                    LIMIT
                        $limit
                ");
            } else {
                $cached_query = new cached_query("
                    SELECT
                        order_item.stats_supplier_id
                    FROM
                        order_item INNER JOIN
                        product ON (product.product_id = order_item.product_id)
                    WHERE
                        order_item.status < " . OrderConst::STATUS_STORNO . " AND
                        order_item.added > NOW() - INTERVAL 60 DAY AND
                        product.product_type != '" . ProductConst::PRODUCT_TYPE_LEISTUNG . "' 
                    GROUP BY
                        order_item.stats_supplier_id     
                    ORDER BY
                        COUNT(*) DESC
                    LIMIT
                        $limit
                ");
            }

            $included_supplier_ids = $this->db->execute($cached_query)->asSingleArray();

            $suppliers = $this->db->query("
                SELECT
                    supplier.supplier_id,
                    supplier.supplier_name
                FROM
                    supplier
                WHERE
                    supplier.supplier_id IN (" . $this->db->in($included_supplier_ids) . ")
            ")->asSingleArray('supplier_id');

            foreach ($included_supplier_ids as $supplier_id) {
                $supplier_name = $suppliers[$supplier_id];

                $key = preg_replace('~[^a-z0-9]~', '', strtolower($supplier_name));
                $sql_condition = 'order_item.stats_supplier_id = ' . $supplier_id;
                $map[$key] = ['name' => $supplier_name, 'sql_condition' => $sql_condition];
            }
        }

        $map['rest'] = ['name' => 'Rest', 'sql_condition' => 'order_item.stats_supplier_id NOT IN (' . $this->db->in($included_supplier_ids) . ')'];

        return $map;
    }


    private function getBrandsGroupMap(string $order_view_group): array
    {
        $brands_group = $this->brands_groups[$order_view_group];

        $period_sql = " BETWEEN '" . $this->date_from->db('begin') . "' AND '" . $this->date_till->db('end') . "'";

        $filter_where = '';

        if ($this->order_origin_ids) {
            $filter_where .= ' AND orders.order_origin_id IN (' . $this->db->makeIn($this->order_origin_ids) . ') ';
        }

        if ($this->product_types) {
            $filter_where .= ' AND product.product_type IN (' . $this->db->makeIn($this->product_types) . ') ';
        }

        $map = [];

        $included_brand_ids = [];

        if ($brands_group['map'] === 'top10_filter' || $brands_group['map'] === 'top20_filter' || $brands_group['map'] === 'top50_filter') {
            $limit = 10;
            if ($brands_group['map'] === 'top20_filter') {
                $limit = 20;
            }
            if ($brands_group['map'] === 'top50_filter') {
                $limit = 50;
            }

            $cached_query = new cached_query("
                SELECT
                    product.brand_id
                FROM
                    order_item INNER JOIN
                    orders ON (order_item.order_id = orders.order_id) INNER JOIN
                    product ON (product.product_id = order_item.product_id)
                WHERE
                    order_item.status < " . OrderConst::STATUS_STORNO . " AND
                    order_item.added $period_sql AND
                    product.product_type != '" . ProductConst::PRODUCT_TYPE_LEISTUNG . "' AND
                    product.brand_id NOT IN (" . ProductConst::BRAND_ID_ALLEGO . ")
                    $filter_where
                GROUP BY
                    product.brand_id
                ORDER BY
                    COUNT(*) DESC
                LIMIT
                    $limit
            ");

            $included_brand_ids = $this->db->execute($cached_query)->asSingleArray();

            $brands = $this->db->query("
                SELECT
                    product_brand.brand_id,
                    product_brand.brand_name
                FROM
                    product_brand
                WHERE
                    product_brand.brand_id IN (" . $this->db->in($included_brand_ids) . ")
            ")->asSingleArray('brand_id');

            foreach ($included_brand_ids as $brand_id) {
                $brand_name = $brands[$brand_id];

                $key = preg_replace('~[^a-z0-9]~', '', strtolower($brand_name));
                $sql_condition = 'product.brand_id = ' . $brand_id;
                $map[$key] = ['name' => $brand_name, 'sql_condition' => $sql_condition];
            }
        }

        $map['rest'] = ['name' => 'Rest', 'sql_condition' => 'product.brand_id NOT IN (' . $this->db->in($included_brand_ids) . ')'];

        return $map;
    }


    private function getCats(int $cat_tree_id, string $order_view_group): array
    {
        $cat_groups = [];

        $parent_cat_id = (int)$this->getOrderViewGroupSuffix($order_view_group);

        $result = $this->db->query("
            SELECT
                product_cat.cat_id,
                product_cat.cat_name,
                product_cat.cat_id_ebene_1,
                product_cat.cat_id_ebene_2,
                product_cat.cat_id_ebene_3,
                product_cat.cat_id_ebene_4
            FROM
                product_cat
            WHERE
                product_cat.parent_cat_id = $parent_cat_id AND
                product_cat.cat_tree_id = $cat_tree_id
            ORDER BY
                product_cat.cat_pos
        ");

        $cat_ids = [];

        foreach ($result as $row) {
            $ebene = 1;
            for ($i = 1; $i <= 4; $i++) {
                if ($row['cat_id'] === $row['cat_id_ebene_' . $i]) {
                    $ebene = $i;
                    break;
                }
            }

            $cat_ids[] = $row['cat_id'];

            $cat_group = [
                'key' => 'cat_id_' . $row['cat_id'],
                'name' => $row['cat_name'],
                'sql_condition' => 'product_cat.cat_id_ebene_' . $ebene . ' = ' . $row['cat_id']
            ];

            $cat_groups[$cat_group['key']] = $cat_group;
        }

        //das mit der ebene ist nicht clean, geht aber solange nur kategorien einer ebene angeschaut werden
        $cat_group = [
            'key' => 'cat_id_other',
            'name' => 'Andere',
            'sql_condition' => 'product_cat.cat_id_ebene_' . $ebene . ' NOT IN (' . $this->db->in($cat_ids) . ')'
        ];

        $cat_groups[$cat_group['key']] = $cat_group;

        return $cat_groups;
    }

    private function getCountries(string $order_view_group): array
    {
        //$country_ids = [CountryConst::COUNTRY_ID_DE, CountryConst::COUNTRY_ID_AT];

        $country_ids = $this->db->query("
            SELECT
                order_addresses.country_id
            FROM
                orders INNER JOIN
                order_addresses ON (order_addresses.order_id = orders.order_id AND order_addresses.address_type IN (" . $this->db->in($this->getCountryAddressTypesByOrderViewGroup($order_view_group)) . "))
            WHERE
                orders.added BETWEEN '" . $this->date_from->db('begin') . "' AND '" . $this->date_till->db('end') . "'
            GROUP BY
                order_addresses.country_id
            ORDER BY
                COUNT(*) DESC
            LIMIT
                10
        ")->asSingleArray();

        $country_groupes = [];

        foreach ($country_ids as $country_id) {
            $country_groupes['country_id_' . $country_id] = [
                'key' => 'country_id_' . $country_id,
                'name' => CountryRepository::getCountryNameById($country_id),
                'sql_condition' => "order_addresses.country_id = " . $country_id . ""
            ];
        }

        return $country_groupes;
    }

    private function getCountryAddressTypesByOrderViewGroup(string $order_view_group): array
    {
        return match ($order_view_group) {
            'country__delivery_address' => [Order::ADDRESS_TYPE_LIEFERRECHUNG, Order::ADDRESS_TYPE_LIEFER],
            'country__invoice_address' => [Order::ADDRESS_TYPE_LIEFERRECHUNG, Order::ADDRESS_TYPE_RECHUNG]
        };
    }
}
