<?php

namespace wws\Order;

use billsafe;
use bqp\Address\Address;
use config;
use payment_repository;

class OrderDocumentDefaults
{
    public static function getPreDefault(OrderDocument $order_document): string
    {
        switch ($order_document->getDocumentType()) {
            case OrderDocument::TYPE_OFFER:
                return $order_document->getOrder()->getCustomer()->getGrussformel() . "<br><br>" .
                    "wir danken für Ihre Anfrage und unterbreiten Ihnen hiermit folgendes Angebot.";
        }

        return '';
    }

    public static function getPostDefault(OrderDocument $order_document): string
    {
        switch ($order_document->getDocumentType()) {
            case OrderDocument::TYPE_OFFER:
                return '';
            case OrderDocument::TYPE_CONFIRMATION:
            case OrderDocument::TYPE_PRE_CONFIRMATION:
                if (
                    $order_document->getOrder()->getZahlungsart() == OrderConst::PAYMENT_RECHNUNG ||
                    $order_document->getOrder()->getZahlungsart() == OrderConst::PAYMENT_RECHNUNG_BILLSAFE
                ) {
                    return "Zahlungsziel innerhalb von 7 Tagen nach Rechnungserhalt, ohne Abzüge.";
                }
        }

        return '';
    }

    public static function getPostDefaultByOrder($document_type, Order $order)
    {
        switch ($document_type) {
            case OrderDocument::TYPE_INVOICE:
                $delivery_date = $order->getDeliveryDate();

                if (!$delivery_date->isValid()) {
                    $delivery_date->setTimestamp(time());
                }

                $terms = 'Der Ausführer der Waren, auf die sich dieses Handelspapier bezieht, erklärt, dass diese Waren, soweit nicht anders angegeben, präferenzbegünstigte CE Ursprungswaren sind. Alle Lieferungen und Leistungen basieren auf unseren Allgemeinen Geschäftsbedingungen, welche wir Ihnen auf Wunsch gern zusenden.';

                if ($order->getZahlungsart() == OrderConst::PAYMENT_RECHNUNG) {
                    $terms .= "\n\n" . 'Zahlbar bis zum ' . date('d.m.Y', $delivery_date->getTimestamp() + (60 * 60 * 24 * 7)) . ', ohne Abzüge.';
                }

                if ($order->getZahlungsart() == OrderConst::PAYMENT_RECHNUNG_BILLSAFE) {
                    $billsafe = new billsafe(config::getLegacy('ecom/payment/billsafe'));
                    $result = $billsafe->getPaymentInstruction($order);

                    $terms .= "\n\n";
                    $terms .= '<b>' . $result['note'];
                    $terms .= "\n";
                    $terms .= 'Verwendungszweck: <b>' . $result['reference'] . '</b>' . "\n";
                    $terms .= "Empfänger: " . $result['emfaenger'] . "\n";
                    /*$terms .= "Kontonummer: ".$result['kontonummer']."\n";
                    $terms .= "BLZ: ".$result['blz']."\n";*/
                    $terms .= "IBAN: " . $result['iban'] . "</b>\n";
                    $terms .= "BIC: " . $result['bic'] . "\n";
                    $terms .= "Bank: " . $result['bank'] . "\n";
                    $terms .= "\n" . $result['lega_note'];
                }

                $terms .= "\n\n" . 'Bitte bewahren Sie Ihre Rechnung nach §14b Umsatzsteuergesetz, 2 Jahre auf.';

                if (trim($order->getInvoiceNotice())) {
                    $terms .= "\n\n" . $order->getInvoiceNotice();
                }

                if ($order->getZahlungsart() == OrderConst::PAYMENT_CHECK24 && $order->isAbweichendeLieferadresse()) {
                    $terms .= "\n\nLieferadresse:\n";
                    $terms .= $order->getLieferAddress()->format(Address::FORMAT_FULL, false);
                }

                $payment_terms = payment_repository::getPaymentTermsInvoice($order->getZahlungsart());

                if ($payment_terms) {
                    $terms .= "\n\n" . $payment_terms;
                }

                return $terms;
        }
    }
}
