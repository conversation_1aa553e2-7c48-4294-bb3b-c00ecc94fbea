<?php

namespace wws\Order;

use bqp\Address\Address;

abstract class OrderAddressVerification
{
    public function verifyAddress(Order $order, ?Address $address = null): OrderAddressVerificationResult
    {
        return $this->verifyDeliveryAddress($order, $address);
    }

    abstract public function verifyDeliveryAddress(Order $order, ?Address $address = null): OrderAddressVerificationResult;

    abstract public function verifyInvoiceAddress(Order $order, ?Address $address = null): OrderAddressVerificationResult;
}
