<?php

namespace wws\Order;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;
use order_repository;

class OrderRepository
{
    private db_generic $db;

    /**
     * @var Order[]
     */
    private array $instance_cache = [];

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * @param int $order_id
     * @return Order
     * @throws SmartDataEntityNotFoundException
     */
    public function load(int $order_id): Order
    {
        return new Order($order_id);
    }

    /**
     * Lädt eine Bestellung aus der Datenbank, oder falls bereits geladen aus dem Speicher.
     * Einzelnen Instanzen können über OrderRepository::removeInstanceFromCache() aus dem Speicher entfernt werden.
     *
     * @param int $order_id
     * @return Order
     * @throws SmartDataEntityNotFoundException
     */
    public function loadCached(int $order_id): Order
    {
        if (!isset($this->instance_cache[$order_id])) {
            $this->instance_cache[$order_id] = $this->load($order_id);
        }

        return $this->instance_cache[$order_id];
    }

    public function removeInstanceFromCache(int $order_id): void
    {
        unset($this->instance_cache[$order_id]);
    }

    public function save(Order $order): int
    {
        return $order->save();
    }

    public function create(): Order
    {
        $order = new Order();

        return $order;
    }


    public function orderExistsByOrderId(int $order_id): bool
    {
        return (bool)$this->db->fieldQuery("
            SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.order_id = $order_id
        ");
    }

    public function getCustomerIdByOrderId(int $order_id): int
    {
        $customer_id = $this->db->fieldQuery("
            SELECT
                orders.customer_id
            FROM
                orders
            WHERE
                orders.order_id = '" . (int)$order_id . "'
        ");

        return $customer_id;
    }

    public function loadByAuftnr(string $auftnr): Order
    {
        $order_id = (int)order_repository::getOrderIdByAuftnr($auftnr);

        return $this->load($order_id);
    }

    public function tryGetOrderByAuftnr(string $auftnr): ?Order
    {
        try {
            return $this->loadByAuftnr($auftnr);
        } catch (SmartDataEntityNotFoundException $e) {
            return null;
        }
    }
}
