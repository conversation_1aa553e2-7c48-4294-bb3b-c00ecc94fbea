<?php

namespace wws\Order;

use output;

class OrderToMemo
{

    public function getAsText(Order $order): string
    {
        $bemerkung = "Bestellung: (" . $order->getOrderOriginId() . ")\n";
        $i = 1;

        foreach ($order->getOrderItems() as $order_item) {
            $bemerkung .= "$i.) " . $order_item->getQuantity() . " x " . $order_item->getProductName() . " " . output::formatPrice($order_item->getVkBrutto()) . "\n";
            $i++;
        }

        $bemerkung .= " für " . output::formatPrice($order->getRechnungsBetrag()) . " per " . $order->getZahlungsartName();

        if ($order->getKundenBemerkung() !== '') {
            $bemerkung .= "\n\nBemerkung des Kunden: \n---------------------------\n" . trim($order->getKundenBemerkung()) . "\n---------------------------";
        }

        return $bemerkung;
    }

    public function getAsMemo(Order $order): OrderMemo
    {
        return new OrderMemo($this->getAsText($order), OrderMemo::VERKAUF);
    }

    /**
     * fügt die Bestellung als Eintrag in der order_memofeld hinzu (speicher order nicht!)
     *
     * @param Order $order
     */
    public function addOrderToOrderMemo(Order $order): void
    {
        $memo = $this->getAsMemo($order);
        $order->addOrderMemo($memo);
    }
}
