<?php

namespace wws\Order;

use bqp\AddressVerification\AddressVerificationResult;

class OrderAddressVerificationResult
{
    /**
     * address is valid
     */
    public const STATE_OK = 'ok';

    /**
     * address is invalid, but business rules allow to ignore it
     */
    public const STATE_WARN = 'warn';

    /**
     * address is invalid
     */
    public const STATE_ERROR = 'error';

    private string $state;
    private AddressVerificationResult $address_verification_result;

    public function __construct(string $state, AddressVerificationResult $address_verification_result)
    {
        $this->state = $state;
        $this->address_verification_result = $address_verification_result;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function getAddressVerificationResult(): AddressVerificationResult
    {
        return $this->address_verification_result;
    }

    public function hasCorrection(): bool
    {
        return $this->address_verification_result->hasCorrection();
    }


    public function isOk(): bool
    {
        return $this->state === self::STATE_OK;
    }

    public function canAutoProceed(): bool
    {
        return $this->state === self::STATE_OK || $this->state === self::STATE_WARN;
    }
}
