<?php

namespace wws\Order;

use bqp\db\db_generic;

/**
 * Ermöglicht es zu einer Bestellung festzuhalten, dass eine bestimmte Aktion ausgeführt wurde.
 * Hauptaufgabe ist mehrfache ausführungen zu verhindern.
 */
class OrderActionLog
{
    public const ACTION_PAYMENT_INCOMPLETE_SEND = 'payment_incomplete_send';
    public const ACTION_AMAZON_COMPLETE_SEND = 'amazon_complete';
    public const ACTION_AMAZON_INVOICE_UPLOADED = 'amazon_invoice_uploaded';
    public const ACTION_ALLYOUNEED_COMPLETE_SEND = 'allyouneed_complete';
    public const ACTION_KAUFLAND_INVOICE_UPLOADED = 'kaufland_invoice_uploaded';
    public const ACTION_ORDER_CONFIRMATION_SENT = 'order_confirmation_sent';

    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function addEntry(int $order_id, string $action_type, string $action_value = ''): void
    {
        $this->db->query("
            INSERT INTO
                orders_action_log
            SET
                orders_action_log.order_id = '" . $order_id . "',
                orders_action_log.action_date = NOW(),
                orders_action_log.action_type = " . $this->db->quote($action_type) . ",
                orders_action_log.action_value = " . $this->db->quote($action_value) . "
        ");
    }

    public function isEntry(int $order_id, string $action_type): bool
    {
        return (bool)$this->getEntries($order_id, $action_type);
    }

    public function getEntries(int $order_id, ?string $action_type = null): array
    {
        $where = '';
        if ($action_type) {
            $where = " AND orders_action_log.action_type = " . $this->db->quote($action_type);
        }

        $result = $this->db->query("
            SELECT
                orders_action_log.action_type,
                orders_action_log.action_date,
                orders_action_log.action_value
            FROM
                orders_action_log
            WHERE
                orders_action_log.order_id = " . $order_id . "
                $where
        ")->asArray();

        return $result;
    }
}
