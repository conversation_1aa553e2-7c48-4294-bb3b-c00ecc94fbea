<?php

namespace wws\Order\Actions;

use bqp\Date\DateObj;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;

class OrderClone
{
    /**
     * Klont eine Bestellung um eine neue Rechnung zu erstellen. (durch Einschränkungen in der Buchhaltung, derzeit
     * kommt die Buchhaltung nicht mit mehreren Rechnungen für ein Auftrag klar. (weil Auftragsnummer als Belegnummer verwendet wird))
     *
     * @param Order $order
     * @return Order
     */
    public function cloneOrderForInvoice(Order $order): Order
    {
        $new_order = new Order();

        $this->copyOrderBase($order, $new_order);

        foreach ($order->getOrderItems() as $order_item) {
            //für die Rechnungskorrektur können die Positionen fast 1:1 kopiert werden
            $daten = $order_item->getAsArray();

            unset($daten['order_item_id']);
            unset($daten['externe_referenz']);
            unset($daten['quantity_progress']);
            unset($daten['quantity_completed']);
            unset($daten['quantity_planed']);
            unset($daten['added']);
            unset($daten['versand_status']);
            unset($daten['versand_datum']);

            $daten['quantity_open'] = $daten['quantity'];

            $new_order_item = new OrderItem($new_order);
            $new_order_item->setByArray($daten);
            $new_order_item->setDateAdded(new DateObj());

            $new_order->addOrderItem($new_order_item);
        }

        $this->copyAddresses($order, $new_order);

        $this->cleanOrderTags($new_order);

        $new_order->setStatus(OrderConst::STATUS_BEENDET);

        return $new_order;
    }

    public function cloneOrder(Order $order): Order
    {
        $new_order = new Order();

        $this->copyOrderBase($order, $new_order);

        foreach ($order->getOrderItems() as $order_item) {
            $new_order_item = $new_order->createOrderItem($order_item->getProductId());
            $new_order_item->setPos($order_item->getPos());
            $new_order_item->setTyp($order_item->getTyp());
            $new_order_item->setTypValue($order_item->getTypValue());
            $new_order_item->setProductNr($order_item->getProductNr());
            $new_order_item->setProductName($order_item->getProductName());
            $new_order_item->setQuantity($order_item->getQuantity());
            $new_order_item->setUnitCode($order_item->getUnitCode());
            $new_order_item->setVkBrutto($order_item->getVkBrutto());

            if (!$order_item->isDeliveryAble()) { //bei allen was kein Produkt ist (derzeit als Versandkosten, Gebühren usw), den EK 1:1 übernehmen
                $new_order_item->setEkBrutto($order_item->getEkBrutto());
                $new_order_item->setEkNnnBrutto($order_item->getEkNnnBrutto());
            }

            if ($order_item->getWarenkorbType() == OrderConst::WARENKORB_TYP_ASWO) { //bei ASWO Dummy Position EK übernehmen
                $new_order_item->setEkBrutto($order_item->getEkBrutto());
                $new_order_item->setEkNnnBrutto($order_item->getEkNnnBrutto());
            }
        }

        $new_order->setStatus(OrderConst::STATUS_AB_LEERFELD);
        $new_order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_NEUTRAL);

        $date = new DateObj();
        $date->addSimple('days', 7);
        $new_order->setVorkassenFrist($date);

        $this->copyAddresses($order, $new_order);
        $this->cleanOrderTags($new_order);

        return $new_order;
    }


    private function copyOrderBase(Order $order, Order $new_order): void
    {
        $daten = $order->getAsArray();
        unset($daten['order_id']);
        unset($daten['shop_referenz']);
        unset($daten['rechnungs_id']);
        unset($daten['bestell_datum']);
        unset($daten['auftnr']);
        unset($daten['is_payed']);
        unset($daten['gelangensbestaetigung']);
        unset($daten['payment_url']);
        unset($daten['order_aktiv']);

        $new_order->setByArray($daten);
    }

    private function copyAddresses(Order $order, Order $new_order): void
    {


        $delivery_address = $order->getAbweichendeLieferAddress();
        if ($delivery_address->isValid()) {
            $address = $order->getRechnungAddress();
            $new_order->setAddress($address, $order::ADDRESS_TYPE_RECHUNG);

            $new_order->setAddress($delivery_address, $order::ADDRESS_TYPE_LIEFER);
        } else {
            $address = $order->getRechnungAddress();
            $new_order->setAddress($address, $order::ADDRESS_TYPE_LIEFERRECHUNG);
        }
    }

    private function cleanOrderTags(Order $new_order): void
    {
        $new_order->removeOrderTag(OrderConst::TAG_UNREAD_MAIL);
        $new_order->removeOrderTag(OrderConst::TAG_ABHOLUNG_BEREITGESTELLT);
        $new_order->removeOrderTag(OrderConst::TAG_ABHOLUNG_KUNDE_INFORMIERT);
    }
}
