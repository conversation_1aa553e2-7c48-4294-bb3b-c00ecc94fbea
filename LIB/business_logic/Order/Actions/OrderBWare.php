<?php

namespace wws\Order\Actions;

use bqp\db\db_generic;
use wws\Mails\Mail;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Product\ProductConst;

class OrderBWare
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * Prüft, ob die Bestellung B-Ware Position enthält. Passiert derzeit über den Produktstamm.
     * ->ACHTUNG: die Methode wird aktuell nicht genutzt, der B-Ware Tag wird über \wws\Order\Order::addProduct() gesetzt.
     *
     * @param Order $order
     * @return bool
     */
    public function hasOrderBwarePositions(Order $order): bool
    {
        $product_ids = [];
        foreach ($order->getOrderItems() as $order_item) {
            $product_ids[$order_item->getProductId()] = $order_item->getProductId();
        }

        $result = $this->db->query("
            SELECT
                product.condition_id
            FROM
                product
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ")
        ")->asArray();

        foreach ($result as $row) {
            if ($row['condition_id'] != ProductConst::CONDITION_NEW && $row['condition_id'] != ProductConst::CONDITION_VERPACKUNGSSCHADEN) {
                return true;
            }
        }

        return false;
    }

    public function processOrder(Order $order): void
    {
        $has_b_ware = $this->hasOrderBwarePositions($order);

        if ($has_b_ware) {
            $order->addOrderTag(OrderConst::TAG_B_WARE);
        } else {
            $order->removeOrderTag(OrderConst::TAG_B_WARE);
        }
    }

    public function sendBWareMail(Order $order): void
    {
        $mail = new Mail();
        $mail->setOrder($order);
        $mail->loadVorlage('b_ware');

        $mail->send();
    }
}
