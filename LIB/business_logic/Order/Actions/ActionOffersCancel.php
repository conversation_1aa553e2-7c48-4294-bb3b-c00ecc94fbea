<?php

namespace wws\Order\Actions;

use bqp\db\db_generic;
use wws\Order\Order;
use wws\Order\OrderConst;

class ActionOffersCancel
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function execute(): void
    {
        $result = $this->db->query("
            SELECT
                DISTINCT order_item.order_id
            FROM
                order_item
            WHERE
                order_item.status = " . OrderConst::STATUS_ANGEBOT_OFFEN . " AND
                order_item.added < NOW() - INTERVAL 60 DAY
        ");

        foreach ($result as $row) {
            $order = new Order($row['order_id']);
            $order->setStatus(OrderConst::STATUS_ANGEBOT_STORNIERT);
            $order->save();
        }
    }
}
