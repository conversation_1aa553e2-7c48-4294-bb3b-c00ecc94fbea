<?php

namespace wws\Order\Actions;

use order_repository;
use wws\Order\Order;
use wws\Order\OrderMemo;

class Gelangensbestaetigung
{

    /**
     * @var int
     */
    private $status;
    /**
     * @var string
     */
    private $bemerkung;
    /**
     * @var Order
     */
    private $order;

    /**
     * @param int $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @param string $bemerkung
     */
    public function setBemerkung($bemerkung)
    {
        $this->bemerkung = $bemerkung;
    }

    /**
     * @param Order $order
     */
    public function setOrder(Order $order)
    {
        $this->order = $order;
    }

    public function execute()
    {
        $this->order->setGelangensbestaetigung($this->status);

        $text = 'neuer Gelangensbestätigungs Status: ' . order_repository::getGelangensbestaetigungStatusName($this->status);

        if (trim($this->bemerkung)) {
            $text .= "\n\n";
            $text .= $this->bemerkung;
        }

        $memo = new OrderMemo($text, OrderMemo::BUCHHALTUNG);

        $this->order->addOrderMemo($memo);

        $this->order->save();
    }
}
