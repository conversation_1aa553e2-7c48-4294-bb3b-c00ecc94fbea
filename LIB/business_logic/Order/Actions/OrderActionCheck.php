<?php

namespace wws\Order\Actions;

use bqp\BoolWithReason;
use bqp\Exceptions\SecurityException;
use db;
use env;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;
use wws\Supplier\SupplierOrder;
use wws\Users\UserSession;

/**
 * Bildet die möglichkeit ab, zu prüfen ob verschiedene Aktionen auf eine Bestellung möglich sind.
 * Diverse Prüfungen, wie, darf eine Position gelöscht werden, kann die Bestellung geteilt werden,
 * kann die Bestellung storniert werden, sind mittlerweile an mehreren Stellen nötig.
 *
 * Tendenziell ist es nicht Ziel, die Logik in die Klasse zu packen, sondern ein einfaches interface zu bieten.
 * Grade in der Kundenmaske, mächte ich nicht mit 20 explizit Instanzen von Servicen herumhantieren. Das soll diese Klasse
 * hier übernehmen.
 * Es kann aber auch die Logik hier implementiert werden.
 *
 * Class OrderActionCheck
 * @package wws\Order\Actions
 */
class OrderActionCheck
{
    private ?UserSession $user;

    public function __construct()
    {
        $this->user = env::getUserSession();
    }

    public function isOrderCancelable(Order $order): BoolWithReason
    {
        if ($order->hasInvoice()) {
            return new BoolWithReason(false, 'Für diesen Auftrag existiert bereits eine Rechnung.');
        }

        if ($order->getMinStatus() == OrderConst::STATUS_STORNO) {
            return new BoolWithReason(false, 'Dieser Auftrag ist bereits storniert.');
        }

        if ($order->getMinStatus() < OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST or $order->getMinStatus() == OrderConst::STATUS_PARK) {
            return new BoolWithReason(true);
        }

        //fall through
        return new BoolWithReason(false, 'Keine zutreffende Regel gefunden.');
    }

    public function isOrderItemCancelable(Order $order, OrderItem $order_item): BoolWithReason
    {
        if ($order->hasInvoice()) {
            return new BoolWithReason(false, 'Für diesen Auftrag existiert bereits eine Rechnung.');
        }

        if ($order_item->getStatus() > OrderConst::STATUS_STORNO) {
            return new BoolWithReason(false, 'Angebotspositionen können nicht storniert werden.');
        }

        if ($order_item->getStatus() == OrderConst::STATUS_STORNO) {
            return new BoolWithReason(false, 'Position ist bereits storniert.');
        }

        if ($order_item->getStatus() >= OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST) {
            return new BoolWithReason(false, 'Position ist bereits ausgelöst oder beendet.');
        }

        return new BoolWithReason(true);
    }

    public function isOrderItemDeletable(Order $order, OrderItem $order_item): BoolWithReason
    {
//        if ($this->user->hasRight('rights.superuser')) { //?!
//            return new BoolWithReason(true);
//        }

        return $this->isOrderItemCancelable($order, $order_item);
    }

    public function isOrderSplittable(Order $order): BoolWithReason
    {
        if ($order->hasInvoice()) {
            return new BoolWithReason(false, 'Für diesen Auftrag wurde bereits eine Rechnung erstellt.');
        }

        if ($order->isStorno()) {
            return new BoolWithReason(false, 'Dieser Auftrag ist storniert.');
        }

        //eventuell mal schauen das die Versionposition hier ausgenommenw erden
        if (count($order->getOrderItems()) === 1) {
            return new BoolWithReason(false, 'Dieser Auftrag beinhaltet nur eine Position.');
        }

        return new BoolWithReason(true);
    }

    public function isOfferConvertable(Order $order): BoolWithReason
    {
        return new BoolWithReason($order->getOrderType() === OrderConst::ORDER_TYPE_OFFER);
    }

    public function isOfferCancelable(Order $order): BoolWithReason
    {
        return new BoolWithReason($order->getOrderType() === OrderConst::ORDER_TYPE_OFFER);
    }

    public function isOfferDeleteable(Order $order): BoolWithReason
    {
        return new BoolWithReason($order->getOrderType() === OrderConst::ORDER_TYPE_OFFER);
    }


    /**
     * Gibt die Statuse zurück die für das OrderItem manuell gesetzt werden dürfen.
     *
     * @param OrderItem $order_item
     * @param $allow_all_states
     * @return array
     * @throws SecurityException
     */
    public function getPossibleStatuse(OrderItem $order_item, bool $allow_all_states = false): array
    {
        if ($allow_all_states && !$this->user->hasRight('rights.superuser')) {
            throw new SecurityException('allow all states without superuser rights');
        }

        $status_edit_disabled = false;

        //statuse schützen
        if ($order_item->getStatus() == OrderConst::STATUS_BEENDET && !$allow_all_states) {
            $status_edit_disabled = true;
        }
        if ($order_item->getStatus() == OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST && !$allow_all_states) {
            $status_edit_disabled = true;
        }
        if ($order_item->getStatus() == OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND && !$allow_all_states) {
            if ($this->isActiveSupplierOrder($order_item->getOrderId())) {
                $status_edit_disabled = true;
            }
        }

        $result = db::getInstance()->query("
                SELECT
                    einst_status.status_name,
                    einst_status.status_id,
                    einst_status.status_group
                FROM
                    einst_status
                ORDER BY
                    einst_status.status_id
        ")->asArray('status_id');

        $skip_status = [OrderConst::STATUS_STORNO, OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST, OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND];

        $possible_statuse = [];

        $status_group = $result[$order_item->getStatus()]['status_group'];

        foreach ($result as $status_daten) {
            if ($status_edit_disabled) {
                if ($status_daten['status_id'] == $order_item->getStatus()) {
                    $possible_statuse[$status_daten['status_id']] = $status_daten['status_name'];
                }
                continue;
            }

            if (!$allow_all_states && $status_daten['status_id'] != $order_item->getStatus() && in_array($status_daten['status_id'], $skip_status)) {
                continue;
            }

            if (!$allow_all_states && $status_group !== $status_daten['status_group']) {
                continue;
            }

            $possible_statuse[$status_daten['status_id']] = $status_daten['status_name'];
        }

        return $possible_statuse;
    }

    public function isActiveSupplierOrder(int $order_id): bool
    {
        return db::getInstance()->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                supplier_order
            WHERE
                supplier_order.order_id = $order_id AND
                supplier_order.status = '" . SupplierOrder::STATUS_BESTELLT . "'
        ") > 0;
    }
}
