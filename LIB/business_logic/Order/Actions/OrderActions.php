<?php

namespace wws\Order\Actions;

use wws\Country\CountryConst;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderTags;
use wws\Shipment\ShipmentRepository;

class OrderActions
{
    public static function calcOrderTags(Order $order): void
    {
        if ($order->getVersandKundeOpt() === 'abh' || $order->getSpedId() == ShipmentRepository::SPED_ABHOLUNG) {
            $order->addOrderTag(OrderConst::TAG_ABHOLUNG);
        } else {
            $order->removeOrderTag(OrderConst::TAG_ABHOLUNG);
        }

        if ($order->isAddressLoaded()) {
            $address = $order->getLieferAddress();

            if ($address->getCountryId() === CountryConst::DE && ShipmentRepository::plzIsGermanIsland($address->getPlz())) {
                $order->addOrderTag(OrderConst::TAG_ISLAND);
            } else {
                $order->removeOrderTag(OrderConst::TAG_ISLAND);
            }

            foreach ($order->getOrderTags() as $tag) {
                if (OrderTags::isCountryFlag($tag)) {
                    $order->removeOrderTag($tag);
                }
            }

            if ($address->getCountryId() != CountryConst::DE) {
                $order->addOrderTag(OrderConst::TAG_OUTLAND);

                $tag = OrderTags::getCountryFlagTagForCountry($address->getCountryId());
                if ($tag) {
                    $order->addOrderTag($tag);
                }
            } else {
                $order->removeOrderTag(OrderConst::TAG_OUTLAND);
            }


            $plzs = [
                '01053',
                '01054',
                '01055',
                '01056',
                '01057',
                '01058',
                '01059',
                '01060',
                '01061',
                '01062',
                '01063',
                '01064',
                '01065',
                '01066',
                '01067',
                '01069',
                '01070',
                '01095',
                '01097',
                '01099',
                '01108',
                '01109',
                '01127',
                '01129',
                '01139',
                '01156',
                '01157',
                '01159',
                '01169',
                '01187',
                '01189',
                '01212',
                '01213',
                '01217',
                '01219',
                '01237',
                '01239',
                '01255',
                '01257',
                '01259',
                '01264',
                '01277',
                '01279',
                '01303',
                '01304',
                '01305',
                '01307',
                '01309',
                '01324',
                '01326',
                '01328',
                '01333',
                '01445',
                '01454',
                '01458',
                '01462',
                '01465',
                '01468',
                '01471',
                '01474',
                '01477',
                '01478',
                '01561',
                '01640',
                '01662',
                '01665',
                '01689',
                '01705',
                '01723',
                '01728',
                '01730',
                '01731',
                '01734',
                '01737',
                '01744',
                '01768',
                '01774',
                '01793',
                '01796',
                '01809',
                '01827',
                '01829',
                '01833',
                '01844',
                '01847',
                '01848',
                '01877',
                '01896',
                '01900',
                '01906',
                '01909',
                '01917',
                '01920',
                '01936',
                '09629',
                '01328'
            ];

            if ($address->getCountryId() == CountryConst::DE && in_array($address->getPlz(), $plzs) && !$order->isOrderTag(OrderConst::TAG_ABHOLUNG)) {
                $order->addOrderTag(OrderConst::TAG_UMGEBUNG);
            } else {
                $order->removeOrderTag(OrderConst::TAG_UMGEBUNG);
            }
        }

        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
            $order->addOrderTag(OrderConst::TAG_AMAZON);
        } else {
            $order->removeOrderTag(OrderConst::TAG_AMAZON);
        }

        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_EBAY) {
            $order->addOrderTag(OrderConst::TAG_EBAY);
        } else {
            $order->removeOrderTag(OrderConst::TAG_EBAY);
        }

        if ($order->getOrderOriginId() === OrderConst::PAYMENT_CHECK24) {
            $order->addOrderTag(OrderConst::TAG_CHECK24);
        } else {
            $order->removeOrderTag(OrderConst::TAG_CHECK24);
        }
    }
}
