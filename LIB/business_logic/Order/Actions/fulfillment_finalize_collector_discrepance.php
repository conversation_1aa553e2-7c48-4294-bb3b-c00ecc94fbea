<?php

namespace wws\Order\Actions;

use Exception;

class fulfillment_finalize_collector_discrepance extends Exception
{

    /**
     * @var fulfillment_finalize_collector
     */
    protected $fulfillment_finalize;

    public function setFulfillmentFinalize(fulfillment_finalize_collector $fulfillment_finalize)
    {
        $this->fulfillment_finalize = $fulfillment_finalize;
    }

    /**
     * @return fulfillment_finalize_collector
     */
    public function getFilfillmentFinanlize()
    {
        return $this->fulfillment_finalize;
    }

    public function getDetailsAsText()
    {
        return $this->getFilfillmentFinanlize()->getAsText();
    }
}
