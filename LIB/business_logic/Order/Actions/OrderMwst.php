<?php

namespace wws\Order\Actions;

use db;
use service_loader;
use wws\Country\CountryConst;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderRepository;
use wws\Shipment\ShipmentRepository;

/**
 * bestimmt, ob für einen Auftrag Umsatzsteuer fällig wird
 *
 * @package wws\Order\Actions
 */
class OrderMwst
{
    public static function setTaxStatus(Order $order): void
    {
        if ($order->isTaxStatusManual()) {
            return;
        }

        $tax_status = OrderConst::TAX_STATUS_NORMAL;

        if ($order->getRechnungAddress()->getCountryId() === CountryConst::DE) {
            $tax_status = OrderConst::TAX_STATUS_NORMAL;
        } elseif (
            $order->getRechnungAddress()->getCountry()->isEu() &&
            ($order->getUstIdnr() != '' && $order->getUstIdnrStatus() == 1) &&
            $order->getSpedId() != ShipmentRepository::SPED_ABHOLUNG
        ) {
            $tax_status = OrderConst::TAX_STATUS_IG_LIEFERUNG;
        } elseif (
            !$order->getLieferAddress()->getCountry()->isEu() &&
            !$order->getRechnungAddress()->getCountry()->isEu() &&
            $order->getSpedId() != ShipmentRepository::SPED_ABHOLUNG
        ) {
            $tax_status = OrderConst::TAX_STATUS_IG_AUSFUHRLIEFERUNG;
        }

        $order->setTaxStatus($tax_status);
    }

    /**
     * aktualisiert die mwst für alle noch aktiven aufträge
     *
     * @param int $customer_id
     */
    public static function updateTaxStatusByCustomer(int $customer_id): void
    {
        $result = db::getInstance()->query("
                SELECT
                    orders.order_id
                FROM
                    customers INNER JOIN
                    orders ON (customers.customer_id = orders.customer_id)
                WHERE
                    customers.customer_id = '$customer_id' AND
                    orders.order_aktiv = 1 AND
                    orders.tax_status_manual = 0
        ");

        foreach ($result as $row) {
            $order = service_loader::get(OrderRepository::class)->loadCached($row['order_id']);
            OrderMwst::setTaxStatus($order);
            $order->save();
        }
    }
}
