<?php

namespace wws\Order\Actions\K11OrderSplit\Processor;

use bqp\db\db_generic;
use debug;
use Exception;
use system_protokoll;
use wws\Order\Actions\ActionOrderSplit;
use wws\Order\Actions\K11OrderSplit\ActionSendOrderSplitMail;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;
use wws\Shipment\ShipmentRepository;
use wws\Supplier\SuppliersConst;

class SplitOrdersProcessor
{
    private db_generic $db;
    private system_protokoll $logger;
    private ActionOrderSplit $action_order_split;
    private ActionSendOrderSplitMail $action_send_order_split_mail;

    public function __construct(
        db_generic $db,
        system_protokoll $logger,
        ActionOrderSplit $action_order_split,
        ActionSendOrderSplitMail $action_send_order_split_mail
    ) {
        $this->db = $db;
        $this->logger = $logger;
        $this->action_order_split = $action_order_split;
        $this->action_order_split->addShippingPosition(true);
        $this->action_send_order_split_mail = $action_send_order_split_mail;
    }

    public function process(array $order_ids): void
    {
        foreach ($order_ids as $order_id) {
            $this->processOrderId($order_id);
        }
    }

    public function processOrderId(int $order_id): void
    {
        $order = new Order($order_id);

        $this->action_order_split->setOrderId($order_id);

        $order_items = $order->getOrderItemsByWarenkorbTypes([OrderConst::WARENKORB_TYP_PRODUKT, OrderConst::WARENKORB_TYP_ASWO]);

        $shipping_items = $order->getOrderItemsByWarenkorbTypes([
            OrderConst::WARENKORB_TYP_VERSAND,
            OrderConst::WARENKORB_TYP_VERSANDAUTO
        ]);

        $other_items = $order->getOrderItemsByWarenkorbTypes([OrderConst::WARENKORB_TYP_GUTSCHEIN]);

        $parcel_order_items = [];
        $mail_order_items = [];

        foreach ($order_items as $order_item) {
            $sped_id = (int)$this->db->fieldQuery("
                SELECT
                    einst_shipment_types.sped_id
                FROM
                    product INNER JOIN
                    einst_shipment_types ON (product.versand_id = einst_shipment_types.shipment_type_id)
                WHERE
                    product.product_id = " . $order_item->getProductId() . "
            ");

            if ($sped_id == ShipmentRepository::SPED_POST) {
                $mail_order_items[] = $order_item;
            } else {
                $parcel_order_items[] = $order_item;
            }
        }

        $extension_id = 0;

        //@todo Paket/Post?! -> nach Ausgangslager muss getrennt werden, und bei unseren Lager ggf. nach Sped

        foreach ($this->splitOrderItemsBySupplier($parcel_order_items) as $order_items_by_supplier) {
            $extension_id++;
            foreach ($order_items_by_supplier as $order_item_by_supplier) {
                $this->action_order_split->addMapping($order_item_by_supplier->getOrderItemId(), $extension_id);
            }
        }

        foreach ($this->splitOrderItemsBySupplier($mail_order_items) as $order_items_by_supplier) {
            $extension_id++;
            foreach ($order_items_by_supplier as $order_item_by_supplier) {
                $this->action_order_split->addMapping($order_item_by_supplier->getOrderItemId(), $extension_id);
            }
        }

        foreach ($shipping_items as $shipping_item) {
            $this->action_order_split->addMapping($shipping_item->getOrderItemId(), 1);
        }

        foreach ($other_items as $other_item) {
            $this->action_order_split->addMapping($other_item->getOrderItemId(), $extension_id);
        }

        if (!$this->action_order_split->check()) {
            $this->logger->info(sprintf(
                'Der Auftrag %s konnte nicht geteilt werden: %s',
                $order_id,
                $this->action_order_split->getReason()
            ));
            return;
        }

        try {
            $org_order_id = $this->action_order_split->execute();
        } catch (Exception $e) {
            debug::dumpException($e);
            return;
        }

        $this->logger->info(sprintf('Der Auftrag wurde geteilt: %s %s', $order_id, $order->getAuftnr()));


        $order_ids = $this->db->prepare('
            SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.org_order_id = :org_order_id
        ')->execute([$org_order_id])->asSingleArray();

        $orders = [];
        foreach ($order_ids as $child_order_id) {
            $child_order = new Order($child_order_id);
            $child_order->autoDetermineSped();

            //voerst die executed flag hier wieder rauskicken, damit die automatik wieder drauf anspringt
            //@todo diese ganze trennen logik vor die leerfeld automatik setzen (dafür aber die entscheidung ob getrennt wird von sql nach php ziehen und ggf. hier integrieren)
            $child_order->removeOrderTag(OrderConst::TAG_AB_LEERFELD_AUTOMATIC_EXECUTED);

            $child_order->save();

            $orders[] = $child_order;
        }

        $this->action_send_order_split_mail->sendOrderSplitMail($order_id, $orders);
    }

    /**
     * @param OrderItem[] $order_items
     * @return array
     */
    private function splitOrderItemsBySupplier(array $order_items): array
    {
        $order_items_by_supplier = [];

        foreach ($order_items as $order_item) {
            $grossist = $order_item->getSupplierId();

            if (in_array($order_item->getSupplierId(), [SuppliersConst::SUPPLIER_ID_LAGER_K11, SuppliersConst::SUPPLIER_ID_LAGER_ECOM])) {
                $grossist = 'k11';
            }

            if (!array_key_exists($grossist, $order_items_by_supplier)) {
                $order_items_by_supplier[$grossist] = [];
            }
            $order_items_by_supplier[$grossist][] = $order_item;
        }

        return $order_items_by_supplier;
    }
}
