<?php

namespace wws\Order\Actions\K11OrderSplit;

use Exception;
use League\Flysystem\FileNotFoundException;
use order_repository;
use wws\Mails\Mail;
use wws\Mails\MailHelperOrder;
use wws\Order\Order;

class ActionSendOrderSplitMail
{
    /**
     * @param int $org_order_id
     * @param Order[] $child_orders
     * @return bool
     * @throws FileNotFoundException
     * @throws Exception|FileNotFoundException
     */
    public function sendOrderSplitMail(int $org_order_id, array $child_orders): bool
    {
        $auftnr = order_repository::getBaseAuftnr(
            order_repository::getAuftnrByOrderId($org_order_id)
        );

        $mail = new Mail();
        $mail->setOrderId($org_order_id);
        $mail->loadVorlage('order_split');
        $mail->assign('org_auftnr', $auftnr);

        $splitted_orders_text = [];
        foreach ($child_orders as $child_order) {
            $mail_helper = new MailHelperOrder($child_order);

            $splitted_orders_text[] = sprintf(
                "Auftrag %s\n\n%s",
                $child_order->getAuftnr(),
                $mail_helper->getListSimple()
            );
        }

        $mail->assign('splited_orders', implode("\n\n", $splitted_orders_text));
        $mail->parse();

        return $mail->send();
    }
}
