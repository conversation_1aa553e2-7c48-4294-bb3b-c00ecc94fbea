<?php

namespace wws\Order\Actions\K11OrderSplit\DataProvider;

use bqp\db\db_generic;
use wws\business_structure\Shop;
use wws\Country\CountryConst;
use wws\Order\OrderConst;
use wws\Shipment\ShipmentRepository;
use wws\Supplier\SuppliersConst;

class OrdersToSplitDataProvider
{
    public function getOrderIdsToSplit(db_generic $db): array
    {

        //@see https://allego.myjetbrains.com/youtrack/issue/WWS-1698/

        $order_ids_statement = $db->prepare("
                SELECT
                    orders.order_id
                FROM
                    orders INNER JOIN
                    customers ON (orders.customer_id = customers.customer_id) INNER JOIN
                    order_item AS oi1 ON (orders.order_id = oi1.order_id AND oi1.status = " . OrderConst::STATUS_AB_LEERFELD . " AND oi1.supplier_id IN(" . SuppliersConst::SUPPLIER_ID_LAGER_K11 . ", " . SuppliersConst::SUPPLIER_ID_LAGER_ECOM . ")) INNER JOIN
                    product ON (oi1.product_id = product.product_id) INNER JOIN
                    einst_shipment_types ON (product.versand_id = einst_shipment_types.shipment_type_id AND einst_shipment_types.sped_id = " . ShipmentRepository::SPED_POST . ") INNER JOIN
                    order_item AS oi2 ON (orders.order_id = oi2.order_id AND oi2.status = " . OrderConst::STATUS_AB_LEERFELD . " AND oi2.supplier_id IN (" . SuppliersConst::SUPPLIER_ID_KREMPEL . ", " . SuppliersConst::SUPPLIER_ID_KREMPEL_BSH . "))
                WHERE
                    customers.country_id != " . CountryConst::COUNTRY_ID_AT . " AND
                    customers.land2 != " . CountryConst::COUNTRY_ID_AT . " AND
                    orders.shop_id = " . Shop::ERSATZTEILSHOP . " AND
                    orders.added > DATE_SUB(NOW(), INTERVAL 2 MONTH) AND (
                        (
                            orders.zahlungs_id = " . OrderConst::PAYMENT_KREDITKARTE . " AND
                            orders.zahlungs_status = " . OrderConst::PAYMENT_STATUS_KREDITKARTE_BOOKED . "
                        ) OR (
                            orders.zahlungs_id != " . OrderConst::PAYMENT_KREDITKARTE . " AND
                            orders.zahlungs_status != " . OrderConst::PAYMENT_STATUS_LEGACY_0 . "
                        )
                    ) AND
                    orders.order_origin_id = '" . OrderConst::ORDER_ORIGIN_SHOPWARE . "'
                GROUP BY
                    orders.order_id
            UNION
                SELECT
                    orders.order_id
                FROM
                    orders INNER JOIN
                    customers ON (orders.customer_id = customers.customer_id) INNER JOIN
                    order_item AS oi3 ON (orders.order_id = oi3.order_id AND oi3.status IN(" . OrderConst::STATUS_WARTE_AUSLIEFERUNG . "," . OrderConst::STATUS_AB_LEERFELD . ") AND oi3.supplier_id IN (" . SuppliersConst::SUPPLIER_ID_EURAS . ")) LEFT JOIN
                    order_item AS oi1 ON (orders.order_id = oi1.order_id AND oi1.status IN(" . OrderConst::STATUS_WARTE_AUSLIEFERUNG . "," . OrderConst::STATUS_AB_LEERFELD . ") AND oi1.supplier_id IN(" . SuppliersConst::SUPPLIER_ID_LAGER_K11 . ", " . SuppliersConst::SUPPLIER_ID_LAGER_ECOM . ")) LEFT JOIN
                    order_item AS oi2 ON (orders.order_id = oi2.order_id AND oi2.status IN(" . OrderConst::STATUS_WARTE_AUSLIEFERUNG . "," . OrderConst::STATUS_AB_LEERFELD . ") AND oi2.supplier_id IN (" . SuppliersConst::SUPPLIER_ID_KREMPEL . ", " . SuppliersConst::SUPPLIER_ID_KREMPEL_BSH . "))
                WHERE
                    (
                        oi1.order_item_id IS NOT NULL OR
                        oi2.order_item_id IS NOT NULL
                    ) AND
                    orders.shop_id = " . Shop::ERSATZTEILSHOP . " AND
                    orders.added > DATE_SUB(NOW(), INTERVAL 2 MONTH) AND (
                        (
                            orders.zahlungs_id = " . OrderConst::PAYMENT_KREDITKARTE . " AND
                            orders.zahlungs_status = " . OrderConst::PAYMENT_STATUS_KREDITKARTE_BOOKED . "                        
                        ) OR (
                            orders.zahlungs_id != " . OrderConst::PAYMENT_KREDITKARTE . " AND
                            orders.zahlungs_status != " . OrderConst::PAYMENT_STATUS_LEGACY_0 . "
                        )
                    ) AND
                    orders.order_origin_id = '" . OrderConst::ORDER_ORIGIN_SHOPWARE . "'
                GROUP BY
                    orders.order_id
        ");

        $order_ids = $order_ids_statement->execute()->asSingleArray();

        return array_unique($order_ids);
    }
}
