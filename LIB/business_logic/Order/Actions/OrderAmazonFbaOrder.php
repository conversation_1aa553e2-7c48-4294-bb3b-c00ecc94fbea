<?php

namespace wws\Order\Actions;

use system_protokoll;
use wws\Lager\WarenausgangLieferscheinUtils;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderMemo;
use wws\Shipment\ShipmentRepository;

class OrderAmazonFbaOrder
{
    private system_protokoll $protokoll;
    private int $fba_lager_id;

    public function __construct()
    {
        $this->protokoll = system_protokoll::getInstance('amazon_fba');
    }

    public function setFBALagerId(int $fba_lager_id): void
    {
        $this->fba_lager_id = $fba_lager_id;
    }

    public function processOrder(Order $order): void
    {
        $order->addOrderTag(OrderConst::TAG_AMAZON_FBA);
        $order->save();

        $order = new Order($order->getOrderId()); //es gibt ein bug bei switchen des lagers -> der führt dazu das der freie bestand nicht korrekt berechnet wird

        $this->protokoll->winfo('neue FBA Bestellung ' . $order->getAuftnr() . ' (FBA Lager ' . $this->fba_lager_id . ')', '', $order->getOrderId());

        $order->setLagerId($this->fba_lager_id);
        $order->setSpedId(ShipmentRepository::SPED_AMAZON_FBA);

        $order->addOrderMemo(new OrderMemo('Amazon Fulfillment', OrderMemo::VERSAND));

        $order->setStatus(OrderConst::STATUS_WARTE_AUSLIEFERUNG);

        $order->save();
        $order->createInvoice();
        $order->mailInvoice();
        $order->save();

        //lager muss auf negative bestände erlaubt stehen -> ansonsten kann es hier zu problemen kommen
        $lieferschein = WarenausgangLieferscheinUtils::createWarenausgangLieferscheinByOrder($order);
        WarenausgangLieferscheinUtils::erledigt($lieferschein->getLieferscheinId());
    }
}
