<?php

namespace wws\Order\Actions;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Utils\StringUtils;
use DateTime;
use db;
use env;
use wws\business_structure\Shop;
use wws\Mails\Mail;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItemEstimatedDelivery;
use wws\Order\OrderMemo;
use wws\Order\OrderPaymentReceipt;
use wws\Product\ProductRepositoryLegacy;
use wws\Shipment\ShipmentRepository;

class OrderPaymentAction
{
    private int $user_id;
    private db_generic $db;

    public function __construct()
    {
        $this->user_id = env::getUserId();
        $this->db = db::getInstance();
    }


    public function addPaymentReceiptToOrder(OrderPaymentReceipt $payment, Order $order): void
    {
        $zahlungs_id = $payment->getZahlungsId();

        if (!$zahlungs_id) {
            $zahlungs_id = $order->getZahlungsart();
        }

        $recalc_estimated_delivery = false;
        $mail_vorlage_zahlungseingang = false;
        $set_order_status = false;

        switch ($zahlungs_id) {
            case OrderConst::PAYMENT_VORKASSE:
                if ($payment->isComplete()) {
                    $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_VORKASSE_RECEIVED);

                    $recalc_estimated_delivery = true;

                    $order->addOrderMemo(new OrderMemo('ZE ' . StringUtils::formatPrice($payment->getAmount()) . ' (Forderung komplett ' . StringUtils::formatPrice($order->getRechnungsBetrag()) . ')', OrderMemo::BUCHHALTUNG));

                    $set_order_status = true;

                    $mail_vorlage_zahlungseingang = 'zahlungseingang';

                    if ($order->getSpedId() == ShipmentRepository::SPED_ABHOLUNG) {
                        $mail_vorlage_zahlungseingang = 'zahlungseingang_abholer';
                    }
                } else {
                    $order->setVorkassenBetrag($order->getVorkassenBetrag() + $payment->getAmount());
                    $order->addOrderMemo(new OrderMemo('ZE ' . StringUtils::formatPrice($payment->getAmount()) . ' noch offen ' . StringUtils::formatPrice($order->getRechnungsBetrag() - $order->getVorkassenBetrag()), OrderMemo::BUCHHALTUNG));
                }

                if ($payment->isMailPaymentReceiptPartial()) {
                    $mail = new Mail();
                    $mail->setOrder($order);
                    $mail->loadVorlage('payment_receipt_partial_3');
                    $mail->assignArray([
                        'vorkassenbetrag_eingang' => StringUtils::formatPrice($payment->getAmount()),
                        'vorkassenbetrag_differenz_offen' => StringUtils::formatPrice($order->getRechnungsBetrag() - $order->getVorkassenBetrag())
                    ]);
                    $mail->parse();
                    $mail->send();
                }

                break;
            case OrderConst::PAYMENT_SOFORTUEBERWEISUNG:
                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_SOFORTUEBERWEISUNG_RECEIVED);
                $order->addOrderMemo(new OrderMemo('ZE ' . StringUtils::formatPrice($payment->getAmount()), OrderMemo::BUCHHALTUNG));
                $set_order_status = true;
                break;
            case OrderConst::PAYMENT_DIREKTUEBERWEISUNG:
                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_DIREKTUEBERWEISUNG_RECEIVED);
                $order->addOrderMemo(new OrderMemo('ZE ' . StringUtils::formatPrice($payment->getAmount()), OrderMemo::BUCHHALTUNG));
                $set_order_status = true;
                break;
            case OrderConst::PAYMENT_FINANZIERUNG_HANSEATIC:
                if (!$payment->isComplete()) {
                    throw new DevException('unvollständige zahlung für diese zahlungsart nicht möglich');
                }

                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_FINANZIERUNG_GENEHMIGT);
                $recalc_estimated_delivery = true;

                $order->addOrderMemo(
                    new OrderMemo(
                        'Kreditantrag wurde von der Hanseatic Bank genehmigt.',
                        OrderMemo::BUCHHALTUNG,
                        OrderMemo::USER_SYSTEM
                    )
                );

                $set_order_status = true;
                break;
            case OrderConst::PAYMENT_FINANZIERUNG_COMMERZ:
                if (!$payment->isComplete()) {
                    throw new DevException('unvollständige zahlungsart für diese zahlung nicht möglich');
                }

                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_FINANZIERUNG_GENEHMIGT);
                $recalc_estimated_delivery = true;

                $order->addOrderMemo(
                    new OrderMemo(
                        'Kreditantrag wurde von der Commerz-Bank genehmigt.',
                        OrderMemo::BUCHHALTUNG,
                        OrderMemo::USER_SYSTEM
                    )
                );

                $set_order_status = true;
                break;
            case OrderConst::PAYMENT_PAYPAL:
                if (!$payment->isComplete()) {
                    throw new DevException('unvollständige Zahlungsart für diese Zahlung nicht möglich');
                }

                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_PAYPAL_BOOKED);
                $order->addOrderMemo(
                    new OrderMemo(
                        'Zahlung per Paypal eingegangen. Betrag: ' . StringUtils::formatPrice($payment->getAmount()),
                        OrderMemo::BUCHHALTUNG,
                        OrderMemo::USER_SYSTEM
                    )
                );

                $set_order_status = true;
                break;
            case OrderConst::PAYMENT_KREDITKARTE:
                $order->addOrderMemo(
                    new OrderMemo(
                        StringUtils::formatPrice($payment->getAmount()) . ' am ' . $payment->getDatum()->format(DateObj::DE_DATE) . ' per Kreditkarte abgebucht.',
                        OrderMemo::BUCHHALTUNG
                    )
                );
                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_KREDITKARTE_BOOKED);

                if ($order->getShop()->getShopId() === Shop::ERSATZTEILSHOP) {
                    $mail_vorlage_zahlungseingang = 'zahlungseingang';

                    if ($order->getSpedId() == ShipmentRepository::SPED_ABHOLUNG) {
                        $mail_vorlage_zahlungseingang = 'zahlungseingang_abholer';
                    }
                }
                $set_order_status = true;
                break;
            case OrderConst::PAYMENT_ECOM_UNZER:
                $order->addOrderMemo(
                    new OrderMemo(
                        StringUtils::formatPrice($payment->getAmount()) . ' am ' . $payment->getDatum()->format(DateObj::DE_DATE) . ' per Unzer/Kreditkarte gebucht.',
                        OrderMemo::BUCHHALTUNG
                    )
                );
                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_UNZER_BOOKED);
                $set_order_status = true;
                break;
            case OrderConst::PAYMENT_BAR:
                $order->addOrderMemo(
                    new OrderMemo(
                        'Kunde am ' . $payment->getDatum()->format(DateObj::DE_DATE) . ' ' . StringUtils::formatPrice($payment->getAmount()) . ' barbezahlt.',
                        OrderMemo::BUCHHALTUNG
                    )
                );
                break;
            case OrderConst::PAYMENT_BARZAHLEN:
                $order->setZahlungsartStatus(OrderConst::PAYMENT_STATUS_BARZAHLEN_FREIGEGEBEN);

                $set_order_status = true;
                $recalc_estimated_delivery = true;

                $order->addOrderMemo(
                    new OrderMemo(
                        'Barzahlen Zahlungsbestätigung ' . $payment->getDatum()->format(DateObj::DE_DATE) . ' ' . StringUtils::formatPrice($payment->getAmount()) . '.',
                        OrderMemo::BUCHHALTUNG
                    )
                );
                break;
            case OrderConst::PAYMENT_RECHNUNG:
            case OrderConst::PAYMENT_VERRECHNUNG:
            default:
                if ($payment->isComplete()) {
                    $order->setPayed(1);
                }
                break;
        }

        $this->db->query("
            INSERT INTO
                protokoll_zahlung
            SET
                protokoll_zahlung.user_id = '" . $this->user_id . "',
                protokoll_zahlung.bardatum = '" . $payment->getDatum()->db() . "',
                protokoll_zahlung.barbetrag = '" . $payment->getAmount() . "',
                protokoll_zahlung.order_id = '" . $order->getOrderId() . "',
                protokoll_zahlung.zahlungsart = '" . $zahlungs_id . "'
        ");

        if ($payment->getBemerkung()) {
            $order->addOrderMemo(new OrderMemo("Bemerkung zum ZE: \n\n" . $payment->getBemerkung(), OrderMemo::BUCHHALTUNG));
        }

        if ($set_order_status && $order->hasStatus(OrderConst::STATUS_WARTE_ZAHLUNG)) {
            $status = OrderConst::STATUS_WARTE_AUSLIEFERUNG;

            $ek_has_changed_to_our_disadvantage = false;

            if ($order->getShopId() === Shop::ERSATZTEILSHOP) {
                $status = OrderConst::STATUS_AB_LEERFELD;
            }

            if ($this->hasEkChangedToOurDisadvantage($order)) {
                $status = OrderConst::STATUS_AB_LEERFELD;
                $ek_has_changed_to_our_disadvantage = true;
            }

            foreach ($order->getOrderItems() as $order_item) {
                if ($order_item->getStatus() === OrderConst::STATUS_WARTE_ZAHLUNG) {
                    $order_item->setStatus($status);
                }
            }

            if ($ek_has_changed_to_our_disadvantage) {
                $order->addOrderMemo(new OrderMemo("Einkaufspreis hat sich zwischenzeitlich zu unseren ungunsten geändert. (Warte auf Zahlung)", OrderMemo::EINKAUF));

                //In die erste Position auch nochmal die Bemerkung hinzufügen
                $order_item = current($order->getOrderItems());

                $memo = $order_item->getGrosMemo();
                if ($memo) {
                    $memo .= ' | ';
                }
                $memo .= "Einkaufspreis hat sich zwischenzeitlich zu unseren ungunsten geändert. (Warte auf Zahlung)";
                $order_item->setGrosMemo($memo);
            }
        }

        if ($recalc_estimated_delivery) {
            $date = $order->getBestellDatum();
            $date->addSimple('days', 1);
            $now = new DateTime();

            if ($now > $date) { //wenn Bestellung innerhalb von 24 Stunden passiert ist keine Lieferziet neu berechnen
                foreach ($order->getOrderItems() as $order_item) {
                    if ($order_item->getLiefertermin() > 0) {
                        $delivery_date = new DateObj();
                        $delivery_date->addWerktage($order_item->getLiefertermin());
                    } else {
                        $delivery_date = new DateObj();
                        $delivery_date->addWerktage(ProductRepositoryLegacy::getLieferbaranzeigeInDays($order->getShopId(), $order_item->getLieferbaranzeige()));
                    }

                    $delivery = new OrderItemEstimatedDelivery();
                    $delivery->setEstimatedDeliveryDate($delivery_date);
                    $delivery->setCustomerInformed(false);
                    $delivery->setNote('Zahlungseingang');

                    $order_item->addEstimatedDelivery($delivery);
                }
            }
        }

        if ($mail_vorlage_zahlungseingang && $payment->isMailPaymentReceipt()) {
            //zahlungseingangs mail senden
            $mail = new Mail();
            $mail->setOrder($order);
            $mail->loadVorlage($mail_vorlage_zahlungseingang);
            $mail->send();
        }
    }


    public function hasEkChangedToOurDisadvantage(Order $order): bool
    {
        $order_items = $order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_PRODUKT);

        foreach ($order_items as $order_item) {
            $ek_netto_order = $order_item->getEkNnnBrutto() / $order_item->getVatRate()->getAsFactor();

            $ek_netto_product = (float)$this->db->fieldQuery("
                SELECT
                    product_ek.ek_netto
                    
                FROM
                    product_ek
                WHERE
                    product_ek.supplier_id = " . $order_item->getSupplierId() . " AND
                    product_ek.product_id = " . $order_item->getProductId() . "
            ");

            $difference = $ek_netto_product - $ek_netto_order;

            if ($difference > 0.5) {
                return true;
            }
        }

        return false;
    }
}
