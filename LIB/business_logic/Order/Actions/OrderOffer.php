<?php

namespace wws\Order\Actions;

use bqp\Date\DateObj;
use bqp\Exceptions\FatalException;
use wws\Order\Order;
use wws\Order\OrderConst;

class OrderOffer
{
    private OrderActionCheck $order_action_check;

    public function __construct(OrderActionCheck $order_action_check)
    {
        $this->order_action_check = $order_action_check;
    }

    public function convertOfferToOrder(Order $order): void
    {
        $this->order_action_check->isOfferConvertable($order)->exception(FatalException::class, 'Angebot lässt sich nicht in eine Bestellung umwandeln. %s');

        foreach ($order->getOrderItems() as $order_item) {
            $status = OrderConst::STATUS_AB_LEERFELD;

            if ($order_item->getStatus() === OrderConst::STATUS_ANGEBOT_STORNIERT) {
                $status = OrderConst::STATUS_STORNO;
            }

            $order_item->setStatus($status);
        }

        $payment_due_date = $order->getVorkassenFrist();

        if ($payment_due_date->diffSimple(new DateObj(), 'days') > -7) {
            $payment_due_date = new DateObj();
            $payment_due_date->addSimple('days', 7);
            $order->setVorkassenFrist($payment_due_date);
        }

        $order->setOrderType(OrderConst::ORDER_TYPE_NORMAL);
        $order->save();
    }

    public function cancelOffer(Order $order): void
    {
        $this->order_action_check->isOfferCancelable($order)->exception(FatalException::class, 'Angebot lässt sich nicht stornieren. %s');

        $order->setStatus(OrderConst::STATUS_ANGEBOT_STORNIERT);
        $order->save();
    }
}
