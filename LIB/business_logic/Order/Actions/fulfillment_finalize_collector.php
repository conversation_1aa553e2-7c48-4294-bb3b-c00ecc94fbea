<?php

namespace wws\Order\Actions;

use bqp\Model\SmartDataEntityNotFoundException;
use db;
use Exception;
use LogicException;
use order_repository;
use wws\Lager\WarenausgangLieferschein;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Supplier\SupplierOrder;

/**
 * Class fulfillment_finalize_collector
 *
 * die Klasse dient zum zusammentragen und prüfen aller auftragsrelevanten Daten
 *
 * @package wws\Order\Actions
 */
class fulfillment_finalize_collector
{
    protected $auftnr;
    protected $auftnr_explicit = false;
    protected $order_id;
    protected $order_id_explicit = false;
    protected $customer_id;
    protected $lieferschein_id;
    protected $lieferschein_id_explicit = false;
    protected $supplier_order_id;
    protected $supplier_order_id_explicit = false;


    protected $tracking = [];

    /**
     * Prüft ob die Lieferaten-Bestellung über einen der folgenden Grossisten ausgeführt wurde.
     *
     * @var array
     */
    protected $restricted_supplier_ids = [];

    /**
     * Sucht zu einer order_id eine eventuell vorhandenen Direkt-Lieferantenbestellung
     * (nur wenn Auftrag noch in Status "Artikel bestellt mit Direktversand" ist)
     *
     * @var bool
     */
    protected $search_open_direct_gros_order = false;

    /**
     * wenn keine Lieferantenbestellung referenziert wird, wird nach einer direkt lieferantenbestellung für diesen Auftrag gesucht.
     * Diese wird nur genommen, wenn es genau eine lieferantenbestellung gibt. ($restricted_supplier_ids wird nicht berücksichtigt)
     *
     * @var bool
     */
    protected $search_all_direct_gros_order = false;

    /**
     * @var Order
     */
    protected $order;

    /**
     * @var SupplierOrder
     */
    protected $supplier_order;

    /**
     * @var WarenausgangLieferschein
     */
    protected $warenausgang_lieferschein;

    /**
     * extrahiert aus einer referenz alle bekannten nummernkreise
     * @param $reference
     */
    public function setOrderlessReference($reference)
    {
        if (preg_match('~\bB([0-9]{5,6})\b~i', $reference, $temp)) {
            $this->setSupplierOrderId($temp[1]);
            $reference = str_replace($temp[0], '', $reference);
        }

        if (preg_match('~\bLID:?([0-9]{5,7})\b~i', $reference, $temp)) {
            $this->setLieferscheinId($temp[1]);
            $reference = str_replace($temp[0], '', $reference);
        } elseif (preg_match('~\bL?([0-9]{5,7})\b~i', $reference, $temp)) {
            $this->setLieferscheinId($temp[1]);
            $reference = str_replace($temp[0], '', $reference);
        }

        $result = order_repository::searchAuftnrCustomerIdInText($reference);

        if ($result['auftnr']) {
            $this->setAuftnr($result['auftnr']);
        }

        if ($result['customer_id']) {
            $this->setCustomerId($result['customer_id']);
        }
    }

    /**
     * @return mixed
     */
    public function getAuftnr()
    {
        return $this->auftnr;
    }

    /**
     * @param mixed $auftnr
     */
    public function setAuftnr($auftnr)
    {
        $this->auftnr = $auftnr;
        $this->auftnr_explicit = true;
    }

    /**
     * @return mixed
     */
    public function getOrderId()
    {
        return $this->order_id;
    }

    /**
     * @param mixed $order_id
     */
    public function setOrderId($order_id)
    {
        $this->order_id = $order_id;
        $this->order_id_explicit = true;
    }

    /**
     * @return mixed
     */
    public function getCustomerId()
    {
        return $this->customer_id;
    }

    /**
     * @param mixed $customer_id
     */
    public function setCustomerId($customer_id)
    {
        $this->customer_id = $customer_id;
    }

    /**
     * @return mixed
     */
    public function getLieferscheinId()
    {
        return $this->lieferschein_id;
    }

    /**
     * @param mixed $lieferschein_id
     */
    public function setLieferscheinId($lieferschein_id)
    {
        $this->lieferschein_id = $lieferschein_id;
        $this->lieferschein_id_explicit = true;
    }

    /**
     * @return mixed
     */
    public function getSupplierOrderId()
    {
        return $this->supplier_order_id;
    }

    /**
     * @param mixed $supplier_order_id
     */
    public function setSupplierOrderId($supplier_order_id)
    {
        $this->supplier_order_id = $supplier_order_id;
        $this->supplier_order_id_explicit = true;
    }


    public function setRestrictedSupplierIds(array $restricted_supplier_ids): void
    {
        $this->restricted_supplier_ids = $restricted_supplier_ids;
    }

    public function addRestrictedSupplierId(int $restricted_supplier_id): void
    {
        $this->restricted_supplier_ids[] = $restricted_supplier_id;
    }


    public function addTracking($spedition, $tracking_id, $datum = null)
    {
        $this->tracking = ['spedition' => $spedition, 'tracking_id' => $tracking_id, 'datum' => $datum];
    }


    /**
     * gibt an ob überhaupt eine referenz gesetzt
     *
     * @return bool
     */
    public function isAnyReferenz()
    {
        return $this->auftnr || $this->order_id || $this->lieferschein_id || $this->supplier_order_id;
    }

    public function setSupplierOrder(SupplierOrder $supplier_order): void
    {
        /*if(!$bestellung->getOrderId()) {
            $e = new fulfillment_finalize_exception("Grossisten Bestellung ist an keinen Auftrag gebunden. (".$bestellung->getBestellNr().")");
            $e->setFulfillmentFinalize($this);
            throw $e;
        }*/

        if ($this->restricted_supplier_ids) {
            if (!in_array($supplier_order->getSupplierId(), $this->restricted_supplier_ids)) {
                $e = new fulfillment_finalize_collector_discrepance('The supplier_order.supplier_id is not included in $restricted_supplier_ids. (' . $supplier_order->getSupplierId() . ')');
                $e->setFulfillmentFinalize($this);
                throw $e;
            }
        }

        if ($this->getOrderId() && $supplier_order->getOrderId()) {
            if ($this->getOrderId() != $supplier_order->getOrderId()) {
                $e = new fulfillment_finalize_collector_discrepance('Missmatch between supplier_order.order_id and order.order_id');
                $e->setFulfillmentFinalize($this);
                throw $e;
            }
        }

        if ($supplier_order->getOrderId()) {
            $this->setOrder(new Order($supplier_order->getOrderId()));
        }

        $this->supplier_order = $supplier_order;
    }

    public function setLieferschein(WarenausgangLieferschein $lieferschein)
    {
        if ($this->getOrderId()) {
            if ($lieferschein->getOrderId() != $this->getOrderId()) {
                $e = new fulfillment_finalize_collector_discrepance('Missmatch between setted order_id and warenausgang_lieferschein.order_id');
                $e->setFulfillmentFinalize($this);
                throw $e;
            }
        }

        $this->warenausgang_lieferschein = $lieferschein;
        if (!$this->order) {
            $this->setOrder(new Order($lieferschein->getOrderId()));
        }
    }

    public function setOrder(Order $order)
    {
        if ($this->getAuftnr()) {
            if ($this->getAuftnr() != $order->getAuftnr()) {
                if (strpos($order->getAuftnr(), $this->getAuftnr()) !== 0) { //wenn wir bei einem geteilen Auftrag die Base Auftnr bekommen, ist das auch OK.
                    $e = new fulfillment_finalize_collector_discrepance('Missmatch between setted auftnr and order.auftnr');
                    $e->setFulfillmentFinalize($this);
                    throw $e;
                }
            }
        }

        if ($this->getCustomerId()) {
            if ($this->getCustomerId() != $order->getCustomerId()) {
                $e = new fulfillment_finalize_collector_discrepance('Missmatch between setted customer_id and order.customer_id');
                $e->setFulfillmentFinalize($this);
                throw $e;
            }
        }

        $this->customer_id = $order->getCustomerId();
        $this->auftnr = $order->getAuftnr();
        $this->order_id = $order->getOrderId();

        $this->order = $order;
    }

    /**
     * lädt anhand der gesetzten Informationen alle weiteren Informationen und prüft diese wenn möglich auf widersprüche
     * @throws Exception
     * @throws fulfillment_finalize_collector_discrepance
     */
    public function fill()
    {
        $order = null;

        try {
            if ($this->getOrderId()) {
                $order = new Order($this->getOrderId());
            } elseif ($this->getAuftnr() && !$this->getOrderId()) {
                $order = order_repository::getOrderByAuftnr($this->getAuftnr());
            }
        } catch (SmartDataEntityNotFoundException $src_e) {
            if (!$this->isSupplierOrderWithSplitedOrderBaseAuftnr()) {
                $e = new fulfillment_finalize_collector_discrepance('Auftrag nicht gefunden (' . $src_e->getMessage() . ')');
                $e->setFulfillmentFinalize($this);
                throw $e;
            }
        }

        if ($order) {
            $this->setOrder($order);
        }

        if ($order && !$this->getSupplierOrderId() && $this->search_open_direct_gros_order) {
            if ($order->getMinStatus() == OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND) {
                $db = db::getInstance();

                $where = '';

                if ($this->restricted_supplier_ids) {
                    $where .= ' AND supplier_order.supplier_id IN (' . $db->in($this->restricted_supplier_ids) . ') ';
                }

                $supplier_order_id = $db->fieldQuery("
                    SELECT
                        supplier_order.supplier_order_id
                    FROM
                        supplier_order
                    WHERE
                        supplier_order.order_id = '" . $order->getOrderId() . "' AND
                        supplier_order.lager_id = 0 AND
                        supplier_order.status IN ('" . SupplierOrder::STATUS_BESTELLT . "')
                        $where
                ");

                if ($supplier_order_id) {
                    $this->supplier_order_id = $supplier_order_id;
                }
            }
        }

        if ($order && !$this->supplier_order_id && $this->search_all_direct_gros_order) {
            $db = db::getInstance();

            $supplier_order_ids = $db->query("
                SELECT
                    supplier_order.supplier_order_id
                FROM
                    supplier_order
                WHERE
                    supplier_order.order_id = '" . $order->getOrderId() . "' AND
                    supplier_order.lager_id = 0
            ")->asSingleArray();

            if (count($supplier_order_ids) === 1) {
                $this->supplier_order_id = current($supplier_order_ids);
            }
        }

        if ($this->getSupplierOrderId()) {
            $supplier_order = new SupplierOrder($this->getSupplierOrderId());
            $this->setSupplierOrder($supplier_order);
        }

        if ($this->getLieferscheinId()) {
            $lieferschein_id = $this->getLieferscheinId();

            $lieferschein = new WarenausgangLieferschein($lieferschein_id);

            $this->setLieferschein($lieferschein);
        }
    }

    public function getAsText()
    {
        $text = '';

        $text .= 'auftnr: ' . $this->getAuftnr();
        if ($this->auftnr_explicit) {
            $text .= ' (explizit)';
        }
        $text .= "\n";

        $text .= 'order_id: ' . $this->getOrderId();
        if ($this->order_id_explicit) {
            $text .= ' (explizit)';
        }
        $text .= "\n";

        $text .= 'customer_id: ' . $this->getCustomerId() . "\n";


        $text .= 'lieferschein_id: ' . $this->getLieferscheinId();
        if ($this->lieferschein_id_explicit) {
            $text .= ' (explizit)';
        }
        $text .= "\n";

        $text .= 'supplier_order_id: ' . $this->getSupplierOrderId();
        if ($this->supplier_order_id_explicit) {
            $text .= ' (explizit)';
        }

        return $text;
    }

    /**
     * @return bool
     */
    public function isSupplierOrder(): bool
    {
        return (bool)$this->supplier_order;
    }

    /**
     * @return SupplierOrder
     */
    public function getSupplierOrder()
    {
        if (!$this->supplier_order) {
            throw new LogicException('no supplier_order found -> use isSupplierOrder() to check');
        }

        return $this->supplier_order;
    }

    /**
     * @return bool
     */
    public function isOrder()
    {
        return (bool)$this->order;
    }

    /**
     * @return Order
     */
    public function getOrder()
    {
        if (!$this->order) {
            throw new LogicException('no order found -> use isOrder() to check');
        }

        return $this->order;
    }


    /**
     * @param bool $search_open_direct_gros_order
     */
    public function setSearchOpenDirectGrosOrder(bool $search_open_direct_gros_order): void
    {
        $this->search_open_direct_gros_order = $search_open_direct_gros_order;
    }

    /**
     * @param bool $search_closed_direct_gros_order
     * @see self::$search_closed_direct_gros_order
     */
    public function setSearchAllDirectGrosOrder(bool $search_closed_direct_gros_order): void
    {
        $this->search_all_direct_gros_order = $search_closed_direct_gros_order;
    }


    /**
     * @return bool
     */
    private function isSupplierOrderWithSplitedOrderBaseAuftnr(): bool
    {
        if (!$this->supplier_order_id_explicit) {
            return false;
        }
        if (!$this->auftnr_explicit) {
            return false;
        }

        $db = db::getInstance();

        $org_auftnr = $db->fieldQuery("
            SELECT
                orders.auftnr
            FROM
                supplier_order INNER JOIN
                orders ON (supplier_order.order_id = orders.order_id)
            WHERE
                supplier_order.supplier_order_id = '" . $db->escape($this->supplier_order_id) . "'
        ");

        if (!$org_auftnr) {
            return false;
        }

        return strpos($org_auftnr, $this->auftnr) === 0;
    }
}
