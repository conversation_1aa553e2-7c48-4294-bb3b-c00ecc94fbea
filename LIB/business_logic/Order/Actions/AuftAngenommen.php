<?php

namespace wws\Order\Actions;

use config;
use env;
use service_loader;
use wws\business_structure\Shop;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderToMemo;

class AuftAngenommen
{
    private Order $order;

    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }

    public function execute(): void
    {
        if ($this->order->getKundenBemerkungRelevanz() === 1) {
            $bemerkung = $this->order->getKundenBemerkung();
            foreach ($this->order->getOrderItems() as $order_item) {
                $order_item->appendGrosMemo('Bemerkung des Kunden: ' . $bemerkung);
            }
        }

        //bearbeiter speichern
        $this->order->setUserId(env::getUserId());

        //memo
        $order_to_memo = service_loader::getDiContainer()->get(OrderToMemo::class);
        $order_to_memo->addOrderToOrderMemo($this->order);

        //auftrag weiterschalten
        foreach ($this->order->getOrderItems() as $order_item) {
            if ($this->order->getShopId() === Shop::ALLEGO) {
                $order_item->setStatus(OrderConst::STATUS_AB_LEERFELD);

                if (!$order_item->isDeliveryAble()) {
                    $order_item->setStatus(OrderConst::STATUS_WARTE_AUSLIEFERUNG);
                    $order_item->setLiefertermin(config::gs_lieferzeiten('sofort'));
                }
            }

            if ($this->order->getShopId() === Shop::ERSATZTEILSHOP) {
                $new_order_item_status = $this->order->getStatusForWarteAuslieferung();
                if ($new_order_item_status === OrderConst::STATUS_WARTE_AUSLIEFERUNG) {
                    $new_order_item_status = OrderConst::STATUS_AB_LEERFELD;
                }
                $order_item->setStatus($new_order_item_status);
            }
        }

        $this->order->save();
    }
}
