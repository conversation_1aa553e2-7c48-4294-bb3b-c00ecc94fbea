<?php

namespace wws\Order\Actions;

use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use order_repository;
use wws\Customer\Customer;
use wws\Customer\CustomerMemo;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Shipment\RateCalculator\InvalidShipmentGroupException;
use wws\Shipment\RateCalculator\NoShippableProductsException;
use wws\Shipment\ShipmentRepository;

class ActionOrderSplit
{
    private db_generic $db;
    private array $mapping = [];
    private int $order_id;
    private string $auftnr;
    private int $customer_id;

    /**
     * Beinhaltet den Grund warum ein Auftrag nicht teilbar ist. Wird befüllt durch isSplittable()
     */
    private string $splittable_message = '';

    /**
     * Grund warum das Splitten fehlgeschlagen ist.
     */
    private string $reason = '';

    private bool $add_shipping_position = false;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function setOrderId(int $order_id): void
    {
        $this->order_id = $order_id;

        $row = $this->db->singleQuery("
            SELECT
                orders.customer_id,
                orders.auftnr
            FROM
                orders
            WHERE
                orders.order_id = '$this->order_id'
        ");

        $this->auftnr = $row['auftnr'];
        $this->customer_id = (int)$row['customer_id'];

        $this->mapping = [];
    }

    public function addShippingPosition(bool $add_shipping_position): void
    {
        $this->add_shipping_position = $add_shipping_position;
    }

    public function isSplittable(): bool
    {
        $this->splittable_message = '';

        $daten = $this->db->singleQuery("
            SELECT
                einst_zahlungsarten.beschreibung,
                einst_zahlungsarten.order_is_splittable,
                einst_zahlungsarten.order_is_splittable_hint,
                orders.rechnungs_id
            FROM
                orders INNER JOIN
                einst_zahlungsarten ON (orders.zahlungs_id = einst_zahlungsarten.zahlungs_id)
            WHERE
                orders.order_id = " . (int)$this->order_id . "
        ");

        if ($daten['rechnungs_id']) {
            $this->splittable_message .= 'Rechnung für Auftrag bereits gelegt.';
            return false;
        }

        if ($daten['order_is_splittable']) {
            return true;
        }

        $this->splittable_message = '<b>Zahlungsart:</b> ' . $daten['beschreibung'];
        if ($daten['order_is_splittable_hint']) {
            $this->splittable_message .= '<br>Hinweis: <i>' . $daten['order_is_splittable_hint'] . '</i>';
        }

        return false;
    }

    public function getSplittableMessage(): string
    {
        return $this->splittable_message;
    }


    public function getPossibleAuftnrExtensions(): array
    {
        $auftnr_base = order_repository::getBaseAuftnr($this->auftnr);

        $auftnr_ext = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
        unset($auftnr_ext[0]); //damit der index mit dem wert übereinstimmt

        $auftnr_result = $this->db->query("
            SELECT
                DISTINCT order_item.auftnr
            FROM
                orders INNER JOIN
                order_item ON (order_item.order_id = orders.order_id)
            WHERE
                order_item.auftnr LIKE '" . $auftnr_base . "-%' AND
                order_item.auftnr != '" . $this->auftnr . "' AND
                orders.customer_id = '" . $this->customer_id . "'
        ")->asSingleArray();

        foreach ($auftnr_result as $auftnr) {
            $extension = $this->extractAuftnrExtension($auftnr);
            if ($extension) {
                unset($auftnr_ext[$extension]);
            }
        }

        return $auftnr_ext;
    }


    private function extractAuftnrExtension(string $auftnr): ?int
    {
        $parts = explode('-', $auftnr);

        if (isset($parts[1])) {
            return (int)$parts[1];
        }

        return null;
    }

    public function addMapping(int $order_item_id, int $extension): void
    {
        $this->mapping[$order_item_id] = $extension;
    }

    private function getMappingGrouped(): array
    {
        $result = [];

        foreach ($this->mapping as $order_item_id => $extension) {
            if (!isset($result[$extension])) {
                $result[$extension] = [];
            }

            $result[$extension][] = $order_item_id;
        }

        return $result;
    }

    private function getNewOrderMap(): array
    {
        $base_auftnr = order_repository::getBaseAuftnr($this->auftnr);

        $order_map = [];

        foreach ($this->mapping as $order_item_id => $extension) {
            if (!isset($order_map[$extension])) {
                $order_map[$extension] = [
                    'auftnr_new' => $base_auftnr . '-' . $extension,
                    'extension' => $extension,
                    'preserve_order_id' => false,
                    'order_item_ids' => []
                ];
            }

            $order_map[$extension]['order_item_ids'][] = $order_item_id;
        }

        //preserve_order_id setzen
        $primary_extension = $this->calcPrimaryExtension($order_map);
        $order_map[$primary_extension]['preserve_order_id'] = true;

        return $order_map;
    }

    /**
     * Beim Teilen muss die originale $order_id erhalten bleiben.
     * Diese Funktion bestimmt welche Extension die original $order_id behält.
     *
     * @param array $order_map
     * @return int
     */
    private function calcPrimaryExtension(array $order_map): int
    {
        //preserve_order_id die meisten order_item_ids, und dann ggf. die kleinste extension
        usort($order_map, function (array $a, array $b) {
            return [count($b['order_item_ids']), -$b['extension']] <=> [count($a['order_item_ids']), -$a['extension']];
        });
        return $order_map[0]['extension'];
    }

    public function check(): bool
    {
        if (count($this->getMappingGrouped()) <= 1) {
            $this->reason = 'Es wurden keine Bestellpositionen zum teilen angegeben.';
            return false;
        }

        $order = new Order($this->order_id);
        if ($order->getLieferAddress()->getVorname() === 'MeinEinkauf') { //k11
            $this->reason = 'MeinEinkauf Aufträge dürfen nicht geteilt werden.';
            return false;
        }

        if ($order->getMaxStatus() >= OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST) {
            $this->reason = 'Dieser Auftrag ist bereits ausgelöst wurden.';
            return false;
        }

        if ($order->getMaxStatus() == OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND) {
            $this->reason = 'Dieser Auftrag ist bereits ausgelöst wurden.';
            return false;
        }

        $order_item_ids = [];
        foreach ($order->getOrderItems() as $order_item) {
            $order_item_ids[$order_item->getOrderItemId()] = $order_item->getOrderItemId();
        }

        //schauen ob order_item_ids vollständig und ob überhaupt etwas zu tun ist
        foreach ($this->mapping as $order_item_id => $extension) {
            if (!isset($order_item_ids[$order_item_id])) {
                return false;
            }

            unset($order_item_ids[$order_item_id]);
        }

        if (count($order_item_ids) > 0) {
            $this->reason = 'INTERN: In diesem Auftrag sind weitere Positionen vorhanden.';
            return false;
        }

        return true;
    }

    public function getReason(): string
    {
        return $this->reason ?? '';
    }


    /**
     * teilt den Auftrag
     */
    public function execute(): int
    {
        if (!$this->check()) {
            throw new FatalException('Dieser Auftrag ist nicht teilbar.');
        }

        $this->db->begin();

        $auftrags_daten = $this->db->singleQuery("
                SELECT
                    *
                FROM
                    orders
                WHERE
                    orders.order_id = '" . $this->order_id . "'
        ");

        $order_ids = [$this->order_id];

        $order_sql = [];
        foreach ($auftrags_daten as $field => $value) {
            if ($field === 'id' || $field === 'order_id') {
                continue;
            }

            $order_sql[$field] = "orders.`$field` = " . $this->db->quote($value);
        }

        $order_map = $this->getNewOrderMap();

        foreach ($order_map as $order_part) {
            $auftnr_new = $order_part['auftnr_new'];

            if ($order_part['preserve_order_id']) {
                $this->db->query("
                    UPDATE
                        order_item INNER JOIN
                        orders ON (order_item.order_id = orders.order_id)
                    SET
                        orders.auftnr = '" . $auftnr_new . "',
                        order_item.auftnr = '" . $auftnr_new . "'
                    WHERE
                        order_item.order_item_id IN (" . $this->db->in($order_part['order_item_ids']) . ")
                ");
            } else {
                //neuer eintrag in orders erzeugen
                $order_sql['auftnr'] = "orders.auftnr = '" . $auftnr_new . "'";

                $this->db->query("INSERT INTO orders SET " . implode(',', $order_sql));

                $order_id = $this->db->insert_id();

                $order_ids[] = $order_id;

                //auftrag updaten
                $this->db->query("
                    UPDATE
                        order_item
                    SET
                        order_item.auftnr = '" . $auftnr_new . "',
                        order_item.order_id = '$order_id'
                    WHERE
                        order_item.order_item_id IN (" . $this->db->in($order_part['order_item_ids']) . ")
                ");

                //lieferadresse kopieren
                $addresses = $this->db->query("
                    SELECT
                        *
                    FROM
                        order_addresses
                    WHERE
                        order_addresses.order_id = '$auftrags_daten[order_id]'
                ");

                foreach ($addresses as $address) {
                    unset($address['order_id']);
                    $address['order_id'] = $order_id;

                    $sql = [];

                    foreach ($address as $key => $value) {
                        $sql[] = $this->db->quoteIdentifier($key) . ' = ' . $this->db->quote($value);
                    }

                    $this->db->query("INSERT INTO order_addresses SET " . implode(',', $sql));
                }
            }
        }

        if ($this->add_shipping_position) {
            $this->doAddShippingPosition($order_ids);
        }

        $this->db->commit();

        $this->createCustomerMemoEntry($order_map);

        return $auftrags_daten['org_order_id'];
    }

    private function createCustomerMemoEntry(array $order_map): void
    {
        $auftnrs_new = array_column($order_map, 'auftnr_new');

        $message = "Auftrag " . $this->auftnr . " geteilt. ";
        $message .= '(' . implode(', ', $auftnrs_new) . ')';

        $customer = new Customer($this->customer_id);
        $customer->addMemo(new CustomerMemo($message));
        $customer->save();
    }

    /**
     * @param array $order_ids
     * @return void
     * @throws InvalidShipmentGroupException
     */
    private function doAddShippingPosition(array $order_ids): void
    {
        foreach ($order_ids as $order_id) {
            $order = new Order($order_id);
            $order_item = $order->getVersandItem(ShipmentRepository::VERSAND);

            if ($order_item->getOrderItemId() <= 0) {
                //neue Position
                $order_item->setProductName('Standardversand');
                $order_item->setVkBrutto(0);
                $order_item->setEkBrutto(0);
                $order_item->setEkNnnBrutto(0);
            }

            if ($order_item->getWarenkorbType() === OrderConst::WARENKORB_TYP_VERSANDAUTO) {
                //wir kalkulieren die kosten für die position neu... -> ohne die spedi neu zu bestimmen, aber auch nicht ganz so sinnvoll.
                try {
                    $result = $order->getShipmentCalculator()->getShipmentPositions();

                    if (isset($result[ShipmentRepository::VERSAND])) {
                        $order_item->setEkBrutto($result[ShipmentRepository::VERSAND]->getEkBrutto());
                        $order_item->setEkNnnBrutto($result[ShipmentRepository::VERSAND]->getEkBrutto());
                    }
                } catch (NoShippableProductsException) {
                }
            }

            $order->save();
        }
    }
}
