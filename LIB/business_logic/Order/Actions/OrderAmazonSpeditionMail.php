<?php

namespace wws\Order\Actions;

use wws\business_structure\Shop;
use wws\Mails\Mail;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Product\ProductConst;
use wws\Product\ProductRepository;
use wws\Shipment\SpeditionRepository;

class OrderAmazonSpeditionMail
{
    private ProductRepository $product_repository;
    private SpeditionRepository $spedition_repository;

    public function __construct(ProductRepository $product_repository, SpeditionRepository $spedition_repository)
    {
        $this->product_repository = $product_repository;
        $this->spedition_repository = $spedition_repository;
    }

    /**
     * sendet, wenn nötig, die Speditions-Mail (Lieferhinweise, Telefonnummer, etc)
     */
    public function processOrder(Order $order): void
    {
        if ($this->checkOrder($order)) {
            $this->sendMail($order);
        }
    }

    public function sendMail(Order $order): void
    {
        $mail = new Mail();
        $mail->setOrder($order);
        $mail->loadVorlage('amazon_tel');

        $reference = $order->getShopReferenz();
        if (!$reference) {
            $reference = $order->getAuftnr();
        }
        $mail->assign('reference', $reference);

        $mail->send();
    }

    /**
     * prüft ob die Speditionsmail gesendet werden soll
     *
     * @param Order $order
     * @return bool
     */
    public function checkOrder(Order $order): bool
    {
        if ($order->getShopId() != Shop::ALLEGO) {
            return false;
        }

        if ($order->getOrderOriginId() !== OrderConst::ORDER_ORIGIN_AMAZON) {
            return false;
        }

        $product_ids = $this->getProductIds($order);

        if (!$product_ids) {
            return false;
        }

        $products = $this->product_repository->loadProducts($product_ids);

        foreach ($products as $product) {
            if ($product->getProductType() === ProductConst::PRODUCT_TYPE_WG) {
                return true;
            }

            if ($product->getPaketfaehig() != 1) {
                return true;
            }
        }

        $sped = $this->spedition_repository->loadSped($order->getSpedId());
        if ($sped->getSpedType() === $sped::SPED_TYPE_SPED) {
            return true;
        }

        return false;
    }

    /**
     * @param Order $order
     * @return array
     */
    private function getProductIds(Order $order)
    {
        $product_ids = [];

        foreach ($order->getOrderItems() as $order_item) {
            if ($order_item->getPreis() < 100) { //unter 100€ wird kein spedi versand sein
                continue;
            }

            $product_ids[] = $order_item->getProductId();
        }

        return $product_ids;
    }
}
