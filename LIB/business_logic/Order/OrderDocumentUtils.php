<?php

namespace wws\Order;

use Lam<PERSON>\Barcode\Barcode;

class OrderDocumentUtils
{
    public static function getAuftnrAsBarcodeHtml(string $auftnr): string
    {
        $barcode = Barcode::factory(
            'code39',
            'image',
            ['text' => $auftnr, 'barHeight' => 40, 'drawText' => false]
        );

        ob_start();
        $image = $barcode->draw();
        imagepng($image);
        $image_data = ob_get_clean();

        $image_data_base64 = base64_encode($image_data);

        return sprintf(
            '<img alt="%s" src="data:image/png;base64,%s">',
            $auftnr,
            $image_data_base64
        );
    }
}
