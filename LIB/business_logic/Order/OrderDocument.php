<?php

namespace wws\Order;

use bqp\Exceptions\DevException;
use bqp\Pdf\PdfUtils;
use db;
use wws\core\DocumentTemplateSys;
use wws\Documents\DocumentPdfService;

class OrderDocument
{
    public const TYPE_OFFER = 'offer';
    public const TYPE_CONFIRMATION = 'confirmation';
    public const TYPE_PRE_CONFIRMATION = 'pre_confirmation';
    public const TYPE_INVOICE = 'invoice';
    public const TYPE_ORDER = 'order';

    protected Order $order;
    protected string $document_type = self::TYPE_OFFER;
    protected bool $texts_loaded = false;
    protected ?string $pre_text = null;
    protected ?string $post_text = null;

    public function __construct(Order $order = null)
    {
        if ($order) {
            $this->setOrder($order);
        }
    }

    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setDocumentType(string $type): void
    {
        $this->document_type = $type;
    }

    public function getDocumentType(): string
    {
        return $this->document_type;
    }

    public function save(): void
    {
        $db = db::getInstance();

        if (!$this->texts_loaded) {
            return;
        }

        $db->query("
            INSERT INTO
                order_documents
            SET
                order_documents.order_id = '" . $this->order->getOrderId() . "',
                order_documents.document_type = '" . $this->getDocumentType() . "',
                order_documents.pre_text = '" . $db->escape($this->pre_text) . "',
                order_documents.post_text = '" . $db->escape($this->post_text) . "'
            ON DUPLICATE KEY UPDATE
                order_documents.pre_text = VALUES(order_documents.pre_text),
                order_documents.post_text = VALUES(order_documents.post_text)
        ");
    }

    public function loadDocumentTexts(): void
    {
        if ($this->texts_loaded) {
            return;
        }

        $daten = db::getInstance()->singleQuery("
            SELECT
                order_documents.pre_text,
                order_documents.post_text
            FROM
                order_documents
            WHERE
                order_documents.order_id = '" . $this->order->getOrderId() . "' AND
                order_documents.document_type = '" . $this->getDocumentType() . "'
        ");

        $this->texts_loaded = true;

        if (!$daten) {
            return;
        }

        $this->pre_text = $daten['pre_text'];
        $this->post_text = $daten['post_text'];
    }

    public function display(string $type = 'html'): void
    {
        switch ($type) {
            case 'html':
                $tpl = DocumentTemplateSys::getInstance();

                $tpl->assign('order', $this->order);
                $tpl->assign('order_document', $this);

                $tpl->setTitle($this->order->getAuftnr());
                $tpl->dokumentDisplay('dokumente/order_document.tpl');
                break;
            case 'pdf':
                PdfUtils::sendHeader($this->order->getAuftnr() . '_' . $this->getTypeName() . '.pdf');
                echo $this->getAsPdf();

                break;
        }
    }

    public function getAsPdf(): string
    {
        $pdf_service = new DocumentPdfService();
        return $pdf_service->order_document($this->getOrder()->getOrderId(), $this->getDocumentType());
    }

    public function getTypeName(): string
    {
        switch ($this->document_type) {
            case self::TYPE_OFFER:
                return 'Angebot';
            case self::TYPE_CONFIRMATION:
                return 'Auftragsbestätigung';
            case self::TYPE_PRE_CONFIRMATION:
                return 'Vorabbestätigung';
            case self::TYPE_ORDER:
                return 'Auftrag';
        }

        throw new DevException('Unbekannter Dokumententyp.');
    }

    public function getAuftnrAliasName(): string
    {
        switch ($this->document_type) {
            case self::TYPE_OFFER:
                return 'Angebotsnr.';
            case self::TYPE_CONFIRMATION:
            case self::TYPE_PRE_CONFIRMATION:
            case self::TYPE_ORDER:
                return 'Auftnr.';
        }

        throw new DevException('Unbekannter Dokumententyp.');
    }

    public function getPreText(): string
    {
        $this->loadDocumentTexts();

        if ($this->isPreText()) {
            return $this->pre_text;
        }

        return $this->getStdPreText();
    }

    public function getPostText(): string
    {
        $this->loadDocumentTexts();

        if ($this->isPostText()) {
            return $this->post_text;
        }

        return $this->getStdPostText();
    }

    public function isPreText(): bool
    {
        return $this->pre_text !== null;
    }

    public function isPostText(): bool
    {
        return $this->post_text !== null;
    }

    public function getStdPreText(): string
    {
        return OrderDocumentDefaults::getPreDefault($this);
    }

    public function getStdPostText(): string
    {
        return OrderDocumentDefaults::getPostDefault($this);
    }

    public function initIndividualTexts(): void
    {
        $this->loadDocumentTexts();

        if (!$this->isPreText()) {
            $this->pre_text = $this->getStdPreText();
        }

        if (!$this->isPostText()) {
            $this->post_text = $this->getStdPostText();
        }
    }

    public function setPreText(string $text): void
    {
        $this->initIndividualTexts();

        $this->pre_text = $text;
    }

    public function setPostText(string $text): void
    {
        $this->initIndividualTexts();

        $this->post_text = $text;
    }
}
