<?php

namespace wws\Order\Form;

use bqp\form\form_element_select;
use order_repository;

class FormElementOrderItemStatusFilter extends form_element_select
{
    public function __construct()
    {
        parent::__construct();

        $this->option_callback = [$this, 'getStatuse'];

        $this->addEmptyOption(true);
    }

    public function getStatuse(): array
    {
        return order_repository::getStatusNames();
    }
}
