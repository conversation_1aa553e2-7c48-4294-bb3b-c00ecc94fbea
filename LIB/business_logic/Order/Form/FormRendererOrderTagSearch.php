<?php

namespace wws\Order\Form;

use bqp\form\form_renderer_generic;

class FormRendererOrderTagSearch extends form_renderer_generic
{
    protected $template_after = '';

    public function render($modus = null)
    {
        /* @var FormElementOrderTagSearch $element */
        $element = $this->element;

        $order_tag_search = $element->getOrderTagSearch();
        $order_tag_search_switch = $element->getOrderTagSearchSwitch();
        $order_tags = $element->getElementOrderTags();

        if ($element->isError()) {
            $order_tag_search->addError('');
        }
        $return = '<div class="order_tag_search_container">';
        $return .= $order_tag_search->getInputRenderer()->render();
        $return .= '</br>';
        $return .= $order_tags->getInputRenderer()->render();
        $return .= '<div>';
        $return .= $order_tag_search_switch->getInputRenderer()->render();
        $return .= "<label style='text-align: left; width: unset' for='{$order_tag_search_switch->getId()}'>" . $order_tag_search_switch->getLabel() . "</label>";
        $return .= '</div>';
        $return .= '</div>';
        //@todo nerv... in der form ist nur ein solches element mäglich.
        $return .= <<<EOT
        <script>
        var order_tag_search_container = document.getElementsByClassName('order_tag_search_container');
        var key = 0;
        for (let el of order_tag_search_container) {
            el.id = 'order_tag_search_container'+key;
            const search = el.getElementsByClassName('{$order_tag_search->getClass()}')[0]
            search.autocomplete = 'off'; 
            const select = el.getElementsByClassName('{$order_tags->getClass()}')[0]
            const options = select.getElementsByTagName('option');
            search.addEventListener('input',function(event) {
                for (const option of options) {
                    if (
                        option.value.toLowerCase().includes(event.explicitOriginalTarget.value.toLowerCase()) ||
                        option.innerText.toLowerCase().includes(event.explicitOriginalTarget.value.toLowerCase())
                    ) {
                        option.disabled = false
                        option.style.display = 'block';
                        continue;
                    }
                    option.style.display = 'none';
                    option.disabled = true
                }
            })
            el.addEventListener('focusout',function(event) {
                if (!el.contains(event.relatedTarget)) {
                    for (const option of options) {
                        if (option.selected) {
                            option.disabled = false
                            option.style.display = 'block';
                            continue;
                        }
                        option.style.display = 'none';
                        option.disabled = true
                    }
                }
                search.value = '';
            })
            var event = new Event('focusout');
            el.dispatchEvent(event);
            key++
        }
        </script>
        EOT;

        return $return . $this->template($this->template_after);
    }

    public function appendAfter($template)
    {
        $this->template_after = $template;
    }
}