<?php

namespace wws\Order\Form;

use bqp\form\form_element_select;
use order_repository;

class FormElementOrderOrigin extends form_element_select
{
    public function __construct()
    {
        parent::__construct();

        $this->setLabel('Bestellherkunft');
    }

    public function getOptions(): array
    {
        if (!$this->options) {
            $this->setOptionsSimple(order_repository::getOrderOriginNames());
        }

        return parent::getOptions();
    }
}
