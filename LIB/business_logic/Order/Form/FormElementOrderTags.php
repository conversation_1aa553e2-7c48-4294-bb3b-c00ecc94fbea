<?php

namespace wws\Order\Form;

use bqp\form\form_element_container;
use bqp\form\form_element_input;
use bqp\form\form_element_select;
use bqp\form\form_renderer_std;
use bqp\form\FormRendererInline;
use bqp\form\input_handler;
use service_loader;
use wws\Order\OrderTags;

class FormElementOrderTags extends form_element_container implements FormRendererInline, input_handler
{
    private $allow_new_order_tags = false;

    public function __construct()
    {
        $this->setLabel('Order-Tags');

        $id = rand(0, 10000);

        //$this->setId('order_tags_container_'.$id);

        $this->setId('order_tags_test_list'); //@todo / @see product tag pucker

        $element = new form_element_select();
        $element->setName('[]order_tags[]');
        $element->setStyle("display: none;");
        $element->setId('order_tags_' . $id);

        $this->addElement($element);

        $element = new form_element_input();
        $element->setName('[]order_tag');
        $element->setId('order_tag_' . $id);

        $this->addElement($element);
    }

    public function getValue($pre_filter = false)
    {
        return [$this->getName() => $this->getElementSelect()->getValue($pre_filter)];
    }

    public function setValue($value)
    {
        $options = [];

        $known_tags = service_loader::get(OrderTags::class)->getOrderTags();

        foreach ($value as $tag) {
            if (array_key_exists($tag, $known_tags)) {
                $known_tag = $known_tags[$tag];

                $text = '<b>' . $known_tag['order_tag'] . '</b>';

                if ($known_tag['name']) {
                    $text .= ': ' . $known_tag['name'];
                }

                if (!empty($known_tag['img'])) {
                    $text .= ' <img src="/res/images/order_tags/' . $known_tag['img'] . '">';
                }

                $options[$tag] = htmlentities($text);
            } elseif ($this->allow_new_order_tags) {
                $options[$tag] = $tag;
            }
        }

        $this->getElementSelect()->setOptionsSimple($options);
        $this->getElementSelect()->setValue($value);
    }

    public function createRenderer()
    {
        $renderer = new form_renderer_std($this);

        $renderer->setInputRenderer($this->getInputRenderer());

        return $renderer;
    }

    public function renderInputInline(?string $mode = null): string
    {

        $return = '';

        $return .= '<div id="' . $this->getId() . '" class="wws_autocomplete_list">';

        $return .= $this->getElementOrderTag()->getInputRenderer()->render();

        $return .= $this->getElementSelect()->getInputRenderer()->render();


        $return .= '<script type="text/javascript">';
        $return .= '    var list = new wws_autocomplete_list("#' . $this->getId() . '");';
        $return .= '    var order_tags_new = 0;';

        $return .= '    new wws_autocomplete(jQuery("#' . $this->getElementOrderTag()->getId() . '"), {';
        $return .= '                url: "/ax/ajax_autocomplete/order_tags/",';
        //$return .= '                url: "/ax/ajax_autocomplete/product_tags/",';
        $return .= '                onSelect: function(daten) {';
        $return .= '                    jQuery("#' . $this->getElementOrderTag()->getId() . '").val("");';
        $return .= '                    list.addValue(daten.order_tag, daten.name);';
        $return .= '                },';
        $return .= '                template: function(daten) {';
        $return .= '                    return daten.name;';
        $return .= '                }';
        $return .= '            });';
        $return .= '</script>';

        $return .= '</div>';

        return $return;
    }

    public function getInputRenderer()
    {
        return $this;
    }


    /**
     * @return form_element_select
     */
    public function getElementSelect(): form_element_select
    {
        return $this->elements[0];
    }

    /**
     * @return form_element_input
     */
    public function getElementOrderTag(): form_element_input
    {
        return $this->elements[1];
    }

    public function setInputRaw($name, $data)
    {
        $search = $name . '[order_tags]';

        $values = [];
        foreach ($data as $key => $value) {
            if (strpos($key, $search) === 0) {
                $values[] = $value;
            }
        }

        $this->setInputValueWithoutValidation($values);
    }
}
