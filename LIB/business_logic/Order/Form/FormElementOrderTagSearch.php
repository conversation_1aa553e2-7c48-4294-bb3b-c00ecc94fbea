<?php

namespace wws\Order\Form;

use bqp\db\db_generic;
use bqp\form\form_element_checkbox;
use bqp\form\form_element_container;
use bqp\form\form_element_input;
use bqp\form\form_element_select;
use bqp\form\form_renderer_std;
use service_loader;
use wws\Order\OrderTags;

class FormElementOrderTagSearch extends form_element_container
{
    protected $is_required = false;
    protected $elements = [];

    public function __construct($show_switch = true)
    {
        $element = new form_element_input();
        $element->setName('order_tag_search');
        $element->setClass('order_tag_search');

        $this->addElement($element);

        $options = new form_element_select();
        $options->setName('order_tags[]');
        $options->setClass('order_tags');
        $order_tag_service = service_loader::get(OrderTags::class);
        $order_tags = $order_tag_service->getOrderTags();
        foreach ($order_tags as $key => $order_tag) {
            $order_tags[$key] = $order_tag['name'];
        }
        $options->addOptionsSimple($order_tags);

        $this->addElement($options);

        if ($show_switch) {
            $element = new form_element_checkbox();
            $element->setName('order_tag_search_switch');
            $element->setLabel('&#9746; Sucht nach Bestellungen, die alle der Angewählten Order-Tags haben</br>&#9744; Sucht nach Bestellungen, die einen der Angewählten Order-Tags haben');

            $this->addElement($element);
        }
    }

    public static function buildSql(db_generic $db, array $order_tags, bool $require_all_tags = false): string
    {
        $filter_where = '(';
        if (!$order_tags) { //hm...
            $filter_where .= 1;
        }
        foreach ($order_tags as $order_tag) {
            $filter_where .= "FIND_IN_SET('" . $db->escape($order_tag) . "', orders.order_tags) " . ($require_all_tags ? 'AND' : 'OR') . ' ';
        }
        $filter_where = rtrim($filter_where, ($require_all_tags ? ' AND ' : ' OR '));
        $filter_where .= ')';
        return $filter_where;
    }

    public function createRenderer()
    {
        $renderer = new form_renderer_std($this);

        $renderer->setInputRenderer($this->getInputRenderer());

        return $renderer;
    }

    public function getOrderTagSearch(): form_element_input
    {
        return $this->elements[0];
    }

    public function getElementOrderTags(): form_element_select
    {
        return $this->elements[1];
    }

    public function getOrderTagSearchSwitch(): form_element_checkbox
    {
        return $this->elements[2];
    }

    private function getInputRenderer()
    {
        return new FormRendererOrderTagSearch($this);
    }
}
