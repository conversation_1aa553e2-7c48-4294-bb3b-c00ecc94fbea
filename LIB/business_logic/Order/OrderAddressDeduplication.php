<?php

namespace wws\Order;

use debug;
use wws\Customer\Customer;

class OrderAddressDeduplication
{

    /**
     * Order und Customer müssen vom aufrufenden Code beendet werden.
     *
     * @param Order $order
     * @param Customer $customer
     * @return bool ob die adressen geändert wurden
     */
    public function deduplicate(Order $order, Customer $customer): bool
    {
        if (!$order->isAbweichendeLieferadresse()) {
            return false;
        }

        $address_a = $order->getRechnungAddress();

        if ($address_a->getHash() !== $customer->getAddress()->getHash()) {
            debug::dump('Kundenadresse weicht von Rechnungsadresse ab. @todo sollte bei dem aktuellen use case nicht vorkommen.', $customer->getCustomerId());
            return false;
        }

        $address_b = $order->getLieferAddress();

        $diff = $address_a->compare($address_b);

        $changed = false;

        //teilweise haben wir bei liefer adressen keine anrede, dann aus der rechnungsadresse übernehmen, solange der vorname gleich ist
        if (array_key_exists('anrede', $diff) && !array_key_exists('vorname', $diff)) {
            if ($diff['anrede'][1] === '') {
                $address_b->setAnrede($address_a->getAnrede());
                unset($diff['anrede']);
                $changed = true;
            }
        }

        if (!array_key_exists('tel_1', $diff)) {
            return $changed;
        }

        if (count($diff) !== 1) {
            return $changed;
        }

        //adresse unterscheidet sich nur anhand der Telefonnummer
        $tel_a_is_valid = $diff['tel_1'][0] !== '-' && $diff['tel_1'][0] !== '';
        $tel_b_is_valid = $diff['tel_1'][1] !== '-' && $diff['tel_1'][1] !== '';

        //beide adressen haben eine ungültige Telefonnummer
        if ($tel_a_is_valid && $tel_b_is_valid) {
            //unterscheidliche telefonnummer nix machen
            return $changed;
        }

        if (!$tel_a_is_valid && !$tel_b_is_valid) {
            $order->setAddress($address_a, Order::ADDRESS_TYPE_LIEFERRECHUNG);
            $customer->setAddress($address_a);
            return true;
        }

        if ($tel_a_is_valid && !$tel_b_is_valid) {
            $order->setAddress($address_a, Order::ADDRESS_TYPE_LIEFERRECHUNG);
            $customer->setAddress($address_a);
            return true;
        }

        if (!$tel_a_is_valid && $tel_b_is_valid) {
            $order->setAddress($address_b, Order::ADDRESS_TYPE_LIEFERRECHUNG);
            $customer->setAddress($address_b);
            return true;
        }

        return $changed;
    }
}
