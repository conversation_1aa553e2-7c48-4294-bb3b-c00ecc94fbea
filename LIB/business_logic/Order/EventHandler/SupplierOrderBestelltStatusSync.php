<?php

namespace wws\Order\EventHandler;

use order_repository;
use wws\Order\OrderConst;
use wws\Supplier\Event\EventSupplierOrderBestellt;

class SupplierOrderBestelltStatusSync
{
    public function handle(EventSupplierOrderBestellt $event): void
    {
        $supplier_order = $event->getSupplierOrder();

        foreach ($supplier_order->getItems() as $item) {
            if (!$item->getOrderItemIds()) {
                continue;
            }

            foreach ($item->getOrderItemIds() as $order_item_id) {
                $order = order_repository::getOrderByOrderItemId($order_item_id);
                $order_item = $order->getOrderItemByOrderItemId($order_item_id);

                switch ($order_item->getStatus()) {
                    case OrderConst::STATUS_WARTE_AUSLIEFERUNG:
                    case OrderConst::STATUS_AUSLIEFERUNG_VERSCHOBEN:
                        if ($supplier_order->isDirektLieferung()) {
                            //$order_item->setStatus(order::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND);
                            $order->setStatus(OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND);
                            $order->save();
                        } else {
                            $order_item->setStatus(OrderConst::STATUS_ARTIKEL_BESTELLT);
                            $order->save();
                        }

                        break;
                    default:
                        break;
                }
            }
        }
    }
}
