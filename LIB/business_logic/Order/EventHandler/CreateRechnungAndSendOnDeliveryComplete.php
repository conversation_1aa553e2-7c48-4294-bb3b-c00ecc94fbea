<?php

namespace wws\Order\EventHandler;

use wws\Lager\Event\EventWarenausgangLieferscheinErledigt;
use wws\Order\OrderMailProfile;

class CreateRechnungAndSendOnDeliveryComplete
{
    public function handleEvent(EventWarenausgangLieferscheinErledigt $event): void
    {
        $lieferschein = $event->getWarenausgangLieferschein();
        $order = $lieferschein->getOrder();

        if ($order->isCompleteDeliverd() && $order->isInvoiceAutoCreate() && !$order->hasInvoice()) {
            $order->createInvoice();

            $profile = new OrderMailProfile();
            $profile->setOrder($order);

            if ($profile->isMail($profile::MAIL_TYPE_INVOICE)) {
                $order->mailInvoice();
            }
        }
    }
}
