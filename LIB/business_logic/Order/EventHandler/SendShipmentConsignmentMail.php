<?php

namespace wws\Order\EventHandler;

use bqp\Event\AsyncEvent;
use bqp\Event\AsyncEventHandler;
use wws\Mails\Mail;
use wws\Order\Order;

class SendShipmentConsignmentMail implements AsyncEventHandler
{

    public function processAsyncEvent(AsyncEvent $async_event): void
    {
        $event_message = $async_event->getMessage();

        $order = new Order($event_message['order_id']);

        $mail = new Mail();
        $mail->setOrder($order);
        if ($mail->loadVorlage('versand_kommissionierung')) {
            $mail->send();
        }
    }
}
