<?php

namespace wws\Order\EventHandler;

use wws\Order\Event\EventOrderGutschriftCreated;
use wws\Order\OrderMemo;
use wws\Order\OrderRepository;

class MarkOrderPayedOnGutschriftCreate
{
    private OrderRepository $order_repository;

    public function __construct(OrderRepository $order_repository)
    {
        $this->order_repository = $order_repository;
    }

    public function handleEvent(EventOrderGutschriftCreated $event): void
    {
        $gutschrift = $event->getGutschrift();
        $order = $this->order_repository->load($gutschrift->getOrderId());

        if (!$order->isPayed() && abs($order->getRechnungsBetrag() - $gutschrift->getAmount()) < 0.10) {
            $order->setPayed(1);

            $memo = new OrderMemo('Komplett-Gutschrift: Auftrag als bezahlt markiert.', OrderMemo::BUCHHALTUNG);
            $memo->setAutoMemo(false);
            $order->addOrderMemo($memo);

            $this->order_repository->save($order);
        }
    }
}
