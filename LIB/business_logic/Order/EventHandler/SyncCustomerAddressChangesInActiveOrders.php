<?php

namespace wws\Order\EventHandler;

use bqp\db\db_generic;
use wws\Customer\Event\EventCustomerAddressChanged;
use wws\Order\Order;
use wws\Order\OrderRepository;

/**
 * Class SyncCustomerAddressChangesInActiveOrders
 *
 * Syncronisiert Änderungen an der Kundenadresse in die aktuellen Bestellungen (aktiven).
 *
 * @todo derzeit wird das auch bei Änderungen durch Kunden im Shop getriggert. Das muss verhindert werden.
 *
 * @package wws\Order\EventHandler
 */
class SyncCustomerAddressChangesInActiveOrders
{
    private OrderRepository $order_repository;
    private db_generic $db;

    public function __construct(db_generic $db, OrderRepository $order_repository)
    {
        $this->db = $db;
        $this->order_repository = $order_repository;
    }

    public function handle(EventCustomerAddressChanged $event): void
    {
        $customer = $event->getCustomer();
        $old_address = $event->getOldAddress();

        $result = $this->db->query("
            SELECT
                orders.order_id
            FROM
                orders LEFT JOIN
                order_addresses ON (orders.order_id = order_addresses.order_id)
            WHERE
                orders.customer_id = '" . $customer->getCustomerId() . "' AND
                orders.order_aktiv = 1 AND
                order_addresses.hash = '" . $old_address->getHash() . "' AND
                order_addresses.address_type IN ('" . Order::ADDRESS_TYPE_RECHUNG . "', '" . Order::ADDRESS_TYPE_LIEFERRECHUNG . "')
        ");

        foreach ($result as $row) {
            $order = $this->order_repository->load($row['order_id']);

            if ($order->isAbweichendeLieferadresse()) {
                $order->setAddress($customer->getAddress(), Order::ADDRESS_TYPE_RECHUNG);
            } else {
                $order->setAddress($customer->getAddress());
            }

            $this->order_repository->save($order);
        }
    }
}
