<?php

namespace wws\Order\EventHandler;

use bqp\Event\AsyncEvent;
use bqp\Event\AsyncEventHandler;
use bqp\Model\SmartDataEntityNotFoundException;
use Closure;
use wws\Mails\Event\EventMailOrderProcessed;
use wws\Mails\Event\EventMailOrderUnprocessed;
use wws\Mailsystem\MailsystemMail;
use wws\Mailsystem\MailsystemRepository;
use wws\Order\OrderConst;
use wws\Order\OrderRepository;

class AsyncEventHandlerOrderTagUnreadMail implements AsyncEventHandler
{
    private OrderRepository $order_repository;
    private MailsystemRepository $mailsystem_repository;

    /**
     * @var Closure
     */
    private $blacklist_callback;

    /**
     * @param OrderRepository $order_repository
     * @param MailsystemRepository $mailsystem_repository
     */
    public function __construct(
        OrderRepository $order_repository,
        MailsystemRepository $mailsystem_repository
    ) {
        $this->order_repository = $order_repository;
        $this->mailsystem_repository = $mailsystem_repository;

        $this->blacklist_callback = function (MailsystemMail $mail) {
            if (strpos($mail->getBetreff(), 'Neuer Bestelleingang') === 0) { //'<EMAIL>'
                return true;
            }

            if ($mail->getSenderEmail() === '<EMAIL>') {
                return true;
            }

            if (strpos($mail->getSenderEmail(), '@checkout.idealo.de') !== false) {
                return true;
            }

            return false;
        };
    }

    /**
     * @param AsyncEvent $async_event
     */
    public function processAsyncEvent(AsyncEvent $async_event): void
    {
        $event_message = $async_event->getMessage();
        if (!$event_message['order_id']) {
            return;
        }

        if ($async_event->getEventType() === EventMailOrderUnprocessed::class && $this->ignoreEvent($async_event)) {
            return;
        }

        try {
            $order = $this->order_repository->load($event_message['order_id']);
        } catch (SmartDataEntityNotFoundException $smart_data_entity_not_found_exception) {
            return;
        }

        switch ($async_event->getEventType()) {
            case EventMailOrderUnprocessed::class:
                $order->addOrderTag(OrderConst::TAG_UNREAD_MAIL);
                break;
            case EventMailOrderProcessed::class:
                $order->removeOrderTag(OrderConst::TAG_UNREAD_MAIL);
                break;
        }

        $this->order_repository->save($order);
    }

    /**
     * @param AsyncEvent $event
     * @return bool
     */
    private function ignoreEvent(AsyncEvent $event): bool
    {
        $mail_id = (int)$event->getMessage()['mail_id'];

        try {
            $mail = $this->mailsystem_repository->loadMail($mail_id);
        } catch (SmartDataEntityNotFoundException $smart_data_entity_not_found_exception) {
            return true;
        }

        if ($this->blacklist_callback) {
            return call_user_func($this->blacklist_callback, $mail);
        }

        return false;
    }
}
