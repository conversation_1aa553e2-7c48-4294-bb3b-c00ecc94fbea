<?php

namespace wws\Order\EventHandler;

use bqp\Event\AsyncEvent;
use bqp\Event\AsyncEventHandler;
use wws\Order\Actions\OrderBWare;
use wws\Order\Order;
use wws\Order\OrderConst;

class SendBWareMail implements AsyncEventHandler
{
    private OrderBWare $order_b_ware;

    public function __construct(OrderBWare $order_b_ware)
    {
        $this->order_b_ware = $order_b_ware;
    }

    public function processAsyncEvent(AsyncEvent $async_event): void
    {
        $event_message = $async_event->getMessage();

        $order = new Order($event_message['order_id']);

        if ($order->isOrderTag(OrderConst::TAG_B_WARE)) {
            $this->order_b_ware->sendBWareMail($order);
        }
    }
}
