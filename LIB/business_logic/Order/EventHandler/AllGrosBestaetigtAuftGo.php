<?php

namespace wws\Order\EventHandler;

use system_protokoll;
use wws\Order\Event\EventOrderChange;
use wws\Order\Order;
use wws\Order\OrderConst;

class AllGrosBestaetigtAuftGo
{
    public function handle(EventOrderChange $event): void
    {
        if (!$this->hasRelevantStatusChange($event)) {
            return;
        }

        if (!$this->allOrderItemsConfirmed($event->getOrder())) {
            return;
        }

        $this->processOrder($event->getOrder());
    }

    public function hasRelevantStatusChange(EventOrderChange $event): bool
    {
        $changes = $event->getEntityChanges()->getChanges('item.status');

        foreach ($changes as $change) {
            if ($change['new_value'] === OrderConst::STATUS_GROSS_BESTAETIGT) {
                return true;
            }
        }

        return false;
    }

    public function allOrderItemsConfirmed(Order $order): bool
    {
        foreach ($order->getOrderItemsCustomer() as $order_item) {
            if (!$order_item->isDeliveryAble()) {
                continue;
            }

            if ($order_item->getStatus() < OrderConst::STATUS_GROSS_BESTAETIGT) {
                return false;
            }
        }

        return true;
    }

    public function processOrder(Order $order): void
    {
        //prüfen ob auftrag automatisch bestätigt werden kann
        $auto_bestaetigen = true;
        foreach ($order->getOrderItems() as $order_item) {
            if ($order_item->isStorno()) {
                continue;
            }

            if (trim($order_item->getGrosMemo())) {
                $auto_bestaetigen = false;
            }

            if ($order_item->getLiefertermin() < 0) {
                $auto_bestaetigen = false;
            }
        }

        $log = $auto_bestaetigen ? 'Auto' : 'Manuell';

        if ($auto_bestaetigen) {
            $order->auft_go();
            $order->sendZugangsbestaetigung(false);
            $order->save();

            $log .= ' (mail send)';
        }

        system_protokoll::getInstance('gs_bestaetigt')->wlog(system_protokoll::DEBUG, $order->getAuftnr() . ' ' . $order->getOrderId() . ' Log: ' . $log);
    }
}
