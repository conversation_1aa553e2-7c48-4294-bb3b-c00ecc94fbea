<?php

namespace wws\Order\EventHandler;

use wws\core\Nachrichtensystem;
use wws\Order\Event\EventOrderBeforeSave;
use wws\Order\OrderConst;
use wws\Shipment\ShipmentRepository;
use wws\Users\UserConst;

class SelfPickupOrderCanceledWarehouseNotification
{
    public function handle(EventOrderBeforeSave $event): void
    {
        $order = $event->getOrder();

        //auftrag nicht bereitgestellt -> abbruch
        if (!$order->isOrderTag(OrderConst::TAG_ABHOLUNG_BEREITGESTELLT)) {
            return;
        }

        //auftag ist nicht storniert und steht noch auf abholung -> abbruch
        if (!$order->isStorno() && $order->getSpedId() === ShipmentRepository::SPED_ABHOLUNG) {
            return;
        }

        //bereitstellung aufheben
        $order->removeOrderTag(OrderConst::TAG_ABHOLUNG_BEREITGESTELLT);

        //nachricht schicken
        if ($order->isStorno()) {
            $text = $order->getAuftnr() . ' bereitgestellter Abholauftrag storniert.';
        } else {
            $text = $order->getAuftnr() . ' bereitgestellter Abholauftrag steht nicht mehr auf Abholung.';
        }

        $msg = new Nachrichtensystem(UserConst::USER_ID_SYSTEM);
        $msg->send(UserConst::USER_ID_RENE_KEMPE, $order->getAuftnr() . ' Abholauftrag umgestellt', $text);
    }
}
