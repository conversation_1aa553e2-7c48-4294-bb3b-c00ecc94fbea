<?php

namespace wws\Order\EventHandler;

use wws\Order\Event\EventOrderBeforeSave;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Supplier\SuppliersConst;

class K11CalcOrderTags
{
    public function handle(EventOrderBeforeSave $event): void
    {
        $order = $event->getOrder();

        if ($order->getMinStatus() >= OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST) {
            return;
        }

        $this->processOrder($order);
    }

    public function processOrder(Order $order): void
    {
        if ($this->hasAswoOrderItem($order)) {
            $order->addOrderTag(OrderConst::TAG_ASWO);
        } else {
            $order->removeOrderTag(OrderConst::TAG_ASWO);
        }

        if ($this->hasKremplBeschaffungOrderItem($order)) {
            $order->addOrderTag(OrderConst::TAG_KREMPL_BESCHAFFUNG);
        } else {
            $order->removeOrderTag(OrderConst::TAG_KREMPL_BESCHAFFUNG);
        }
    }

    public function hasAswoOrderItem(Order $order): bool
    {
        foreach ($order->getOrderItems() as $order_item) {
            if ($order_item->getWarenkorbType() === OrderConst::WARENKORB_TYP_ASWO) {
                return true;
            }

            if ($order_item->getSupplierId() === SuppliersConst::SUPPLIER_ID_EURAS) {
                return true;
            }
        }

        return false;
    }

    public function hasKremplBeschaffungOrderItem(Order $order): bool
    {
        foreach ($order->getOrderItems() as $order_item) {
            if (in_array($order_item->getSupplierId(), [SuppliersConst::SUPPLIER_ID_KREMPEL_BSH, SuppliersConst::SUPPLIER_ID_KREMPEL_ELECTROLUX])) {
                return true;
            }
        }

        return false;
    }
}
