<?php

namespace wws\Order\EventHandler;

use wws\Customer\Event\EventCustomerChanged;
use wws\Order\Actions\OrderMwst;
use wws\Order\Event\EventOrderBeforeSave;

class RecalcUstStatus
{
    public function handle(EventOrderBeforeSave $event): void
    {
        $order = $event->getOrder();

        if ($order->isTaxStatusManual()) {
            return;
        }

        $changes = $event->getOrder()->getAllChanges();

        $recalc = $changes->isChange(['order_addresses.*', 'order.tax_status_manual', 'sped_id', 'sped']);

        if ($recalc) {
            OrderMwst::setTaxStatus($order);
        }
    }

    public function handleEventCustomerChanged(EventCustomerChanged $event): void
    {
        $changes = $event->getEntityChanges();

        if ($changes->isChange('ustidnr_status')) {
            OrderMwst::updateTaxStatusByCustomer($event->getCustomerId());
        }
    }
}
