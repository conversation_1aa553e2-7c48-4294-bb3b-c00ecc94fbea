<?php

namespace wws\Order;

use bqp\Exceptions\FatalException;
use bqp\extern\SpbGarantGarantie\SpbGarantGarantie;
use config;
use wws\Product\ProductExtraServices;

class OrderExtraServicesService
{
    public function addServiceToOrderItem(string $raw_service_id, Order $order, OrderItem $order_item): OrderItem
    {
        $pos = strpos($raw_service_id, '|');
        if ($pos) {
            $service_id_sub = substr($raw_service_id, $pos + 1);
            $service_id = substr($raw_service_id, 0, $pos);
        } else {
            $service_id = $raw_service_id;
            $service_id_sub = '';
        }

        $service = new ProductExtraServices($service_id);

        $product_id = $service->getProductIds()[0];

        $ek_brutto = '';
        $vk_brutto = '';
        $product_name = '';
        $typ_value = '';

        switch ($service->getServiceHandler()) {
            case 'wertgarantie':
                $garantie = new SpbGarantGarantie(config::getLegacy('garantie_spb_garant'));
                $daten = $garantie->calcPrices($service->getServiceHandlerExtra(), $service_id_sub, $order_item->getVkBrutto());

                $ek_brutto = $daten['ek_netto'] * $order_item->getVatRate()->getAsFactor();
                $vk_brutto = $daten['vk_brutto'];
                $typ_value = $daten['artnr'];
                $product_name = $service->getServiceNameWarenkorb();
                $product_name = str_replace('{{$jahre}}', $daten['text'], $product_name);

                break;
            case 'fix_vk':
                $ek_brutto = $service->getServiceEkBrutto();
                $vk_brutto = $service->getServiceVkBrutto();
                $product_name = $service->getServiceNameWarenkorb();
                $typ_value = $service_id;
                break;
        }

        if (!$product_name) {
            throw new FatalException('unknown service');
        }

        $product_name = str_replace('{{$product_name}}', $order_item->getProductName(), $product_name);

        $order_item = $order->createOrderItem($product_id);
        $order_item->setQuantity($order_item->getQuantity());
        $order_item->setVkBrutto($vk_brutto);
        $order_item->setEkBrutto($ek_brutto);
        $order_item->setEkNnnBrutto($ek_brutto);
        $order_item->setProductName($product_name);
        $order_item->setTypValue($typ_value);

        return $order_item;
    }
}
