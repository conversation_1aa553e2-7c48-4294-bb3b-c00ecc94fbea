<?php

namespace wws\B2B;

use bqp\Vat\VatRate;
use db;
use Exception;
use wws\Product\ProductFeature\ProductFeatureUtils;

class B2BProduct
{
    protected $daten = [];


    public function __construct($mv_master_product_id = null, $b2b_price_group_id = null)
    {
        if ($mv_master_product_id !== null) {
            $this->setMvMasterProductId($mv_master_product_id, $b2b_price_group_id);
        }
    }

    public function setMvMasterProductId($mv_master_product_id, $b2b_price_group_id)
    {
        $db = db::getInstance('market_view');

        $this->daten = $db->singleQuery("
            SELECT
                market_view_master_product.mv_master_product_id,
                market_view_master_product.product_id,
                market_view_master_product.mv_master_cat_id,
                market_view_master_product.ean,
                market_view_master_product.mpn,
                market_view_master_product.product_name,
                market_view_master_product.product_name_src,
                market_view_master_product.mv_hersteller_id,
                market_view_master_product.sources_online,
                market_view_master_product.sources_available,

                market_view_hersteller.hersteller_name
            FROM
                market_view_master_product INNER JOIN
                market_view_hersteller ON (market_view_master_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id)
            WHERE
                market_view_master_product.mv_master_product_id = '" . (int)$mv_master_product_id . "'
        ");

        if ($b2b_price_group_id) {
            $daten = $db->singleQuery("
                SELECT
                    market_view_b2b_offer_prices.vk_netto,
                    market_view_b2b_offer_prices.vk_netto_best
                FROM
                    market_view_b2b_offer_prices
                WHERE
                    market_view_b2b_offer_prices.mv_master_product_id = '" . $this->daten['mv_master_product_id'] . "' AND
                    market_view_b2b_offer_prices.b2b_price_group_id = '" . (int)$b2b_price_group_id . "'
            ");

            $this->daten = array_merge($this->daten, $daten);
        }

        $beschreibung = $db->fieldQuery("
            SELECT
                market_view_product.beschreibung
            FROM
                market_view_product INNER JOIN
                market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_product.mv_master_product_id = '" . (int)$mv_master_product_id . "' AND
                market_view_product.beschreibung != ''
            ORDER BY
                market_view_source.source_std_priority DESC
            LIMIT
                1
        ");

        $this->daten['beschreibung'] = $beschreibung;
    }


    public function getBeschreibung()
    {
        return $this->daten['beschreibung'];
    }

    public function getProductName()
    {
        return $this->daten['product_name'];
    }

    public function getMvMasterProductId()
    {
        return $this->daten['mv_master_product_id'];
    }

    public function getMpn()
    {
        return $this->daten['mpn'];
    }

    public function getEan()
    {
        return $this->daten['ean'];
    }

    public function getHersteller()
    {
        return $this->daten['hersteller_name'];
    }


    public function getFeatures()
    {
        if (!$this->daten['product_id']) {
            return false;
        }

        $daten = ProductFeatureUtils::getCommonFeaturesForProductId($this->daten['product_id']);

        return $daten;
    }

    public function getVkNetto()
    {
        if (!array_key_exists('vk_netto', $this->daten)) {
            throw new Exception('Preis nicht geladen.');
        }

        return $this->daten['vk_netto'];
    }

    public function getPriceByProductListConfig($config)
    {
        $vk = $this->getVkNetto();

        if ($config['b2b_view'] == 'vk') {
            $vk *= $config['b2b_view_vk_addition'] / 100 + 1;
            if ($config['b2b_view_vk_brutto'] && $config['b2b_vat_rate'] instanceof VatRate) {
                $vk *= $config['b2b_vat_rate']->getAsFactor();
            }
        }

        return $vk;
    }
}
