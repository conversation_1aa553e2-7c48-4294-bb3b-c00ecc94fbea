<?php

namespace wws\B2B;

use bqp\db\db_mysqli;
use db;

class B2BAccessLog
{
    private db_mysqli $db;

    private $customer_id;
    private $session_id;

    public function __construct($customer_id, $session_id)
    {
        $this->customer_id = $customer_id;
        $this->session_id = $session_id;

        $this->db = db::getInstance();
    }

    public function logLogin(): void
    {
        $this->db->query("
            INSERT IGNORE INTO
                b2b_access_log
            SET
                b2b_access_log.customer_id = '" . $this->db->escape($this->customer_id) . "',
                b2b_access_log.session_id = '" . $this->db->escape($this->session_id) . "',
                b2b_access_log.ip = '" . $_SERVER['REMOTE_ADDR'] . "',
                b2b_access_log.date_login = NOW(),
                b2b_access_log.date_last = NOW()
        ");
    }

    public function logClick(): void
    {
        $this->db->query("
            UPDATE
                b2b_access_log
            SET
                b2b_access_log.date_last = NOW(),
                b2b_access_log.clicks = b2b_access_log.clicks+1
            WHERE
                b2b_access_log.customer_id = '" . $this->db->escape($this->customer_id) . "' AND
                b2b_access_log.session_id = '" . $this->db->escape($this->session_id) . "'
        ");
    }
}
