<?php

namespace wws\B2B;

use bqp\Model\SmartDataObj;
use db;

class B2BCustomer
{
    protected SmartDataObj $daten;


    public function __construct($customer_id = null)
    {
        $this->daten = new SmartDataObj($this);

        if ($customer_id !== null) {
            $this->load($customer_id);
        }
    }

    public function load($customer_id)
    {
        $daten = db::getInstance()->singleQuery("
            SELECT
                b2b_customer.customer_id,
                b2b_customer.b2b_price_group_id,
                b2b_customer.b2b_view_after_login,
                b2b_customer.b2b_view_vk_brutto,
                b2b_customer.b2b_view_ek_code,
                b2b_customer.b2b_view_vk_addition
            FROM
                b2b_customer
            WHERE
                b2b_customer.customer_id = '" . $customer_id . "'
        ");

        if ($daten) {
            $this->daten->loadDaten($daten);
        } else {
            $this->setCustomerId($customer_id);
        }
    }

    public function save()
    {
        if ($this->daten->getObjStatus() == SmartDataObj::STATUS_LOADED) {
            return $this->getCustomerId();
        }

        $db = db::getInstance();

        $sql = [];

        foreach ($this->daten->getChanges() as $field => $old_value) {
            $sql[] = "b2b_customer.$field = '" . $db->escape($this->daten[$field]) . "'";
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db->query("
                    INSERT INTO
                        b2b_customer
                    SET
                        " . implode(',', $sql) . "
                ");

                break;
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                    UPDATE
                        b2b_customer
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        b2b_customer.customer_id = '" . $this->getCustomerId() . "'
                    LIMIT
                        1
                ");
                break;
        }

        $this->daten->setSaved();

        return $this->getCustomerId();
    }

    public function setCustomerId($customer_id)
    {
        return $this->daten->setter('customer_id', $customer_id);
    }

    public function getCustomerId()
    {
        return $this->daten['customer_id'];
    }

    public function setB2bPriceGroupId($b2b_price_group_id)
    {
        return $this->daten->setter('b2b_price_group_id', $b2b_price_group_id);
    }

    public function getB2bPriceGroupId()
    {
        return $this->daten['b2b_price_group_id'];
    }

    public function getB2bViewAfterLogin()
    {
        return $this->daten['b2b_view_after_login'];
    }

    public function setB2bViewAfterLogin($view)
    {
        return $this->daten->setter('b2b_view_after_login', $view);
    }

    public function isB2bViewVkBrutto()
    {
        return (bool)$this->daten['b2b_view_vk_brutto'];
    }

    public function setB2bViewVkBrutto($status)
    {
        return $this->daten->setter('b2b_view_vk_brutto', $status ? 1 : 0);
    }

    public function isB2bViewEkCode()
    {
        return (bool)$this->daten['b2b_view_ek_code'];
    }

    public function setB2bViewEkCode($status)
    {
        return $this->daten->setter('b2b_view_ek_code', $status ? 1 : 0);
    }

    public function getB2bViewVkAddition()
    {
        return $this->daten['b2b_view_vk_addition'];
    }

    public function setB2bViewVkAddition($value)
    {
        return $this->daten->setter('b2b_view_vk_addition', $value);
    }
}
