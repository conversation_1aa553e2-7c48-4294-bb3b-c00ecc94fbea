<?php

namespace wws\B2B;

use db;
use wws\Customer\Customer;

class B2BRepository
{
    public static function getPriceGroupes()
    {
        $daten = db::getInstance()->query("
            SELECT
                b2b_price_groupes.b2b_price_group_id,
                b2b_price_groupes.b2b_price_group_name
            FROM
                b2b_price_groupes
            ORDER BY
                b2b_price_groupes.b2b_price_group_name
        ")->asSingleArray('b2b_price_group_id');

        return $daten;
    }

    public static function loadB2bCustomerByCustomer(Customer $customer)
    {
        return new B2BCustomer($customer->getCustomerId());
    }

    public static function getPriceGroupeConditionsAsText($b2b_price_group_id)
    {
        $return = '';

        $daten = db::getInstance()->singleQuery("
            SELECT
                b2b_price_groupes.b2b_price_group_name,
                b2b_price_groupes.price_10,
                b2b_price_groupes.price_20,
                b2b_price_groupes.price_50,
                b2b_price_groupes.price_75,
                b2b_price_groupes.price_100,
                b2b_price_groupes.price_250,
                b2b_price_groupes.price_500,
                b2b_price_groupes.price_750,
                b2b_price_groupes.price_1000,
                b2b_price_groupes.price_2000,
                b2b_price_groupes.price_gt_2000
            FROM
                b2b_price_groupes
            WHERE
                b2b_price_groupes.b2b_price_group_id = '$b2b_price_group_id'
        ");

        $staffeln = [
            'price_10' => 'bis 10€',
            'price_20' => 'bis 20€',
            'price_50' => 'bis 50€',
            'price_75' => 'bis 75€',
            'price_100' => 'bis 100€',
            'price_250' => 'bis 250€',
            'price_500' => 'bis 500€',
            'price_750' => 'bis 750€',
            'price_1000' => 'bis 1000€',
            'price_2000' => 'bis 2000€',
            'price_gt_2000' => 'Rest'
        ];

        $old = null;
        $old_condition = null;

        foreach ($staffeln as $key => $value) {
            $condition = $daten[$key];
            if ($old_condition === null) {
                $old_condition = $condition;
            }

            if ($old_condition != $condition) {
                $return .= $old . "<br>";
                $old_condition = $condition;
            }

            $old = $value . ': ' . $condition . '%';
        }
        $return .= $old;

        return $return;
    }

    public static function createToken($customer_id)
    {
        return substr(base64_encode(md5($customer_id, 'allegosecret')), 0, 10);
    }

    public static function checkToken($customer_id, $token)
    {
        return self::createToken($customer_id) == $token;
    }
}
