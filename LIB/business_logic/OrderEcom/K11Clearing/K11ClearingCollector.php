<?php

namespace wws\OrderEcom\K11Clearing;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Vat\VatRate;
use db;
use wws\business_structure\Shop;
use wws\business_structure\ShopRepository;
use wws\Lager\WarenausgangLieferschein;
use wws\Order\OrderConst;
use wws\ProductStock\ProductStockConst;
use wws\Shipment\ShipmentRepository;

class K11ClearingCollector
{

    /**
     * @var float
     */
    private $k11_shipment_costs;

    /**
     * @var array
     */
    private $speds_to_ignore;

    /**
     * @var array
     */
    private $speds_reverse;

    /**
     * @var int[]
     */
    private $ecom_lager_ids;

    /**
     * @var int[]
     */
    private $k11_lager_ids;

    /**
     * @var DateObj
     */
    private $range_start;

    /**
     * @var DateObj
     */
    private $range_end;

    private db_generic $db;

    private VatRate $vat_rate;


    public function __construct()
    {
        $this->speds_to_ignore = [
            ShipmentRepository::SPED_DHL,
            ShipmentRepository::SPED_DHL_ECOM_WITH_K11,
            ShipmentRepository::SPED_POST
        ];
        $this->speds_reverse = [ShipmentRepository::SPED_DHL_ECOM_WITH_K11];

        $this->db = db::getInstance();

        $this->k11_lager_ids = [ProductStockConst::LAGER_ID_K11];
        $this->ecom_lager_ids = [ProductStockConst::LAGER_ID_LAGER];

        //@todo... das ist ganz dumm! Der Collector müsste eigentlich rein Netto arbeiten.
        //Aufwand/Nutzen... das bleibt in der Form bestehen. Das dürften noch 1, 2 Abrechnungen sein und danach ist das Ding out of work.
        $this->vat_rate = ShopRepository::getDefaultVatRate(1);

        //wenn K11 was für uns rausschickt -> dhl k11 (sped_id 151) / versandkosten -> (2,8+3,5)/2 (misch kalkulation)
        $this->k11_shipment_costs = 3.15;
    }

    public function setDateRange(DateObj $range_start, DateObj $range_end): void
    {
        $this->range_start = $range_start;
        $this->range_end = $range_end;
    }

    public function execute(): K11ClearingResult
    {
        $clearing_result = new K11ClearingResult($this->range_start, $this->range_end);
        $clearing_result->setVatRate($this->vat_rate);

        $this->collectVerkauf($clearing_result);
        $this->collectEinkauf($clearing_result);
        $this->collectLogistic($clearing_result);
        $this->collectCod($clearing_result);

        return $clearing_result;
    }

    private function collectVerkauf(K11ClearingResult $clearing_result): void
    {
        //versand aus ecom lager
        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.datum_erledigt AS datum,
                order_item.auftnr,
                (warenausgang_lieferschein_items.anzahl * order_item.ek_nnn_brutto) AS total_amount,
                warenausgang_lieferschein_items.anzahl,
                order_item.ek_nnn_brutto AS item_amount,
                order_item.product,
                order_item.product_id
            FROM
                warenausgang_lieferschein INNER JOIN
                warenausgang_lieferschein_items ON (warenausgang_lieferschein.lieferschein_id = warenausgang_lieferschein_items.lieferschein_id) INNER JOIN
                order_item ON (warenausgang_lieferschein_items.order_item_id = order_item.order_item_id)
            WHERE
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                warenausgang_lieferschein.shop_id = 2 AND
                warenausgang_lieferschein.lager_id IN (" . $this->db->in($this->ecom_lager_ids) . ") AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "'
        ")->asArray();

        foreach ($result as $row) {
            $clearing_result->addVerkauf($row);
        }

        //lager transfer von ecom an K11
        $result = $this->db->query("
            SELECT
                lager_transfer.date_end AS datum,
                CONCAT('TRANSFER ',lager_transfer.lager_transfer_id) AS auftnr,
                ROUND(lager_transfer_items.anzahl * lager_transfer_items.lager_ek*" . $this->vat_rate->getAsFactor() . ",2) AS total_amount,
                lager_transfer_items.anzahl,
                ROUND(lager_transfer_items.lager_ek*" . $this->vat_rate->getAsFactor() . ",2) AS item_amount,
                product.product_name AS product,
                product.product_id
            FROM
                lager_transfer INNER JOIN
                lager_transfer_items ON (lager_transfer.lager_transfer_id = lager_transfer_items.lager_transfer_id) INNER JOIN
                product ON (lager_transfer_items.product_id = product.product_id)
            WHERE
                lager_transfer.lager_from IN (" . $this->db->in($this->ecom_lager_ids) . ") AND
                lager_transfer.lager_to IN (" . $this->db->in($this->k11_lager_ids) . ") AND
                lager_transfer.lager_transfer_status = 'beendet' AND
                lager_transfer.date_end BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                lager_transfer.bemerkung NOT LIKE '%ohne Verrechnung%'
        ");

        foreach ($result as $row) {
            $clearing_result->addVerkauf($row);
        }
    }

    private function collectLogistic(K11ClearingResult $clearing_result): void
    {
        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.datum_erledigt,
                order_item.auftnr,
                order_item.ek_nnn_brutto,
                order_item.product,
                order_item.sped_id,
                warenausgang_lieferschein.lager_id
            FROM
                warenausgang_lieferschein INNER JOIN
                orders ON (warenausgang_lieferschein.order_id = orders.order_id) INNER JOIN
                order_item ON (orders.order_id = order_item.order_id)
            WHERE
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                warenausgang_lieferschein.shop_id = 2 AND
                warenausgang_lieferschein.lager_id IN (" . $this->db->in($this->ecom_lager_ids) . ") AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "' AND
                order_item.typ = '" . OrderConst::WARENKORB_TYP_VERSANDAUTO . "' AND
                order_item.typ_value = 'versand' AND
                order_item.sped_id NOT IN (" . $this->db->in($this->speds_to_ignore) . ")
        ");

        foreach ($result as $daten) {
            $clearing_result->addLogistik($daten);
        }

        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.datum_erledigt,
                order_item.auftnr,
                order_item.ek_nnn_brutto,
                order_item.product,
                order_item.sped_id,
                warenausgang_lieferschein.lager_id
            FROM
                warenausgang_lieferschein INNER JOIN
                orders ON (warenausgang_lieferschein.order_id = orders.order_id) INNER JOIN
                order_item ON (orders.order_id = order_item.order_id)
            WHERE
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                warenausgang_lieferschein.shop_id = 2 AND
                warenausgang_lieferschein.lager_id IN (" . $this->db->in($this->k11_lager_ids) . ") AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "' AND
                order_item.typ = '" . OrderConst::WARENKORB_TYP_VERSANDAUTO . "' AND
                order_item.typ_value = 'versand' AND
                order_item.sped_id NOT IN (" . $this->db->in($this->speds_to_ignore) . ")
        ");

        foreach ($result as $daten) {
            $clearing_result->addLogistik($daten);
        }


        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.datum_erledigt,
                order_item.auftnr,
                
                order_item.ek_nnn_brutto,
                order_item.product,
                0 AS 'sped_id',
                warenausgang_lieferschein.lager_id
            FROM
                warenausgang_lieferschein INNER JOIN
                orders ON (warenausgang_lieferschein.order_id = orders.order_id) INNER JOIN
                order_item ON (orders.order_id = order_item.order_id)
            WHERE
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                warenausgang_lieferschein.shop_id = 2 AND
                warenausgang_lieferschein.lager_id IN (" . $this->db->in($this->ecom_lager_ids) . ") AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "' AND
                order_item.typ = '" . OrderConst::WARENKORB_TYP_VERSANDAUTO . "' AND
                order_item.typ_value = 'zahlart' AND
                orders.zahlungs_id = " . OrderConst::PAYMENT_NACHNAHME . "
        ");

        foreach ($result as $daten) {
            $clearing_result->addLogistik($daten);
        }


        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.datum_erledigt,
                order_item.auftnr,
                order_item.ek_nnn_brutto AS ek_nnn_brutto,
                order_item.product,
                order_item.sped_id,
                warenausgang_lieferschein.lager_id
            FROM
                warenausgang_lieferschein INNER JOIN
                orders ON (warenausgang_lieferschein.order_id = orders.order_id) INNER JOIN
                order_item ON (orders.order_id = order_item.order_id)
            WHERE
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                warenausgang_lieferschein.shop_id = " . Shop::ALLEGO . " AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "' AND
                order_item.typ = '" . OrderConst::WARENKORB_TYP_VERSANDAUTO . "' AND
                order_item.typ_value = 'versand' AND
                order_item.sped_id IN (" . $this->db->in($this->speds_reverse) . ")
        ");

        foreach ($result as $daten) {
            $daten['ek_nnn_brutto'] = $this->k11_shipment_costs;
            $clearing_result->addLogistikK11($daten);
        }
    }

    private function collectCod(K11ClearingResult $clearing_result): void
    {
        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.datum_erledigt,
                orders.auftnr,
                SUM(order_item.preis*order_item.quantity) AS nachnahme_betrag
            FROM
                warenausgang_lieferschein INNER JOIN
                orders ON (warenausgang_lieferschein.order_id = orders.order_id) INNER JOIN
                order_item ON (orders.order_id = order_item.order_id)
            WHERE
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                warenausgang_lieferschein.shop_id = 2 AND
                warenausgang_lieferschein.lager_id IN (" . $this->db->in($this->ecom_lager_ids) . ") AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "' AND
                order_item.status < " . OrderConst::STATUS_STORNO . " AND
                orders.zahlungs_id = " . OrderConst::PAYMENT_NACHNAHME . "
            GROUP BY
                orders.order_id
        ");

        foreach ($result as $row) {
            $clearing_result->addNachnahme($row);
        }
    }


    public function collectEinkauf(K11ClearingResult $clearing_result): void
    {
        //ecom aus K11 Lager
        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.datum_erledigt AS datum,
                order_item.auftnr,
                (warenausgang_lieferschein_items.anzahl * order_item.ek_nnn_brutto) AS total_amount,
                warenausgang_lieferschein_items.anzahl,
                order_item.ek_nnn_brutto AS item_amount,
                order_item.product,
                order_item.product_id
            FROM
                warenausgang_lieferschein INNER JOIN
                warenausgang_lieferschein_items ON (warenausgang_lieferschein.lieferschein_id = warenausgang_lieferschein_items.lieferschein_id) INNER JOIN
                order_item ON (warenausgang_lieferschein_items.order_item_id = order_item.order_item_id)
            WHERE
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                warenausgang_lieferschein.shop_id = " . Shop::ALLEGO . " AND
                warenausgang_lieferschein.lager_id IN (" . $this->db->in($this->k11_lager_ids) . ") AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "'
        ");

        foreach ($result as $row) {
            $clearing_result->addEinkauf($row);
        }

        //lagertransfer k11 an ecom
        $result = $this->db->query("
            SELECT
                lager_transfer.date_end AS datum,
                CONCAT('TRANSFER ',lager_transfer.lager_transfer_id) AS auftnr,
                ROUND(lager_transfer_items.anzahl * lager_transfer_items.lager_ek*" . $this->vat_rate->getAsFactor() . ",2) AS total_amount,
                lager_transfer_items.anzahl,
                ROUND(lager_transfer_items.lager_ek*" . $this->vat_rate->getAsFactor() . ",2) AS item_amount,
                product.product_name AS product,
                product.product_id
            FROM
                lager_transfer INNER JOIN
                lager_transfer_items ON (lager_transfer.lager_transfer_id = lager_transfer_items.lager_transfer_id) INNER JOIN
                product ON (lager_transfer_items.product_id = product.product_id)
            WHERE
                lager_transfer.lager_from IN (" . $this->db->in($this->k11_lager_ids) . ") AND
                lager_transfer.lager_to IN (" . $this->db->in($this->ecom_lager_ids) . ") AND
                lager_transfer.lager_transfer_status = 'beendet' AND
                lager_transfer.date_end BETWEEN '" . $this->range_start->db() . "' AND '" . $this->range_end->db() . "' AND
                lager_transfer.bemerkung NOT LIKE '%ohne Verrechnung%'
        ");

        foreach ($result as $row) {
            $clearing_result->addEinkauf($row);
        }
    }
}
