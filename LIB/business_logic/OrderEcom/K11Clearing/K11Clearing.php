<?php

namespace wws\OrderEcom\K11Clearing;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\FileAttachment\FileAttachment;
use DomainException;
use wws\Customer\Customer;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderMemo;
use wws\Order\OrderMemoRepository;

class K11Clearing
{
    private db_generic $db;
    private OrderMemoRepository $order_memo_repository;

    /**
     * @var int
     */
    private $customer_id = 251236;

    /**
     * @var int
     */
    private $dummy_product_id = 15696;

    public function __construct(db_generic $db, OrderMemoRepository $order_memo_repository)
    {
        $this->db = $db;
        $this->order_memo_repository = $order_memo_repository;
    }

    public function createClearingForMonth(string $month, string $year): void
    {
        $range_start = new DateObj($year . '-' . $month . '-01 00:00:00');
        $range_end = clone $range_start;
        $range_end->setToMonthEnd();

        $reference = $range_start->format('m.Y');

        if ($this->clearingExists($reference)) {
            throw new DevException('clearing exits');
        }

        //angenommen ich mach das clearing zeitlich frei... was müsste ich dabei beachten?
        //reference und zeitpunt trennen? -> naming ist ungünstig?! "21.12.2018 bis 25.01.2019" uhrzeiten fehlen... das ist doch schon ungeünstig
        //es bräuchte eine extra tabelle um die zeitbereiche zu merken... es muss sichergestellt werden, das die zeitbereiche nahtlos aneinander sind
        //$reference, \DatePeriod::

        $clearing = new K11ClearingCollector();
        $clearing->setDateRange($range_start, $range_end);

        $result = $clearing->execute();

        $writer = new K11ClearingXlsWriter($result);

        //bestellung erzeugen
        $order = $this->createOrder($reference, $result->getSumTotalVerkauf(), $result->getSumTotalEinkauf());
        $order->save();

        //details als Excel als Anlage speichern
        $memo = new OrderMemo('Abrechnung ' . $reference);
        $memo->setAutoMemo(false);

        $attachment = new FileAttachment();
        $attachment->setFilename('details_' . $range_start->format('Ym') . '.xlsx');

        $this->order_memo_repository->saveAttachment($order->getOrderId(), $attachment, $writer->getAsString());

        $memo->addFileAttachment($attachment);

        $order->addOrderMemo($memo);
        $order->save();

        //rechnung erstellern
        $order->createInvoice();
        $order->save();
    }

    public function createOrder(string $reference, float $verkauf_amount = 2, float $einkauf_amount = 2): Order
    {
        if ($this->clearingExists($reference)) {
            throw new DomainException('clearing exits');
        }

        $customer = new Customer($this->customer_id);

        $order = new Order();
        $order->prefillByCustomer($customer);
        $order->setKundeBeleg($reference);
        $order->setZahlungsart(OrderConst::PAYMENT_RECHNUNG);

        $order_item = $order->createOrderItem($this->dummy_product_id);
        $order_item->setProductName('Wareneinkauf und Logistik ' . $reference . ' (siehe Anlage)');
        $order_item->setVkBrutto($verkauf_amount);
        $order_item->setEkBrutto($verkauf_amount);
        $order_item->setEkNnnBrutto($verkauf_amount);
        $order_item->setStatus(OrderConst::STATUS_ZUSTELLUNG_BEENDET);

        $order_item = $order->createOrderItem($this->dummy_product_id);
        $order_item->setProductName('Wareneinkauf eCom von K11 Ersatzteilshop Gmbh ' . $reference . ' (siehe Anlage)');
        $order_item->setVkBrutto(-$einkauf_amount);
        $order_item->setEkBrutto(-$einkauf_amount);
        $order_item->setEkNnnBrutto(-$einkauf_amount);
        $order_item->setStatus(OrderConst::STATUS_ZUSTELLUNG_BEENDET);

        return $order;
    }

    public function clearingExists(string $reference): bool
    {
        return (bool)$this->getOrderIdForClearing($reference);
    }

    public function getOrderIdForClearing(string $reference): ?int
    {
        return $this->db->fieldQuery("
            SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.customer_id = " . (int)$this->customer_id . " AND
                orders.kunde_beleg = '" . $this->db->escape($reference) . "' AND
                orders.kunde_beleg != ''
        ");
    }
}
