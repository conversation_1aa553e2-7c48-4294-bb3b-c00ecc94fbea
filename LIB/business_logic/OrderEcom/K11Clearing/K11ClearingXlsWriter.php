<?php

namespace wws\OrderEcom\K11Clearing;

use bqp\Utils\PhpSpreadsheet;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class K11ClearingXlsWriter
{
    protected Spreadsheet $xls;

    /**
     * @var K11ClearingResult
     */
    private $clearing_result;

    public function __construct(K11ClearingResult $clearing_result)
    {
        $this->clearing_result = $clearing_result;

        $this->xls = new Spreadsheet();
        $this->createTemplate();
    }

    public function createTemplate()
    {
        $this->xls->setActiveSheetIndex(0)->setTitle('Übersicht');
        $sheet = $this->xls->createSheet(1)->setTitle('Warenverkauf (ECOM an K11)');
        $sheet->setCellValue('A1', 'Datum');
        $sheet->setCellValue('B1', 'Auftnr');
        $sheet->setCellValue('C1', 'Summe');
        $sheet->setCellValue('D1', 'Anzahl');
        $sheet->setCellValue('E1', 'einzel Preis');
        $sheet->setCellValue('F1', 'Produkt');
        $sheet->setCellValue('G1', 'product_id');

        $sheet = $this->xls->createSheet(2)->setTitle('Wareneinkauf (K11 an ECOM)');
        $sheet->setCellValue('A1', 'Datum');
        $sheet->setCellValue('B1', 'Auftnr');
        $sheet->setCellValue('C1', 'Summe');
        $sheet->setCellValue('D1', 'Anzahl');
        $sheet->setCellValue('E1', 'einzel Preis');
        $sheet->setCellValue('F1', 'Produkt');
        $sheet->setCellValue('G1', 'product_id');

        $sheet = $this->xls->createSheet(3)->setTitle('Logistik (ECOM für K11)');
        $sheet->setCellValue('A1', 'Datum');
        $sheet->setCellValue('B1', 'Auftnr');
        $sheet->setCellValue('C1', 'Summe');
        $sheet->setCellValue('D1', 'Produkt');
        $sheet->setCellValue('E1', 'Sped');
        $sheet->setCellValue('F1', 'Lager');
        $sheet->setCellValue('G1', 'sped_id');

        $sheet = $this->xls->createSheet(4)->setTitle('Logistik (K11 für ECOM)');
        $sheet->setCellValue('A1', 'Datum');
        $sheet->setCellValue('B1', 'Auftnr');
        $sheet->setCellValue('C1', 'Summe');
        $sheet->setCellValue('D1', 'Produkt');
        $sheet->setCellValue('E1', 'Sped');
        $sheet->setCellValue('F1', 'Lager');
        $sheet->setCellValue('G1', 'sped_id');

        $sheet = $this->xls->createSheet(5)->setTitle('Nachnahme');
        $sheet->setCellValue('A1', 'Datum');
        $sheet->setCellValue('B1', 'Auftnr');
        $sheet->setCellValue('C1', 'Betrag');
    }

    private function gen(): void
    {
        $this->writeSummary();
        $this->writeVerkaufPositions();
        $this->writeEinkaufPositions();
        $this->writeLogistik();
        $this->writeLogistikK11();
        $this->writeNachnahme();
    }

    public function save($filename): void
    {
        $this->gen();

        $objWriter = new Xlsx($this->xls);
        $objWriter->save($filename);
    }

    public function getAsString(): string
    {
        $this->gen();

        $writer = new Xlsx($this->xls);

        return PhpSpreadsheet::getAsString($writer);
    }

    private function writeSummary(): void
    {
        $sheet = $this->xls->setActiveSheetIndex(0);

        $pos = 1;

        $sheet->setCellValue('A' . $pos, 'Einkauf (inkl. ' . $this->clearing_result->getVatRate()->getValue() . '% Mwst!)');
        $pos++;
        $sheet->setCellValue('A' . $pos, 'Artikel');
        $sheet->setCellValue('C' . $pos, 'Summe');
        $pos++;
        $sheet->setCellValue('A' . $pos, $this->clearing_result->getVerkaufQuantity());
        $sheet->setCellValue('C' . $pos, $this->clearing_result->getVerkaufAmount());
        $sheet->getStyle('C' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

        $sheet->setCellValue('D' . $pos, 'ECOM AN K11');

        $pos++;
        $sheet->setCellValue('A' . $pos, $this->clearing_result->getEinkaufQuantity());
        $sheet->setCellValue('C' . $pos, $this->clearing_result->getEinkaufAmount());
        $sheet->getStyle('C' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

        $sheet->setCellValue('D' . $pos, 'K11 AN ECOM');

        $pos++;
        $pos++;

        $sheet->setCellValue('A' . $pos, 'Logistik (inkl. ' . $this->clearing_result->getVatRate()->getValue() . '% Mwst!)');
        $sheet->setCellValue('B' . $pos, 'ECOM AN K11');
        $pos++;
        $sheet->setCellValue('A' . $pos, 'Spedition');
        $sheet->setCellValue('B' . $pos, 'Sendungen');
        $sheet->setCellValue('C' . $pos, 'Summe');
        $quantity = 0;
        $amount = 0;
        foreach ($this->clearing_result->getLogistikSums() as $sped_name => $logistik) {
            $pos++;
            $sheet->setCellValue('A' . $pos, $sped_name);
            $sheet->setCellValue('B' . $pos, $logistik['quantity']);
            $sheet->setCellValue('C' . $pos, $logistik['amount']);
            $sheet->getStyle('C' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

            $quantity += $logistik['quantity'];
            $amount += $logistik['amount'];
        }

        $pos++;
        $sheet->setCellValue('A' . $pos, 'Summe');
        $sheet->setCellValue('B' . $pos, $quantity);
        $sheet->setCellValue('C' . $pos, $amount);
        $sheet->getStyle('C' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

        $pos++;
        $pos++;

        $sheet->setCellValue('A' . $pos, 'Logistik (inkl. ' . $this->clearing_result->getVatRate()->getValue() . '% Mwst!)');
        $sheet->setCellValue('B' . $pos, 'K11 an ECOM');
        $pos++;
        $sheet->setCellValue('A' . $pos, 'Spedition');
        $sheet->setCellValue('B' . $pos, 'Sendungen');
        $sheet->setCellValue('C' . $pos, 'Summe');
        $quantity = 0;
        $amount = 0;
        foreach ($this->clearing_result->getLogistikK11Sums() as $sped_name => $logistik) {
            $pos++;
            $sheet->setCellValue('A' . $pos, $sped_name);
            $sheet->setCellValue('B' . $pos, $logistik['quantity']);
            $sheet->setCellValue('C' . $pos, $logistik['amount']);
            $sheet->getStyle('C' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

            $quantity += $logistik['quantity'];
            $amount += $logistik['amount'];
        }

        $pos++;
        $sheet->setCellValue('A' . $pos, 'Summe');
        $sheet->setCellValue('B' . $pos, $quantity);
        $sheet->setCellValue('C' . $pos, $amount);
        $sheet->getStyle('C' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

        $pos++;
        $pos++;

        $sheet->setCellValue('A' . $pos, 'zu berechnen ECOM an K11:');
        $sheet->setCellValue('B' . $pos, $this->clearing_result->getSumTotalVerkauf());
        $sheet->getStyle('B' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

        $pos++;

        $sheet->setCellValue('A' . $pos, 'zu berechnen K11 an ECOM:');
        $sheet->setCellValue('B' . $pos, $this->clearing_result->getSumTotalEinkauf());
        $sheet->getStyle('B' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

        $pos++;
        $pos++;

        $sheet->setCellValue('A' . $pos, 'Saldo:');
        $sheet->setCellValue('B' . $pos, $this->clearing_result->getSumTotalVerkauf() - $this->clearing_result->getSumTotalEinkauf());
        $sheet->getStyle('B' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

        $pos++;
        $pos++;

        $sheet->setCellValue('A' . $pos, 'Nachnahmen über ECOM');
        $pos++;
        $sheet->setCellValue('A' . $pos, 'Bestellungen');
        $sheet->setCellValue('B' . $pos, 'Betrag');
        $pos++;
        $sheet->setCellValue('A' . $pos, $this->clearing_result->getNachnahmeQuantity());
        $sheet->setCellValue('B' . $pos, $this->clearing_result->getNachnahmeAmount());
        $sheet->getStyle('B' . $pos)->getNumberFormat()->setFormatCode('#,##0.00€');

    }

    public function writeVerkaufPositions(): void
    {
        $sheet = $this->xls->getSheet(1);

        $pos = 2;

        foreach ($this->clearing_result->getVerkaufPositions() as $row) {
            $sheet->setCellValue('A' . $pos, $row['datum']);
            $sheet->setCellValue('B' . $pos, $row['auftnr']);
            $sheet->setCellValue('C' . $pos, $row['total_amount']);
            $sheet->setCellValue('D' . $pos, $row['anzahl']);
            $sheet->setCellValue('E' . $pos, $row['item_amount']);
            $sheet->setCellValue('F' . $pos, $row['product']);
            $sheet->setCellValue('G' . $pos, $row['product_id']);
            $pos++;
        }
    }

    public function writeEinkaufPositions()
    {
        $sheet = $this->xls->getSheet(2);
        $pos = 2;

        foreach ($this->clearing_result->getEinkaufPositions() as $row) {
            $sheet->setCellValue('A' . $pos, $row['datum']);
            $sheet->setCellValue('B' . $pos, $row['auftnr']);
            $sheet->setCellValue('C' . $pos, $row['total_amount']);
            $sheet->setCellValue('D' . $pos, $row['anzahl']);
            $sheet->setCellValue('E' . $pos, $row['item_amount']);
            $sheet->setCellValue('F' . $pos, $row['product']);
            $sheet->setCellValue('G' . $pos, $row['product_id']);
            $pos++;
        }
    }

    public function writeLogistik(): void
    {
        $sheet = $this->xls->getSheet(3);

        $pos = 2;

        foreach ($this->clearing_result->getLogistikPositions() as $row) {
            $sheet->setCellValue('A' . $pos, $row['datum_erledigt']);
            $sheet->setCellValue('B' . $pos, $row['auftnr']);
            $sheet->setCellValue('C' . $pos, $row['ek_nnn_brutto']);
            $sheet->setCellValue('D' . $pos, $row['product']);
            $sheet->setCellValue('E' . $pos, $row['spedname']);
            $sheet->setCellValue('F' . $pos, $row['lager_id']);
            $sheet->setCellValue('G' . $pos, $row['sped_id']);
            $pos++;
        }
    }

    public function writeLogistikK11(): void
    {
        $sheet = $this->xls->getSheet(4);

        $pos = 2;

        foreach ($this->clearing_result->getLogistikK11Positions() as $row) {
            $sheet->setCellValue('A' . $pos, $row['datum_erledigt']);
            $sheet->setCellValue('B' . $pos, $row['auftnr']);
            $sheet->setCellValue('C' . $pos, $row['ek_nnn_brutto']);
            $sheet->setCellValue('D' . $pos, $row['product']);
            $sheet->setCellValue('E' . $pos, $row['spedname']);
            $sheet->setCellValue('F' . $pos, $row['lager_id']);
            $sheet->setCellValue('G' . $pos, $row['sped_id']);
            $pos++;
        }
    }

    public function writeNachnahme(): void
    {
        $sheet = $this->xls->getSheet(5);

        $pos = 2;

        foreach ($this->clearing_result->getNachnahmePositions() as $row) {
            $sheet->setCellValue('A' . $pos, $row['datum_erledigt']);
            $sheet->setCellValue('B' . $pos, $row['auftnr']);
            $sheet->setCellValue('C' . $pos, $row['nachnahme_betrag']);

            $pos++;
        }
    }
}