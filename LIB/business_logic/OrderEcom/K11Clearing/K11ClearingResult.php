<?php

namespace wws\OrderEcom\K11Clearing;

use bqp\Date\DateObj;
use bqp\Vat\VatRate;
use wws\Shipment\ShipmentRepository;

class K11ClearingResult
{
    /**
     * @var DateObj
     */
    public $date_start;

    /**
     * @var DateObj
     */
    public $date_end;

    /**
     * @var array
     */
    private $sped_names;

    protected $sum_total_verkauf = 0;
    protected $sum_total_einkauf = 0;

    protected $logistik_sums = [];
    protected $logistik_k11_sums = [];

    protected $sum_verkauf = ['amount' => 0, 'quantity' => 0];
    protected $sum_einkauf = ['amount' => 0, 'quantity' => 0];
    protected $sum_nachnahme = ['amount' => 0, 'quantity' => 0];


    private $verkauf_positions = [];
    private $einkauf_positions = [];

    private $logistik_positions = [];
    private $logistik_k11_positions = [];

    private $nachnahme_positions = [];

    /**
     * @var VatRate
     */
    private $vat_rate;

    public function __construct(DateObj $date_start, DateObj $date_end)
    {
        $this->sped_names = ShipmentRepository::getAllSpeds();

        $this->date_start = $date_start;
        $this->date_end = $date_end;
    }

    public function getVerkaufAmount(): float
    {
        return round($this->sum_verkauf['amount'], 2);
    }

    public function getVerkaufQuantity(): int
    {
        return $this->sum_verkauf['quantity'];
    }

    public function getEinkaufAmount(): float
    {
        return $this->sum_einkauf['amount'];
    }

    public function getEinkaufQuantity(): int
    {
        return $this->sum_einkauf['quantity'];
    }

    public function getLogistikSums(): array
    {
        return $this->logistik_sums;
    }

    public function getLogistikK11Sums(): array
    {
        return $this->logistik_k11_sums;
    }

    public function getSumTotalVerkauf(): float
    {
        return round($this->sum_total_verkauf, 2);
    }

    public function getSumTotalEinkauf(): float
    {
        return round($this->sum_total_einkauf, 2);
    }

    public function getNachnahmeAmount(): float
    {
        return $this->sum_nachnahme['amount'];
    }

    public function getNachnahmeQuantity(): int
    {
        return $this->sum_nachnahme['quantity'];
    }

    public function getVerkaufPositions(): array
    {
        return $this->verkauf_positions;
    }

    public function getEinkaufPositions(): array
    {
        return $this->einkauf_positions;
    }

    public function getLogistikPositions(): array
    {
        return $this->logistik_positions;
    }

    public function getLogistikK11Positions(): array
    {
        return $this->logistik_k11_positions;
    }

    public function getNachnahmePositions(): array
    {
        return $this->nachnahme_positions;
    }


    public function addVerkauf(array $row): void
    {
        $this->verkauf_positions[] = $row;

        $this->sum_verkauf['amount'] += $row['total_amount'];
        $this->sum_verkauf['quantity'] += $row['anzahl'];
        $this->sum_total_verkauf += $row['total_amount'];
    }

    public function addEinkauf(array $row): void
    {
        $this->einkauf_positions[] = $row;

        $this->sum_einkauf['amount'] += $row['total_amount'];
        $this->sum_einkauf['quantity'] += $row['anzahl'];
        $this->sum_total_einkauf += $row['total_amount'];
    }

    public function addLogistik(array $row): void
    {
        if ($row['sped_id']) {
            $spedname = $this->getSpedName($row['sped_id']);
        } else {
            $spedname = 'Nachnahmezuschlag';
        }

        $row['spedname'] = $spedname;

        if (!isset($this->logistik_sums[$spedname])) {
            $this->logistik_sums[$spedname] = ['quantity' => 0, 'amount' => 0];
        }

        $this->logistik_sums[$spedname]['amount'] += $row['ek_nnn_brutto'];
        $this->logistik_sums[$spedname]['quantity']++;

        $this->sum_total_verkauf += $row['ek_nnn_brutto'];

        $this->logistik_positions[] = $row;
    }

    public function addLogistikK11(array $row): void
    {
        if ($row['sped_id']) {
            $spedname = $this->getSpedName($row['sped_id']);
        } else {
            $spedname = 'Nachnahmezuschlag';
        }

        $row['spedname'] = $spedname;

        if (!isset($this->logistik_k11_sums[$spedname])) {
            $this->logistik_k11_sums[$spedname] = ['quantity' => 0, 'amount' => 0];
        }
        $this->logistik_k11_sums[$spedname]['amount'] += $row['ek_nnn_brutto'];
        $this->logistik_k11_sums[$spedname]['quantity']++;

        $this->sum_total_einkauf += $row['ek_nnn_brutto'];

        $this->logistik_k11_positions[] = $row;
    }

    public function addNachnahme(array $row): void
    {
        $this->sum_nachnahme['amount'] += $row['nachnahme_betrag'];
        $this->sum_nachnahme['quantity']++;

        $this->nachnahme_positions[] = $row;
    }

    public function setVatRate(VatRate $vat_rate): void
    {
        $this->vat_rate = $vat_rate;
    }

    public function getVatRate(): VatRate
    {
        return $this->vat_rate;
    }

    private function getSpedName(int $sped_id): string
    {
        return $this->sped_names[$sped_id];
    }
}
