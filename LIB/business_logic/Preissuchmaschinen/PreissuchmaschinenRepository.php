<?php

namespace wws\Preissuchmaschinen;

use bqp\db\db_generic;
use db;

class PreissuchmaschinenRepository
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function load(int $psm_id): Preissuchmaschine
    {
        return $this->db->getDoctrine()->find(Preissuchmaschine::class, $psm_id);
    }

    public function loadByTrackingCode(string $tracking_code): Preissuchmaschine
    {
        return $this->db->getDoctrine()->getRepository(Preissuchmaschine::class)->findOneBy(['trackingcode' => $tracking_code]);
    }

    public function save(Preissuchmaschine $psm): int
    {
        $this->db->getDoctrine()->flush();

        return $psm->getId();
    }

    /**
     * @return Preissuchmaschine
     */
    public function create(): Preissuchmaschine
    {
        $psm = new Preissuchmaschine();
        $this->db->getDoctrine()->persist($psm);

        return $psm;
    }


    public static function getPsmNameByTrackingId(string $tracking_id): string
    {
        static $psms = null;

        if ($psms === null) {
            $psms = db::getInstance()->query("
                SELECT
                    einst_preissuchmaschinen.psm_name,
                    einst_preissuchmaschinen.trackingcode
                FROM
                    einst_preissuchmaschinen
            ")->asSingleArray('trackingcode');
        }

        if (isset($psms[$tracking_id])) {
            return $psms[$tracking_id];
        }

        return 'Unbekannt (' . $tracking_id . ')';
    }

    public static function getFilterListIdByTrackingId(string $tracking_id): int
    {
        $db = db::getInstance();

        return $db->fieldQuery("
            SELECT
                einst_preissuchmaschinen.filter_list_id
            FROM
                einst_preissuchmaschinen
            WHERE
                einst_preissuchmaschinen.trackingcode = '" . $db->escape($tracking_id) . "'
        ");
    }

    public static function isPsmOnlineByTrackingId(string $tracking_id): bool
    {
        $db = db::getInstance();

        return $db->fieldQuery("
            SELECT
                einst_preissuchmaschinen.psm_status
            FROM
                einst_preissuchmaschinen
            WHERE
                einst_preissuchmaschinen.trackingcode = '" . $db->escape($tracking_id) . "'
        ") == 'online';
    }

    public static function getPsmNames(): array
    {
        static $psms = null;

        if ($psms === null) {
            $psms = db::getInstance()->query("
                SELECT
                    einst_preissuchmaschinen.psm_id,
                    einst_preissuchmaschinen.psm_name
                FROM
                    einst_preissuchmaschinen
            ")->asSingleArray('psm_id');
        }

        return $psms;
    }

    public static function getPsmNamesWithTrackingCodeShowInStats(): array
    {
        return db::getInstance()->query("
            SELECT
                einst_preissuchmaschinen.trackingcode,
                einst_preissuchmaschinen.psm_name
            FROM
                einst_preissuchmaschinen
            WHERE
                einst_preissuchmaschinen.show_in_stats = 1
        ")->asSingleArray('trackingcode');
    }
}
