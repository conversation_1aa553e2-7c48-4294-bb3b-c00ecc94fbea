<?php

namespace wws\Preissuchmaschinen;

class Preissuchmaschine
{
    protected $psm_id;
    protected $psm_name;
    protected $bemerkung;
    protected $url;
    protected $cpc;
    protected $grundgebuehr;
    protected $psm_status;
    protected $trackingcode;
    protected $filter_list_id;

    protected int $price_group_id = 0;

    public function __construct()
    {
    }

    public function setName($value)
    {
        $this->psm_name = $value;
    }

    public function setBemerkung($value)
    {
        $this->bemerkung = $value;
    }

    public function setUrl($value)
    {
        $this->url = $value;
    }

    public function setCpc($value)
    {
        $this->cpc = $value;
    }

    public function setGrundgebuehr($value)
    {
        $this->grundgebuehr = $value;
    }

    public function setStatus($value)
    {
        $this->psm_status = $value;
    }

    public function setTrackingcode($value)
    {
        $this->trackingcode = $value;
    }

    public function setFilterListId($filter_list_id)
    {
        $this->filter_list_id = $filter_list_id;
    }

    public function getId(): int
    {
        return (int)$this->psm_id;
    }

    public function getName()
    {
        return $this->psm_name;
    }

    public function getBemerkung()
    {
        return $this->bemerkung;
    }

    public function getUrl()
    {
        return $this->url;
    }

    public function getCpc()
    {
        return $this->cpc;
    }

    public function getGrundgebuehr()
    {
        return $this->grundgebuehr;
    }

    public function getStatus()
    {
        return $this->psm_status;
    }

    public function isEnabled(): bool
    {
        return $this->getStatus() === 'online';
    }

    public function getTrackingcode()
    {
        return $this->trackingcode;
    }

    public function getFilterListId()
    {
        return $this->filter_list_id;
    }

    public function getPriceGroupId(): int
    {
        return $this->price_group_id;
    }

    public function setPriceGroupId(int $price_group_id): void
    {
        $this->price_group_id = $price_group_id;
    }
}
