<?php

namespace wws\OrderRefund;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\extern\Amazon\Order\AmazonOrderRefundExecuter;
use bqp\extern\Paypal\PaypalFactory;
use bqp\extern\Unzer\UnzerFactory;
use bqp\Utils\ArrayUtils;
use service_loader;
use wws\Order\Order;

class OrderRefundRepository
{
    public const REFUND_METHOD_AMAZON = 3;
    public const REFUND_METHOD_PAYPAL = 4;
    public const REFUND_METHOD_BANK = 2;
    public const REFUND_METHOD_UNZER = 9;

    protected db_generic $db;
    private OrderRefundMethodResolver $refund_method_resolver;

    public function __construct(db_generic $db, OrderRefundMethodResolver $refund_method_resolver)
    {
        $this->db = $db;
        $this->refund_method_resolver = $refund_method_resolver;
    }


    /**
     * @param Order $order
     * @return OrderRefund
     */
    public function createByOrder(Order $order): OrderRefund
    {
        $rueckzahlung = new OrderRefund();
        $rueckzahlung->setOrderId($order->getOrderId());

        //bankdaten aus kunden nachladen
        $daten = $this->db->singleQuery("
                SELECT
                    customers.blz,
                    customers.knr,
                    customers.bn,
                    customers.kin,
                    customers.iban,
                    customers.bic
                FROM
                    customers
                WHERE
                    customers.customer_id = '" . (int)$order->getCustomerId() . "'
        ");

        $rueckzahlung->setBlz($daten['blz']);
        $rueckzahlung->setKontonummer($daten['knr']);
        $rueckzahlung->setBank($daten['bn']);
        $rueckzahlung->setInhaber($daten['kin']);
        $rueckzahlung->setIban($daten['iban']);
        $rueckzahlung->setBic($daten['bic']);

        $refund_method_id = $this->refund_method_resolver->getRefundMethodIdByOrder($order);

        $rueckzahlung->setRefundMethodId($refund_method_id);

        return $rueckzahlung;
    }

    public function load($refund_id): OrderRefund
    {
        return new OrderRefund($refund_id);
    }

    /**
     *
     * @param OrderRefund $rueckzahlung
     * @return int $rückzahlungs_id
     */
    public function save(OrderRefund $rueckzahlung): int
    {
        return $rueckzahlung->save();
    }

    /**
     * @param int $refund_method_id
     * @param int $shop_id
     * @return OrderRefund[]
     */
    public function getQueuedRefunds(int $refund_method_id, int $shop_id): array
    {
        $refund_ids = $this->db->query("
            SELECT
                DISTINCT buchhaltung_rueckzahlung.refund_id
            FROM
                buchhaltung_rueckzahlung INNER JOIN
                orders ON (buchhaltung_rueckzahlung.order_id = orders.order_id)
            WHERE
                buchhaltung_rueckzahlung.refund_status = '" . OrderRefund::STATUS_QUEUED . "' AND
                buchhaltung_rueckzahlung.refund_method_id = '" . $refund_method_id . "' AND
                orders.shop_id = '" . $shop_id . "'
            ORDER BY
                buchhaltung_rueckzahlung.refund_id
        ")->asSingleArray();

        $order_refunds = [];

        foreach ($refund_ids as $refund_id) {
            $order_refunds[] = $this->load($refund_id);
        }

        return $order_refunds;
    }


    public function getPriotritaetenColor($prioritaet): string
    {
        switch ($prioritaet) {
            case 3:
                return '#00cc00';
            case 2:
                return '#ffff00';
            case 1:
                return '#cc0000';
        }

        return '';
    }

    public function getPrioritaeten(): array
    {
        return [
            '3' => 'Niedrig',
            '2' => 'Mittel',
            '1' => 'Hoch'
        ];
    }

    public function getRefundMethodNames(): array
    {
        return ArrayUtils::extract($this->getRefundMethods(), 'refund_method_name', true);
    }

    public function getRefundMethods(): array
    {
        static $refund_methods = null;

        if ($refund_methods === null) {
            $refund_methods = $this->db->query("
                SELECT
                    einst_refund_methods.refund_method_id,
                    einst_refund_methods.refund_method_name,
                    einst_refund_methods.refund_queueable,
                    einst_refund_methods.refund_instantable
                FROM
                    einst_refund_methods
            ")->asArray('refund_method_id');
        }

        return $refund_methods;
    }

    public function getAllRefundMethodExtReasons(): array
    {
        return $this->db->query("
            SELECT
                einst_refund_method_ext_reason.refund_method_id, 
                einst_refund_method_ext_reason.refund_ext_reason_id,
                einst_refund_method_ext_reason.refund_ext_reason_name,
                einst_refund_method_ext_reason.sort
            FROM
                einst_refund_method_ext_reason
            ORDER BY
                einst_refund_method_ext_reason.sort,
                einst_refund_method_ext_reason.refund_ext_reason_name
        ")->asMultiArray('refund_method_id');
    }

    public function getAllRefundMethodExtReasonNames(): array
    {
        $result = $this->db->query("
            SELECT
                einst_refund_method_ext_reason.refund_method_id, 
                einst_refund_method_ext_reason.refund_ext_reason_id,
                einst_refund_method_ext_reason.refund_ext_reason_name
            FROM
                einst_refund_method_ext_reason
            ORDER BY
                einst_refund_method_ext_reason.sort,
                einst_refund_method_ext_reason.refund_ext_reason_name
        ");

        $return = [];

        foreach ($result as $row) {
            if (!isset($return[$row['refund_method_id']])) {
                $return[$row['refund_method_id']] = [];
            }

            $return[$row['refund_method_id']][$row['refund_ext_reason_id']] = $row['refund_ext_reason_name'];
        }

        return $return;
    }

    public function getRefundStatusNames()
    {
        return [
            OrderRefund::STATUS_OUTSTANDING => 'offen',
            OrderRefund::STATUS_EXECUTED => 'erledigt',
            OrderRefund::STATUS_CANCELED => 'abgebrochen',
            OrderRefund::STATUS_QUEUED => 'eingereiht',
            OrderRefund::STATUS_QUEUE_PROCESSING => 'eingereiht (wird verarbeitet)',
            OrderRefund::STATUS_FAILED => 'fehler'
        ];
    }

    public function getRefundStatusName(string $refund_status): string
    {
        return $this->getRefundStatusNames()[$refund_status];
    }

    public function isRefundMethodQueueable($refund_method_id): bool
    {
        return (bool)$this->getRefundMethods()[$refund_method_id]['refund_queueable'];
    }

    public function isRefundMethodInstantable($refund_method_id): bool
    {
        return (bool)$this->getRefundMethods()[$refund_method_id]['refund_instantable'];
    }

    /**
     * @param OrderRefund $order_refund
     * @return OrderRefundMethodExecuter
     */
    public function getRefundExecuter(OrderRefund $order_refund): OrderRefundMethodExecuter
    {
        switch ($order_refund->getRefundMethodId()) {
            case self::REFUND_METHOD_PAYPAL:
                $shop_id = $this->db->fieldQuery("
                    SELECT
                        orders.shop_id
                    FROM
                        orders
                    WHERE
                        orders.order_id = '" . (int)$order_refund->getOrderId() . "'
                ");

                return PaypalFactory::getPaypalOrderRefundExecuter($shop_id);
            case self::REFUND_METHOD_BANK:
                return service_loader::get(OrderRefundMethodExecuterBank::class);
            case self::REFUND_METHOD_AMAZON:
                return service_loader::get(AmazonOrderRefundExecuter::class);
            case self::REFUND_METHOD_UNZER:
                return service_loader::get(UnzerFactory::class)->getUnzerRefundExecuter();
        }

        throw new DevException('no refund executer defined');
    }


    public function searchGutschriftsNummer(OrderRefund $order_refund): ?string
    {
        return $this->db->fieldQuery("
            SELECT
                buchhaltung_gutschriften.gs_nr
            FROM
                buchhaltung_gutschriften
            WHERE
                buchhaltung_gutschriften.order_id = " . $order_refund->getOrderId() . " AND
                ABS(buchhaltung_gutschriften.gs_wert - " . $order_refund->getBetrag() . ") < 0.1     
        ");
    }
}
