<?php

namespace wws\OrderRefund;

use bqp\db\db_generic;

class OrderRefundExtReasonMapping
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * @param int $refund_method_id
     * @param int $gutschrift_reason_id
     * @return string
     */
    public function byGutschriftReasonId(int $refund_method_id, int $gutschrift_reason_id): string
    {
        return (string)$this->db->fieldQuery("
            SELECT
                einst_refund_method_ext_reason_gutschrift_map.refund_ext_reason_id
            FROM
                einst_refund_method_ext_reason_gutschrift_map
            WHERE
                einst_refund_method_ext_reason_gutschrift_map.refund_method_id = '" . $refund_method_id . "' AND
                einst_refund_method_ext_reason_gutschrift_map.gutschrift_reason_id = '" . $gutschrift_reason_id . "'
        ");
    }

    /**
     * @param int $refund_method_id
     * @param int $storno_reason_id
     * @return string
     */
    public function byStornoReasonId(int $refund_method_id, int $storno_reason_id): string
    {
        return (string)$this->db->fieldQuery("
            SELECT
                einst_refund_method_ext_reason_storno_map.refund_ext_reason_id
            FROM
                einst_refund_method_ext_reason_storno_map
            WHERE
                einst_refund_method_ext_reason_storno_map.refund_method_id = '" . $refund_method_id . "' AND
                einst_refund_method_ext_reason_storno_map.storno_reason_id = '" . $storno_reason_id . "'
        ");
    }
}