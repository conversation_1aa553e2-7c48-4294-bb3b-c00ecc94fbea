<?php

namespace wws\OrderRefund;

use bqp\Exceptions\DevException;

class OrderRefundOutputHelper
{
    /**
     * @var OrderRefundRepository
     */
    protected $order_refund_repository;

    public function __construct(OrderRefundRepository $order_refund_repository)
    {
        $this->order_refund_repository = $order_refund_repository;
    }

    public function tableHelper_prioritaet($daten)
    {
        $prios = $this->order_refund_repository->getPrioritaeten();

        return $prios[$daten['prioritaet']];
    }

    /**
     * @param $daten
     * @return string
     * @todo OrderRefundRepository->getRefundStatusNames() nutzen
     */
    public function tableHelper_status($daten): string
    {
        switch ($daten['refund_status']) {
            case OrderRefund::STATUS_OUTSTANDING:
                return 'offen';
            case OrderRefund::STATUS_EXECUTED:
                return 'erledigt';
            case OrderRefund::STATUS_CANCELED:
                return 'abgebrochen';
            case OrderRefund::STATUS_QUEUED:
                return 'eingereiht';
            case OrderRefund::STATUS_QUEUE_PROCESSING:
                return 'eingereiht (wird verarbeitet)';
            case OrderRefund::STATUS_FAILED:
                return 'fehler';
            default:
                throw new DevException('unknown status');
        }
    }

    public function tableHelper_refound_method($daten)
    {
        $methods = $this->order_refund_repository->getRefundMethodNames();

        return $methods[$daten['refund_method_id']];
    }
}
