<?php

namespace wws\OrderRefund;

interface OrderRefundMethodExecuter
{
    /**
     * @param OrderRefund $order_refund
     * @return OrderRefundMethodExecuterResult
     */
    public function executeRefund(OrderRefund $order_refund): OrderRefundMethodExecuterResult;

    /**
     * @param OrderRefund $order_refund
     * @return bool
     */
    public function isQueueable(OrderRefund $order_refund): bool;

    /**
     * @return string
     */
    public function getNotQueueableReason(): string;
}
