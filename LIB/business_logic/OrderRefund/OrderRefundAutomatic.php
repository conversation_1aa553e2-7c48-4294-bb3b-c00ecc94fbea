<?php

namespace wws\OrderRefund;

use bqp\db\db_generic;

class OrderRefundAutomatic
{
    private db_generic $db;
    private OrderRefundRepository $refund_repository;

    public function __construct(
        db_generic $db,
        OrderRefundRepository $refund_repository
    ) {
        $this->db = $db;
        $this->refund_repository = $refund_repository;
    }


    public function run(): void
    {
        $result = $this->db->query("
            SELECT
                buchhaltung_rueckzahlung.refund_id
            FROM
                buchhaltung_rueckzahlung
            WHERE
                buchhaltung_rueckzahlung.refund_status = '" . OrderRefund::STATUS_OUTSTANDING . "' AND
                buchhaltung_rueckzahlung.refund_method_id IN (" . OrderRefundConst::ORDER_REFUND_METHOD_ID_AMAZON . ", " . OrderRefundConst::ORDER_REFUND_METHOD_ID_PAYPAL . ") AND
                buchhaltung_rueckzahlung.added < NOW() - INTERVAL 1 HOUR
        ");

        foreach ($result as $row) {
            $refund = $this->refund_repository->load($row['refund_id']);

            if ($this->isAutomaticRefundable($refund)) {
                $refund->setRefundStatus(OrderRefund::STATUS_QUEUED);
                $this->refund_repository->save($refund);
            }
        }
    }

    private function getOrderTotalAmountIncludingCanceledPositions(int $order_id): float
    {
        return (float)$this->db->fieldQuery("
            SELECT
                SUM(order_item.quantity * order_item.preis)
            FROM
                order_item
            WHERE
                order_item.order_id = $order_id
        ");
    }

    private function isAutomaticRefundable(OrderRefund $order_refund): bool
    {
        if ($order_refund->getRefundMethodId() == OrderRefundConst::ORDER_REFUND_METHOD_ID_PAYPAL) {
            return true;
        }

        if ($order_refund->getRefundMethodId() == OrderRefundConst::ORDER_REFUND_METHOD_ID_AMAZON) {
            $total = $this->getOrderTotalAmountIncludingCanceledPositions($order_refund->getOrderId());
            if (abs($total - $order_refund->getBetrag()) < 0.01) {
                return true;
            }
        }

        return false;
    }
}
