<?php

namespace wws\OrderRefund;

use bqp\Date\DateObj;
use bqp\Event\EventDispatcher;
use bqp\Exceptions\InputException;
use bqp\field_manager;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use db;
use service_loader;
use wws\OrderRefund\Event\EventOrderRefundCanceled;
use wws\OrderRefund\Event\EventOrderRefundCreated;
use wws\OrderRefund\Event\EventOrderRefundExecuted;

class OrderRefund
{
    const STATUS_OUTSTANDING = 'outstanding';
    const STATUS_EXECUTED = 'execute';
    const STATUS_CANCELED = 'cancel';
    const STATUS_QUEUED = 'queue';
    const STATUS_QUEUE_PROCESSING = 'queue_processing';
    const STATUS_FAILED = 'fail';

    protected SmartDataObj $daten;

    public function __construct($refund_id = null)
    {
        $this->daten = new SmartDataObj($this);

        if ($refund_id !== null) {
            $this->load($refund_id);
        } else {
            $this->loadDefaults();
        }
    }

    private function loadDefaults()
    {
        $defaults = field_manager::getInstance()->getDefaults('buchhaltung_rueckzahlung');

        $this->daten->setDefaults($defaults);
    }

    public function load($refund_id)
    {
        $daten = db::getInstance()->singleQuery("
                SELECT
                    buchhaltung_rueckzahlung.refund_id,
                    buchhaltung_rueckzahlung.order_id,
                    buchhaltung_rueckzahlung.refund_status,
                    buchhaltung_rueckzahlung.added,
                    buchhaltung_rueckzahlung.date_ended,
                    buchhaltung_rueckzahlung.prioritaet,
                    buchhaltung_rueckzahlung.betrag,
                    buchhaltung_rueckzahlung.inhaber, 
                    buchhaltung_rueckzahlung.bank,
                    buchhaltung_rueckzahlung.kontonummer,
                    buchhaltung_rueckzahlung.blz,
                    buchhaltung_rueckzahlung.user_id,
                    buchhaltung_rueckzahlung.iban,
                    buchhaltung_rueckzahlung.bic,
                    buchhaltung_rueckzahlung.frist,
                    buchhaltung_rueckzahlung.bemerkung,
                    buchhaltung_rueckzahlung.gsnr,
                    buchhaltung_rueckzahlung.vormerken,
                    buchhaltung_rueckzahlung.refund_method_id,
                    buchhaltung_rueckzahlung.refund_ext_reason_id
                FROM
                    buchhaltung_rueckzahlung
                WHERE
                    buchhaltung_rueckzahlung.refund_id = '" . (int)$refund_id . "'
        ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException('Rückzahlung konnte nicht geladen werden. (' . $refund_id . ')');
        }

        $this->daten->loadDaten($daten);
    }

    /**
     *
     * @return int $refund_id
     */
    public function save(): int
    {
        $this->validate();

        if ($this->daten->getObjStatus() == SmartDataObj::STATUS_NEW) {
            $this->setAdded(new DateObj());
            global $_user;
            if ($_user) {
                $this->setUserId($_user->getUserId());
            }
        }

        $changes = $this->daten->getChanges(SmartDataObj::CHANGES_NEW_VALUES);

        if (!$changes) {
            return $this->getRefundId();
        }

        $db = db::getInstance();

        foreach ($changes as $key => $value) {
            $sql[] = "buchhaltung_rueckzahlung.$key = '" . $db->escape($value) . "'";
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db->query("
                    INSERT INTO
                        buchhaltung_rueckzahlung
                    SET
                        " . implode(',', $sql) . "
                ");

                $this->daten->setterDirect('refund_id', $db->insert_id());
                $this->daten->setSaved();

                service_loader::getEventDispatcher()->dispatch(new EventOrderRefundCreated($this));
                break;
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                    UPDATE
                        buchhaltung_rueckzahlung
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        buchhaltung_rueckzahlung.refund_id = '" . $this->getRefundId() . "'
                    LIMIT
                        1
                ");

                $this->daten->setSaved();
                break;
            case SmartDataObj::STATUS_DEL:
                $db->query("
                    DELETE
                        buchhaltung_rueckzahlung
                    WHERE
                        buchhaltung_rueckzahlung.refund_id = '" . $this->getRefundId() . "'
                    LIMIT
                        1
                ");
                break;
        }

        if (isset($changes['refund_status'])) {
            if ($changes['refund_status'] === self::STATUS_EXECUTED) {
                $event_service = service_loader::getDiContainer()->get(EventDispatcher::class);
                $event_service->dispatch(new EventOrderRefundExecuted($this));
            }

            if ($changes['refund_status'] === self::STATUS_CANCELED) {
                $event_service = service_loader::getDiContainer()->get(EventDispatcher::class);
                $event_service->dispatch(new EventOrderRefundCanceled($this));
            }
        }

        return $this->getRefundId();
    }

    public function validate()
    {
        $errors = new InputException();

        if (!$this->getBetrag()) {
            $errors->add('betrag', 'Bitte geben Sie ein Betrag an.');
        }

        $errors->check();
    }

    /**
     * setzt das Feld order_id
     * @param $order_id
     * @return boolean
     */
    public function setOrderId($order_id)
    {
        return $this->daten->setter('order_id', $order_id);
    }

    /**
     * setzt das Feld added
     * @param DateObj $added
     * @return bool
     */
    public function setAdded(DateObj $added): bool
    {
        return $this->daten->setter('added', $added->db());
    }

    public function setDateEnded(DateObj $added): bool
    {
        return $this->daten->setter('date_ended', $added->db());
    }

    /**
     * setzt das Feld prioritaet
     * @param $prioritaet
     * @return boolean
     */
    public function setPrioritaet($prioritaet)
    {
        return $this->daten->setter('prioritaet', $prioritaet);
    }

    /**
     * setzt das Feld betrag
     * @param $betrag
     * @return boolean
     */
    public function setBetrag($betrag)
    {
        return $this->daten->setter('betrag', $betrag);
    }

    /**
     * setzt das Feld inhaber
     * @param $inhaber
     * @return boolean
     */
    public function setInhaber($inhaber)
    {
        return $this->daten->setter('inhaber', $inhaber);
    }

    /**
     * setzt das Feld bank
     * @param $bank
     * @return boolean
     */
    public function setBank($bank)
    {
        return $this->daten->setter('bank', $bank);
    }

    /**
     * setzt das Feld kontonummer
     * @param $kontonummer
     * @return boolean
     */
    public function setKontonummer($kontonummer)
    {
        return $this->daten->setter('kontonummer', $kontonummer);
    }

    /**
     * setzt das Feld blz
     * @param $blz
     * @return boolean
     */
    public function setBlz($blz)
    {
        return $this->daten->setter('blz', $blz);
    }

    /**
     * @param int $user_id
     * @return boolean
     */
    public function setUserId(int $user_id): bool
    {
        return $this->daten->setter('user_id', $user_id);
    }

    /**
     * setzt das Feld iban
     * @param $iban
     * @return boolean
     */
    public function setIban($iban)
    {
        return $this->daten->setter('iban', $iban);
    }

    /**
     * setzt das Feld bic
     * @param $bic
     * @return boolean
     */
    public function setBic($bic)
    {
        return $this->daten->setter('bic', $bic);
    }

    /**
     * setzt das Feld frist
     * @param DateObj $frist
     * @return bool
     */
    public function setFrist(DateObj $frist): bool
    {
        return $this->daten->setter('frist', $frist->db('date'));
    }

    /**
     * setzt das Feld bemerkung
     * @param $bemerkung
     * @return boolean
     */
    public function setBemerkung($bemerkung)
    {
        return $this->daten->setter('bemerkung', $bemerkung);
    }


    public function addBemerkung($bemerkung): bool
    {
        $org = $this->getBemerkung();
        if ($org) {
            $org = trim($org) . "\n\n";
        }
        return $this->setBemerkung($org . $bemerkung);
    }

    /**
     * setzt das Feld gsnr
     * @param $gsnr
     * @return boolean
     */
    public function setGsnr($gsnr)
    {
        return $this->daten->setter('gsnr', $gsnr);
    }

    public function setRefundStatus(string $refund_status): bool
    {
        $changed = $this->daten->setter('refund_status', $refund_status);

        if ($refund_status == self::STATUS_EXECUTED && $changed) {
            $this->setDateEnded(new DateObj());
        }

        return $changed;
    }

    /**
     * setzt das Feld vormerken
     * @param $vormerken
     * @return boolean
     */
    public function setVormerken($vormerken)
    {
        return $this->daten->setter('vormerken', $vormerken);
    }

    public function setRefundMethodId($refund_method_id)
    {
        return $this->daten->setter('refund_method_id', $refund_method_id);
    }


    /**
     * gibt das Feld refund_id zurück
     * @return int $refund_id
     */
    public function getRefundId(): int
    {
        return (int)$this->daten->getter('refund_id');
    }

    /**
     * gibt das Feld order_id zurück
     * @return int order_id
     */
    public function getOrderId(): int
    {
        return (int)$this->daten->getter('order_id');
    }

    /**
     * gibt das Feld added zurück
     * @return DateObj added
     */
    public function getAdded(): DateObj
    {
        return new DateObj($this->daten->getter('added'));
    }

    public function getDateEnded(): DateObj
    {
        return new DateObj($this->daten->getter('date_ended'));
    }

    /**
     * gibt das Feld prioritaet zurück
     * @return string prioritaet
     */
    public function getPrioritaet()
    {
        return $this->daten->getter('prioritaet');
    }

    /**
     * gibt das Feld betrag zurück
     * @return float betrag
     */
    public function getBetrag()
    {
        return (float)$this->daten->getter('betrag');
    }

    /**
     * gibt das Feld inhaber zurück
     * @return string inhaber
     */
    public function getInhaber()
    {
        return $this->daten->getter('inhaber');
    }

    /**
     * gibt das Feld bank zurück
     * @return string bank
     */
    public function getBank()
    {
        return $this->daten->getter('bank');
    }

    /**
     * gibt das Feld kontonummer zurück
     * @return string kontonummer
     */
    public function getKontonummer()
    {
        return $this->daten->getter('kontonummer');
    }

    /**
     * gibt das Feld blz zurück
     * @return string blz
     */
    public function getBlz()
    {
        return $this->daten->getter('blz');
    }

    /**
     * gibt das Feld editor zurück
     * @return string editor
     */
    public function getUserId()
    {
        return $this->daten->getter('user_id');
    }

    /**
     * gibt das Feld iban zurück
     * @return string iban
     */
    public function getIban()
    {
        return $this->daten->getter('iban');
    }

    /**
     * gibt das Feld bic zurück
     * @return string bic
     */
    public function getBic()
    {
        return $this->daten->getter('bic');
    }

    /**
     * gibt das Feld frist zurück
     * @return string frist
     */
    public function getFrist()
    {
        $frist_stamp = $this->daten->getter('frist');

        $frist = new DateObj($frist_stamp);
        if (!$frist_stamp) {
            $frist->setValid(false);
        }

        return $frist;
    }

    /**
     * gibt das Feld bemerkung zurück
     * @return string bemerkung
     */
    public function getBemerkung()
    {
        return $this->daten->getter('bemerkung');
    }

    /**
     * gibt das Feld gsnr zurück
     * @return string gsnr
     */
    public function getGsnr()
    {
        return $this->daten->getter('gsnr');
    }

    /**
     * @return string
     */
    public function getRefundStatus(): string
    {
        return $this->daten->getter('refund_status');
    }

    /**
     * gibt das Feld vormerken zurück
     * @return string vormerken
     */
    public function getVormerken()
    {
        return $this->daten->getter('vormerken');
    }

    public function getRefundMethodId()
    {
        return $this->daten->getter('refund_method_id');
    }

    public function getRefundExtReasonId(): string
    {
        return $this->daten->getter('refund_ext_reason_id');
    }

    public function setRefundExtReasonId(string $refund_ext_reason_id): bool
    {
        return $this->daten->setter('refund_ext_reason_id', $refund_ext_reason_id);
    }
}
