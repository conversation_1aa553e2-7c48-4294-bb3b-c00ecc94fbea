<?php

namespace wws\OrderRefund;

use bqp\Csv\Column\CsvColumn;
use bqp\Csv\CsvWriter;
use bqp\Exceptions\DevException;
use db;
use wws\Order\Order;

class OrderRefundMethodExecuterBank implements OrderRefundMethodExecuter
{
    /**
     * @var string
     */
    private $not_queueable_reason = '';

    /**
     * @var OrderRefundRepository
     */
    private $order_refund_repository;

    public function __construct(OrderRefundRepository $order_refund_repository)
    {
        $this->order_refund_repository = $order_refund_repository;
    }

    /**
     * @param OrderRefund $order_refund
     * @return OrderRefundMethodExecuterResult
     */
    public function executeRefund(OrderRefund $order_refund): OrderRefundMethodExecuterResult
    {
        throw new DevException('not implemented');
    }

    /**
     * @param OrderRefund $order_refund
     * @return bool
     */
    public function isQueueable(OrderRefund $order_refund): bool
    {
        $this->not_queueable_reason = '';

        if (!trim($order_refund->getIban())) {
            $this->not_queueable_reason = 'Die IBAN fehlt.';
            return false;
        }

        if ($order_refund->getBetrag() <= 0) {
            $this->not_queueable_reason = 'Es wurde kein Rückzahlungsbetrag angegeben.';
            return false;
        }

        if (!trim($order_refund->getInhaber())) {
            $this->not_queueable_reason = 'Der Kontoinhaber fehlt.';
            return false;
        }

        $prefix = substr($order_refund->getIban(), 0, 2);
        if ($prefix !== 'DE' && !$order_refund->getBic()) {
            $this->not_queueable_reason = 'Die BIC fehlt.';
            return false;
        }

        return true;
    }

    /**
     * @return string
     */
    public function getNotQueueableReason(): string
    {
        return $this->not_queueable_reason;
    }

    /**
     * hrm... vielleicht doch besser pro shop eine neue rückzahlungsart anlegen, dann kann die iban über den construct rein
     *
     * @param $shop_id
     * @return mixed
     */
    protected function getSenderIban($shop_id)
    {
        static $ibans = null;

        if ($ibans === null) {
            $ibans = db::getInstance()->query("
                SELECT
                    einst_shop.shop_id,
                    einst_shop.iban
                FROM
                    einst_shop
            ")->asSingleArray('shop_id');
        }

        return $ibans[$shop_id];
    }

    public function createExport(array $refund_ids): string
    {
        $refund_repository = OrderRefundFactory::getOrderRefundRepository();

        $csv = new CsvWriter();
        $csv->setHeader(false);
        $csv->setEncoding('CP1252');

        $empfaenger_bic = new CsvColumn('empfaenger_bic', 'BIC');
        $empfaenger_bic->setQuoteOverride($csv::QUOTES_NONE);

        $amount = new CsvColumn('amount', 'Rechnungsbetrag');
        $amount->setQuoteOverride($csv::QUOTES_NONE);

        $csv->setColumnsWithName([
            /*'sender_iban' => 'AG-IBAN',*/
            'empfaenger' => 'Begünstiger/Zahlungspflichtiger',
            $empfaenger_bic,
            'empfaenger_iban' => 'IBAN',
            $amount,
            'auftragsart' => 'Auftragsart',
            'verwendungszweck1' => 'VWZ1',
            'verwendungszweck2' => 'VWZ2'
        ]);

        foreach ($refund_ids as $refund_id) {
            $refund = $refund_repository->load($refund_id);

            $order = new Order($refund->getOrderId());

            $csv->setValue('auftragsart', 'CCS');
            //$csv->setValue('sender_iban', $this->getSenderIban($order->getShopId()));
            $csv->setValue('empfaenger', $refund->getInhaber());
            $csv->setValue('empfaenger_bic', $refund->getBic());
            $csv->setValue('empfaenger_iban', str_replace(' ', '', $refund->getIban()));
            $csv->setValue('amount', number_format($refund->getBetrag(), 2, ',', '.'));
            $csv->setValue('verwendungszweck1', $order->getCustomerNr() . ' ' . $order->getAuftnr());

            $gutschriftsnummer = $this->order_refund_repository->searchGutschriftsNummer($refund);
            if ($gutschriftsnummer) {
                $csv->setValue('verwendungszweck2', 'Beleg ' . $gutschriftsnummer);
            } else {
                $csv->setValue('verwendungszweck2', '');
            }

            $csv->writeRow();
        }

        return $csv->getAsString();
    }
}
