<?php

namespace wws\OrderRefund;

class OrderRefundMethodExecuterResult
{
    protected $message;
    protected $success = true;
    protected $refund_amount = null;

    /**
     * @param bool $status
     * @param string|null $message
     * @return $this
     */
    public function setSuccess($status, $message = null)
    {
        $this->success = (bool)$status;
        if ($message !== null) {
            $this->setMessage($message);
        }
        return $this;
    }

    public function isSuccess()
    {
        return $this->success;
    }

    public function getMessage()
    {
        return $this->message;
    }

    public function setMessage($message)
    {
        $this->message = $message;
    }

    public function setRefundAmount($refund_amount)
    {
        $this->refund_amount = $refund_amount;
    }
}
