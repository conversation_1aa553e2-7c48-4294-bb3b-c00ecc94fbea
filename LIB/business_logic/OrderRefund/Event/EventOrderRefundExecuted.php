<?php

namespace wws\OrderRefund\Event;

use bqp\Event\Event;
use wws\OrderRefund\OrderRefund;

class EventOrderRefundExecuted extends Event
{
    /**
     * @var OrderRefund
     */
    private $order_refund;

    public function __construct(OrderRefund $order_refund)
    {
        $this->order_refund = $order_refund;
    }

    public function getOrderRefund(): OrderRefund
    {
        return $this->order_refund;
    }

    public function getMessage(): array
    {
        return [
            'refund_id' => $this->order_refund->getRefundId(),
            'order_id' => $this->order_refund->getOrderId()
        ];
    }
}
