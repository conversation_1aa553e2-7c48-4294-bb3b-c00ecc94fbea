<?php

namespace wws\OrderRefund\Event;

use bqp\Event\Event;
use wws\OrderRefund\OrderRefund;

class EventOrderRefundCanceled extends Event
{
    /**
     * @var OrderRefund
     */
    private $order_refund;

    public function __construct(OrderRefund $order_refund)
    {
        $this->order_refund = $order_refund;
    }

    public function getOrderRefund(): OrderRefund
    {
        return $this->order_refund;
    }
}
