<?php

namespace wws\OrderRefund;

use bqp\db\db_generic;
use wws\Order\Order;
use wws\Order\OrderConst;

class OrderRefundMethodResolverDefault implements OrderRefundMethodResolver
{
    const DEFAULT_REFUND_METHOD_ID = 1;
    const DEFAULT_REFUND_EBAY_PAYPAL = 5;

    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function getRefundMethodIdByOrder(Order $order): int
    {
        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_EBAY && $order->getZahlungsart() == OrderConst::PAYMENT_PAYPAL) {
            return self::DEFAULT_REFUND_EBAY_PAYPAL;
        }

        $refund_method_id = $this->db->fieldQuery("
            SELECT
                einst_zahlungsarten.refund_method_id
            FROM
                einst_zahlungsarten
            WHERE
                einst_zahlungsarten.zahlungs_id = " . (int)$order->getZahlungsart() . "
        ");

        if (!$refund_method_id) {
            $refund_method_id = self::DEFAULT_REFUND_METHOD_ID;
        }

        return $refund_method_id;
    }
}