<?php

namespace wws\OrderRefund;

use wws\Order\Order;
use wws\Order\OrderConst;

class OrderRefundNecessary
{
    private string $reason;

    public function check(Order $order): bool
    {
        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
            $this->setReason('Amazon Auftrag');
            return true;
        }

        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_C24) {
            $this->setReason('Check24 Auftrag');
            return true;
        }

        if ($order->getZahlungsart() === OrderConst::PAYMENT_KAUFLAND) {
            $this->setReason('Kaufland.de Auftrag');
            return true;
        }

        if ($order->getZahlungsart() === OrderConst::PAYMENT_ECOM_GALAXUS) {
            $this->setReason('Galaxus.de Auftrag');
            return true;
        }

        if ($order->getZahlungsart() === OrderConst::PAYMENT_ECOM_IDEALO) {
            $this->setReason('Idealo.de Auftrag');
            return true;
        }

        if ($order->getZahlungsart() == OrderConst::PAYMENT_PAYPAL) {
            if ($order->getZahlungsartStatus() == OrderConst::PAYMENT_STATUS_PAYPAL_BOOKED) {
                $this->setReason('Zahlung per Paypal erfolgt.');
                return true;
            } else {
                $this->setReason('Paypal, aber kein Zahlungseingang gebucht.');
                return false;
            }
        }

        if ($order->getZahlungsart() == OrderConst::PAYMENT_KREDITKARTE) {
            if ($order->getZahlungsartStatus() == OrderConst::PAYMENT_STATUS_KREDITKARTE_BOOKED) {
                $this->setReason('Zahlung per Kreditkarte erfolgt.');
                return true;
            } else {
                $this->setReason('Kreditkarte, aber noch nicht gebucht.');
                return false;
            }
        }

        if ($order->getZahlungsart() == OrderConst::PAYMENT_ECOM_UNZER) {
            if ($order->getZahlungsartStatus() == OrderConst::PAYMENT_STATUS_UNZER_BOOKED) {
                $this->setReason('Zahlung per Unzer/Kreditkarte erfolgt.');
                return true;
            } else {
                $this->setReason('Kreditkarte, aber noch nicht gebucht.');
                return false;
            }
        }

        if ($order->getZahlungsart() == OrderConst::PAYMENT_VORKASSE) {
            if ($order->getZahlungsartStatus() == OrderConst::PAYMENT_STATUS_VORKASSE_RECEIVED) {
                $this->setReason('Zahlung per Vorkasse erfolgt.');
                return true;
            } else {
                $this->setReason('Vorkasse, aber noch keine Zahlungseingang gebucht.');
                return false;
            }
        }

        if ($order->getZahlungsart() == OrderConst::PAYMENT_NACHNAHME) {
            if ($order->getMinStatus() < OrderConst::STATUS_ZUSTELLUNG_BEENDET) {
                $this->setReason('Nachnahme, Auftag noch nicht versendet.');
                return false;
            } else {
                $this->setReason('Nachnahme, Zahlungsstatus aber unbekannt.');
                return false;
            }
        }

        $this->setReason('Keine passende Regel gefunden.');
        return false;
    }

    private function setReason(string $reason): void
    {
        $this->reason = $reason;
    }

    public function getReason(): string
    {
        return $this->reason;
    }
}
