<?php

namespace wws\OrderRefund;

use service_loader;

class OrderRefundFactory
{

    /**
     * @return OrderRefundRepository
     */
    public static function getOrderRefundRepository(): OrderRefundRepository
    {
        $db = service_loader::getDiContainer()->get('db');

        $resolver = new OrderRefundMethodResolverDefault($db);

        return new OrderRefundRepository($db, $resolver);
    }

    /**
     * @return OrderRefundOutputHelper
     */
    public static function getOrderRefundOutputHelper(): OrderRefundOutputHelper
    {
        return new OrderRefundOutputHelper(self::getOrderRefundRepository());
    }
}
