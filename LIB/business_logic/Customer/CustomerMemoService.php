<?php

namespace wws\Customer;

use wws\Users\UserRepository;

class CustomerMemoService
{
    /**
     * @var CustomerMemoRepository
     */
    private $customer_memo_repository;

    public function __construct(CustomerMemoRepository $customer_memo_repository)
    {
        $this->customer_memo_repository = $customer_memo_repository;
    }

    public function customerMemoToText(CustomerMemo $customer_memo): string
    {
        $username = UserRepository::getUsername($customer_memo->getUserId());

        $memo = $customer_memo->getDateAdded()->format("d.m.y H:i") . " (" . $username . ") :\n";
        $memo .= $customer_memo->getComment() . "\n\n\n";

        return $memo;
    }

    public function getCustomerMemosAsText(int $customer_id): string
    {
        $customer_memos = $this->customer_memo_repository->getByCustomerId($customer_id);

        $memo = '';

        foreach ($customer_memos as $customer_memo) {
            $memo .= $this->customerMemoToText($customer_memo);
        }

        return $memo;
    }

    /**
     * @param int[] $customer_ids
     * @return string[]
     */
    public function getCustomerMemosAsTextByCustomerIds(array $customer_ids): array
    {
        $memos = [];
        foreach ($customer_ids as $customer_id) {
            $memos[$customer_id] = '';
        }

        $customer_memos = $this->customer_memo_repository->getByCustomerIds($customer_ids);

        foreach ($customer_memos as $customer_memo) {
            $memos[$customer_memo->getCustomerId()] .= $this->customerMemoToText($customer_memo);
        }

        return $memos;
    }

    public function save(CustomerMemo $customer_memo): void
    {
        $this->customer_memo_repository->save($customer_memo);
    }
}
