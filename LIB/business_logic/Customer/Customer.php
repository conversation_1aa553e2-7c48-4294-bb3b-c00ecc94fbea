<?php

namespace wws\Customer;

use bank_account;
use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\Exceptions\InputException;
use bqp\Model\EntityChanges;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use bqp\Utils\TelephoneUtils;
use db;
use service_loader;
use wws\Country\CountryConst;
use wws\Country\CountryRepository;
use wws\Customer\Event\EventCustomerAddressChanged;
use wws\Customer\Event\EventCustomerChanged;
use wws\Customer\Event\EventCustomerCreated;

class Customer
{
    private SmartDataObj $data;

    /**
     * Teilweise wird bei änderungen an der Adresse die alte Adresse in Form von einem Address Objekts gebraucht,
     * das ist bisher in ziemlich böser form von außen passiert... daher jetzt so und als Event nach draußen
     */
    private Address $old_address;

    private InputException $error_handler;

    /**
     * @var CustomerMemo[]
     */
    private array $customer_memo_cache = [];

    public function __construct(int $customer_id = null)
    {
        $this->old_address = new Address();

        $this->data = new SmartDataObj($this);

        if ($customer_id) {
            $this->load($customer_id);
        } else {
            $this->loadDefaults();
        }
    }

    public function load(int $customer_id): void
    {
        $db = db::getInstance();

        $row = $db->singleQuery("
            SELECT
                customers.customer_id,
                customers.customer_type,
                customers.customer_nr,
                customers.debtor_account,
                customers.anrede,
                customers.firma,
                customers.name,
                customers.vorname,
                customers.strasse AS adresse1,
                customers.adresse2,
                customers.plz,
                customers.ort,
                customers.country_id,
                customers.telefon,
                customers.mobil,
                customers.fax,
                customers.email,

                customers.blz AS konto_blz,
                customers.knr AS konto_nummer,
                customers.bn AS konto_bank,
                customers.kin AS konto_inhaber,
                customers.iban AS konto_iban,
                customers.bic AS konto_bic,

                customers.newsletter,

                customers.ustidnr,
                customers.ustidnr_status,

                customers.customer_status,

                customers.shop_id,

                customers.name_phone,
                customers.vorname_phone,

                customers.zahlungsart,
                customers.lager_id,

                customers.gebdatum
            FROM
                customers
            WHERE
                customers.customer_id = '" . $customer_id . "'
        ");

        if (!$row['customer_id']) {
            throw new SmartDataEntityNotFoundException("Kunde konnte nicht geladen werden.");
        }

        $this->data->loadDaten($row);

        $this->old_address = clone $this->getAddress();
    }

    public function loadDefaults(): void
    {
        $this->setCustomerType(CustomerConst::CUSTOMER_TYPE_NORMAL);
        $this->setCustomerStatus(CustomerConst::CUSTOMER_STATUS_OK);
        $this->setUstidnr('');
        $this->setUstidnrValid(false);

        $this->setAnrede('');
        $this->setVorname('');
        $this->setName('');
        $this->setFirma('');
        $this->setAdresse1('');
        $this->setAdresse2('');
        $this->setPlz('');
        $this->setOrt('');
        $this->setCountryId(CountryConst::COUNTRY_ID_DE);

        $this->setTelefon('');
        $this->setMobil('');
        $this->setFax('');
        $this->setEmail('');

        $this->setKontoBlz('');
        $this->setKontoNummer('');
        $this->setKontoBank('');
        $this->setKontoInhaber('');
        $this->setKontoIban('');
        $this->setKontoBic('');

        $this->setNewsletter(0);
        $this->setStdZahlungsart(0);
        $this->setLagerId(null);
    }

    private function getErrors(): InputException
    {
        if (!isset($this->error_handler)) {
            $this->error_handler = new InputException();
        }

        return $this->error_handler;
    }

    /**
     * @return int customer_id
     */
    public function save(): int
    {
        //prüfen auf pflicht felder
        $errors = $this->getErrors();

        if ($this->getCustomerType() != CustomerConst::CUSTOMER_TYPE_DUMMY) {
            if (!$this->data->getter('name')) {
                $errors->add('name', 'Bitte geben Sie einen Namen an.');
            }
            if (!$this->data->getter('vorname')) {
                $errors->add('vorname', 'Bitte geben Sie einen Vornamen an.');
            }
            if (!$this->data->getter('adresse1')) {
                $errors->add('adresse1', 'Bitte geben Sie eine Adresse an.');
            }
            if (!$this->data->getter('plz')) {
                $errors->add('plz', 'Bitte geben Sie ein PLZ an.');
            }
            if (!$this->data->getter('ort')) {
                $errors->add('ort', 'Bitte geben Sie einen Ort an.');
            }
            if (!$this->data->getter('telefon')) {
                $errors->add('telefon', 'Bitte geben Sie eine Telefonnummer an.');
            }
            if (!$this->data->getter('country_id')) {
                $errors->add('country_id', 'Bitte wählen Sie ein Land.');
            }
        }

        if ($this->data->isChange('ustidnr') && $this->getUstidnr() !== '') {
            $validator = new CustomerVatNumberValidator();
            $validator->validateSyntaxByCustomer($this, $errors);

            if ($errors->isError('ustidnr')) {
                $this->setUstidnrValid(false);
            }
        }

        if (!$this->data->getter('shop_id')) {
            $errors->add('shop_id', 'Bitte geben Sie ein gültigen Shop an.');
        }

        $errors->check();

        if ($this->data->isChange('ustidnr_status') && $this->isUstidnrValid()) {
            $this->addMemo(new CustomerMemo('Status der Ustidnr (' . $this->getUstidnr() . ') auf "gültig" gesetzt.'));
        }

        $protokoll = $this->getAllChanges();

        if ($protokoll->isChange()) {
            $db = db::getInstance();

            $sql = [];

            foreach ($this->data->getChanges() as $field => $value) {
                switch ($field) {
                    case 'adresse1':
                        $sql[] = "customers.strasse = '" . $db->escape($value) . "'";
                        break;
                    case 'konto_blz':
                        $sql[] = "customers.blz = '" . $db->escape($value) . "'";
                        break;
                    case 'konto_nummer':
                        $sql[] = "customers.knr = '" . $db->escape($value) . "'";
                        break;
                    case 'konto_bank':
                        $sql[] = "customers.bn = '" . $db->escape($value) . "'";
                        break;
                    case 'konto_inhaber':
                        $sql[] = "customers.kin = '" . $db->escape($value) . "'";
                        break;
                    case 'konto_iban':
                        $sql[] = "customers.iban = '" . $db->escape($value) . "'";
                        break;
                    case 'konto_bic':
                        $sql[] = "customers.bic = '" . $db->escape($value) . "'";
                        break;
                    default:
                        $sql[] = "customers.$field = " . $db->quote($value);
                }
            }

            switch ($this->data->getObjStatus()) {
                case SmartDataObj::STATUS_NEW:
                    //neue kundennummer
                    $customer_id = CustomerRepository::getNewCustomerId($this->getShopId());

                    //customer_nr erstmal immer statisch... -> wenn die von außen gesetzt werden soll, dann muss hier geprüft werden, ob diese bereit gesetzt wurde.
                    $customer_nr = CustomerRepository::getCustomerNr($customer_id, $this->getShopId());

                    $sql[] = "customers.customer_id = '$customer_id'";
                    $sql[] = "customers.customer_nr = '" . $db->escape($customer_nr) . "'";
                    $sql[] = "customers.added = NOW()";
                    $sql[] = "customers.debtor_account = '" . CustomerRepository::getDebtorAccount($customer_id, $this->getShopId()) . "'";

                    $db->query("
                        INSERT INTO
                            customers
                        SET
                            " . implode(',', $sql) . "
                    ");
                    $this->data->setterDirect('customer_id', $customer_id);
                    $this->data->setterDirect('customer_nr', $customer_nr);
                    $this->data->setSaved();

                    $event = new EventCustomerCreated($this);
                    service_loader::getEventDispatcher()->dispatch($event);

                    break;
                case SmartDataObj::STATUS_UPDATE:
                    $db->query("
                        UPDATE
                            customers
                        SET
                            " . implode(',', $sql) . "
                        WHERE
                            customers.customer_id = '" . $this->getCustomerId() . "'
                    ");

                    $event = new EventCustomerChanged($this, $protokoll);
                    service_loader::getEventDispatcher()->dispatch($event);

                    break;
            }
        }

        $protokoll->setEntityId($this->getCustomerId());
        $protokoll->save();

        $this->saveCustomerMemos();

        if ($this->getAddress()->compare($this->old_address)) {
            $event = new EventCustomerAddressChanged($this, $this->old_address);
            service_loader::getEventDispatcher()->dispatch($event);
        }

        return $this->getCustomerId();
    }

    private function saveCustomerMemos(): void
    {
        $customer_memo_service = service_loader::getDiContainer()->get(CustomerMemoService::class);
        foreach ($this->customer_memo_cache as $customer_memo) {
            $customer_memo->setCustomerId($this->getCustomerId());
            $customer_memo_service->save($customer_memo);
        }
        $this->customer_memo_cache = [];
    }

    public function getAllChanges(): EntityChanges
    {
        $protokoll = new EntityChanges('customers');
        $protokoll->addBySmartDataObj($this->data, 'stamm');

        return $protokoll;
    }

    //getter

    /**
     * Gibt $customer_id zurück.
     * @return int $customer_id
     */
    public function getCustomerId(): int
    {
        return (int)$this->data->getter('customer_id');
    }

    /**
     * Gibt Kundennummer mit Format zurück.
     * @return string Kundenummer formatiert
     */
    public function getCustomerNr(): string
    {
        if ($this->getCustomerId()) {
            return $this->data->getter('customer_nr');
        }

        return 'neuer Kunde';
    }

    /**
     * Gibt das Debitoren-Konto für die Buchhaltung zurück
     *
     * @return string
     */
    public function getDebtorAccount(): string
    {
        return $this->data->getter('debtor_account') ?? '';
    }

    public function getAnrede(): string
    {
        return $this->data->getter('anrede');
    }

    /**
     * gibt die passende Grußformel für den Kunden zurück. (ACHTUNG ohne Komma)
     * @return string Grußformel
     */
    public function getGrussformel(): string
    {
        return CustomerRepository::getGrussformel($this->getAnrede(), $this->getVorname(), $this->getName());
    }

    public function getVorname(): string
    {
        return $this->data->getter('vorname');
    }

    public function getName(): string
    {
        return $this->data->getter('name');
    }

    public function getFirma(): string
    {
        return $this->data->getter('firma');
    }

    /**
     * gibt ein address Objekt zurück
     */
    public function getAddress(): Address
    {
        return new Address($this->data->getAsArray());
    }

    /**
     * Gibt Adressefeld 1 zurück
     * @return string Adressfeld 1
     */
    public function getAdresse1(): string
    {
        return $this->data->getter('adresse1');
    }

    /**
     * Gibt Adressefeld 2 zurück
     * @return string Adressfeld 2
     */
    public function getAdresse2(): string
    {
        return $this->data->getter('adresse2');
    }

    public function getPlz(): string
    {
        return $this->data->getter('plz');
    }

    public function getOrt(): string
    {
        return $this->data->getter('ort');
    }

    public function getCountryId(): int
    {
        return (int)$this->data->getter('country_id');
    }

    public function getCountryName(): string
    {
        return CountryRepository::getCountryNameById($this->getCountryId());
    }

    public function getTelefon(): string
    {
        return $this->data->getter('telefon');
    }

    public function getTelefon2(): string
    {
        return $this->data->getter('mobil');
    }

    /**
     * gibt die erste gefunden Handynummer zurück
     * @return ?string
     */
    public function getMobil(): ?string
    {
        if (TelephoneUtils::isGermanMobileNumber($this->getTelefon())) {
            return $this->getTelefon();
        }

        if (TelephoneUtils::isGermanMobileNumber($this->getTelefon2())) {
            return $this->getTelefon2();
        }

        return null;
    }

    public function getFax(): string
    {
        return $this->data->getter('fax');
    }

    public function getEmail(): string
    {
        return $this->data->getter('email');
    }


    /**
     * Gibt die Kontodaten als objekt bank_account zurück
     * @return bank_account $konto
     */
    public function getKonto(): bank_account
    {
        $konto = new bank_account();
        $konto->setBank($this->getKontoBank());
        $konto->setBlz($this->getKontoBlz());
        $konto->setKontonummer($this->getKontoNummer());
        $konto->setInhaber($this->getKontoInhaber());
        $konto->setIban($this->getKontoIban());
        $konto->setBic($this->getKontoBic());

        return $konto;
    }

    public function getKontoBlz(): string
    {
        return $this->data->getter('konto_blz');
    }

    public function getKontoNummer(): string
    {
        return $this->data->getter('konto_nummer');
    }

    public function getKontoBank(): string
    {
        return $this->data->getter('konto_bank');
    }

    public function getKontoInhaber(): string
    {
        return $this->data->getter('konto_inhaber');
    }

    public function getKontoIban(): string
    {
        return $this->data->getter('konto_iban');
    }

    public function getKontoBic(): string
    {
        return $this->data->getter('konto_bic');
    }

    public function hasNewsletter(): bool
    {
        return (bool)$this->data->getter('newsletter');
    }

    public function getUstidnr(): string
    {
        return $this->data->getter('ustidnr');
    }

    /**
     * Gibt Status zurück ob UstIdnr gültig ist
     * @return bool
     */
    public function isUstidnrValid(): bool
    {
        return (bool)$this->data->getter('ustidnr_status');
    }

    /**
     * Gibt den Status des Kunden zurück
     * @return string (ok,del)
     */
    public function getCustomerStatus(): string
    {
        return $this->data->getter('customer_status');
    }

    public function getBemerkung(): string
    {
        $customer_memo_service = service_loader::getDiContainer()->get(CustomerMemoService::class);

        $memo_text = $customer_memo_service->getCustomerMemosAsText($this->getCustomerId());

        foreach ($this->customer_memo_cache as $customer_memo) {
            $memo_text .= $customer_memo_service->customerMemoToText($customer_memo);
        }

        return $memo_text;
    }

    public function getShopId(): int
    {
        return $this->data->getter('shop_id');
    }

    public function getCustomerTyp(): string
    {
        return $this->data->getter('customer_type');
    }

    public function getLagerId(): ?int
    {
        return $this->data->getter('lager_id');
    }

    public function getBirthDate(): ?DateObj
    {
        if (!$this->data->getter('gebdatum')) {
            return null;
        }
        return new DateObj($this->data->getter('gebdatum'));
    }

    //setter
    public function setAnrede(string $value): bool
    {
        return $this->data->setter('anrede', $value);
    }

    public function setVorname(string $value): bool
    {
        return $this->data->setter('vorname', $value);
    }

    public function setName(string $value): bool
    {
        return $this->data->setter('name', $value);
    }

    public function setFirma(string $value): bool
    {
        return $this->data->setter('firma', $value);
    }

    public function setAdresse1(string $value): bool
    {
        return $this->data->setter('adresse1', $value);
    }

    public function setAdresse2(string $value): bool
    {
        return $this->data->setter('adresse2', $value);
    }

    public function setPlz(string $value): bool
    {
        return $this->data->setter('plz', $value);
    }

    public function setOrt(string $value): bool
    {
        return $this->data->setter('ort', $value);
    }

    public function setCountryId(int $country_id): bool
    {
        return $this->data->setter('country_id', $country_id);
    }

    public function setTelefon(string $value): bool
    {
        return $this->data->setter('telefon', $value);
    }

    public function setMobil(string $value): bool
    {
        return $this->data->setter('mobil', $value);
    }

    public function setFax(string $value): bool
    {
        return $this->data->setter('fax', $value);
    }

    public function setEmail(string $value): bool
    {
        return $this->data->setter('email', $value);
    }

    /**
     * bank_accoubt Konto setzen
     * @param bank_account $konto
     */
    public function setKonto(bank_account $konto): void
    {
        $this->setKontoBlz($konto->getBlz());
        $this->setKontoNummer($konto->getKontonummer());
        $this->setKontoBank($konto->getBank());
        $this->setKontoInhaber($konto->getInhaber());
        $this->setKontoIban($konto->getIban());
        $this->setKontoBic($konto->getBic());
    }

    public function setKontoBlz(string $value): bool
    {
        return $this->data->setter('konto_blz', $value);
    }

    public function setKontoNummer(string $value): bool
    {
        return $this->data->setter('konto_nummer', $value);
    }

    public function setKontoBank(string $value): bool
    {
        return $this->data->setter('konto_bank', $value);
    }

    public function setKontoInhaber(string $value): bool
    {
        return $this->data->setter('konto_inhaber', $value);
    }

    public function setKontoIban(string $value): bool
    {
        return $this->data->setter('konto_iban', $value);
    }

    public function setKontoBic(string $value): bool
    {
        return $this->data->setter('konto_bic', $value);
    }


    public function setShopId(int $value): bool
    {
        return $this->data->setter('shop_id', $value);
    }

    public function setNewsletter(int $value): bool
    {
        return $this->data->setter('newsletter', $value);
    }

    public function setUstidnr(string $ustidnr): bool
    {
        return $this->data->setter('ustidnr', $ustidnr);
    }

    public function setUstidnrValid(bool $status): bool
    {
        return $this->data->setter('ustidnr_status', $status ? 1 : 0);
    }

    public function addMemo(CustomerMemo $customer_memo): void
    {
        $this->customer_memo_cache[] = $customer_memo;
    }

    public function setCustomerType(string $value): bool
    {
        return $this->data->setter('customer_type', $value);
    }

    public function getCustomerType(): string
    {
        return $this->data->getter('customer_type');
    }

    public function setCustomerStatus(string $value): bool
    {
        return $this->data->setter('customer_status', $value);
    }

    /**
     * setzt die Standardzahlungsart des Kunden
     * @param int $value
     */
    public function setStdZahlungsart(int $value): bool
    {
        return $this->data->setter('zahlungsart', $value);
    }

    public function setLagerId(?int $lager_id): bool
    {
        return $this->data->setter('lager_id', $lager_id);
    }

    public function setBirthDate(?DateObj $date): bool
    {
        return $this->data->setter('gebdatum', $date ? $date->db('date') : null);
    }

    public function setAddress(Address $address): void
    {
        $this->setAnrede($address->getAnrede());
        $this->setVorname($address->getVorname());
        $this->setName($address->getName());
        $this->setFirma($address->getFirma());
        $this->setAdresse1($address->getAdresse1());
        $this->setAdresse2($address->getAdresse2());
        $this->setPlz($address->getPlz());
        $this->setOrt($address->getOrt());
        $this->setCountryId($address->getCountryId());

        if ($address->getTel1()) {
            $this->setTelefon($address->getTel1());
        }

        if ($address->getEmail()) {
            $this->setEmail($address->getEmail());
        }
    }
}
