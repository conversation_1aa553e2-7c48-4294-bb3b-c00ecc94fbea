<?php

namespace wws\Customer;

use bqp\db\db_generic;
use Generator;

class CustomerMemoRepository
{
    private $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function create(): CustomerMemo
    {
        return new CustomerMemo();
    }

    public function save(CustomerMemo $customer_memo): int
    {
        $customer_memo->validate();

        $smart_data_obj = $customer_memo->getSmartDataObj();

        $data = $smart_data_obj->getAsArray();

        $this->db->simpleInsertUpdate('customer_memo', $data);

        if ($smart_data_obj->isNew()) {
            $smart_data_obj->setterDirect('customer_memo_id', $this->db->insert_id());
        }

        return $customer_memo->getCustomerMemoId();
    }

    /**
     * @param int $customer_id
     * @return Generator|CustomerMemo[]
     */
    public function getByCustomerId(int $customer_id): Generator
    {
        return $this->getBySql("customer_memo.customer_id = $customer_id");
    }

    /**
     * @param int[] $customer_ids
     * @return Generator|CustomerMemo[]
     */
    public function getByCustomerIds(array $customer_ids): Generator
    {
        return $this->getBySql("customer_memo.customer_id IN (" . $this->db->in($customer_ids) . ")");
    }

    /**
     * @param string $sql_where
     * @return Generator|CustomerMemo[]
     */
    public function getBySql(string $sql_where): Generator
    {
        $result = $this->db->query("
            SELECT
                customer_memo.customer_memo_id,
 	            customer_memo.customer_id,
 	            customer_memo.order_memo_id,
 	            customer_memo.user_id,
 	            customer_memo.date_added,
 	            customer_memo.comment 
            FROM
                customer_memo
            WHERE
                $sql_where
            ORDER BY
                customer_memo.date_added,
                customer_memo.customer_memo_id 
        ");

        foreach ($result as $row) {
            $customer_memo = new CustomerMemo();
            $customer_memo->getSmartDataObj()->loadDaten($row);

            yield $customer_memo;
        }
    }
}
