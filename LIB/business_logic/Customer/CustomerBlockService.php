<?php

namespace wws\Customer;

use bqp\db\db_generic;
use wws\core\Informer;

class CustomerBlockService
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * @param Customer $customer
     * @param string $reason
     */
    public function blockCustomer(Customer $customer, string $reason): void
    {
        $this->db->query("
            INSERT INTO
                customers_sperre
            SET
                customers_sperre.customer_id = '" . (int)$customer->getCustomerId() . "',
                customers_sperre.added = NOW(),
                customers_sperre.sperr_grund = '" . $this->db->escape($reason) . "',
                customers_sperre.email = '" . $this->db->escape($customer->getEmail()) . "'
            ON DUPLICATE KEY UPDATE
                customers_sperre.sperr_grund = VALUES(customers_sperre.sperr_grund),
                customers_sperre.email = VALUES(customers_sperre.email)
        ");
    }

    /**
     * @param Customer $customer
     */
    public function unblockCustomer(Customer $customer): void
    {
        $this->db->query("
            DELETE FROM
                customers_sperre
            WHERE
                customers_sperre.customer_id = '" . (int)$customer->getCustomerId() . "'
        ");
    }

    public function checkCustomer(Customer $customer): bool
    {
        return (bool)$this->getBlockDataByCustomer($customer);
    }

    public function autofillInformer(Customer $customer, Informer $informer): void
    {
        $data = $this->getBlockDataByCustomer($customer);

        if ($data) {
            $informer->warn("Kunde ist als gesperrt markiert:<br><blockquote>" . nl2br(htmlentities($data['sperr_grund'])) . '</blockquote>');
        }
    }

    private function getBlockDataByCustomer(Customer $customer): array
    {
        $where = '';

        if (strlen($customer->getEmail()) > 5) { //damit wir kein müll erwischen
            $where = " OR
                customers_sperre.email = '" . $this->db->escape($customer->getEmail()) . "' ";
        }

        $data = $this->db->singleQuery("
            SELECT
                customers_sperre.customer_id,
                customers_sperre.sperr_grund
            FROM
                customers_sperre
            WHERE
                customers_sperre.customer_id = '" . $customer->getCustomerId() . "'
                $where
            LIMIT
                1
        ");

        return $data;
    }

    public function getBlockData(int $customer_id): array
    {
        $daten = $this->db->singleQuery("
            SELECT
                customers_sperre.customer_id,
                customers_sperre.sperr_grund
            FROM
                customers_sperre
            WHERE
                customers_sperre.customer_id = '$customer_id'
        ");

        return $daten ?? [];
    }
}
