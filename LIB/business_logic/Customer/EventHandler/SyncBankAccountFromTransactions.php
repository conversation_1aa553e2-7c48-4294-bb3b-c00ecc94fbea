<?php

namespace wws\Customer\EventHandler;

use bqp\db\db_generic;
use wws\BankAccount\Event\EventTransactionBooked;
use wws\buchhaltung\AccountingConsts;
use wws\Customer\Customer;
use wws\Order\OrderConst;

class SyncBankAccountFromTransactions
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function handleEvent(EventTransactionBooked $event): void
    {
        $transaction = $event->getBankTransaction();

        $row = $this->db->singleQuery("
            SELECT
                customers.customer_id
            FROM
                buchhaltung_bank_transactions INNER JOIN
                customers ON (buchhaltung_bank_transactions.customer_id = customers.customer_id) INNER JOIN
                orders ON (buchhaltung_bank_transactions.order_id = orders.order_id)
            WHERE
                buchhaltung_bank_transactions.transaction_id = '" . $transaction->getTransactionId() . "' AND
                buchhaltung_bank_transactions.account_id IN (" . AccountingConsts::BANK_ACCOUNT_COMMERZ . ", " . AccountingConsts::BANK_ACCOUNT_COMMERZ_K11 . ") AND
                customers.iban = '' AND
                orders.zahlungs_id NOT IN (" . OrderConst::PAYMENT_NACHNAHME . ")
        ");

        if ($row) {
            $customer = new Customer($row['customer_id']);
            $customer->setKontoIban($transaction->getExtra('ktonr'));
            $customer->setKontoBic($transaction->getExtra('blz'));
            $customer->setKontoInhaber($transaction->getSenderName());
            $customer->save();
        }
    }
}
