<?php

namespace wws\Customer\EventHandler;

use bqp\Exceptions\DevException;
use bqp\Utils\StringUtils;
use wws\Order\OrderMemo;
use wws\Order\OrderRepository;
use wws\OrderRefund\Event\EventOrderRefundCanceled;
use wws\OrderRefund\Event\EventOrderRefundCreated;
use wws\OrderRefund\Event\EventOrderRefundExecuted;
use wws\OrderRefund\OrderRefund;

class OrderRefundsToCustomerMemo
{
    private $order_repository;

    public function __construct(OrderRepository $order_repository)
    {
        $this->order_repository = $order_repository;
    }

    /**
     * @param object $event
     */
    public function handleEvent(object $event): void
    {
        $class = get_class($event);

        switch ($class) {
            case EventOrderRefundCreated::class:
                $this->onRefundCreated($event->getOrderRefund());
                break;
            case EventOrderRefundCanceled::class:
                $this->onRefundCanceled($event->getOrderRefund());
                break;
            case EventOrderRefundExecuted::class:
                $this->onRefundExecuted($event->getOrderRefund());
                break;
            default:
                throw new DevException('unknown event');
        }
    }

    /**
     * @param OrderRefund $order_refund
     */
    public function onRefundCreated(OrderRefund $order_refund): void
    {
        $order = $this->order_repository->load($order_refund->getOrderId());
        $order->addOrderMemo(new OrderMemo(
            'Rückzahlung über ' . StringUtils::formatPrice($order_refund->getBetrag()) . ' erstellt.',
            OrderMemo::BUCHHALTUNG
        ));
        $this->order_repository->save($order);
    }

    /**
     * @param OrderRefund $order_refund
     */
    public function onRefundExecuted(OrderRefund $order_refund): void
    {
        $order = $this->order_repository->load($order_refund->getOrderId());
        $order->addOrderMemo(new OrderMemo(
            'Rückzahlung über ' . StringUtils::formatPrice($order_refund->getBetrag()) . ' ausgeführt.',
            OrderMemo::BUCHHALTUNG
        ));
        $this->order_repository->save($order);
    }

    /**
     * @param OrderRefund $order_refund
     */
    public function onRefundCanceled(OrderRefund $order_refund): void
    {
        $order = $this->order_repository->load($order_refund->getOrderId());
        $order->addOrderMemo(new OrderMemo(
            'Rückzahlung über ' . StringUtils::formatPrice($order_refund->getBetrag()) . ' storniert.',
            OrderMemo::BUCHHALTUNG
        ));
        $this->order_repository->save($order);
    }
}
