<?php

namespace wws\Customer\EventHandler;

use wws\Order\Event\EventOrderGutschriftCreated;
use wws\Order\OrderMemo;
use wws\Order\OrderRepository;

class NewGutschriftToCustomerMemo
{
    private $order_repository;

    public function __construct(OrderRepository $order_repository)
    {
        $this->order_repository = $order_repository;
    }

    public function handleEvent(EventOrderGutschriftCreated $event): void
    {
        $gutschrift = $event->getGutschrift();
        $order = $this->order_repository->load($gutschrift->getOrderId());

        $order->addOrderMemo(new OrderMemo("Gutschrift erstellt " . $gutschrift->getGutschriftsNr() . "  in Höhe von " . number_format($gutschrift->getAmount(), 2, ',', '.') . " €.", OrderMemo::BUCHHALTUNG));
        $this->order_repository->save($order);
    }
}
