<?php

namespace wws\Customer\Event;

use bqp\Event\Event;
use bqp\Model\EntityChanges;
use wws\Customer\Customer;

class EventCustomerChanged extends Event
{
    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var EntityChanges
     */
    private $entity_changes;

    public function __construct(Customer $customer, EntityChanges $changes)
    {
        $this->customer = $customer;
        $this->entity_changes = $changes;
    }

    public function getCustomerId(): int
    {
        return (int)$this->customer->getCustomerId();
    }

    public function getEntityChanges(): EntityChanges
    {
        return $this->entity_changes;
    }
}
