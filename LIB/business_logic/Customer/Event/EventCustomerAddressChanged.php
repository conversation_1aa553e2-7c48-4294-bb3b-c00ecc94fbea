<?php

namespace wws\Customer\Event;

use bqp\Address\Address;
use bqp\Event\Event;
use wws\Customer\Customer;

/**
 * wird nach EventCustomerChanged/EventCustomerCreated aufgerufen
 *
 * Class EventCustomerAddressChanged
 * @package wws\Customer\Event
 */
class EventCustomerAddressChanged extends Event
{
    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var Address
     */
    private $old_address;

    public function __construct(Customer $customer, Address $old_address)
    {
        $this->customer = $customer;
        $this->old_address = $old_address;
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }

    public function getCustomerId(): int
    {
        return (int)$this->customer->getCustomerId();
    }

    public function getOldAddress(): Address
    {
        return $this->old_address;
    }
}
