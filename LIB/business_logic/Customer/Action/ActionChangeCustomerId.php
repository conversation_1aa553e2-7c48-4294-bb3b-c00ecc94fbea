<?php

namespace wws\Customer\Action;

use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use wws\Customer\Customer;
use wws\Customer\CustomerMemo;
use wws\Customer\CustomerRepository;

class ActionChangeCustomerId
{
    private $db;

    private $tables = [
        'b2b_access_log',
        'b2b_customer',
        'buchhaltung_bank_transactions',
        'buchhaltung_offeneposten',
        'buchhaltung_offeneposten_belege',
        'buchhaltung_opos_addison',
        'buchhaltung_opos_addison_orders',
        'callback',
        'calls_active',
        'customer_fulltext_search',
        'customer_memo',
        'customer_mail_archive',
        'customers_sperre',
        'mailsystem_mails',
        'mailsystem_mails_archiv',
        'orders',
        'protokoll_customer',
        'rekla_e',
        'rekla_stamm',
        'rekla_transport',
        'warenausgang_lieferschein'
    ];

    private $tables_ignore = [
        'customers',
        'customer_free_customer_id'
    ];

    public function __construct(db_generic $db)
    {
        $this->db = $db;

        $this->assertTables();
    }

    private function assertTables(): void
    {
        $tables = $this->db->query("
            SELECT
                TABLE_NAME
            FROM
                information_schema.COLUMNS
            WHERE
                TABLE_SCHEMA LIKE 'ecom' AND
                COLUMN_NAME LIKE 'customer_id' AND
                TABLE_NAME NOT IN (" . $this->db->in($this->tables_ignore) . ")
        ")->asSingleArray();

        $missing = array_diff($tables, $this->tables);

        if ($missing) {
            throw new FatalException('Die Tabellen-Definition entspricht nicht dem aktuellen Stand der Datenbank. (in Definition nicht vorhanden: ' . implode(', ', $missing) . ')');
        }

        $missing = array_diff($this->tables, $tables);

        if ($missing) {
            throw new FatalException('Die Tabellen-Definition entspricht nicht dem aktuellen Stand der Datenbank. (in Datenbank nicht vorhanden: ' . implode(', ', $missing) . ')');
        }

        //CREATE TABLE `customers_new_customer_id` ( `old_customer_id` INT NOT NULL , `new_customer_id` INT NOT NULL )
        //ALTER TABLE `customers_new_customer_id` ADD PRIMARY KEY( `old_customer_id`);
    }

    public function newCustomerId(int $old_customer_id): int
    {
        $new_customer_id = CustomerRepository::getNewCustomerId(1);
        $this->changeCustomerId($old_customer_id, $new_customer_id);

        return $new_customer_id;
    }

    public function changeCustomerId(int $old_customer_id, int $new_customer_id): void
    {
        $this->queueChange($old_customer_id, $new_customer_id);
        $this->createDummyCustomer($old_customer_id, $new_customer_id);
    }

    private function createDummyCustomer(int $old_customer_id, int $new_customer_id): void
    {
        $this->db->query("
            INSERT INTO
                customers
            SET
                customers.customer_id = $new_customer_id,
                customers.firma = $old_customer_id
        ");
    }

    private function queueChange(int $old_customer_id, int $new_customer_id): void
    {
        $this->db->query("
            INSERT INTO
                customers_new_customer_id
            SET
                customers_new_customer_id.old_customer_id = $old_customer_id,
                customers_new_customer_id.new_customer_id = $new_customer_id
        ");
    }

    public function runQueue(): void
    {
        foreach ($this->tables as $table) {
            $this->db->query("
                UPDATE
                    $table INNER JOIN
                    customers_new_customer_id ON ($table.customer_id = customers_new_customer_id.old_customer_id)
                SET
                    $table.customer_id = customers_new_customer_id.new_customer_id
            ");
        }


        $result = $this->db->query("
            SELECT
                customers.*,
                customers_new_customer_id.new_customer_id
            FROM
                customers_new_customer_id INNER JOIN
                customers ON (customers_new_customer_id.old_customer_id = customers.customer_id)
        ");

        foreach ($result as $row) {
            $new_customer_id = $row['new_customer_id'];
            $old_customer_id = $row['customer_id'];
            unset($row['new_customer_id'], $row['customer_id'], $row['id']);

            $row['id'] = $new_customer_id;
            $this->db->simpleUpdate('customers', $row, "customers.customer_id = '" . $new_customer_id . "' AND customers.firma = '" . $old_customer_id . "'");


            $customer = new Customer($new_customer_id);
            $customer->addMemo(new CustomerMemo('Customer-Id geändert von "' . $old_customer_id . '" zu "' . $new_customer_id . '"'));
            $customer->save();

            $this->db->query("DELETE FROM customers WHERE customers.customer_id = '" . $old_customer_id . "'");
            $this->db->query("DELETE FROM customers_new_customer_id WHERE customers_new_customer_id.old_customer_id = '" . $old_customer_id . "'");
        }
    }
}
