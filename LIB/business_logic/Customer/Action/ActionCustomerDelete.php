<?php

namespace wws\Customer\Action;

use bqp\db\db_generic;
use DomainException;

class ActionCustomerDelete
{
    private $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * @param int $customer_id
     * @throws DomainException
     */
    public function deleteCustomer(int $customer_id): void
    {
        $bestellungen = $this->db->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                orders
            WHERE
                orders.customer_id = '" . $customer_id . "'
        ");

        if ($bestellungen) {
            throw new DomainException('Kunde ' . $customer_id . ' kann nicht gelöscht werden. Aufträge vorhanden.');
        }

        $this->db->query("
            DELETE FROM
                customers
            WHERE
                customers.customer_id = '" . $customer_id . "'
        ");
    }
}
