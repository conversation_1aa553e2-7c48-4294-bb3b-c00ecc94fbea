<?php

namespace wws\Customer;

use bqp\Date\DateObj;
use bqp\Exceptions\InputException;
use bqp\Model\EntityChanges;
use bqp\Model\Model;
use bqp\Model\SmartDataObj;
use env;

class CustomerMemo implements Model
{
    private SmartDataObj $data;

    public function __construct(?string $comment = null)
    {
        $this->data = new SmartDataObj($this);

        $this->setDateAdded(new DateObj());
        $this->setUserId(env::getUserId());
        $this->setOrderMemoId(0);

        if ($comment !== null) {
            $this->setComment($comment);
        }
    }

    public function setCustomerId(int $customer_id): bool
    {
        return $this->data->setter('customer_id', $customer_id);
    }

    public function setUserId(int $user_id): bool
    {
        return $this->data->setter('user_id', $user_id);
    }

    public function setDateAdded(DateObj $date_added): bool
    {
        return $this->data->setter('date_added', $date_added->db());
    }

    public function setComment(string $comment): bool
    {
        return $this->data->setter('comment', $comment);
    }

    public function addCommentLine(string $comment): void
    {
        $new_coment = $this->data->getter('comment') ?? '';
        if ($new_coment) {
            $new_coment .= "\n";
        }

        $new_coment .= $comment;
        $this->setComment($new_coment);
    }

    public function getCustomerMemoId(): int
    {
        return (int)$this->data->getter('customer_memo_id');
    }

    public function getCustomerId(): int
    {
        return (int)$this->data->getter('customer_id');
    }

    public function getUserId(): int
    {
        return (int)$this->data->getter('user_id');
    }

    public function getDateAdded(): DateObj
    {
        return new DateObj($this->data->getter('date_added'));
    }

    public function getComment(): string
    {
        return $this->data->getter('comment');
    }

    public function setOrderMemoId(int $order_memo_id): void
    {
        $this->data->setter('order_memo_id', $order_memo_id);
    }

    public function getOrderMemoId(): int
    {
        return $this->data->getter('order_memo_id');
    }

    /**
     * @return SmartDataObj
     */
    public function getSmartDataObj(): SmartDataObj
    {
        return $this->data;
    }

    /**
     * @return EntityChanges
     */
    public function getAllChanges(): EntityChanges
    {
        $changes = new EntityChanges();
        $changes->addBySmartDataObj($this->data);
        return $changes;
    }

    /**
     * @throws InputException
     */
    public function validate(): void
    {
        $e = new InputException();
        if (!$this->data->getter('customer_id')) {
            $e->add('customer_id', 'customer_id not setted');
        }

        if ($e->isError()) {
            throw $e;
        }
    }
}
