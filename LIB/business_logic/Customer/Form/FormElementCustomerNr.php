<?php

namespace wws\Customer\Form;

use bqp\form\form_element_input;
use bqp\form\Validator\FormValidatorCallback;
use order_repository;

class FormElementCustomerNr extends form_element_input
{

    public function __construct()
    {
        $this->setLabel('Kundennummer');

        $this->addValidator(new FormValidatorCallback(function (string $value, FormValidatorCallback $validator) {
            if ($value === '') {
                return;
            }

            if (preg_match('~^' . order_repository::getCustomerNrRegexp() . '$~i', $value)) {
                return;
            }

            if (is_numeric($value) && order_repository::isPossibleValidCustomerId((int)$value)) {
                return;
            }

            $validator->addError('Bitte geben Sie eine gültige Kundennummer an.');
        }));

        $this->addOutputFilterCallback(function ($value) {
            $customer_id = (int)$value;
            if (order_repository::isPossibleValidCustomerId($customer_id)) {
                return $customer_id;
            }

            return null;
        });
    }
}