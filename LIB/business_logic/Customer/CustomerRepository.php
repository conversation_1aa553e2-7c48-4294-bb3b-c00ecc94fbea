<?php

namespace wws\Customer;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Model\SmartDataEntityNotFoundException;
use db;
use wws\business_structure\Shop;
use wws\Country\CountryConst;
use wws\Nummernkreis\Nummernkreis;

class CustomerRepository
{
    /**
     * @param array $row
     * @param string $field
     * @return string
     * @throws FatalException
     */
    public static function tableHelper_customer_nr(array $row, string $field): string
    {
        if (!array_key_exists('shop_id', $row)) {
            throw new DevException('Feld shop_id ist für customer_nr nötig');
        }

        if (array_key_exists($field, $row)) {
            $customer_id = $row[$field];
        } elseif (array_key_exists('customer_id', $row)) {
            $customer_id = $row['customer_id'];
        } elseif (array_key_exists('customer_nr', $row)) {
            $customer_id = $row['customer_nr'];
        } else {
            throw new FatalException('customer_id not found');
        }

        if ($customer_id) {
            return '<a href="/ax/customer/default/?customer_id=' . $customer_id . '" target="inline">' . self::getCustomerNr($customer_id, $row['shop_id']) . '</a>';
        }

        return '';
    }

    /**
     * @param int $order_id
     * @return Customer
     */
    public static function loadByOrderId(int $order_id): Customer
    {
        $customer_id = db::getInstance()->fieldQuery("
            SELECT
                orders.customer_id
            FROM
                orders
            WHERE
                orders.order_id = $order_id
        ");

        return new Customer($customer_id);
    }


    public static function convertCustomerNrToCustomerId(string $customer_nr): ?int
    {
        if (preg_match('~^([0-9]{6,7})~', $customer_nr, $temp)) {
            $customer_id = (int)$temp[1];

            $check = db::getInstance()->fieldQuery("
                SELECT
                    customers.customer_id
                FROM
                    customers
                WHERE
                    customers.customer_id = $customer_id
            ");

            if ($check) {
                return $customer_id;
            }
        }

        return null;
    }

    /**
     *
     * @param int $customer_id
     * @return Customer
     */
    public static function loadByCustomerId(int $customer_id): Customer
    {
        return new Customer($customer_id);
    }

    /**
     * @param int $customer_id
     * @param int|null $shop_id
     * @return string
     * @throws SmartDataEntityNotFoundException
     */
    public static function getCustomerNr(int $customer_id, ?int $shop_id = null): string
    {
        if (!$customer_id) {
            trigger_error('customer::getCustomerNr() called without $customer_id', E_USER_NOTICE);
            return '';
        }

        if ($shop_id !== null) {
            return $customer_id . "-" . self::getCustomerSuffixByShopId($shop_id);
        }

        $customer_nr = db::getInstance()->fieldQuery("
                SELECT
                    customers.customer_nr
                FROM
                    customers
                WHERE
                    customers.customer_id = '" . $customer_id . "'
        ");

        if (!$customer_nr) {
            throw new SmartDataEntityNotFoundException('customer entity "' . $customer_id . '" not found');
        }

        return $customer_nr;
    }

    /**
     * Gibt den Kunden Suffix für eine Shop Id zurück
     *
     * @param int $shop_id
     * @return string
     */
    public static function getCustomerSuffixByShopId(int $shop_id): string
    {
        static $customer_suffix;

        if (!isset($customer_suffix)) {
            $customer_suffix = db::getInstance()->query("
                    SELECT
                        einst_shop.shop_id,
                        einst_shop.vkunr
                    FROM
                        einst_shop
            ")->asSingleArray('shop_id');
        }

        if (!isset($customer_suffix[$shop_id])) {
            throw new FatalException("customer suffix not set for shop_id '{$shop_id}'");
        }

        return $customer_suffix[$shop_id];
    }

    public static function getDebtorAccount(int $customer_id, int $shop_id): int
    {
        if ($customer_id > 1_000_000) {
            return $customer_id;
        }

        if ($shop_id == Shop::ALLEGO && $customer_id > 699_999) {
            $customer_id = 699_999;
        }

        $customer_id = $customer_id + 1_000_000;

        return $customer_id;
    }


    /**
     * gibt möglich Anreden als Array zurück
     *
     * @return array $anreden
     * @todo -> Anrede -> Salutation
     */
    public static function getAnreden(): array
    {
        return [
            '' => '',
            'Herr' => 'Herr',
            'Frau' => 'Frau',
            /*'Familie' => 'Familie',*/
            /*'Firma' => 'Firma', */
        ];
    }

    public static function getProtokollTypes(): array
    {
        return [
            'stamm' => 'Stamm'
        ];
    }

    public static function getCustomerTypeNames(): array
    {
        return db::getInstance()->query("
            SELECT
                customer_type.customer_type,
                customer_type.customer_type_name
            FROM
                customer_type
            ORDER BY
                customer_type.pos
        ")->asSingleArray('customer_type');
    }

    /**
     * gibt die passende Grußformel für den Kunden zurück. (ACHTUNG ohne Komma)
     *
     * @param string $anrede
     * @param string $name
     * @return string
     */
    public static function getGrussformel(string $anrede, string $vorname, string $name): string
    {
        switch ($anrede) {
            case 'Herr':
                return 'Sehr geehrter Herr ' . $name;
            case 'Frau':
                return 'Sehr geehrte Frau ' . $name;
            case 'Familie':
                return 'Sehr geehrte Familie ' . $name;
        }

        return sprintf('Guten Tag %s %s', $vorname, $name);
    }

    public static function getShopIdByCustomerId(int $customer_id): int
    {
        return db::getInstance()->fieldQuery("
                SELECT
                    customers.shop_id
                FROM
                    customers
                WHERE
                    customers.customer_id = '" . $customer_id . "'
        ");
    }


    /**
     * Gibt eine noch Freie Kundennummer zurück
     *
     * @param $shop_id
     * @return int $customer_id
     */
    public static function getNewCustomerId(int $shop_id): int
    {
        $db = db::getInstance();

        $customer_id = $db->transaction(function (db_generic $db) {
            $customer_id = $db->fieldQuery("
                SELECT
                    customer_free_customer_id.customer_id
                FROM
                    customer_free_customer_id
                WHERE
                    customer_free_customer_id.free = 1
                LIMIT
                    1
                FOR UPDATE
            ");

            if ($customer_id) {
                $db->query("
                    UPDATE
                        customer_free_customer_id
                    SET
                        customer_free_customer_id.free = 0
                    WHERE
                        customer_free_customer_id.customer_id = $customer_id
                ");
            }

            return $customer_id;
        });

        if (!$customer_id) {
            $nk = new Nummernkreis('customer_id', ['type_id' => $shop_id]);

            $customer_id = $nk->getNumber();
        }

        return $customer_id;
    }

    public static function getDummy(int $shop_id = 1): Customer
    {
        $customer = new Customer();
        $customer->setShopId($shop_id);
        $customer->setCustomerType(CustomerConst::CUSTOMER_TYPE_DUMMY);
        $customer->setCountryId(CountryConst::DE);
        $customer->save();

        return $customer;
    }
}
