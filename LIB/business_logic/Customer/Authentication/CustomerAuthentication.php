<?php

namespace wws\Customer\Authentication;

use bqp\db\db_generic;
use config;
use db;
use wws\Customer\CustomerRepository;

class CustomerAuthentication
{
    /**
     * @var db_generic
     */
    private $db;

    /**
     * @var CustomerPasswordHasing
     */
    private $password_hashing;

    public function __construct()
    {
        $this->password_hashing = new CustomerPasswordHasing();
        $this->password_hashing->setAppSalt(config::system('app_salt'));
        $this->db = db::getInstance();
    }

    /**
     * @param string $email
     * @param string $password
     * @param int $shop_id
     * @return int $customer_id
     * @throws CustomerAuthenticationException
     */
    public function authenticateByEmail(string $email, string $password, int $shop_id): int
    {
        $result = $this->db->query("
                SELECT
                    customers.customer_id,
                    customers.email,
                    customers.passwort AS password_hash
                FROM
                    customers
                WHERE
                    customers.email = '" . $this->db->escape($email) . "' AND
                    customers.shop_id = '" . $shop_id . "'
        ");

        foreach ($result as $row) {
            if ($this->password_hashing->verify($row['password_hash'], $password)) {
                $this->setNewPassword($row['customer_id'], $password);

                return $row['customer_id'];
            }
        }

        throw new CustomerAuthenticationException();
    }

    /**
     * @param string $customer_nr
     * @param string $password
     * @return int $customer_id
     * @throws CustomerAuthenticationException
     */
    public function authenticateByCustomerNr(string $customer_nr, string $password): int
    {
        $customer_id = CustomerRepository::convertCustomerNrToCustomerId($customer_nr);

        if (!$customer_id) {
            throw new CustomerAuthenticationException('Kundennummer existiert nicht.');
        }

        return $this->authenticateByCustomerId($customer_id, $password);
    }

    /**
     * @param int $customer_id
     * @param string $password
     * @return int $customer_id
     * @throws CustomerAuthenticationException
     */
    public function authenticateByCustomerId(int $customer_id, string $password): int
    {
        $row = $this->db->singleQuery("
            SELECT
                customers.customer_id,
                customers.passwort AS password_hash
            FROM
                customers
            WHERE
                customers.customer_id = '" . $this->db->escape($customer_id) . "'
        ");

        if (!$row) {
            throw new CustomerAuthenticationException('Kundennummer oder Passwort falsch.');
        }

        if ($this->password_hashing->verify($row['password_hash'], $password)) {
            if ($this->password_hashing->needRehash($row['password_hash'])) {
                $this->setNewPassword($row['customer_id'], $password);
            }

            return $row['customer_id'];
        }

        throw new CustomerAuthenticationException('Kundennummer oder Passwort falsch.');
    }

    public function setNewPassword(int $customer_id, string $password): void
    {
        $hash = $this->password_hashing->hash($password);

        $this->setPasswordHash($customer_id, $hash);
    }

    public function getRandomPassword(): string
    {
        return substr(md5(microtime()), 1, 8);
    }

    public function transferAuthenfication(int $src_customer_id, int $dst_customer_id): void
    {
        $password_hash = $this->db->fieldQuery("
            SELECT
                customers.passwort
            FROM
                customers
            WHERE
                customers.customer_id = $src_customer_id
        ");

        $this->setPasswordHash($dst_customer_id, $password_hash);
    }

    public function removePassword(int $customer_id): void
    {
        $this->setPasswordHash($customer_id, '');
    }

    private function setPasswordHash(int $customer_id, string $password_hash): void
    {
        $this->db->query("
            UPDATE
                customers
            SET
                customers.passwort = '" . $password_hash . "'
            WHERE
                customers.customer_id = '" . $customer_id . "'
        ");
    }
}
