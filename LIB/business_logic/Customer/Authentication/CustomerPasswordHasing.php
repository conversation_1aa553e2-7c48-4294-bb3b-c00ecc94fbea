<?php

namespace wws\Customer\Authentication;

use bqp\Utils\StringUtils;

class CustomerPasswordHasing
{
    private $app_salt = '';

    public function __construct()
    {
    }

    public function setAppSalt($app_salt)
    {
        $this->app_salt = $app_salt;
    }


    public function hash($password)
    {
        return password_hash($this->app_salt . $password, PASSWORD_DEFAULT);
    }

    public function verify($hash, $password)
    {
        if (strlen($hash) == 32) {
            return $hash === md5($password);
        }


        if (StringUtils::begins($hash, '$2k$07$')) {
            $t = md5($password);
            return $this->t($hash) === md5($t . $t . $this->app_salt . $t . $t . $t . $this->app_salt . $t . $t . $t . $this->app_salt);
        } else {
            return password_verify($this->app_salt . $password, $hash);
        }
    }

    public function needRehash($hash)
    {
        if (StringUtils::begins($hash, '$2k$07$')) {
            return true;
        }
        if (strlen($hash) == 32) {
            return true;
        }

        return false;
    }

    public function t($hash)
    {
        $hash = substr($hash, 7, -5);
        for ($i = 0, $c = 0, $r = ''; $i < 32; $i++) {
            $r .= $hash[$c];
            $c++;
            if ($i % 2 === 0) {
                $c++;
            }
        }
        $hash = strrev(str_rot13(substr($r, 24) . substr($r, 0, 24)));
        return $hash;
    }
}
