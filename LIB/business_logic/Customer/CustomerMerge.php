<?php

namespace wws\Customer;

use bqp\Exceptions\FatalException;
use db;
use wws\Customer\Authentication\CustomerAuthentication;

class CustomerMerge
{


    /**
     * @var CustomerAuthentication
     */
    private $authentication;

    public function __construct(CustomerAuthentication $authentication)
    {
        $this->authentication = $authentication;
    }

    /**
     * überführt die kundendaten von $old_customer_id in die $new_customer_id,
     * und markiert den alten kunden als gelöscht
     *
     * @param int $dst_customer_id neue/ziel kundennummer
     * @param int $src_customer_id alte kundennummer
     */
    public function merge(int $dst_customer_id, int $src_customer_id): void
    {
        $dst_customer_id = (int)$dst_customer_id;
        $src_customer_id = (int)$src_customer_id;

        if ($dst_customer_id === $src_customer_id) {
            throw new FatalException('dst and src customer are equal!');
        }

        $customer_dst = new Customer($dst_customer_id);
        $customer_src = new Customer($src_customer_id);

        $customer_dst->setAnrede($customer_src->getAnrede());
        $customer_dst->setFirma($customer_src->getFirma());
        $customer_dst->setName($customer_src->getName());
        $customer_dst->setVorname($customer_src->getVorname());
        $customer_dst->setAdresse1($customer_src->getAdresse1());
        $customer_dst->setAdresse2($customer_src->getAdresse2());
        $customer_dst->setPlz($customer_src->getPlz());
        $customer_dst->setOrt($customer_src->getOrt());
        $customer_dst->setCountryId($customer_src->getCountryId());
        $customer_dst->setTelefon($customer_src->getTelefon());
        $customer_dst->setFax($customer_src->getFax());
        $customer_dst->setEmail($customer_src->getEmail());
        $customer_dst->setMobil($customer_src->getTelefon2());

        $memo = new CustomerMemo('Kunden zusammengeführt, alte Kundennummer: ' . $customer_src->getCustomerNr());
        $memo->addCommentLine("altes Memofeld:");
        $memo->addCommentLine("--------------------------------");
        $memo->addCommentLine($customer_src->getBemerkung());
        $memo->addCommentLine("--------------------------------");

        $customer_dst->addMemo($memo);

        $customer_dst->save();

        $customer_src->setCustomerStatus(CustomerConst::CUSTOMER_STATUS_DELETE);
        $customer_src->addMemo(new CustomerMemo('Neue Kundennummer: ' . $customer_dst->getCustomerNr()));
        $customer_src->save();

        $this->authentication->transferAuthenfication($customer_src->getCustomerId(), $customer_dst->getCustomerId());
        $this->authentication->removePassword($customer_src->getCustomerId());

        $db = db::getInstance();

        //bestellungen updaten
        $db->query("
                UPDATE
                    orders
                SET
                    orders.customer_id = $dst_customer_id
                WHERE
                    orders.customer_id = $src_customer_id
            ");

        $db->query("
            UPDATE
                customer_mail_archive
            SET
                customer_mail_archive.customer_id = $dst_customer_id
            WHERE
                customer_mail_archive.customer_id = $src_customer_id
        ");

        $db->query("
                UPDATE
                    mailsystem_mails
                SET
                    mailsystem_mails.customer_id = $dst_customer_id
                WHERE
                    mailsystem_mails.customer_id = $src_customer_id
            ");
        /*
        $db->query("
            UPDATE
                mailsystem_mails_archiv
            SET
                mailsystem_mails_archiv.customer_id = $new_customer_id
            WHERE
                mailsystem_mails_archiv.customer_id = $old_customer_id
        ");
        */
        //rekla updaten
        $db->query("
                UPDATE
                    rekla_stamm
                SET
                    rekla_stamm.customer_id = $dst_customer_id
                WHERE
                    rekla_stamm.customer_id = $src_customer_id
            ");
    }
}

