<?php

namespace wws\Customer;

use bqp\db\db_generic;

class CustomerDuplicateFinder
{
    protected db_generic $db;
    private array $ignore_mail_addresses = [];

    public function __construct(db_generic $db)
    {
        $this->db = $db;

        $this->ignore_mail_addresses[] = '*';
        $this->ignore_mail_addresses[] = '<EMAIL>'; //crowdfox bestellungen
        $this->ignore_mail_addresses[] = '<EMAIL>';
    }

    /**
     * sucht anhand eines customer objektes, weitere kunden mit der selben mail adresse oder selben namen im plz beriech
     *
     * @param Customer $customer
     * @return int[]
     */
    public function findByCustomer(Customer $customer): array
    {
        $mail = $customer->getEmail();
        $sql_mail = '';
        if ($mail && !in_array($mail, $this->ignore_mail_addresses)) {
            $sql_mail = " OR (
                customers.email = '" . $this->db->escape($mail) . "'
            ) ";
        }

        $result = $this->db->query("
            SELECT
                customers.customer_id
            FROM
                customers
            WHERE
                (
                    (
                        customers.firma = '" . $this->db->escape($customer->getFirma()) . "' AND
                        customers.name = '" . $this->db->escape($customer->getName()) . "' AND
                        customers.vorname = '" . $this->db->escape($customer->getVorname()) . "' AND
                        customers.plz = '" . $this->db->escape($customer->getPlz()) . "' AND
                        customers.strasse = '" . $this->db->escape($customer->getAdresse1()) . "'
                    )
                    $sql_mail
                ) AND
                customers.customer_id <> '" . $customer->getCustomerId() . "' AND
                customers.customer_status = '" . CustomerConst::CUSTOMER_STATUS_OK . "' AND
                customers.shop_id = '" . $customer->getShopId() . "'
        ")->asSingleArray();

        return $result;
    }

    /**
     * sucht anhand eines customer objektes, weitere kunden mit der selben mail adresse oder selben namen im plz beriech
     *
     * @param Customer $customer
     * @return int|null
     */
    public function findByEmailAndInvoiceAddress(Customer $customer): ?int
    {
        $customer_ids = $this->findAllCustomerIdsByEmailAndInvoiceAddress($customer);

        if ($customer_ids) {
            return current($customer_ids);
        }

        return null;
    }

    /**
     * sucht anhand eines customer objektes, weitere kunden mit der selben mail adresse oder selben namen im plz beriech
     *
     * @param Customer $customer
     * @return int[]
     */
    public function findAllCustomerIdsByEmailAndInvoiceAddress(Customer $customer): array
    {
        $mail = $customer->getEmail();
        if (!$customer->getEmail()
            || $customer->getEmail() === '*'
            || in_array($customer->getEmail(), $this->ignore_mail_addresses, true)
        ) {
            return [];
        }

        $sql_mail = " AND (
                customers.email = '" . $this->db->escape($mail) . "'
        ) ";

        return $this->db->prepare("
            SELECT
                customers.customer_id
            FROM
                customers
            WHERE
                (
                    (
                        customers.name = :name AND
                        customers.vorname = :vorname AND
                        customers.plz = :plz AND
                        customers.strasse = :strasse AND
                        customers.ort = :ort
                    )
                    $sql_mail
                ) AND
                customers.customer_id <> :customer_id AND
                customers.customer_status = 'ok' AND
                customers.shop_id = :shop_id
            ORDER BY
                customers.customer_id DESC
        ")->execute([
            'name' => $customer->getName(),
            'vorname' => $customer->getVorname(),
            'plz' => $customer->getPlz(),
            'strasse' => $customer->getAdresse1(),
            'ort' => $customer->getOrt(),
            'customer_id' => $customer->getCustomerId(),
            'shop_id' => $customer->getShopId(),
        ])->asSingleArray();
    }
}
