<?php

namespace wws\Customer;

use bqp\Exceptions\InputException;
use bqp\Vat\EuVatNumberFormatValidator;
use bqp\Vat\VatNumberFormatException;
use wws\Country\CountryRepository;

class CustomerVatNumberValidator
{
    /**
     * @var EuVatNumberFormatValidator
     */
    private $eu_validator;

    public function __construct()
    {
        $this->eu_validator = new EuVatNumberFormatValidator();
    }

    public function validateSyntaxByCustomer(Customer $customer, InputException $errors): void
    {
        if (CountryRepository::isEu($customer->getCountryId())) {
            try {
                $this->eu_validator->validateVatNumberFormat(CountryRepository::getCountrySignById($customer->getCountryId()), $customer->getUstidnr());
            } catch (VatNumberFormatException $e) {
                $errors->add('ustidnr', 'Bitte geben Sie eine gültige Ustidnr an. Format für dieses Land: ' . $e->getFormatDescription());
            }
        }
    }
}
