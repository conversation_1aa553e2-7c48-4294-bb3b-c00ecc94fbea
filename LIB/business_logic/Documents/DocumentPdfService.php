<?php

namespace wws\Documents;

use bqp\Pdf\Wkhtmltopdf;
use config;
use service_loader;
use wws\buchhaltung\Invoice\InvoiceDocument;
use wws\core\DocumentTemplateSys;

class DocumentPdfService
{

    /**
     * @var Wkhtmltopdf
     */
    protected $wkhtmltopdf;
    protected $url;

    public function __construct()
    {
        $this->wkhtmltopdf = service_loader::wkhtmltopdf();
        $this->wkhtmltopdf->setMargin(16);
        $this->wkhtmltopdf->disableSmartShrinking();

        $this->url = config::system('url');

        //todo...
        $this->url = str_replace(':8080', '', $this->url); //diskrepanz zwischen außerhalb und innerhalb der vm...
    }

    public function order_document($order_id, $document_type)
    {
        $url = $this->url . 'ext/order_document.php?order_id=' . $order_id . '&document_type=' . $document_type . '&wkhtmltopdf=1';

        $sig = service_loader::urlParameterSignature();
        $url = $sig->sigUrl($url);

        return $this->wkhtmltopdf->convert($url);
    }

    /**
     * nicht direkt nutzen!
     * @param InvoiceDocument $beleg
     * @return string
     * @see \wws\buchhaltung\Invoice\InvoiceDocumentUtils::tryCreatePdf()
     * @see \wws\buchhaltung\Invoice\InvoiceDocumentUtils::createPdf()
     *
     */
    public function invoice(InvoiceDocument $beleg): string
    {
        $tpl = DocumentTemplateSys::getInstance();

        $tpl->assign('beleg', $beleg);
        $tpl->setTitle('Beleg');
        $tpl->addCss('/res/css/invoice/invoice' . $beleg->getInvoiceVersion() . '.css');
        ob_start();
        $tpl->dokumentDisplay($beleg->getInvoiceTemplatePath('html'), true);
        $invoice = ob_get_clean();

        return $this->html2pdf($invoice);
    }

    public function html2pdf(string $html): string
    {
        do {
            $id = mt_rand(1000000, 9999999);
            $tmp_file = config::system('temp_dir') . '/wkhtmltopdf_' . $id . '.tmp';
        } while (file_exists($tmp_file));

        file_put_contents($tmp_file, $html);

        $url = $this->url . 'ext/wkhtmltopdf.php?file_id=' . $id . '&modus=html&wkhtmltopdf=1&tan=';

        $sig = service_loader::urlParameterSignature();
        $url = $sig->sigUrl($url);

        $pdf_blob = $this->wkhtmltopdf->convert($url);
        unlink($tmp_file);

        return $pdf_blob;
    }
}
