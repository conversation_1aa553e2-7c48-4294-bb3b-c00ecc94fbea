<?php

namespace wws\Documents;

use wws\business_structure\Shop;

class DocumentRepository
{
    /**
     * @param $shop_id
     * @return string
     */
    public static function getHtmlFooter($shop_id): string
    {
        $shop = new Shop($shop_id);

        $return = '<table width="100%">';

        $return .= '<tr><td colspan="5"><hr /></td></tr>';

        $signature = $shop->getDocumentSignature();

        $flipped_signature = [];

        /**
         * @var array $row
         */
        foreach ($signature as $row_key => $row) {
            foreach ($row as $col_key => $col) {
                $flipped_signature[$col_key][$row_key] = $col;
            }
        }

        /** @var array $rows */
        foreach ($flipped_signature as $rows) {
            $return .= '<tr>';

            foreach ($rows as $field) {
                $return .= '<td>' . $field . '</td>';
            }

            $return .= '</tr>';
        }

        $return .= '</table>';

        return $return;
    }
}
