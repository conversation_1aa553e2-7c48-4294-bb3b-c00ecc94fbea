<?php

namespace wws\BankAccount;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use bqp\Utils\FileUtils;
use system_protokoll;
use wws\BankAccountExporter\BankAccountExporterFactory;
use wws\BankAccountExporter\BankAccountExporterProfileException;
use wws\buchhaltung\ExportArchiveEntity;
use wws\buchhaltung\ExportArchiveRepository;

class BankAccountExportService
{
    private ExportArchiveRepository $export_archive_repository;
    private BankAccountCheckerFactory $account_checker_factory;

    /**
     * @var BankAccount[]
     */
    private array $bank_accounts;
    private db_generic $db;
    private BankAccountExporterFactory $bank_account_exporter_factory;

    public function __construct(
        BankAccountRepository $bank_account_repository,
        BankAccountExporterFactory $bank_account_exporter_factory,
        ExportArchiveRepository $export_archive_repository,
        BankAccountCheckerFactory $account_checker_factory,
        db_generic $db
    ) {
        $this->export_archive_repository = $export_archive_repository;
        $this->account_checker_factory = $account_checker_factory;
        $this->bank_account_exporter_factory = $bank_account_exporter_factory;

        $this->bank_accounts = $bank_account_repository->loadBySqlWhere("1");
        $this->db = $db;
    }


    public function runAutomatic(array $account_ids): void
    {
        $protocol = system_protokoll::getInstance('buchhaltung_export');

        foreach ($account_ids as $account_id) {
            $account = $this->getAccount($account_id);

            $date = new DateObj();

            for ($i = 1; $i <= 3; $i++) { //die letzten 3 monate prüfen
                $date->subSimple('month', 1);
                $date->setToMonthBegin();

                try {
                    $export_id = $this->export($account->getAccountId(), $date, false);

                    $protocol->info('Export für Account ' . $account->getAccountId() . ' wurde erstellt. (' . $export_id . ')');
                } catch (BankAccountExportServiceCheckResult $e) {
                } catch (BankAccountExporterProfileException $e) {
                }
            }
        }
    }


    public function check(int $account_id, DateObj $date): BankAccountExportServiceCheckResult
    {
        $date = $date->clone()->setToMonthBegin();
        $date_end = $date->clone()->setToMonthEnd();

        $check_result = new BankAccountExportServiceCheckResult();

        $account = $this->getAccount($account_id);

        //prüfen ob bereits exportiert
        $export_key = $this->getExportKey($account, $date);
        if ($this->export_archive_repository->hasKey($export_key)) {
            $check_result->add('Die Transaktionen wurden bereits exportiert.');
        }

        //prüfen ob vollständig
        $account_checker = $this->account_checker_factory->getBankAccountChecker($account_id);
        $result = $account_checker->check($date, $date->clone()->setToMonthEnd());
        if ($result->isAbnormal()) {
            $check_result->add('Es wurden Lücken in den Transaktionen gefunden: ' . $result->getAsText());
        }

        $unknown_transactions = $this->db->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                buchhaltung_bank_transactions
            WHERE
                buchhaltung_bank_transactions.account_id = $account_id AND
                buchhaltung_bank_transactions.transaction_date BETWEEN '" . $date->db('begin') . "' AND '" . $date_end->db('end') . "' AND
                (
                    buchhaltung_bank_transactions.transaction_status IN ('" . BankTransaction::TRANSACTION_STATUS_UNKNOWN . "', '" . BankTransaction::TRANSACTION_STATUS_FAILED . "') OR
                    buchhaltung_bank_transactions.transaction_type = '" . BankTransaction::TRANSACTION_TYPE_UNKNOWN . "'
                )
        ");

        if ($unknown_transactions > 0) {
            $check_result->add('Es wurden unklare Transaktionen gefunden. (Anzahl: ' . $unknown_transactions . ')');
        }

        return $check_result;
    }

    public function export(int $account_id, DateObj $month_date, bool $ignore_check = false): int
    {
        $month_date = $month_date->clone()->setToMonthBegin();

        if (!$ignore_check) {
            $check_result = $this->check($account_id, $month_date);

            if ($check_result->isError()) {
                throw $check_result;
            }
        }

        $transaction_exporter = $this->bank_account_exporter_factory->getExporterForAccountId($account_id);
        $transaction_exporter->setDateRange($month_date, $month_date->clone()->setToMonthEnd());

        $content = $transaction_exporter->getAsString();
        $filename = $transaction_exporter->getPossibleFilename();

        $account = $this->getAccount($account_id);

        $export = new ExportArchiveEntity();
        $export->setExportType($account->getExportType());
        $export->setShopId($account->getShopId());

        list($export_key, $version) = $this->getFreeExportKey($account, $month_date);

        $export->setExportKey($export_key);

        if ($version > 1) {
            $filename = FileUtils::appendBeforExtenstion($filename, 'REV' . $version);
        }

        $export->setFilename($filename);
        $export->setDescription(str_replace('{{$filename}}', $filename, $account->getExportDescription()));

        $this->export_archive_repository->saveFile($filename, $content);
        $this->export_archive_repository->save($export);

        return $export->getExportId();
    }

    public function getExistingExportIds(int $account_id, DateObj $date): array
    {
        $account = $this->getAccount($account_id);

        $export_key = $this->getExportKey($account, $date);

        $result = $this->db->query("
            SELECT
                buchhaltung_export_archive.export_id
            FROM
                buchhaltung_export_archive
            WHERE
                buchhaltung_export_archive.export_key LIKE '" . $this->db->escape($export_key) . "%'
        ")->asSingleArray();

        return $result;
    }


    public function getAccount(int $account_id): BankAccount
    {
        return $this->bank_accounts[$account_id];
    }

    private function getExportKey(BankAccount $account, DateObj $date): string
    {
        return 'account_' . $account->getAccountId() . '_' . $date->format('Ym');
    }

    private function getFreeExportKey(BankAccount $account, DateObj $date): array
    {
        $export_key = $this->getExportKey($account, $date);
        if (!$this->export_archive_repository->hasKey($export_key)) {
            return [$export_key, 1];
        }

        for ($i = 2; $i <= 20; $i++) {
            $act_export_key = $export_key . '-' . $i;
            if (!$this->export_archive_repository->hasKey($act_export_key)) {
                return [$act_export_key, $i];
            }
        }

        throw new FatalException('no free export key found');
    }

    public function getExportStatusForYear(DateObj $date, array $account_ids): array
    {
        $export_result = [];

        $date_begin = $date->clone()->setBeginOfYear();
        $date_end = $date->clone()->setEndOfYear();

        $date_range = $date_begin->rangeTo($date_end, 'month');

        $known_exports = $this->db->query("
            SELECT
                buchhaltung_export_archive.export_key,
                buchhaltung_export_archive.export_id,
                buchhaltung_export_archive.downloaded
            FROM
                buchhaltung_export_archive
            WHERE
                buchhaltung_export_archive.export_key LIKE 'account_%_" . $date_begin->getYear() . "%'
        ")->asArray('export_key');

        foreach ($date_range as $month) {
            $export_result_month = [];

            foreach ($account_ids as $account_id) {
                $account = $this->getAccount($account_id);

                $key = $this->getExportKey($account, $month);

                $export = [
                    'export_id' => null,
                    'downloaded' => false
                ];

                if (isset($known_exports[$key])) {
                    $export['export_id'] = $known_exports[$key]['export_id'];
                    if ($known_exports[$key]['downloaded']) {
                        $export['downloaded'] = true;
                    }
                }

                $export_result_month[$account_id] = $export;
            }

            $export_result[$month->format('Ym')] = $export_result_month;
        }

        return $export_result;
    }
}
