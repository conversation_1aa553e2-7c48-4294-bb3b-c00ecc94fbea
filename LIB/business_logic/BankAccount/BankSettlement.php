<?php

namespace wws\BankAccount;

use bqp\Date\DateObj;
use bqp\FileAttachment\FileAttachments;
use bqp\FileAttachment\FileAttachmentSerializer;
use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataObj;
use Exception;

class BankSettlement implements ProvideSmartDataObj
{
    private SmartDataObj $daten;

    /**
     * @var BankTransaction[]
     */
    private array $bank_transactions = [];

    /**
     * @var FileAttachments
     */
    private ?FileAttachments $attachments = null;

    public function __construct()
    {
        $this->daten = new SmartDataObj($this);

        $this->loadDefaults();
    }

    public function loadDefaults(): void
    {
        $this->setAmount(0);
        $this->setQuantity(0);
        $this->setSettlementReference('');
        $this->setSettlementExported(false);
    }

    public function __clone()
    {
        $this->daten = clone $this->daten;
        $this->daten->setObj($this);
    }

    public function setSettlementId(int $settlement_id): bool
    {
        return $this->daten->setter('settlement_id', $settlement_id);
    }

    public function setAccountId(int $account_id): bool
    {
        return $this->daten->setter('account_id', $account_id);
    }

    public function setSettlementName(string $settlement_name): bool
    {
        return $this->daten->setter('settlement_name', $settlement_name);
    }

    /**
     * @param DateObj $settlement_date
     * @return boolean
     */
    public function setSettlementDate(DateObj $settlement_date): bool
    {
        return $this->daten->setter('settlement_date', $settlement_date->db());
    }

    public function setSettlementAttachmentsSerialized(string $serialized_attachments): bool
    {
        return $this->daten->setter('settlement_attachments', $serialized_attachments);
    }

    public function setAmount(float $amount): bool
    {
        return $this->daten->setter('amount', round($amount, 2));
    }

    public function setQuantity(int $quantity): bool
    {
        return $this->daten->setter('quantity', $quantity);
    }

    public function addTransaction(BankTransaction $trx): void
    {
        $this->setQuantity($this->getQuantity() + 1);
        $this->setAmount($this->getAmount() + $trx->getAmount());

        $trx->setBankSettlement($this);
        $trx->setAccountId($this->getAccountId());
        $trx->setSettlementId($this->getSettlementId());

        $this->bank_transactions[] = $trx;
    }

    public function _recalcAmountAndQuantity(): void
    {
        if (!$this->bank_transactions) {
            throw new Exception('no transactions to recalc');
        }

        $amount = 0;

        foreach ($this->bank_transactions as $trx) {
            $amount += $trx->getAmount();
        }

        $this->setAmount($amount);
        $this->setQuantity(count($this->bank_transactions));
    }

    /**
     * @return int settlement_id
     */
    public function getSettlementId(): int
    {
        return (int)$this->daten->getter('settlement_id');
    }

    /**
     * @return int account_id
     */
    public function getAccountId(): int
    {
        return (int)$this->daten->getter('account_id');
    }

    /**
     * @return string settlement_name
     */
    public function getSettlementName(): string
    {
        return $this->daten->getter('settlement_name');
    }

    public function getSettlementDate(): DateObj
    {
        return new DateObj($this->daten->getter('settlement_date'));
    }

    public function getSettlementAttachments(): FileAttachments
    {
        if (!$this->attachments) {
            $this->attachments = FileAttachmentSerializer::unserialize($this->daten->getter('settlement_attachments'));
        }

        return $this->attachments;
    }

    /**
     * @return float
     */
    public function getAmount(): float
    {
        return $this->daten->getter('amount');
    }

    /**
     * @return integer
     */
    public function getQuantity(): int
    {
        return $this->daten->getter('quantity');
    }

    public function setSettlementDataAdded(DateObj $date): bool
    {
        return $this->daten->setter('settlement_date_added', $date->db());
    }

    /**
     * @return DateObj
     */
    public function getSettlementDateAdded(): DateObj
    {
        return new DateObj($this->daten->getter('settlement_date_added'));
    }

    public function setSettlementReference(string $settlement_reference): bool
    {
        return $this->daten->setter('settlement_reference', $settlement_reference);
    }

    public function getSettlementReference(): string
    {
        return $this->daten->getter('settlement_reference');
    }

    public function setSettlementExported(bool $settlement_exported): bool
    {
        return $this->daten->setter('settlement_exported', $settlement_exported ? 1 : 0);
    }

    public function isSettlementExported(): bool
    {
        return (bool)$this->daten->getter('settlement_exported');
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }

    /**
     * Achtung... das wird nie aus der Datenbank befüllt!
     *
     * @return BankTransaction[]
     */
    public function _getBankTransactions(): array
    {
        return $this->bank_transactions;
    }
}
