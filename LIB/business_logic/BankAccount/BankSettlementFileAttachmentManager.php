<?php

namespace wws\BankAccount;

use bqp\FileAttachment\FileAttachment;
use bqp\FileAttachment\FileAttachmentManager;
use bqp\FileAttachment\FileAttachmentSerializer;
use bqp\storage\storage_factory;
use bqp\storage\storage_mount_manager;
use bqp\Utils\FileUtils;

class BankSettlementFileAttachmentManager extends FileAttachmentManager
{
    private storage_mount_manager $storage;

    public function __construct(storage_factory $storage_factory)
    {
        $this->storage = $storage_factory->get('wws');
    }

    public function process(BankSettlement $bank_settlement): void
    {
        $attachments = $bank_settlement->getSettlementAttachments();

        $this->loadTemp($attachments);

        $attachments_to_store = $attachments->getAttachmentsToStore();

        if ($attachments_to_store) {
            $sub_directroy = $bank_settlement->getAccountId() . '/' . $bank_settlement->getSettlementDate()->format('Y');

            //@todo das konzept mit dem $sub_directroy ist blöd... ich will ggf. auch noch ein Datum mit in den Dateinamen bringen....
//              $dst_filename = \string_utils::slug($attachment_filename, '._');
//              $pos = strrpos($dst_filename, '.');
//              $add = date('Ymd-Hmi');
//            $dst_filename = substr($dst_filename, 0, $pos+1).$add.substr($dst_filename, $pos);

            foreach ($attachments_to_store as $attachment) {
                $this->saveAttachment($attachment, $sub_directroy);
            }
        }

        $bank_settlement->setSettlementAttachmentsSerialized(FileAttachmentSerializer::serialize($attachments));
    }

    private function saveAttachment(FileAttachment $attachment, string $sub_directroy = ''): void
    {
        $content = $attachment->getAttachmentContent()->getContent();

        $path_add = '';

        if ($sub_directroy) {
            $path_add = $sub_directroy;
        }

        $clear_filename = FileUtils::normalizeFilename($attachment->getFilename());

        $storage_url = 'wws://bank_settlement_attachments/' . $path_add . FileUtils::appendBeforExtenstion($clear_filename, md5($content));

        $attachment->setFilesize(strlen($content));
        $attachment->setStorageUrl($storage_url);

        if ($this->storage->has($storage_url)) {
            //muss nix getan werden -> der hash ist auf dem content
            //$this->storage->update($path, $content);
        } else {
            $this->storage->write($storage_url, $content);
        }
    }

    public function getAttachmentDownloadUrl(FileAttachment $attachment): string
    {
        return $this->storage->createHttpUrl($attachment->getStorageUrl(), $attachment->getFilename());
    }

    public function getContent(FileAttachment $attachment): string
    {
        return $this->storage->read($attachment->getStorageUrl());
    }
}
