<?php

namespace wws\BankAccount;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;
use service_loader;

class BankTransactionRepository
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function load(int $transaction_id): BankTransaction
    {
        $transactions = $this->searchBySqlWhere("buchhaltung_bank_transactions.transaction_id = $transaction_id");

        if (count($transactions) !== 1) {
            throw new SmartDataEntityNotFoundException();
        }

        return $transactions[0];
    }

    public function save(BankTransaction $bank_transaction): int
    {
        return $bank_transaction->save();
    }

    public function create(): BankTransaction
    {
        return new BankTransaction();
    }

    /**
     * @param string $sql_where
     * @return BankTransaction[]
     */
    public function searchBySqlWhere(string $sql_where): array
    {
        $result = $this->db->query("
            SELECT
                buchhaltung_bank_transactions.transaction_id,
                buchhaltung_bank_transactions.associated_transaction_id,
                buchhaltung_bank_transactions.account_id,
                buchhaltung_bank_transactions.transaction_hash,
                buchhaltung_bank_transactions.transaction_status,
                buchhaltung_bank_transactions.transaction_type,
                buchhaltung_bank_transactions.transaction_type_extern,
                buchhaltung_bank_transactions.transaction_date,
                buchhaltung_bank_transactions.extern_transaction_id,
                buchhaltung_bank_transactions.amount,
                buchhaltung_bank_transactions.sender_name,
                buchhaltung_bank_transactions.text_reference_1,
                buchhaltung_bank_transactions.text_reference_2,
                buchhaltung_bank_transactions.extra,
                buchhaltung_bank_transactions.settlement_id,
                buchhaltung_bank_transactions.shop_referenz,
                buchhaltung_bank_transactions.customer_id,
                buchhaltung_bank_transactions.order_id,
                buchhaltung_bank_transactions.person_konto_nr,
                buchhaltung_bank_transactions.posting_key,
                buchhaltung_bank_transactions.booking_text,                
                buchhaltung_bank_transactions.notice,
                buchhaltung_bank_transactions.tags,
                buchhaltung_bank_transactions.clarification_list,
                buchhaltung_bank_transactions.classificator_name,
                buchhaltung_bank_transactions.classificator_extra
            FROM
                buchhaltung_bank_transactions
            WHERE
                $sql_where
        ");

        $transactions = [];

        foreach ($result as $row) {
            $transaction = new BankTransaction();
            $transaction->getSmartDataObj()->loadDaten($row);

            $transactions[] = $transaction;
        }

        return $transactions;
    }

    public function getAccountIdByTransactionId(int $transaction_id): int
    {
        return $this->db->fieldQuery("
            SELECT
                buchhaltung_bank_transactions.account_id
            FROM
                buchhaltung_bank_transactions
            WHERE
                buchhaltung_bank_transactions.transaction_id = $transaction_id
        ");
    }

    public function getTransactionTypes(): array
    {
        return [
            BankTransaction::TRANSACTION_TYPE_UNKNOWN => 'Unbekannt',
            BankTransaction::TRANSACTION_TYPE_DEBITOR => 'Kundenzahlung',
            BankTransaction::TRANSACTION_TYPE_FEE => 'Gebühr',
            BankTransaction::TRANSACTION_TYPE_TRANSIT => 'Geldtransfer',
            BankTransaction::TRANSACTION_TYPE_MISC => 'Sonstiges'
        ];
    }

    public function delete(BankTransaction $transaction): void
    {
        $this->db->query("
            DELETE FROM
                buchhaltung_bank_transactions
            WHERE
                buchhaltung_bank_transactions.transaction_id = " . $transaction->getTransactionId() . "
        ");

        if ($transaction->getSettlementId()) {
            $summary = service_loader::get(BankSettlementSummary::class);
            $summary->recalcForSettlementId($transaction->getSettlementId());
        }
    }
}
