<?php

namespace wws\BankAccount;

use bqp\Date\DateObj;
use bqp\Exceptions\InputException;
use bqp\Json;
use bqp\Model\EntityChanges;
use bqp\Model\Model;
use bqp\Model\SmartDataObj;
use bqp\Vat\VatRate;
use bqp\Vat\VatRateSimple;
use db;
use service_loader;
use wws\BankAccount\Event\EventTransactionBooked;
use wws\BankAccount\Event\EventTransactionChanged;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerResult;
use function array_unique;
use function in_array;

class BankTransaction implements Model
{
    public const TRANSACTION_TYPE_UNKNOWN = 'unknown';
    public const TRANSACTION_TYPE_DEBITOR = 'debitor';
    public const TRANSACTION_TYPE_FEE = 'fee';
    public const TRANSACTION_TYPE_TRANSIT = 'transit';
    public const TRANSACTION_TYPE_MISC = 'misc';

    public const TRANSACTION_STATUS_UNKNOWN = 'unknown';
    public const TRANSACTION_STATUS_FILTERED = 'filtered';
    public const TRANSACTION_STATUS_BOOKED = 'booked';
    public const TRANSACTION_STATUS_FAILED = 'failed';

    private SmartDataObj $daten;

    private ?BankSettlement $bank_settlement = null;
    private ?BankTransaction $associated_bank_transaction = null;

    public function __construct()
    {
        $this->daten = new SmartDataObj($this);

        $this->loadDefault();
    }

    public function loadDefault(): void
    {
        $this->daten->setDefaults([
            'associated_transaction_id' => null,
            'account_id' => null,
            'transaction_status' => self::TRANSACTION_STATUS_UNKNOWN,
            'transaction_type' => self::TRANSACTION_TYPE_UNKNOWN,
            'transaction_type_extern' => '',
            'extern_transaction_id' => '',
            'currency_code' => 'EUR',
            'vat_rate_value' => null,
            'order_id' => null,
            'person_konto_nr' => '',
            'sender_name' => '',
            'text_reference_1' => '',
            'text_reference_2' => '',
            'shop_referenz' => '',
            'posting_key' => '',
            'booking_text' => '',
            'notice' => '',
            'tags' => '',
            'clarification_list' => 0,
            'booker_result' => '',
        ]);
    }

    public function __clone()
    {
        $this->daten = clone $this->daten;
        $this->daten->setObj($this);
    }

    /**
     * @return int $transaction_id
     */
    public function save(): int
    {
        if (!$this->daten->getter('transaction_hash')) {
            $this->autoSetTransactionHash();
        }

        if ($this->bank_settlement) {
            $this->setSettlementId($this->bank_settlement->getSettlementId());
            $this->setAccountId($this->bank_settlement->getAccountId());
        }

        if ($this->associated_bank_transaction) {
            $this->setAssociatedTransactionId($this->associated_bank_transaction->getTransactionId());
        }

        if (!$this->daten->isChange()) {
            return $this->getTransactionId();
        }

        $entity_changes = $this->getAllChanges();

        $db = db::getInstance();

        $changes = $this->daten->getChanges();
        $sql = [];

        foreach ($changes as $key => $value) {
            $sql[] = "buchhaltung_bank_transactions.$key = " . $db->quote($value);
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db->query("
                    INSERT INTO
                        buchhaltung_bank_transactions
                    SET
                        " . implode(',', $sql) . "
                ");

                $this->daten->setterDirect('transaction_id', $db->insert_id());

                break;
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                    UPDATE
                        buchhaltung_bank_transactions
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        buchhaltung_bank_transactions.transaction_id = '" . $this->getTransactionId() . "'
                ");
                break;
        }

        $this->daten->setSaved();

        $event = new EventTransactionChanged($this, $entity_changes);
        service_loader::getEventDispatcher()->dispatch($event);

        if (isset($changes['transaction_status']) && $changes['transaction_status'] === 'booked') {
            $event = new EventTransactionBooked($this);
            service_loader::getEventDispatcher()->dispatch($event);
        }

        return $this->getTransactionId();
    }

    public function getAllChanges(): EntityChanges
    {
        $protocol = new EntityChanges('buchhaltung_bank_transactions');
        $protocol->addBySmartDataObj($this->daten);

        return $protocol;
    }

    public function calcTransactionHash(): string
    {
        $data = number_format($this->getAmount(), 2, '.', '');
        $data .= $this->getExternTransactionId();
        $data .= $this->getSenderName();
        $data .= $this->getTextReference1();
        $data .= $this->getTextReference2();

        $extra = $this->getExtras();
        if (isset($extra['valuta'])) { //bank -> ohne valuta sind teilweise überweisungen nicht eindeutig
            $data .= $extra['valuta'];
        }

        return md5($data);
    }

    public function autoSetTransactionHash(): void
    {
        $this->daten->setter('transaction_hash', $this->calcTransactionHash());
    }

    public function setTransactionId(int $transaction_id): bool
    {
        return $this->daten->setter('transaction_id', $transaction_id);
    }

    public function setAssociatedTransactionId(?int $associated_transaction_id): bool
    {
        return $this->daten->setter('associated_transaction_id', $associated_transaction_id);
    }

    public function setAssociatedTransaction(BankTransaction $associated_bank_transaction): void
    {
        $this->associated_bank_transaction = $associated_bank_transaction;
    }

    public function setAccountId(int $account_id): bool
    {
        return $this->daten->setter('account_id', $account_id);
    }

    /**
     * BankTransaction::TRANSACTION_TYPE_*
     *
     * @param string $transaction_type
     * @return bool
     */
    public function setTransactionType(string $transaction_type): bool
    {
        return $this->daten->setter('transaction_type', $transaction_type);
    }

    public function setTransactionTypeExtern(string $transaction_type_extern): bool
    {
        return $this->daten->setter('transaction_type_extern', $transaction_type_extern);
    }

    public function setTransactionStatus(string $transaction_status): bool
    {
        return $this->daten->setter('transaction_status', $transaction_status);
    }

    public function setTransactionDate(DateObj $transaction_date): bool
    {
        return $this->daten->setter('transaction_date', $transaction_date->db());
    }


    public function setExternTransactionId(string $extern_transaction_id): bool
    {
        return $this->daten->setter('extern_transaction_id', $extern_transaction_id);
    }

    public function setAmount(float $amount): bool
    {
        return $this->daten->setter('amount', $amount);
    }

    public function setCurrencyCode(string $currency_code): bool
    {
        return $this->daten->setter('currency_code', $currency_code);
    }

    public function setSenderName(string $sender_name): bool
    {
        return $this->daten->setter('sender_name', $sender_name);
    }

    public function setTextReference1(string $text_reference_1): bool
    {
        return $this->daten->setter('text_reference_1', $text_reference_1);
    }

    public function setTextReference2(string $text_reference_2): bool
    {
        return $this->daten->setter('text_reference_2', $text_reference_2);
    }

    public function setExtra(array $extras): bool
    {
        return $this->daten->setter('extra', serialize($extras));
    }

    /**
     * @param string $key
     * @param string|int|float $value
     * @return bool
     */
    public function addExtra(string $key, $value): bool
    {
        $extras = $this->getExtras();

        $extras[$key] = $value;

        return $this->setExtra($extras);
    }

    public function setSettlementId(int $settlement_id): bool
    {
        return $this->daten->setter('settlement_id', $settlement_id);
    }


    public function setShopReferenz(string $shop_referenz): bool
    {
        return $this->daten->setter('shop_referenz', $shop_referenz);
    }

    public function setOrderId(?int $order_id): bool
    {
        return $this->daten->setter('order_id', $order_id);
    }

    public function setCustomerId(?int $customer_id): bool
    {
        return $this->daten->setter('customer_id', $customer_id);
    }

    public function setNotice(string $notice): bool
    {
        return $this->daten->setter('notice', $notice);
    }

    public function setBookingText(string $booking_text): bool
    {
        return $this->daten->setter('booking_text', $booking_text);
    }

    public function getBookingText(): string
    {
        return $this->daten->getter('booking_text');
    }


    /**
     * @return int transaction_id
     */
    public function getTransactionId(): int
    {
        return $this->daten->getter('transaction_id');
    }

    public function getAssociatedTransactionId(): ?int
    {
        return $this->daten->getter('associated_transaction_id');
    }

    /**
     * @return int account_id
     */
    public function getAccountId(): int
    {
        return (int)$this->daten->getter('account_id');
    }

    public function getTransactionType(): string
    {
        return $this->daten->getter('transaction_type');
    }

    public function getTransactionTypeExtern(): string
    {
        return $this->daten->getter('transaction_type_extern');
    }

    public function getTransactionStatus(): string
    {
        return $this->daten->getter('transaction_status');
    }

    public function getTransactionDate(): DateObj
    {
        return new DateObj($this->daten->getter('transaction_date'));
    }

    public function getExternTransactionId(): string
    {
        return $this->daten->getter('extern_transaction_id');
    }

    public function getAmount(): float
    {
        return $this->daten->getter('amount');
    }

    public function getCurrencyCode(): string
    {
        return $this->daten->getter('currency_code');
    }

    public function getSenderName(): string
    {
        return $this->daten->getter('sender_name');
    }

    public function getTextReference1(): string
    {
        return $this->daten->getter('text_reference_1');
    }

    public function getTextReference2(): string
    {
        return $this->daten->getter('text_reference_2');
    }


    public function hasExtra(string $key): bool
    {
        //@todo das ist in der form sehr ineffizient -> ggf. mal umstellen
        $extras = $this->getExtras();

        return array_key_exists($key, $extras);
    }

    /**
     * @return int|float|string
     */
    public function getExtra(string $key)
    {
        $extras = $this->getExtras();

        return $extras[$key] ?? null;
    }

    public function getExtras(): array
    {
        $daten = $this->daten->getter('extra');

        if ($daten) {
            $daten = unserialize($daten, ['allowed_classes' => false]);
        } else {
            $daten = [];
        }

        return $daten;
    }

    /**
     * @return int $settlement_id
     */
    public function getSettlementId(): int
    {
        return (int)$this->daten->getter('settlement_id');
    }

    /**
     * @return string shop_referenz
     */
    public function getShopReferenz(): string
    {
        return $this->daten->getter('shop_referenz') ?? '';
    }

    /**
     * @return null|int order_id
     */
    public function getOrderId(): ?int
    {
        return $this->daten->getter('order_id');
    }

    public function getCustomerId(): int
    {
        return (int)$this->daten->getter('customer_id');
    }

    public function getNotice(): string
    {
        return $this->daten->getter('notice');
    }

    public function setVatRate(?VatRate $vat_rate): bool
    {
        return $this->daten->setter('vat_rate_value', $vat_rate ? $vat_rate->getValue() : null);
    }

    public function getVatRate(): ?VatRate
    {
        $vat_rate_value = $this->daten->getter('vat_rate_value');

        if ($vat_rate_value === null) {
            return null;
        }

        return new VatRateSimple($vat_rate_value);
    }

    public function isVatRate(): bool
    {
        return $this->daten->getter('vat_rate_value') !== null;
    }


    public function getTags(): array
    {
        if ($this->daten->getter('tags')) {
            return explode(',', $this->daten->getter('tags'));
        }
        return [];
    }

    public function setTags(array $tags): bool
    {
        return $this->daten->setter('tags', implode(',', $tags));
    }

    public function isTag(string $tag): bool
    {
        return in_array($tag, $this->getTags());
    }

    public function addTag(string $tag): bool
    {
        $tags = $this->getTags();
        $tags[] = $tag;
        $tags = array_unique($tags);

        return $this->setTags($tags);
    }

    public function setPersonKontoNr(string $person_konto_nr): bool
    {
        return $this->daten->setter('person_konto_nr', $person_konto_nr);
    }


    public function getPersonKontoNr(): string
    {
        return $this->daten->getter('person_konto_nr');
    }

    public function setPostingKey(string $posting_key): bool
    {
        return $this->daten->setter('posting_key', $posting_key);
    }

    public function getPostingKey(): string
    {
        return $this->daten->getter('posting_key');
    }

    public function isClarificationList(): bool
    {
        return $this->daten->getter('clarification_list');
    }

    public function setClarificationList(bool $clarification_list): bool
    {
        return $this->daten->setter('clarification_list', $clarification_list ? 1 : 0);
    }


    public function setClassificatorName(string $classificator_name): bool
    {
        return $this->daten->setter('classificator_name', $classificator_name);
    }

    public function setClassificatorExtra(array $classificator_extra): bool
    {
        return $this->daten->setter('classificator_extra', json_encode($classificator_extra));
    }

    public function getClassificatorName(): string
    {
        return $this->daten->getter('classificator_name');
    }

    public function getClassificatorExtra(): array
    {
        $extra = $this->daten->getter('classificator_extra');

        if (!$extra) {
            return [];
        }

        return Json::decode($extra);
    }

    public function setBookerResult(TransactionBookerResult $result): bool
    {
        return $this->daten->setter('booker_result', $result->serialize());
    }

    public function getBookerResult(): TransactionBookerResult
    {
        $result = new TransactionBookerResult();
        $result->unserialize($this->daten->getter('booker_result'));
        return $result;
    }

    public function getBankSettlement(): ?BankSettlement
    {
        return $this->bank_settlement;
    }

    public function setBankSettlement(BankSettlement $bank_settlement): void
    {
        $this->bank_settlement = $bank_settlement;
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }

    /**
     * @throws InputException
     */
    public function validate(): void
    {
    }
}
