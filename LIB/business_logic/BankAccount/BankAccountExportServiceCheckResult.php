<?php

namespace wws\BankAccount;

use Exception;

class BankAccountExportServiceCheckResult extends Exception
{
    /**
     * @var array
     */
    private $messages = [];

    public function add(string $message): void
    {
        $this->messages[] = $message;
    }

    public function getMessages(): array
    {
        return $this->messages;
    }

    public function isError(): bool
    {
        return (bool)$this->messages;
    }
}
