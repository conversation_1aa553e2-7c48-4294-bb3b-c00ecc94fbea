<?php

namespace wws\BankAccount\EventHandler;

use wws\BankAccount\BankSettlementSummary;
use wws\BankAccount\Event\EventTransactionChanged;

class RecalcSettlementTransactionStateSummary
{
    /**
     * @var BankSettlementSummary
     */
    private $bank_settlement_summary;

    public function __construct(BankSettlementSummary $bank_settlement_summary)
    {
        $this->bank_settlement_summary = $bank_settlement_summary;
    }

    public function handle(EventTransactionChanged $event): void
    {
        $changes = $event->getEntityChanges();

        if (!$changes->isChange('transaction_status')) {
            return;
        }

        $settlement_id = $event->getBankTransaction()->getSettlementId();

        $this->bank_settlement_summary->updateByTansactionChange($settlement_id, $changes);
    }
}
