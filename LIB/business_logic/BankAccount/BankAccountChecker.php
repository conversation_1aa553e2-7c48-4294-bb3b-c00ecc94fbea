<?php

namespace wws\BankAccount;

use bqp\Date\DateObj;
use bqp\db\db_generic;

class BankAccountChecker
{
    /**
     * @var bool
     */
    private $only_workdays = false;

    /**
     * @var int
     */
    private $day_threshold = 0;


    /**
     * @var int|null
     */
    private $checker_day_interval_threshold = null;

    /**
     * @var db_generic
     */
    private $db;

    /**
     * @var int
     */
    private $account_id;


    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function setAccountId(int $account_id): void
    {
        $this->account_id = $account_id;
    }

    public function setOnlyWorkdays(bool $only_workdays): void
    {
        $this->only_workdays = $only_workdays;
    }

    /**
     * legt fest wieviele Tage in dem gesamten Zeitraum fehlen dürfen
     *
     * @param int $day_threshold
     */
    public function setDayThreshold(int $day_threshold): void
    {
        $this->day_threshold = $day_threshold;
    }

    /**
     * legt fest wie groß ein lücke maximal sein darf
     * @param int $checker_day_interval_threshold
     */
    public function setDayIntervalThreshold(int $checker_day_interval_threshold): void
    {
        $this->checker_day_interval_threshold = $checker_day_interval_threshold;
    }


    public function check(DateObj $start_date, DateObj $end_date): BankAccountCheckerResult
    {
        $rows = $this->getTransactionCounts($start_date, $end_date);

        $result = new BankAccountCheckerResult;
        $result->setDateRange($start_date, $end_date);

        $last_row_was_gap = false;
        foreach ($rows as $row) {
            if (($this->only_workdays && !$last_row_was_gap) && !$row['transaction_count']) {
                $date = new DateObj($row['datum']);
                if (!$date->isWorkingday(5)) {
                    continue;
                }
            }

            if (!$row['transaction_count']) {
                if ($last_row_was_gap === false) {
                    $result->addGapBlock();
                }
                $last_row_was_gap = true;
                $result->addGap($row['datum']);
            } else {
                $last_row_was_gap = false;
            }
        }

        if ($result->getTotalGaps() > $this->day_threshold) {
            $result->setAbnormal(true);
        }

        if ($this->checker_day_interval_threshold !== null) {
            foreach ($result->getGaps() as $gap) {
                if (count($gap) > $this->checker_day_interval_threshold) {
                    $result->setAbnormal(true);
                }
            }
        }

        return $result;
    }

    public function getTransactionCounts(DateObj $start_date, DateObj $end_date): array
    {
        $stmt = $this->db->prepare("
                SELECT
                    system_date_helper.datum,
                    transactions.transaction_count,
                    DAYOFWEEK(system_date_helper.datum)
                FROM
                    system_date_helper LEFT JOIN
                    (
                        SELECT
                            DATE(buchhaltung_bank_transactions.transaction_date) AS transaction_date,
                            COUNT(*) AS transaction_count
                        FROM
                            buchhaltung_bank_transactions
                        WHERE
                            buchhaltung_bank_transactions.account_id = :account_id AND
                            buchhaltung_bank_transactions.transaction_date BETWEEN :begin AND :end
                        GROUP BY
                            DATE(buchhaltung_bank_transactions.transaction_date)
                    ) AS transactions ON (system_date_helper.datum = transactions.transaction_date)
                WHERE
                    system_date_helper.datum BETWEEN :begin AND :end
        ");

        $rows = $stmt->execute([
            'account_id' => $this->account_id,
            'begin' => $start_date->db('begin'),
            'end' => $end_date->db('end')
        ]);

        return $rows->asArray();
    }
}
