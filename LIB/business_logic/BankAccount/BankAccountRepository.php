<?php

namespace wws\BankAccount;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;
use InvalidArgumentException;

class BankAccountRepository
{
    private $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function load(int $account_id): BankAccount
    {
        $accounts = $this->loadBySqlWhere("buchhaltung_bank_accounts.account_id = $account_id");

        if (!array_key_exists($account_id, $accounts)) {
            throw new SmartDataEntityNotFoundException($account_id);
        }

        return $accounts[$account_id];
    }

    public function assertAccountIdExists(int $account_id): void
    {
        try {
            $this->load($account_id);
        } catch (SmartDataEntityNotFoundException $e) {
            throw new InvalidArgumentException('account_id dosent exists');
        }
    }

    /**
     * @param string $sql_where
     * @return BankAccount[]
     */
    public function loadBySqlWhere(string $sql_where): array
    {
        $result = $this->db->query("
            SELECT
                buchhaltung_bank_accounts.account_id,
                buchhaltung_bank_accounts.account_name,
                buchhaltung_bank_accounts.kontonummer,
                buchhaltung_bank_accounts.konto_id,
                buchhaltung_bank_accounts.shop_id,
                buchhaltung_bank_accounts.checker_day_threshold,
                buchhaltung_bank_accounts.checker_only_workdays,
                buchhaltung_bank_accounts.export_type,
                buchhaltung_bank_accounts.export_description
            FROM
                buchhaltung_bank_accounts
            WHERE
                $sql_where
        ");

        $bank_accounts = [];

        foreach ($result as $row) {
            $bank_account = new BankAccount();
            $bank_account->getSmartDataObj()->loadDaten($row);

            $bank_accounts[$bank_account->getAccountId()] = $bank_account;
        }

        return $bank_accounts;
    }

    public function save(BankAccount $bank_account): int
    {
        $bank_account->validate();

        $smart_data = $bank_account->getSmartDataObj();

        if (!$smart_data->isChange()) {
            return $bank_account->getAccountId();
        }

        $values = $smart_data->getAsArray();

        $this->db->simpleInsertUpdate('buchhaltung_bank_accounts', $values, false);

        if ($smart_data->isNew()) {
            $smart_data->setterDirect('account_id', $this->db->insert_id());
        }

        return $bank_account->getAccountId();
    }

    public function getAccountNames(int $shop_id): array
    {
        return $this->db->query("
            SELECT
                buchhaltung_bank_accounts.account_id,
                buchhaltung_bank_accounts.account_name
            FROM
                buchhaltung_bank_accounts
            WHERE
                buchhaltung_bank_accounts.shop_id = $shop_id
        ")->asSingleArray('account_id');
    }

    public function getExportableAccountNames(int $shop_id): array
    {
        return $this->db->query("
            SELECT
                buchhaltung_bank_accounts.account_id,
                buchhaltung_bank_accounts.account_name
            FROM
                buchhaltung_bank_accounts
            WHERE
                buchhaltung_bank_accounts.shop_id = $shop_id AND
                buchhaltung_bank_accounts.export_type != ''
        ")->asSingleArray('account_id');
    }
}
