<?php

namespace wws\BankAccount;

use bqp\Date\DateObj;

class BankAccountCheckerResult
{
    private $gap_block = -1;
    private $gaps = [];

    private $is_abnormal = false;

    /**
     * @var DateObj
     */
    private $start_date;

    /**
     * @var DateObj
     */
    private $end_date;

    public function setDateRange(DateObj $start_date, DateObj $end_date): void
    {
        $this->start_date = $start_date;
        $this->end_date = $end_date;
    }

    public function addGap(string $date): void
    {
        $gap_block = $this->gap_block === -1 ? 0 : $this->gap_block;

        if (!isset($this->gaps[$gap_block])) {
            $this->gaps[$gap_block] = [];
        }

        $this->gaps[$gap_block][] = $date;
    }

    public function getGaps(): array
    {
        return $this->gaps;
    }

    public function isAbnormal(): bool
    {
        return $this->is_abnormal;
    }

    public function setAbnormal(bool $abnormal): void
    {
        $this->is_abnormal = $abnormal;
    }

    public function getAsText(): string
    {
        return "Es wurden mehrere Lücken in den Buchungen für den Zeitraum " . $this->start_date->format('d.m.Y') . " bis " . $this->end_date->format('d.m.Y') . " gefunden.\n" . $this->formatGaps();
    }

    private function formatGaps(): string
    {
        $text = '';

        $pos = 0;

        foreach ($this->gaps as $key => $gap_block) {
            $text .= ++$pos . '. Lücke mit ' . count($gap_block) . ' Tagen: ';

            if (count($gap_block) > 4) {
                $text .= $gap_block[0] . ' bis ' . $gap_block[count($gap_block) - 1];
            } else {
                $text .= implode(', ', $gap_block);
            }

            $text .= "\n";
        }

        return $text;
    }

    public function addGapBlock(): void
    {
        $this->gap_block++;
    }

    public function getTotalGaps(): int
    {
        return array_sum(array_map('count', $this->gaps));
    }

    public function isGap(): bool
    {
        return (bool)$this->gaps;
    }
}
