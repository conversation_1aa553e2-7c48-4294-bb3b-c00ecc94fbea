<?php

namespace wws\BankAccount\Event;

use bqp\Event\Event;
use bqp\Model\EntityChanges;
use wws\BankAccount\BankTransaction;

class EventTransactionChanged extends Event
{
    /**
     * @var BankTransaction
     */
    private $bank_transaction;

    /**
     * @var EntityChanges
     */
    private $entity_changes;

    public function __construct(BankTransaction $bank_transaction, EntityChanges $entity_changes)
    {
        $this->bank_transaction = $bank_transaction;
        $this->entity_changes = $entity_changes;
    }

    public function getEntityChanges(): EntityChanges
    {
        return $this->entity_changes;
    }

    public function getBankTransaction(): BankTransaction
    {
        return $this->bank_transaction;
    }
}
