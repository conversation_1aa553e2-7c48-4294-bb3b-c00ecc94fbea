<?php

namespace wws\BankAccount\Event;

use bqp\Event\Event;
use wws\BankAccount\BankTransaction;

class EventTransactionBooked extends Event
{
    /**
     * @var BankTransaction
     */
    private $bank_transaction;

    public function __construct(BankTransaction $bank_transaction)
    {
        $this->bank_transaction = $bank_transaction;
    }

    public function getBankTransaction(): BankTransaction
    {
        return $this->bank_transaction;
    }
}
