<?php

namespace wws\BankAccount;

use bqp\db\db_generic;
use bqp\Utils\StringUtils;
use db;
use wws\buchhaltung\AccountingConsts;

class bank_repository
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public static function getTransactionStatusNames(): array
    {
        return [
            BankTransaction::TRANSACTION_STATUS_UNKNOWN => 'neu',
            BankTransaction::TRANSACTION_STATUS_BOOKED => 'gebucht',
            BankTransaction::TRANSACTION_STATUS_FAILED => 'fehlgeschlagen',
            BankTransaction::TRANSACTION_STATUS_FILTERED => 'ignoriert',
        ];
    }

    public static function getAccountNames(int $shop_id = null): array
    {
        $filter = '';
        if ($shop_id) {
            $filter = ' WHERE buchhaltung_bank_accounts.shop_id = "' . $shop_id . '"';
        }

        return db::getInstance()->query("
            SELECT
                buchhaltung_bank_accounts.account_id,
                buchhaltung_bank_accounts.account_name
            FROM
                buchhaltung_bank_accounts
            $filter
        ")->asSingleArray('account_id');
    }


    public static function getAccountNamesForBuchhaltung(): array
    {
        return db::getInstance()->query("
            SELECT
                buchhaltung_bank_accounts.account_id,
                buchhaltung_bank_accounts.account_name
            FROM
                buchhaltung_bank_accounts
            WHERE
                buchhaltung_bank_accounts.account_id IN (" . AccountingConsts::BANK_ACCOUNT_COMMERZ . ", " . AccountingConsts::BANK_ACCOUNT_COMMERZ_K11 . ")
        ")->asSingleArray('account_id');
    }


    public static function getKontoNames(): array
    {
        return db::getInstance()->query("
            SELECT
                buchhaltung_bank_accounts.konto_id,
                buchhaltung_bank_accounts.account_name
            FROM
                buchhaltung_bank_accounts
            WHERE
                buchhaltung_bank_accounts.konto_id IS NOT NULL
        ")->asSingleArray('konto_id');
    }

    /**
     * gibt die $account_id für die bank_settlements zurück
     * @param string $kontonummer
     * @return int|null
     */
    public static function searchAccountIdByKontonummer(string $kontonummer): ?int
    {
        return db::getInstance()->fieldQuery("
            SELECT
                buchhaltung_bank_accounts.account_id
            FROM
                buchhaltung_bank_accounts
            WHERE
                buchhaltung_bank_accounts.kontonummer = '" . db::getInstance()->escape($kontonummer) . "'
        ");
    }

    /**
     * gibt die "konto_id" für buchhaltung_konto_transaktionen zurück
     * @param string $kontonummer
     * @return int|null
     */
    public static function searchKontoIdByKontonummer(string $kontonummer): ?int
    {
        return db::getInstance()->fieldQuery("
            SELECT
                buchhaltung_bank_accounts.konto_id
            FROM
                buchhaltung_bank_accounts
            WHERE
                buchhaltung_bank_accounts.kontonummer = '" . db::getInstance()->escape($kontonummer) . "'
        ");
    }


    public static function getAccountName(int $account_id): string
    {
        $accounts = self::getAccountNames();

        return $accounts[$account_id];
    }

    public static function getAccountFilenameSlug(int $account_id): string
    {
        return StringUtils::slug(self::getAccountName($account_id));
    }
}
