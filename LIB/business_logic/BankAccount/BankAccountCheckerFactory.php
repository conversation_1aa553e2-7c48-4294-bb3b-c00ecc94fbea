<?php

namespace wws\BankAccount;

use bqp\db\db_generic;

class BankAccountCheckerFactory
{
    private $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function getBankAccountChecker(int $account_id): BankAccountChecker
    {
        $row = $this->db->singleQuery('
            SELECT
                buchhaltung_bank_accounts.account_id,
                buchhaltung_bank_accounts.checker_day_threshold,
                buchhaltung_bank_accounts.checker_day_interval_threshold,
                buchhaltung_bank_accounts.checker_only_workdays
            FROM
                buchhaltung_bank_accounts
            WHERE
                buchhaltung_bank_accounts.account_id = ' . $account_id . '
        ');

        $account_checker = new BankAccountChecker($this->db);
        $account_checker->setAccountId($row['account_id']);
        if ($row['checker_day_threshold'] !== null) {
            $account_checker->setDayThreshold($row['checker_day_threshold']);
        } else {
            $account_checker->setDayThreshold(100000);
        }

        if ($row['checker_day_interval_threshold'] !== null) {
            $account_checker->setDayIntervalThreshold($row['checker_day_interval_threshold']);
        }

        if ($row['checker_only_workdays']) {
            $account_checker->setOnlyWorkdays((bool)$row['checker_only_workdays']);
        }

        return $account_checker;
    }
}
