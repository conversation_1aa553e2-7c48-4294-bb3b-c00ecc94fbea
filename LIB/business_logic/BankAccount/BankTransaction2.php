<?php

namespace wws\BankAccount;

use bqp\String\Base62;

/**
 * BankTransaction mit einem erweitereten hash algo
 *
 * Class BankTransaction2
 * @package wws\BankAccount
 */
class BankTransaction2 extends BankTransaction
{
    public function calcTransactionHash(): string
    {
        $data = number_format($this->getAmount(), 2, '.', '');
        $data .= $this->getTransactionDate()->format('U');
        $data .= $this->getExternTransactionId();
        $data .= $this->getSenderName();
        $data .= $this->getTextReference1();
        $data .= $this->getTextReference2();

        $extra = $this->getExtras();
        if (isset($extra['valuta'])) { //bank -> ohne valuta sind teilweise überweisungen nicht eindeutig
            $data .= $extra['valuta'];
        }

        return '{2}' . substr(Base62::encode(md5($data, true) . sha1($data, true)), 0, 29);
    }
}
