<?php

namespace wws\BankAccount;

use bqp\db\db_generic;
use bqp\Model\EntityChanges;

class BankSettlementSummary
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function recalcForSettlementId(int $settlement_id): void
    {
        $this->db->query("
            UPDATE
                buchhaltung_bank_settlements INNER JOIN
                (
                    SELECT
                        buchhaltung_bank_settlements.settlement_id,
                        COUNT(DISTINCT buchhaltung_bank_transactions.transaction_id) AS quantity,
                        SUM(IF(buchhaltung_bank_transactions.transaction_status != '" . BankTransaction::TRANSACTION_STATUS_UNKNOWN . "', 1, 0)) AS quantity_processed,
                        SUM(IF(buchhaltung_bank_transactions.transaction_status = '" . BankTransaction::TRANSACTION_STATUS_BOOKED . "', 1, 0)) AS quantity_booked,
                        ROUND(SUM(buchhaltung_bank_transactions.amount), 2) AS amount
                    FROM
                        buchhaltung_bank_settlements LEFT JOIN
                        buchhaltung_bank_transactions ON (buchhaltung_bank_settlements.settlement_id = buchhaltung_bank_transactions.settlement_id)
                    WHERE
                        buchhaltung_bank_settlements.settlement_id = " . $settlement_id . "
                    GROUP BY
                        buchhaltung_bank_settlements.settlement_id
                ) AS quantities ON (buchhaltung_bank_settlements.settlement_id = quantities.settlement_id)
            SET
                buchhaltung_bank_settlements.amount = quantities.amount,
                buchhaltung_bank_settlements.quantity = quantities.quantity,
                buchhaltung_bank_settlements.quantity_processed = quantities.quantity_processed,
                buchhaltung_bank_settlements.quantity_booked = quantities.quantity_booked
        ");
    }

    public function updateByTansactionChange(int $settlement_id, EntityChanges $entity_changes): void
    {
        $change = $entity_changes->getChange('transaction_status');

        if (!$change) {
            return;
        }

        $status = $change['new_value'];
        $old_status = $change['old_value'];

        $quantity_processed = 0;
        $quantity_booked = 0;

        if (($old_status === BankTransaction::TRANSACTION_STATUS_UNKNOWN || $old_status === null) && $status !== BankTransaction::TRANSACTION_STATUS_UNKNOWN) {
            $quantity_processed = 1;
        }

        if (($old_status !== BankTransaction::TRANSACTION_STATUS_UNKNOWN && $old_status !== null) && $status === BankTransaction::TRANSACTION_STATUS_UNKNOWN) {
            $quantity_processed = -1;
        }

        if ($status === BankTransaction::TRANSACTION_STATUS_BOOKED) {
            $quantity_booked = 1;
        }

        if ($old_status === BankTransaction::TRANSACTION_STATUS_BOOKED) {
            $quantity_booked = -1;
        }

        if ($quantity_processed || $quantity_booked) {
            $this->db->query("
                UPDATE
                    buchhaltung_bank_settlements
                SET
                    buchhaltung_bank_settlements.quantity_processed = buchhaltung_bank_settlements.quantity_processed + " . $quantity_processed . ",
                    buchhaltung_bank_settlements.quantity_booked = buchhaltung_bank_settlements.quantity_booked + " . $quantity_booked . "
                WHERE
                    buchhaltung_bank_settlements.settlement_id = " . $settlement_id . "
            ");
        }
    }
}
