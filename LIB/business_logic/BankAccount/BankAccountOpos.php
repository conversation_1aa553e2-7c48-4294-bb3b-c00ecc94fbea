<?php

namespace wws\BankAccount;

use bqp\form\OptionGroup;

/**
 * Class BankAccountOpos
 * @package wws\BankAccount
 *
 * Paket aus Hilfsfunktionen um OPOS über BankTransactions und buchhaltung_rechnung abzubilden
 */
class BankAccountOpos
{

    //private function

    /**
     * Für die Dummy-Ausgleichbuchungen die möglichen Transationstypen, damit man das auch auswerten kann.
     * @return string[]
     * @todo naming? wird als transaction_type_extern
     */
    public function getTransactionTypesExternNames(): array
    {
        $options = $this->getTransactionTypesExternNamesAsOptions();

        $names = [];

        foreach ($options as $key => $value) {
            if ($value instanceof OptionGroup) {
                $names = array_merge($value->getOptions());
            } else {
                $names[$key] = $value;
            }
        }

        return $names;
    }

    public function getTransactionTypesExternNamesAsOptions(): array
    {
        return [
            new OptionGroup('<PERSON>uviel', [
                'Überzahlung' => 'Überzahlung',
                'Doppelzahlung' => 'Doppelzahlung',
                'fehlende Rückzahlung' => 'fehlende Rückzahlung',
                'Rechnung 2020' => 'Rechnung 2020',
            ]),
            new OptionGroup('Zuwenig', [
                'Mahnung' => 'Mahnung',
                'Gutschrift 2020' => 'Gutschrift 2020'
            ]),
            new OptionGroup('fehlende Buchungen', [
                'Saldo' => 'Saldo',
                'fehlende Abrechnung' => 'fehlende Abrechnung',
            ]),
            'Sonstiges' => 'Sonstiges',
            new OptionGroup('System', [
                'initial Saldo' => 'initial Saldo'
            ])
        ];
    }
}