<?php

namespace wws\BankAccount;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\FileAttachment\FileAttachment;
use bqp\FileAttachment\FileAttachments;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;

class BankSettlementRepository
{

    const CHECK_MODUS_NAME = 'name';
    const CHECK_MODUS_REFERENCE = 'reference';
    const CHECK_MODUS_SUMS = 'sums';

    /**
     * @var BankSettlementFileAttachmentManager
     */
    private $file_attachment_manager;

    private db_generic $db;

    /**
     * @var BankTransactionRepository
     */
    private $transaction_repository;

    public function __construct(
        db_generic $db,
        BankSettlementFileAttachmentManager $file_attachment_manger,
        BankTransactionRepository $transaction_repository
    ) {
        $this->db = $db;

        $this->file_attachment_manager = $file_attachment_manger;

        $this->transaction_repository = $transaction_repository;
    }

    /**
     * @param int $settlement_id
     * @return BankSettlement
     */
    public function loadBankSettlement(int $settlement_id): BankSettlement
    {
        return $this->load($settlement_id);
    }

    /**
     * @param BankSettlement $bank_settlement
     * @return int $bank_settlement_id
     */
    public function save(BankSettlement $bank_settlement): int
    {
        $this->file_attachment_manager->process($bank_settlement);

        $data = $bank_settlement->getSmartDataObj();

        if (!$data->isChange()) {
            return $bank_settlement->getSettlementId();
        }

        if (!$data->getter('settlement_date_added')) {
            $bank_settlement->setSettlementDataAdded(new DateObj());
        }

        $changes = $data->getChanges();
        $sql = [];

        foreach ($changes as $key => $value) {
            $sql[] = "buchhaltung_bank_settlements.$key = '" . $this->db->escape($value) . "'";
        }

        $db_trx = false;
        if (!$this->db->isTransactionActive()) {
            $this->db->begin();
            $db_trx = true;
        }

        switch ($data->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $this->db->query("
                    INSERT INTO
                        buchhaltung_bank_settlements
                    SET
                        " . implode(',', $sql) . "
                ");

                $data->setterDirect('settlement_id', $this->db->insert_id());

                break;
            case SmartDataObj::STATUS_UPDATE:
                $this->db->query("
                    UPDATE
                        buchhaltung_bank_settlements
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        buchhaltung_bank_settlements.settlement_id = '" . $bank_settlement->getSettlementId() . "'
                ");
                break;
        }

        $data->setSaved();

        foreach ($bank_settlement->_getBankTransactions() as $bank_transaction) {
            $bank_transaction->save();
        }

        if ($db_trx) {
            $this->db->commit();
        }

        return $bank_settlement->getSettlementId();
    }

    public function load(int $settlement_id): BankSettlement
    {
        $daten = $this->db->singleQuery("
            SELECT
                buchhaltung_bank_settlements.settlement_id,
                buchhaltung_bank_settlements.settlement_date_added,
                buchhaltung_bank_settlements.account_id,
                buchhaltung_bank_settlements.settlement_reference,
                buchhaltung_bank_settlements.settlement_name,
                buchhaltung_bank_settlements.settlement_date,
                buchhaltung_bank_settlements.settlement_attachments,
                buchhaltung_bank_settlements.amount,
                buchhaltung_bank_settlements.quantity,
                buchhaltung_bank_settlements.settlement_exported
            FROM
                buchhaltung_bank_settlements
            WHERE
                buchhaltung_bank_settlements.settlement_id = " . $settlement_id . "
        ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException('bank settlement not found (' . $settlement_id . ')');
        }

        $bank_settlement = new BankSettlement();
        $bank_settlement->getSmartDataObj()->loadDaten($daten);

        return $bank_settlement;
    }

    public function newBankSettlement(): BankSettlement
    {
        return new BankSettlement();
    }

    /**
     * @param int $settlement_id
     */
    public function deleteBankSettlement(int $settlement_id): void
    {
        //@todo attachments

        $this->db->query("
            UPDATE
                buchhaltung_bank_transactions INNER JOIN
                buchhaltung_rueckzahlung ON (buchhaltung_bank_transactions.transaction_id = buchhaltung_rueckzahlung.transaction_id)
            SET
                buchhaltung_rueckzahlung.transaction_id = NULL
            WHERE
                buchhaltung_bank_transactions.settlement_id = " . $settlement_id . " 
        ");

        $this->db->query("
            DELETE FROM
                buchhaltung_bank_transactions
            WHERE
                buchhaltung_bank_transactions.settlement_id = " . $settlement_id . " 
        ");

        $this->db->query("
            DELETE FROM
                buchhaltung_bank_settlements
            WHERE
                buchhaltung_bank_settlements.settlement_id = " . $settlement_id . " 
        ");
    }


    public function existsSettlementByName(int $account_id, string $settlement_name): bool
    {
        $settlement_id = $this->db->fieldQuery("
            SELECT
                buchhaltung_bank_settlements.settlement_id
            FROM
                buchhaltung_bank_settlements
            WHERE
                buchhaltung_bank_settlements.account_id = " . $account_id . " AND
                buchhaltung_bank_settlements.settlement_name = '" . $this->db->escape($settlement_name) . "'
        ");

        return (bool)$settlement_id;
    }

    public function existsSettlementByReference(int $account_id, string $settlement_reference): bool
    {
        $settlement_id = $this->db->fieldQuery("
            SELECT
                buchhaltung_bank_settlements.settlement_id
            FROM
                buchhaltung_bank_settlements
            WHERE
                buchhaltung_bank_settlements.account_id = " . $account_id . " AND
                buchhaltung_bank_settlements.settlement_reference = '" . $this->db->escape($settlement_reference) . "'
        ");

        return (bool)$settlement_id;
    }

    /**
     * @param int $account_id
     * @param string $settlement_reference
     * @return BankSettlement
     * @throws SmartDataEntityNotFoundException
     */
    public function getSettlementByReference(int $account_id, string $settlement_reference): BankSettlement
    {
        $settlement_id = $this->db->fieldQuery("
            SELECT
                buchhaltung_bank_settlements.settlement_id
            FROM
                buchhaltung_bank_settlements
            WHERE
                buchhaltung_bank_settlements.account_id = " . $account_id . " AND
                buchhaltung_bank_settlements.settlement_reference = '" . $this->db->escape($settlement_reference) . "'
        ");

        if (!$settlement_id) {
            throw new SmartDataEntityNotFoundException('bank settlement not found (' . $settlement_reference . ')');
        }

        return $this->load($settlement_id);
    }

    /**
     * gibt die $settlement_id[] für mögliche doppelte abrechnungen zurück
     *
     * @param BankSettlement $settlement
     * @param string $modus
     * @return array
     * @throws DevException
     */
    public function checkForDuplicateSettlement(BankSettlement $settlement, $modus = self::CHECK_MODUS_NAME): array
    {
        switch ($modus) {
            case self::CHECK_MODUS_NAME:
                return $this->db->query("
                    SELECT
                        buchhaltung_bank_settlements.settlement_id
                    FROM
                        buchhaltung_bank_settlements
                    WHERE
                        buchhaltung_bank_settlements.account_id = '" . (int)$settlement->getAccountId() . "' AND
                        buchhaltung_bank_settlements.settlement_name = '" . $this->db->escape($settlement->getSettlementName()) . "' AND
                        buchhaltung_bank_settlements.settlement_id != '" . (int)$settlement->getSettlementId() . "'
                ")->asSingleArray();
            case self::CHECK_MODUS_REFERENCE:
                return $this->db->query("
                    SELECT
                        buchhaltung_bank_settlements.settlement_id
                    FROM
                        buchhaltung_bank_settlements
                    WHERE
                        buchhaltung_bank_settlements.account_id = '" . (int)$settlement->getAccountId() . "' AND
                        buchhaltung_bank_settlements.settlement_reference = '" . $this->db->escape($settlement->getSettlementReference()) . "' AND
                        buchhaltung_bank_settlements.settlement_id != '" . (int)$settlement->getSettlementId() . "'
                ")->asSingleArray();
            case self::CHECK_MODUS_SUMS:
            default:
                throw new DevException('modus not implemented');
        }
    }

    public function viewExtendAttachments(FileAttachments $attachments): void
    {
        foreach ($attachments as $attachment) {
            $url = $this->file_attachment_manager->getAttachmentDownloadUrl($attachment);
            $attachment->setViewValue('url', $url);
        }
    }

    public function getAttachmentContent(FileAttachment $attachment): string
    {
        return $this->file_attachment_manager->getContent($attachment);
    }


    /**
     * @param int $settlement_id
     * @return BankTransaction[]
     */
    public function loadSettlementTransactions(int $settlement_id): array
    {
        return $this->transaction_repository->searchBySqlWhere("buchhaltung_bank_transactions.settlement_id = $settlement_id");
    }

    public function transactionExistsInOtherSettlement(BankTransaction $transaction): bool
    {
        $hash = $transaction->calcTransactionHash();

        $transaction_id = $this->db->fieldQuery("
            SELECT
                buchhaltung_bank_transactions.transaction_id
            FROM
                buchhaltung_bank_transactions
            WHERE
                buchhaltung_bank_transactions.account_id = '" . (int)$transaction->getAccountId() . "' AND
                buchhaltung_bank_transactions.transaction_hash = '" . $hash . "'
        ");

        return (bool)$transaction_id;
    }
}
