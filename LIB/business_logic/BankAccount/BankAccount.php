<?php

namespace wws\BankAccount;

use bqp\Exceptions\InputException;
use bqp\Model\EntityChanges;
use bqp\Model\Model;
use bqp\Model\SmartDataObj;
use wws\buchhaltung\AccountingConsts;

class BankAccount implements Model
{
    private SmartDataObj $data;

    public function __construct()
    {
        $this->data = new SmartDataObj($this);
    }


    /**
     * @param int $account_id
     * @return bool
     */
    public function setAccountId(int $account_id): bool
    {
        return $this->data->setter('account_id', $account_id);
    }

    /**
     * @param string $account_name
     * @return boolean
     */
    public function setAccountName(string $account_name): bool
    {
        return $this->data->setter('account_name', $account_name);
    }

    /**
     * @param int $checker_day_threshold
     * @return bool
     */
    public function setCheckerDayThreshold(int $checker_day_threshold): bool
    {
        return $this->data->setter('checker_day_threshold', $checker_day_threshold);
    }

    /**
     * @param bool $checker_only_workdays
     * @return bool
     */
    public function setCheckerOnlyWorkdays(bool $checker_only_workdays): bool
    {
        return $this->data->setter('checker_only_workdays', $checker_only_workdays ? 1 : 0);
    }

    /**
     * @param string $export_description
     * @return boolean
     */
    public function setExportDescription(string $export_description): bool
    {
        return $this->data->setter('export_description', $export_description);
    }

    /**
     * @param string $export_type
     * @return boolean
     */
    public function setExportType(string $export_type): bool
    {
        return $this->data->setter('export_type', $export_type);
    }

    /**
     * @param int $konto_id
     * @return bool
     */
    public function setKontoId(int $konto_id): bool
    {
        return $this->data->setter('konto_id', $konto_id);
    }

    /**
     * @param string $kontonummer
     * @return boolean
     */
    public function setKontonummer(string $kontonummer): bool
    {
        return $this->data->setter('kontonummer', $kontonummer);
    }

    /**
     * @param int $shop_id
     * @return bool
     */
    public function setShopId(int $shop_id): bool
    {
        return $this->data->setter('shop_id', $shop_id);
    }


    /**
     * @return int account_id
     */
    public function getAccountId(): int
    {
        return (int)$this->data->getter('account_id');
    }

    /**
     * @return string account_name
     */
    public function getAccountName(): string
    {
        return $this->data->getter('account_name');
    }

    /**
     * @return string checker_day_threshold
     */
    public function getCheckerDayThreshold(): string
    {
        return $this->data->getter('checker_day_threshold');
    }

    /**
     * @return string checker_only_workdays
     */
    public function getCheckerOnlyWorkdays(): string
    {
        return $this->data->getter('checker_only_workdays');
    }

    /**
     * @return string export_description
     */
    public function getExportDescription(): string
    {
        return $this->data->getter('export_description');
    }

    /**
     * @return string export_type
     */
    public function getExportType(): string
    {
        return $this->data->getter('export_type');
    }

    /**
     * @return int konto_id
     */
    public function getKontoId(): int
    {
        return (int)$this->data->getter('konto_id');
    }

    /**
     * @return string kontonummer
     */
    public function getKontonummer(): string
    {
        return $this->data->getter('kontonummer');
    }

    /**
     * @return int shop_id
     */
    public function getShopId(): int
    {
        return (int)$this->data->getter('shop_id');
    }


    public function exportPerSettlement(): bool
    {
        return in_array($this->getAccountId(), [
            AccountingConsts::BANK_ACCOUNT_KREDITKARTE,
            AccountingConsts::BANK_ACCOUNT_KREDITKARTE_K11,
            AccountingConsts::BANK_ACCOUNT_CHECK24
        ]);
    }


    /**
     * @return SmartDataObj
     */
    public function getSmartDataObj(): SmartDataObj
    {
        return $this->data;
    }

    /**
     * @return EntityChanges
     */
    public function getAllChanges(): EntityChanges
    {
        $changes = new EntityChanges();
        $changes->addBySmartDataObj($this->data);

        return $changes;
    }

    /**
     * @throws InputException
     */
    public function validate(): void
    {
    }
}
