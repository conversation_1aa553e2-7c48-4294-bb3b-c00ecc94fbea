<?php

namespace wws\Mails;

use bqp\Utils\StringUtils;
use env;
use wws\Country\CountryConst;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderItem;
use wws\Product\ProductRepositoryLegacy;

class MailHelperOrder
{
    private Order $order;


    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * gibt die Auftragspositionsnamen als Aufzählung zurück Produkt 1, Produkt 2, Produkt 3 ...
     *
     * @return string
     */
    public function getListSmall(): string
    {
        $product_names = [];

        foreach ($this->order->getOrderItemsCustomer() as $order_item) {
            $product_names[] = $order_item->getProductName();
        }

        return trim(implode(', ', $product_names), "\t\n ,");
    }

    /**
     * gibt die Auftragspositionen als Liste zurück mit einer Zeile pro Position und der Gesamtsumme
     *
     * 1 x Produkt 1  25,95€
     * 2 x Produkt 2  10,00€
     * ---------------------
     * Gesamtpreis inkl. Mwst: 60,00€
     *
     * @return string
     */
    public function getListSimple(): string
    {
        $list = '';

        foreach ($this->order->getOrderItemsCustomer() as $order_item) {
            $price = $order_item->getVkSummeAutoMwst();
            $list .= sprintf(
                "  %s x %s   %s\n",
                $order_item->getQuantity(),
                $order_item->getProductName(),
                StringUtils::formatPrice($price)
            );
        }

        return $list . $this->getFormattedSummary();
    }

    /**
     * gibt die Auftragspositionen als detaillierte Liste zurück
     * 1. Position
     *   Artikel: Produkt 1
     *   Anzahl: 10
     *   Preis:
     *   Lieferzeit:
     * 2. Position
     *   ....
     * ---------------------
     * Gesamtpreis inkl. Mwst: 60,00€
     *
     * @return string
     */
    public function getListExtended(): string
    {
        $list = '';

        foreach ($this->order->getOrderItemsCustomer() as $order_item) {
            $price = $order_item->getVkSummeAutoMwst();

            $list .= $order_item->getPos() . ". Position\n";
            $list .= sprintf("  Artikel: %s\n", $order_item->getProductName());
            $list .= sprintf("  Anzahl: %s\n", $order_item->getQuantity());
            $list .= sprintf("  Preis: %s\n", StringUtils::formatPrice($price));

            $liefertermin = $this->getLieferterminAsText($this->order, $order_item);
            if ($liefertermin) {
                $list .= sprintf("  Lieferzeit: %s\n", $liefertermin);
            }

            $list .= "\n";
        }

        return $list . $this->getFormattedSummary();
    }

    private function getLieferterminAsText(Order $order, OrderItem $order_item): ?string
    {
        if ($order_item->getTyp() !== OrderConst::WARENKORB_TYP_PRODUKT) {
            return null;
        }

        //unklar -> problem wird wahrscheinlich aswo über unser Lager sein. gibts auch schon eine Sonderbehandlung in: \wws\Shopware\Order\OrderImporter::fixAswoDeliveryTimeForAtOrders()
        if (env::isK11() && $this->order->getLieferAddress()->getCountryId() === CountryConst::COUNTRY_ID_AT) {
            return null;
        }

        if ($order_item->getLieferterminText()) {
            return $order_item->getLieferterminText();
        }

        if ($order_item->getLieferbaranzeige()) {
            return ProductRepositoryLegacy::getLieferbaranzeigeName($order->getShopId(), $order_item->getLieferbaranzeige());
        }

        return null;
    }

    /**
     * @return string
     */
    private function getFormattedSummary(): string
    {
        $summary_format = "  -----------------------\nGesamtpreis %s Mwst: %s\n";

        $vat_string = 'inkl.';
        if ($this->order->getTaxStatus() !== OrderConst::TAX_STATUS_NORMAL) {
            $vat_string = 'exkl.';
        }

        $invoice_amount = StringUtils::formatPrice($this->order->getRechnungsBetrag());

        return sprintf($summary_format, $vat_string, $invoice_amount);
    }
}
