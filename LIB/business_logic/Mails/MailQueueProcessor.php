<?php

namespace wws\Mails;

use bqp\db\db_generic;
use bqp\Mail\MimeMail;
use bqp\Utils\MailUtils;
use config;
use Exception;
use League\Flysystem\Filesystem;
use service_loader;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Exception\RfcComplianceException;
use wws\Mails\MailSearcher\MailSearcher;
use wws\Mails\MailSearcher\MailSearcherWws;

class MailQueueProcessor
{
    private Filesystem $filesystem;
    private db_generic $db;
    public bool $debug = false;

    public function setFilesystem(Filesystem $filesystem): void
    {
        $this->filesystem = $filesystem;
    }

    /**
     * @param db_generic $db
     */
    public function setDb(db_generic $db): void
    {
        $this->db = $db;
    }

    /**
     * @param int $sleep
     * @param int $timeout
     */
    public function pollMails($sleep = 10, $timeout = 3600): void
    {
        $start = time();
        do {
            $this->processMails();
            sleep($sleep);
        } while (time() - $start < $timeout);
    }


    /**
     * Verarbeetet alle Mails im Queue Verzeichnis und löscht diese
     */
    public function processMails(): void
    {
        foreach ($this->filesystem->listContents() as $file) {
            if ($file['extension'] !== 'eml') {
                continue;
            }

            $raw_mail = $this->filesystem->read($file['path']);


            if ($this->debug) {
                $debug_dest = config::system('temp_dir') . '/mail_queue_debug/' . $file['basename'];
                file_put_contents($debug_dest, $raw_mail);
            }

            try {
                $mail = new MimeMail($raw_mail);

                $this->processMail($mail, $raw_mail);
            } catch (RfcComplianceException $e) {
                //bullshit mails, da stimmt nicht mal der Absender...
            }

            $this->filesystem->delete($file['path']);
        }
    }


    public function processMail(MimeMail $mail, $rawMail): void
    {
        if ($mail['sender_email'] === '<EMAIL>') { //krempl versandbestätigung
            $path = config::system('import_dir') . '/krempl/boe_krempl_de/';

            file_put_contents($path . date('YmdHis') . '_' . rand(100000, 999999) . '.mime', $rawMail);
        }

        if (
            strpos($mail['sender_email'], 'manolya.de') !== false &&
            (
                /*strpos($mail->getBetreff(), 'Rechnung') === 0 ||*/
                strpos($mail->getBetreff(), 'Lieferschein') === 0
            )
        ) { //manolya verstandbestätigung
            $path = config::system('import_dir') . '/manolya/versandmails/';

            file_put_contents($path . date('YmdHis') . '_' . rand(100000, 999999) . '.eml', $rawMail);
        }

        if (
            (
                strpos($mail['sender_email'], '@maxcom.de') ||
                strpos($mail['sender_email'], '@maxcom-dresden.de')
            ) &&
            strpos($mail->getBetreff(), 'Versand') === 0
        ) { //maxcom verstandbestätigung
            $path = config::system('import_dir') . '/maxcom/versandmails/';

            file_put_contents($path . date('YmdHis') . '_' . rand(100000, 999999) . '.eml', $rawMail);
        }

        if (strpos($mail['sender_email'], '@gaefgen.de') && strpos($mail->getBetreff(),
                'Auftragsbest') !== false) { //gäfgen auftragsbestätigung
            $path = config::system('import_dir') . '/gaefgen/abs/';
            file_put_contents($path . date('YmdHis') . '_' . rand(100000, 999999) . '.eml', $rawMail);
        }

        if ($mail['sender_email'] === '<EMAIL>' && strpos($mail->getBetreff(),
                'EK/servicegroup eG / Rechnung') === 0) { //
            $path = config::system('import_dir') . '/files_to_print/mails/';

            file_put_contents($path . date('YmdHis') . '_ekbi_rechnung_' . rand(100000, 999999) . '.eml', $rawMail);
        }

        if (strpos($mail['sender_email'], '@termikel.de') && strpos($mail->getBetreff(),
                'Warenverf') !== false) { //termikel verfügbarkeiten
            $path = config::system('import_dir') . '/termikel/';

            file_put_contents($path . date('YmdHis') . '_termikel_bestand_' . rand(100000, 999999) . '.eml', $rawMail);
        }

        //$mail->setSearchModus(wws_mime_mail::SEARCH_MODUS_WITHOUT_MAIL);

        $searcher = new MailSearcherWws();
        $searcher->setByMimeMail($mail);

        $shop_id = $this->findShopIdByMail($mail);
        if ($shop_id) {
            $searcher->setShopId($shop_id);
        }

        //ausgehende mails
        if (in_array(strtolower($mail['sender_email']), config::mail_archiver('mail_send'))) {
            if (!MailUtils::isMailAddressMatching($mail['empfaenger_email'], config::mail_archiver('do_not_archiv'))) {
                $searcher->setSearchModus(MailSearcher::SEARCH_MODUS_RECIVER_MAIL);
                $result = $searcher->search();

                if ($result->getCustomerId()) {
                    MailArchivRepository::addMimeMailToCustomer($mail, $result->getCustomerId(), $result->getOrderId());
                }
            }
        }

        //eingehende mails
        if (in_array(strtolower($mail['empfaenger_email']), config::mail_archiver('mail_recive'))) {
            if (!MailUtils::isMailAddressMatching($mail['sender_email'], config::mail_archiver('do_not_archiv'))) {
                $searcher->setSearchModus(MailSearcher::SEARCH_MODUS_SENDER_MAIL);
                $result = $searcher->search();

                if ($result->getCustomerId()) {
                    MailArchivRepository::addMimeMailToCustomer($mail, $result->getCustomerId(), $result->getOrderId());
                }
            }
        }

        $this->autoResponder($mail);
    }

    private function findShopIdByMail(MimeMail $mail): ?int
    {
        $domain = MailUtils::getHostFromMailAddress($mail->getSenderMail());

        $shop_id = $this->findShopIdByDomain($domain);
        if ($shop_id) {
            return $shop_id;
        }

        $domain = MailUtils::getHostFromMailAddress($mail->getEmpfaengerMail());

        return $this->findShopIdByDomain($domain);
    }

    private function findShopIdByDomain(string $domain): ?int
    {
        switch (strtolower($domain)) {
            case 'allego.de':
            case 'ecom-dresden.de':
            case 'smartgoods.de':
                return 1;
            case 'ersatzteilshop.de':
                return 2;
        }

        return null;
    }

    public function autoResponder(MimeMail $mail): void
    {
        $src_mail = $mail->getSenderMail();
        $dst_mail = $mail->getEmpfaengerMail();

        $daten = $this->db->singleQuery("
            SELECT
                mail_autoresponders.mail,
                mail_autoresponders.autoresponder_subject,
                mail_autoresponders.autoresponder_body,
                mail_autoresponders.autoresponder_ignore_duration
            FROM
                mail_autoresponders
            WHERE
                mail_autoresponders.mail = '" . $this->db->escape($dst_mail) . "' AND
                mail_autoresponders.autoresponder_active = 1
        ");

        if (!$daten) {
            return;
        }

        $this->db->query("
            DELETE FROM
                mail_autoresponders_blocklist
            WHERE
                mail_autoresponders_blocklist.mail =  '" . $this->db->escape($dst_mail) . "' AND
                mail_autoresponders_blocklist.date <= NOW() - INTERVAL '" . $daten['autoresponder_ignore_duration'] . "' SECOND
        ");

        if (strpos($src_mail, 'no-reply') !== false || strpos($src_mail, 'noreply') !== false) {
            return;
        }

        //domains die gesperrt sind
        $blocklist = [
            '@members.ebay.de', //ebay steht nicht auf autoresponder
            '@members.ebay.com',
            '<EMAIL>', //faxe an info -> autoresponder öffnet dann bei den ein Ticker,
            'mailer-daemon@',
            'postmaster@',
            '<EMAIL>'
        ];

        foreach ($blocklist as $block_entry) {
            if (strpos($src_mail, $block_entry) !== false) {
                return;
            }
        }

        //mail adressen die explizit gesperrt sind
        $static_blocklist = [
            'c714556cea5aee045c8ea7481174424b' //anwalt/abmahnung
        ];

        if (in_array($src_mail, $static_blocklist) || in_array(md5($src_mail), $static_blocklist)) {
            return;
        }

        $status = $this->db->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                mail_autoresponders_blocklist
            WHERE
                mail_autoresponders_blocklist.mail = '" . $this->db->escape($dst_mail) . "' AND
                mail_autoresponders_blocklist.src_mail = '" . $this->db->escape($src_mail) . "'
        ");

        $this->db->query("
            INSERT INTO
                mail_autoresponders_blocklist
            SET
                mail_autoresponders_blocklist.mail = '" . $this->db->escape($dst_mail) . "',
                mail_autoresponders_blocklist.src_mail = '" . $this->db->escape($src_mail) . "',
                mail_autoresponders_blocklist.date = NOW()
        ");

        if ($status) {
            return;
        }

        $daten['mail'] = str_replace('autoreply.', '', $daten['mail']);

        $mailer = service_loader::getSymfonyMailer($daten['mail']);

        $message = new Email();
        $message->from($daten['mail']);
        $message->to($src_mail);
        $message->subject($daten['autoresponder_subject']);
        $message->text($daten['autoresponder_body']);

        try {
            $mailer->send($message);
        } catch (Exception $e) {

        }
    }
}
