<?php

namespace wws\Mails;

use bqp\Date\DateObj;
use bqp\Mail\MimeMail;
use env;
use wws\Mailsystem\MailsystemMail;

class MailArchivRepository
{

    public static function addToCustomer(MailsystemMail $mail, int $customer_id, int $order_id = 0): void
    {
        $archive_mail = new MailArchivMail();
        $archive_mail->setDatum($mail->getDateReceive());
        $archive_mail->setOrderId($order_id);
        $archive_mail->setCustomerId($customer_id);
        $archive_mail->setEmailSender($mail->getSenderEmail());
        $archive_mail->setEmailReciver($mail->getEmpfaengerMail());
        $archive_mail->setBetreff($mail->getBetreff());
        $archive_mail->setBody($mail->getBody());
        $archive_mail->setAnlagenRaw($mail->getAnlagenRaw());
        $archive_mail->setUserId(env::getUserId());
        $archive_mail->setMailId($mail->getMailId());

        $archive_mail->save();
    }

    public static function addMimeMailToCustomer(MimeMail $mail, int $customer_id, int $order_id = 0): void
    {
        $arhive_mail = new MailArchivMail();
        $arhive_mail->setDatum(new DateObj()); //$mail->getDatum();
        if ($order_id) {
            $arhive_mail->setOrderId($order_id);
        }
        $arhive_mail->setCustomerId($customer_id);
        $arhive_mail->setEmailSender($mail->getSenderMail());
        $arhive_mail->setEmailReciver($mail->getEmpfaengerMail());
        $arhive_mail->setBetreff($mail->getBetreff());
        $arhive_mail->setBody($mail->getBody());
        $arhive_mail->setUserId(env::getUserId());
        //attachments?

        $arhive_mail->save();
    }
}
