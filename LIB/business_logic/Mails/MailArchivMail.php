<?php

namespace wws\Mails;

use bqp\Date\DateObj;
use bqp\Exceptions\InputException;
use db;

class MailArchivMail
{

    private $daten = [
        'user_id' => 0,
        'order_id' => 0,
        'email_sender' => '',
        'email_reciver' => '',
        'anlagen' => '',
        'aktion' => '',
        'header' => '',
        'mail_id' => 0,
    ];

    protected $autofill_runed = false;

    public function save()
    {
        if (!$this->daten['datum']) {
            $this->daten['datum'] = (new DateObj())->db();
        }

        if (!$this->daten['user_id'] && isset($_SESSION['editor'])) {
            $this->daten['user_id'] = $_SESSION['editor'];
        }

        $this->autoFillIds();
        $this->validate();

        $db = db::getInstance();

        $data = [
            'archive_mail_id' => null,
            'customer_id' => $this->daten['customer_id'],
            'order_id' => $this->daten['order_id'],
            'mail_id' => $this->daten['mail_id'],
            'user_id' => $this->daten['user_id'] ?? 0,
            'date_mail' => $this->daten['datum'],
            'email_sender' => $this->daten['email_sender'],
            'email_reciver' => $this->daten['email_reciver'],
            'subject' => $this->daten['betreff'],
            'body' => $this->daten['body'],
            'header' => $this->daten['header'],
            'attachments' => $this->daten['anlagen']
        ];

        $db->simpleInsertUpdate('customer_mail_archive', $data);
    }


    public function validate(): void
    {
        $this->autoFillIds();

        $errors = new InputException();

        if (!$this->daten['customer_id']) {
            $errors->add('customer_id', '$customer_id fehlt.');
        }

        $errors->check();
    }

    private function autoFillIds(): void
    {
        $db = db::getInstance();

        if ($this->autofill_runed) {
            return;
        }

        $this->autofill_runed = true;

        if (!empty($this->daten['auftnr']) && !empty($this->daten['order_id']) && !empty($this->daten['customer_id'])) {
            return;
        }

        if (!empty($this->daten['order_id'])) {
            $daten = $db->singleQuery("
                SELECT
                    order_item.order_id,
                    orders.customer_id
                FROM
                    order_item INNER JOIN
                    orders ON (orders.order_id = order_item.order_id)
                WHERE
                    order_item.order_id = '" . (int)$this->daten['order_id'] . "'
                LIMIT
                    1
            ");

            $this->setCustomerId($daten['customer_id']);
        } elseif (!empty($this->daten['auftnr'])) {
            $daten = $db->singleQuery("
                SELECT
                    order_item.order_id,
                    orders.customer_id
                FROM
                    order_item INNER JOIN
                    orders ON (orders.order_id = order_item.order_id)
                WHERE
                    order_item.auftnr = '" . $db->escape($this->daten['auftnr']) . "'
                LIMIT
                    1
            ");

            if ($daten) {
                $this->setOrderId($daten['order_id']);
                $this->setCustomerId($daten['customer_id']);
            }
        }
    }

    public function setOrderId($order_id)
    {
        $this->daten['order_id'] = $order_id;
    }

    public function setAuftnr($auftnr)
    {
        $this->daten['auftnr'] = $auftnr;
    }

    public function setCustomerId($customer_id)
    {
        $this->daten['customer_id'] = $customer_id;
    }

    public function setUserId($user_id)
    {
        $this->daten['user_id'] = $user_id;
    }

    public function setDatum(DateObj $date): bool
    {
        $this->daten['datum'] = $date->db();
        return true;
    }

    public function setEmailSender(string $email_sender): bool
    {
        $this->daten['email_sender'] = $email_sender;
        return true;
    }

    public function setEmailReciver(string $email_reciver): bool
    {
        $this->daten['email_reciver'] = $email_reciver;
        return true;
    }

    public function setBetreff($betreff)
    {
        $this->daten['betreff'] = $betreff;
    }

    public function setBody($body)
    {
        $this->daten['body'] = $body;
    }

    public function setAktion($aktion)
    {
        $this->daten['aktion'] = $aktion;
    }

    public function setAnlagenRaw($anlagen)
    {
        $this->daten['anlagen'] = $anlagen;
    }

    public function setMailId(int $mail_id): bool
    {
        $this->daten['mail_id'] = $mail_id;
        return true;
    }
}
