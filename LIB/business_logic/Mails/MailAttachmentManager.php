<?php

namespace wws\Mails;

use bqp\Exceptions\SecurityException;
use bqp\storage\storage_mount_manager;
use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;

/**
 * Übernimmt das Handling um den INHALT von Attachments im Dateisystem zu speichern und laden.
 * Attachments werden über ein hash idendifiziert und lokalisiert.
 *
 * @package wws\mail
 */
class MailAttachmentManager
{
    /** @var string */
    private $filename_prefix;

    /** @var storage_mount_manager */
    private $storage;

    /** @var string */
    private $storage_prefix;

    /**
     * @param storage_mount_manager $storage
     */
    public function __construct(storage_mount_manager $storage)
    {
        $this->storage = $storage;
        $this->filename_prefix = date('Y');
        $this->storage_prefix = 'mail://';
    }

    /**
     * Benutzer definierter Dateinamen Prefix
     * Matcht derzeit nur auf 4 ziffern!
     * Kann aber angepasst werden wenn auf underscore gematcht wird.
     *
     * @param string $filename_prefix
     */
    public function setFilenamePrefix(string $filename_prefix): void
    {
        $this->filename_prefix = $filename_prefix;
    }

    /**
     * @param string $hash
     * @return string
     * @throws FileNotFoundException
     * @throws SecurityException
     */
    public function loadAttachmentContentByHash(string $hash): string
    {
        return $this->storage->read($this->storage_prefix . $this->getAttachmentPathByHash($hash));
    }

    public function displayAttachmentByHash(string $file_hash, string $as = null): string
    {
        if ($as === null) {
            $as = $file_hash . '.unknown';
        }

        header("Content-Type: x-type/subtype");
        header("Content-Disposition: attachment; filename=\"$as\"");

        echo $this->loadAttachmentContentByHash($file_hash);
        exit();
    }

    /**
     * Speichert den Anhang und gibt den Hash zurück
     * @param string $attachment_content
     * @return string
     * @throws FileExistsException
     * @throws SecurityException
     */
    public function saveAttachmentContent(string $attachment_content): string
    {
        $hash = $this->generateHash($attachment_content);
        if ($this->storage->has($this->storage_prefix . $this->getAttachmentPathByHash($hash)) === true) {
            return $hash;
        }

        $this->storage->write($this->storage_prefix . $this->getAttachmentPathByHash($hash), $attachment_content);

        return $hash;
    }

    /**
     * @param string $hash
     * @return string
     * @throws SecurityException
     */
    private function getAttachmentPathByHash(string $hash): string
    {
        if (strpos('/', $hash) !== false || strpos('\\', $hash) !== false) {
            throw new SecurityException('attachment hash with potentional path traversable');
        }

        $filepath = strtolower($hash[0]) . '/';

        if (preg_match('~^(\d{4})_(.)~', $hash, $temp)) {
            $filepath = $temp[1] . '/' . strtolower($temp[2]) . '/';
        }

        return $filepath . $hash;
    }

    /**
     * Erzeugt aus dem Inhalt des Anhangs ein Hash zur Idendifizierung
     *
     * @param string $attachment_content
     * @return string
     */
    private function generateHash(string $attachment_content): string
    {
        $hash = hash('md5', $attachment_content, true);
        // Risiko für Kollisionen reduzieren
        $hash .= hash('crc32', $attachment_content, true);
        $hash = base64_encode($hash);
        $hash = str_replace(['+', '/', '='], ['X', 'X', ''], $hash);

        return $this->filename_prefix . '_' . $hash;
    }
}