<?php

namespace wws\Mails\Templates;

use ArrayIterator;
use bqp\Json;
use IteratorAggregate;
use Traversable;

class MailTemplateAttachments implements IteratorAggregate
{
    protected $attachments = [];

    /**
     * @return string
     */
    public function serialize()
    {
        if ($this->attachments) {
            return json_encode($this->attachments);
        }

        return '';
    }

    /**
     * @param string $serialized
     */
    public function unserialize($serialized)
    {
        if (!$serialized) {
            return;
        }

        $this->attachments = Json::decode($serialized);
    }

    /**
     * @param string $path
     * @param string $name
     */
    public function addAttachment($path, $name)
    {
        $this->attachments[] = ['path' => $path, 'name' => $name];
    }

    public function getIterator(): Traversable
    {
        return new ArrayIterator($this->attachments);
    }
}
