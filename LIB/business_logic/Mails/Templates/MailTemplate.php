<?php

namespace wws\Mails\Templates;

use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataObj;

class MailTemplate implements ProvideSmartDataObj
{
    protected SmartDataObj $daten;

    public function __construct()
    {
        $this->daten = new SmartDataObj($this);
    }

    /**
     * @return SmartDataObj
     */
    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }


    /**
     * @param $mail_id
     * @return boolean
     */
    public function setMailId($mail_id)
    {
        return $this->daten->setter('mail_id', $mail_id);
    }

    /**
     * @param $mail_kat_id
     * @return boolean
     */
    public function setMailKatId($mail_kat_id)
    {
        return $this->daten->setter('mail_kat_id', $mail_kat_id);
    }

    /**
     * @param $shop_id
     * @return boolean
     */
    public function setShopId($shop_id)
    {
        return $this->daten->setter('shop_id', $shop_id);
    }

    /**
     * @param $lang_code
     * @return boolean
     */
    public function setLangCode($lang_code)
    {
        return $this->daten->setter('lang_code', $lang_code);
    }

    /**
     * @param $system_mail
     * @return boolean
     */
    public function setSystemMail($system_mail)
    {
        return $this->daten->setter('system_mail', $system_mail);
    }

    /**
     * @param $beschreibung
     * @return boolean
     */
    public function setBeschreibung($beschreibung)
    {
        return $this->daten->setter('beschreibung', $beschreibung);
    }

    /**
     * @param $absender_name
     * @return boolean
     */
    public function setAbsenderName($absender_name)
    {
        return $this->daten->setter('absender_name', $absender_name);
    }

    /**
     * @param $absender_mail
     * @return boolean
     */
    public function setAbsenderMail($absender_mail)
    {
        return $this->daten->setter('absender_mail', $absender_mail);
    }

    /**
     * @param $betreff
     * @return boolean
     */
    public function setBetreff($betreff)
    {
        return $this->daten->setter('betreff', $betreff);
    }

    /**
     * @param $text_mail
     * @return boolean
     */
    public function setTextMail($text_mail)
    {
        return $this->daten->setter('text_mail', $text_mail);
    }

    public function setTextCutomerMemo($text_customer_memo): bool
    {
        return $this->daten->setter('text_customer_memo', $text_customer_memo);
    }

    /**
     * @param MailTemplateAttachments $attachments
     * @return boolean
     */
    public function setAttachments(MailTemplateAttachments $attachments)
    {
        return $this->daten->setter('attachments', $attachments->serialize());
    }

    /**
     * @return string mail_id
     */
    public function getMailId()
    {
        return $this->daten->getter('mail_id');
    }

    /**
     * @return string mail_kat_id
     */
    public function getMailKatId()
    {
        return $this->daten->getter('mail_kat_id');
    }

    /**
     * @return string shop_id
     */
    public function getShopId()
    {
        return $this->daten->getter('shop_id');
    }

    /**
     * @return string lang_code
     */
    public function getLangCode()
    {
        return $this->daten->getter('lang_code');
    }

    /**
     * @return bool system_mail
     */
    public function isSystemMail()
    {
        return (bool)$this->daten->getter('system_mail');
    }

    /**
     * @return string beschreibung
     */
    public function getBeschreibung()
    {
        return $this->daten->getter('beschreibung');
    }

    /**
     * @return string absender_name
     */
    public function getAbsenderName()
    {
        return $this->daten->getter('absender_name');
    }

    /**
     * @return string absender_mail
     */
    public function getAbsenderMail()
    {
        return $this->daten->getter('absender_mail');
    }

    /**
     * @return string betreff
     */
    public function getBetreff()
    {
        return $this->daten->getter('betreff');
    }

    /**
     * @return string text_mail
     */
    public function getTextMail()
    {
        return $this->daten->getter('text_mail');
    }

    public function getTextCustomerMemo(): ?string
    {
        return $this->daten->getter('text_customer_memo');
    }

    /**
     * @return MailTemplateAttachments
     */
    public function getAttachments()
    {
        $raw_attachments = $this->daten->getter('attachments');

        $attachments = new MailTemplateAttachments();
        $attachments->unserialize($raw_attachments);

        return $attachments;
    }
}
