<?php

namespace wws\Mails\Templates;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\storage\storage_mount_manager;
use bqp\Utils\FileUtils;
use bqp\Utils\StringUtils;
use Exception;
use League\Flysystem\FileNotFoundException;
use service_loader;

class MailTemplateRepository
{
    private static $select_list = "einst_mail.mail_id,
                einst_mail.mail_kat_id,
                einst_mail.shop_id,
                einst_mail.lang_code,
                einst_mail.system_mail,
                einst_mail.beschreibung,
                einst_mail.absender_name,
                einst_mail.absender_mail,
                einst_mail.betreff,
                einst_mail.text_mail,
                einst_mail.text_customer_memo,
                einst_mail.attachments";

    protected db_generic $db;
    protected string $storage_key = '';

    public function __construct(db_generic $db)
    {
        $this->db = $db;

        $this->storage_key = 'pubfiles';
    }

    /**
     * @param string $mail_id
     * @param int $shop_id
     * @param string $lang
     * @return MailTemplate
     */
    public function loadMailTemplate($mail_id, $shop_id, $lang = 'de')
    {
        $daten = $this->db->singleQuery("
            SELECT
                " . self::$select_list . "
            FROM
                einst_mail
            WHERE
                einst_mail.mail_id = " . $this->db->quote($mail_id) . " AND
                einst_mail.shop_id = " . (int)$shop_id . "
        ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException('mail template not found');
        }

        $template = new MailTemplate();
        $template->getSmartDataObj()->loadDaten($daten);

        return $template;
    }

    public function saveMailTemplate(MailTemplate $mail_template)
    {
        $smart_data_obj = $mail_template->getSmartDataObj();

        $daten = $smart_data_obj->getAsArray();

        switch ($smart_data_obj->getObjStatus()) {
            case $smart_data_obj::STATUS_NEW:

                if (!$daten['mail_id']) {
                    $daten['mail_id'] = $this->getFreeMailId($mail_template->getShopId());

                    $smart_data_obj->setterDirect('mail_id', $daten['mail_id']);
                }

                $this->db->simpleInsert('einst_mail', $daten);
                break;
            case $smart_data_obj::STATUS_UPDATE:
                $org_mail_id = $smart_data_obj->getOldValue('mail_id');
                if (!$org_mail_id) {
                    $org_mail_id = $daten['mail_id'];
                }

                $this->db->simpleUpdate('einst_mail', $daten, ' einst_mail.shop_id = ' . (int)$mail_template->getShopId() . ' AND einst_mail.mail_id = ' . $this->db->quote($org_mail_id));
                break;
        }

        $smart_data_obj->setSaved();

        return $mail_template->getMailId();
    }

    /**
     * erzeugt eine neue id für ein mail template
     * @param int $shop_id
     * @return int
     */
    private function getFreeMailId($shop_id)
    {
        $result = $this->db->query("
            SELECT
                einst_mail.mail_id
            FROM
                einst_mail
            WHERE
                einst_mail.shop_id = '" . (int)$shop_id . "'
        ");

        $ids = [];
        foreach ($result as $daten) {
            $ids[(int)$daten['mail_id']] = (int)$daten['mail_id'];
        }

        $max_id = max($ids) + 1;

        return $max_id;
    }

    public function newMailTemplate()
    {
        $template = new MailTemplate();

        $template->getSmartDataObj()->setDefaults([
            'mail_id' => null,
            'mail_kat_id' => null,
            'shop_id' => null,
            'lang_code' => 'de',
            'system_mail' => 0,
            'beschreibung' => '',
            'absender_name' => '',
            'absender_mail' => '',
            'betreff' => '',
            'text_mail' => '',
            'attachments' => ''
        ]);

        return $template;
    }

    public function deleteMailTemplate(MailTemplate $mail_template)
    {
        $this->db->query("
            DELETE FROM
                einst_mail
            WHERE
                einst_mail.mail_id = '" . $this->db->escape($mail_template->getMailId()) . "' AND
                einst_mail.shop_id = '" . (int)$mail_template->getShopId() . "'    
        ");
    }

    public function createMailCat($parent_mail_cat_id, $cat_name)
    {
        $this->db->query("
            INSERT INTO
                einst_mail_kats
            SET
                einst_mail_kats.mail_parent_kat_id = '" . (int)$parent_mail_cat_id . "',
                einst_mail_kats.kat_name = '" . $this->db->escape($cat_name) . "'
        ");
    }

    /**
     * @return storage_mount_manager
     */
    public function getAttachmentStorage()
    {
        return service_loader::getStorageFactory()->get($this->storage_key);
    }

    public function countAttachmentUse($attachment_path, MailTemplate $mail_template = null)
    {
        $search = '"' . str_replace('/', '\\\\/', $attachment_path);

        $sql = "
            SELECT
                COUNT(*)
            FROM
                einst_mail
            WHERE
                einst_mail.attachments LIKE '%" . $this->db->escape($search) . "%'
        ";

        if ($mail_template) {
            $sql .= " AND NOT (einst_mail.mail_id = " . $this->db->quote($mail_template->getMailId()) . " AND einst_mail.shop_id = " . (int)$mail_template->getShopId() . ")";
        }

        return (int)$this->db->fieldQuery($sql);
    }

    /**
     * löscht ein attachment aus dem storage wenn es keinem mail_template mehr zugeordent ist
     * es kann eine mail_template übergeben werden, was aus der zählung ausgenommen wird
     * @param $attachment_path
     */
    public function trySaveDeleteAttachment($attachment_path, MailTemplate $mail_template = null)
    {
        if ($this->countAttachmentUse($attachment_path, $mail_template) == 0) {
            $this->tryDeleteAttachment($attachment_path);
        }
    }

    public function tryDeleteAttachment($attachment_path)
    {
        $storage = $this->getAttachmentStorage();
        try {
            $storage->delete($attachment_path);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    public function uploadAttachment($org_filename, $src)
    {
        $storage = $this->getAttachmentStorage();

        $filename = FileUtils::appendBeforExtenstion($org_filename, 'ma_' . rand(10000, 99999));
        $filename = $this->storage_key . '://' . $filename;

        if (FileUtils::isFilepointer($src)) {
            $storage->putStream($filename, $src);
        } else {
            $storage->put($filename, $src);
        }

        return $filename;
    }

    /**
     * prüft ob der $attachment_path zum storage gehört
     * @param string $attachment_path
     * @return bool
     */
    public function isStorageAttachment($attachment_path)
    {
        return StringUtils::begins($attachment_path, $this->storage_key);
    }

    /**
     * lädt $attachment_path aus dem storage und gibt den Inhalt zurück
     * @param $attachment_path
     * @return string
     * @throws FileNotFoundException
     */
    public function fetchAttachmentContent($attachment_path)
    {
        $storage = $this->getAttachmentStorage();

        return $storage->read($attachment_path);
    }
}
