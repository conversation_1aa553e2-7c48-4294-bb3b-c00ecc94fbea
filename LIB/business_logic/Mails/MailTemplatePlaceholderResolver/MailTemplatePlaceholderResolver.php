<?php

namespace wws\Mails\MailTemplatePlaceholderResolver;

use wws\business_structure\Shop;
use wws\Customer\Customer;
use wws\Mails\Mail;
use wws\Order\Order;

abstract class MailTemplatePlaceholderResolver
{
    protected Mail $mail;
    protected ?Order $order = null;
    protected ?Customer $customer = null;
    protected ?Shop $shop = null;

    /**
     * aktuelle Mail Objekt / zum Freihalten des ctors für DI
     * @param Mail $mail
     * @return void
     */
    public function setMail(Mail $mail): void
    {
        $this->mail = $mail;

        $this->order = null;
        $this->customer = null;
        $this->shop = null;
    }

    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }

    public function setCustomer(Customer $customer): void
    {
        $this->customer = $customer;
    }

    public function setShop(Shop $shop): void
    {
        $this->shop = $shop;
    }

    abstract public function getType(): string;

    /**
     * @throws MailTemplatePlaceholderResolverException
     */
    abstract public function resolve(string $placeholder_key): ?string;
}
