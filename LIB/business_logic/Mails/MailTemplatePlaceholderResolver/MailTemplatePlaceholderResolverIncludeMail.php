<?php

namespace wws\Mails\MailTemplatePlaceholderResolver;

use db;

class MailTemplatePlaceholderResolverIncludeMail extends MailTemplatePlaceholderResolver
{
    public function getType(): string
    {
        return 'include_mail';
    }

    public function resolve(string $placeholder_key): ?string
    {
        return $this->getIncludeMail($placeholder_key);
    }


    private function getIncludeMail(string $mail_id): string
    {
        $db = db::getInstance();

        return (string)$db->fieldQuery("
            SELECT
                einst_mail.text_mail
            FROM
                einst_mail
            WHERE
                einst_mail.mail_id = '" . $db->escape($mail_id) . "' AND
                einst_mail.shop_id = '" . $this->mail->getShopId() . "'
        ");
    }
}
