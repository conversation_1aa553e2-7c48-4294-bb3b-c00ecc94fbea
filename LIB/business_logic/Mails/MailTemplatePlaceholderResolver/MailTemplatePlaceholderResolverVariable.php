<?php

namespace wws\Mails\MailTemplatePlaceholderResolver;

class MailTemplatePlaceholderResolverVariable extends MailTemplatePlaceholderResolver
{
    private array $data;

    public function __construct(array &$data)
    {
        //Achtung: Referenz auf den ursprünglichen array im Mail Objekt, ansonsten müssten in resolve() die Daten jedes Mal neu gezogen werden
        $this->data = &$data;
    }

    public function getType(): string
    {
        return 'variable';
    }

    public function resolve(string $placeholder_key): ?string
    {
        return $this->data[$placeholder_key] ?? null;
    }
}
