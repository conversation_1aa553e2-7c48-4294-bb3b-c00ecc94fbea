<?php

namespace wws\Mails\MailTemplatePlaceholderResolver;

use bqp\extern\Trustpilot\Authenticator;
use bqp\extern\Trustpilot\Client;
use bqp\extern\Trustpilot\Context;
use bqp\extern\Trustpilot\Recipient;
use Exception;
use service_loader;
use wws\Order\Order;
use wws\Order\OrderConst;

class MailTemplatePlaceholderResolverK11 extends MailTemplatePlaceholderResolver
{
    public function getType(): string
    {
        return 'variable';
    }

    public function resolve(string $placeholder_key): ?string
    {
        if (!$this->order) {
            return null;
        }

        switch ($placeholder_key) {
            case 'shop_rating_link':
                return $this->getShopRaringLink($this->order);
            case 'shop_product_rating_link':
                return $this->getProductRaringLink();
        }


        return null;
    }

    private function getShopRaringLink(Order $order): string
    {
        if ($order->getOrderId() % 2 === 0) {
            try {
                return $this->getTrustPilotRatingLink(
                    $order->getAuftnr(),
                    $order->getCustomer()->getEmail(),
                    sprintf('%s %s', $order->getCustomer()->getVorname(), $order->getCustomer()->getName())
                );
            } catch (Exception $exception) {
                //klasse, 2, 3 mal getestet beim refactoring... "Too many attempts, try later".
                //Wer weiß was das Livesystem bisher gemacht hat. Das Ding wurde bei jeder 2ten Mail gecallt.
                //jetzt nur noch bei bedarft und es wird nicht jedes mal oauth ausgeführt
            }
        }
        return $this->getGoogleRatingLink();
    }

    private function getTrustPilotRatingLink(string $auftnr, string $email, string $name): string
    {
        static $client = null;

        $config = service_loader::getConfigRegistry()->get('k11/trustpilot');

        if ($client === null) {
            $authenticator = new Authenticator();
            $access_token = $authenticator->getAccessToken($config->getString('api_key'), $config->getString('api_token'));
            $client = new Client($access_token);
        }

        $context = new Context($config->getString('business_unit_id'), $config->getString('redirect_uri'));
        $recipient = new Recipient($email, $name);

        $result = $client->invitationLink($context, $recipient, $auftnr);
        return $result['url'];
    }

    private function getGoogleRatingLink(): string
    {
        return 'https://g.page/r/CThr7LYJXZa5EAg/review';
    }

    private function getProductRaringLink(): string
    {
        foreach ($this->order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_PRODUKT) as $order_item) {
            $product_id = $order_item->getProductId();
            //hier wurde vorher geprüft ob das storniert ist, spielt aber eigentlich auch keine rolle...

            return $this->order->getShop()->getUrl() . '/redirect-to-product/' . $product_id . '#reviewPanel';
        }

        return 'Leider ist das Produkt zurzeit nicht mehr verfügbar. Schauen Sie in Kürze noch einmal vorbei, wir haben bereits Nachschub bestellt.';
    }
}
