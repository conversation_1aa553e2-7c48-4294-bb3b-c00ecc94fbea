<?php

namespace wws\Mails\MailTemplatePlaceholderResolver;

use db;

class MailTemplatePlaceholderResolverIncludeCms extends MailTemplatePlaceholderResolver
{
    public function getType(): string
    {
        return 'include_cms';
    }

    public function resolve(string $placeholder_key): ?string
    {
        return $this->getIncludeCms($placeholder_key);
    }

    private function getIncludeCms(string $content_id): string
    {
        $db = db::getInstance();

        return (string)$db->fieldQuery("
            SELECT
                content_entry.content_parsed
            FROM
                content_entry
            WHERE
                content_entry.content_id = '" . $db->escape($content_id) . "'
        ");
    }
}
