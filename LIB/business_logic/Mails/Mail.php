<?php

namespace wws\Mails;

use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Json;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Utils\MailUtils;
use config;
use db;
use env;
use Exception;
use League\Flysystem\FileNotFoundException;
use output;
use service_loader;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mime\Address as SymfonyAddress;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Exception\RfcComplianceException;
use Symfony\Component\Mime\Part\DataPart;
use Symfony\Component\Mime\Part\File as SymfonyFile;
use wws\business_structure\Shop;
use wws\Customer\Customer;
use wws\Documents\DocumentRepository;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolver;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverIncludeCms;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverIncludeMail;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverK11;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverVariable;
use wws\Mails\Templates\MailTemplateRepository;
use wws\Order\Order;

/**
 * Mailsystem zum senden von Kundebezogenen E-Mails
 */
class Mail
{
    /**
     * Beinhaltet die Daten für den Parser
     * @var array
     */
    private $prase_daten = [];

    protected $mail_daten = [];

    /**
     * E-Mail Adresse des Empfängers
     * @var string
     */
    private $reciver = '';
    private $recivers = [];

    private $reply_to = null;


    /**
     * flag ob Editor geladen wurde, ansonsten passiert dies automatisch beim laden der vorlage
     * @var bool
     */
    private $editor_is_set = false;

    /**
     * @var int
     */
    private $shop_id;


    private $form_name = 'mail';
    private $form_legend = null;
    private $form_optional_send = false;

    protected $attachments = [];
    protected $signatur = 'normal';


    private $auto_add_to_mailarchiv = true;

    public $body_is_html = false;

    private MailTemplateRepository $mail_template_repository;

    /**
     * @var MailTemplatePlaceholderResolver[]
     */
    private array $placeholder_resolver = [];

    /**
     * @param bool|array $daten
     */
    public function __construct($daten = false)
    {
        $this->mail_template_repository = new MailTemplateRepository(db::getInstance());

        $this->mail_daten = [
            'send_mail' => true,
            'empfaenger_mail' => '',
            'absender_name' => '',
            'absender_mail' => '',
            'betreff' => '',
            'text_mail' => '',
            'customer_memo_vorlage' => ''
        ];

        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverVariable($this->prase_daten);
        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverIncludeMail();
        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverIncludeCms();
        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverK11();

        foreach ($this->placeholder_resolver as $resolver) {
            $resolver->setMail($this);
        }

        if (is_array($daten)) {
            $this->loadByDaten($daten);
        }
    }

    public function loadByDaten($daten)
    {
        $this->prase_daten = Json::decode(base64_decode($daten['prase_daten']));
        $this->mail_daten = $daten['mail_daten'];

        if (isset($this->mail_daten['send_mail_input'])) {
            $this->mail_daten['send_mail'] = isset($this->mail_daten['send_mail']);
        }
    }

    public function setOrderId(int $order_id): void
    {
        $this->setOrder($order_id);
    }

    /**
     * Lädt Auftrags,Kunden und Shopdaten in den Parser
     * @param Order|int $order_object_or_order_id
     */
    public function setOrder($order_object_or_order_id): void
    {
        if ($order_object_or_order_id instanceof Order) {
            $order = $order_object_or_order_id;
        } else {
            $order = new Order($order_object_or_order_id);
        }

        $url_shortener = service_loader::urlShortener($order->getShopId());

        $this->assign('ts_auftnr', urlencode(base64_encode($order->getAuftnr())));
        $this->assign('auft_wert', output::formatPrice($order->getRechnungsBetrag()));

        $this->assign('order_id', $order->getOrderId());
        $this->assign('tax_status', $order->getTaxStatus());
        $this->assign('shop_id', $order->getShopId());
        $this->assign('auft_auftnr', $order->getAuftnr());
        $this->assign('auft_datum', $order->getBestellDatum()->format(DateObj::DE_DATE));
        $this->assign('auft_bestell_datum', $order->getBestellDatum()->format(DateObj::DE_DATE));

        $this->assign('auft_kunde_beleg', $order->getKundeBeleg());
        $this->assign('auft_kunde_zeichen', $order->getKundeZeichen());

        $this->assign('auft_zahlungsart', $order->getZahlungsartName());
        $this->assign('auft_bemerkung', $order->getKundenBemerkung());

        // rechnungs/lieferadresse
        if ($order->isAbweichendeLieferadresse()) {
            $title = 'Rechnungsadresse';

            $address = $order->getLieferAddress();
            $this->assign('auft_different_delivery_address', "Lieferadresse:\n" . self::addIndent($address->format(Address::FROMAT_WITH_LABEL, false)));
        } else {
            $title = 'Rechnungs-/Lieferadresse';
            $this->assign('auft_different_delivery_address', '');
        }

        $address = $order->getRechnungAddress();
        $this->assign('auft_invoice_address', "$title:\n" . self::addIndent($address->format(Address::FROMAT_WITH_LABEL, false)));

        // auftragspositionen
        $mail_helper = new MailHelperOrder($order);

        $this->assign('auft_product_list_small', $mail_helper->getListSmall());
        $this->assign('auft_list_simple', $mail_helper->getListSimple());
        $this->assign('auft_list_extended', $mail_helper->getListExtended());

        foreach ($this->placeholder_resolver as $placeholder) {
            $placeholder->setOrder($order);
        }

        $this->setCustomer($order->getCustomerId());
    }

    protected static function addIndent($value)
    {
        $value = explode("\n", $value);

        $return = '';

        foreach ($value as $t) {
            $return .= '  ' . $t . "\n";
        }

        return $return;
    }

    /**
     * Lädt Kunden und Shopdaten in den Parser
     * @param int $customer_id
     */
    public function setCustomer($customer_id)
    {
        $customer = new Customer($customer_id);

        foreach ($this->placeholder_resolver as $placeholder) {
            $placeholder->setCustomer($customer);
        }

        $daten['customer_id'] = $customer->getCustomerId();
        $daten['kunde_customer_id'] = $customer->getCustomerNr();
        $daten['kunde_customer_nr'] = $customer->getCustomerNr();
        $daten['kunde_anrede'] = $customer->getAnrede();
        $daten['kunde_name'] = $customer->getName();
        $daten['kunde_vorname'] = $customer->getVorname();
        $daten['kunde_firma'] = $customer->getFirma();
        $daten['kunde_tel'] = $customer->getTelefon();
        $daten['kunde_email'] = $customer->getEmail();
        $daten['shop_id'] = $customer->getShopId();
        $daten['kunde_ort'] = $customer->getOrt();
        $daten['kunde_strasse'] = $customer->getAdresse1();
        $daten['kunde_plz'] = $customer->getPlz();
        $daten['kunde_land'] = $customer->getCountryName();
        $daten['kunde_anrede_text'] = $customer->getGrussformel();

        $daten['ts_kunde_email'] = urlencode(base64_encode($daten['kunde_email']));

        $this->setEmpf($daten['kunde_email']);
        $this->assignArray($daten);
        $this->setShop($daten['shop_id']);
    }

    /**
     * Lädt Shopdaten in den Parser
     * @param int $shop_id
     */
    public function setShop($shop_id)
    {
        $daten = [];

        $shop = Shop::getInstance($shop_id);

        foreach ($this->placeholder_resolver as $placeholder) {
            $placeholder->setShop($shop);
        }

        $address = $shop->getAddress();

        $daten['shop_id'] = $shop->getShopId();
        $daten['shop_name'] = $shop->getShortName();
        $daten['shop_short_name'] = $shop->getShortName();

        $daten['shop_hotline'] = $shop->getHotline();
        $daten['shop_hotline_service'] = $shop->getHotlineService();
        $daten['shop_adresse'] = $address->getAdresse1();
        $daten['shop_plz'] = $address->getPlz();
        $daten['shop_ort'] = $address->getOrt();
        $daten['shop_ustidnr'] = $shop->getUstidnr();
        $daten['shop_email'] = $shop->getEmail();
        $daten['shop_service_email'] = $shop->getServiceEmail();
        $daten['shop_noreply_email'] = $shop->getEmailNoreply();


        $daten['shop_url'] = $shop->getHumanUrl();
        $daten['shop_agb_link'] = $shop->getUrl() . '/agb.html';

        $daten['shop_bank_inhaber'] = $shop->getKontoInhaber();
        $daten['shop_bank_iban'] = trim(chunk_split($shop->getIban(), 4, ' '));
        $daten['shop_bank_bic'] = $shop->getBic();
        $daten['shop_bank'] = $shop->getBank();

        $this->assignArray($daten);
        $this->shop_id = $shop_id;
    }

    /**
     * Lädt die Daten des Bearbeiters, wenn nix angegeben ist werden die Daten aus
     * der Sessiong gehollt. Alternativ kann auch die $user_id '0' oder 'system'
     * verwendet werden um eine anonyme Signatur zu verwenden
     * @param bool|int $user_id
     * @throws DevException
     */
    public function setUserId($user_id = false)
    {
        if (!$this->shop_id) {
            throw new DevException('shop_id nicht festgelegt');
        }

        //flag setzen das editor geladen wurde
        $this->editor_is_set = true;

        //prüfen ob aufruf ohne parameter erfolgte... wenn ja $user_id  aus session hollen
        if ($user_id === false && isset($_SESSION['editor'])) {
            $user_id = $_SESSION['editor'];
        }

        if ($user_id == 'system' or !$user_id) {
            $daten = self::loadSystemEditor($this->shop_id);
        } else {
            $daten = self::loadEditor($user_id, $this->shop_id);
        }

        //überbleibsel
        $daten['mitarbeiter_shop_id'] = $this->shop_id;

        $this->assignArray($daten);
    }

    /**
     * Läde die Daten des Bearbeiters
     * @param int $user_id
     * @param int $shop_id
     * @return array
     */
    private static function loadEditor($user_id, $shop_id)
    {
        $user_id = (int)$user_id;
        $shop_id = (int)$shop_id;

        $daten = db::getInstance()->singleQuery("
            SELECT
                user_accounts.anrede AS bearbeiter_anrede,
                user_accounts.firstname AS bearbeiter_vorname,
                user_accounts.surname AS bearbeiter_name,
                user_accounts.email AS bearbeiter_email,
                user_accounts.telefon AS bearbeiter_tel,
                user_accounts.signatur_endkunde,
                user_accounts.signatur_einkauf,
                user_accounts.shop_id,
                user_accounts.shop_id AS mitarbeiter_shop_id
            FROM
                user_accounts
            WHERE
                user_accounts.user_id = '$user_id' AND
                user_accounts.shop_id = '$shop_id'
        ");

        $shop = Shop::getInstance($shop_id);
        $daten['system_signatur'] = $shop->getMailSignature();
        if (empty($daten['signatur_endkunde'])) {
            $daten['signatur_endkunde'] = $daten['system_signatur'];
        }
        if (empty($daten['signatur_einkauf'])) {
            $daten['signatur_einkauf'] = $daten['signatur_endkunde'];
        }

        $daten['signatur'] = $daten['signatur_endkunde'];

        return $daten;
    }

    /**
     * Lädt die Daten eines Anonomyn bearbeiters für den entsrpechenden Shop
     * @param int $shop_id
     * @return array
     */
    private static function loadSystemEditor($shop_id)
    {
        $shop = Shop::getInstance($shop_id);

        $daten = [
            'bearbeiter_anrede' => '',
            'bearbeiter_vorname' => '',
            'bearbeiter_name' => $shop->getShortName(),
            'bearbeiter_email' => $shop->getEmail(),
            'bearbeiter_tel' => $shop->getHotline()
        ];

        $daten['signatur'] = $shop->getMailSignature();
        $daten['system_signatur'] = $daten['signatur'];
        $daten['signatur_einkauf'] = $daten['signatur'];
        $daten['signatur_endkunde'] = $daten['signatur'];

        return $daten;
    }

    /**
     * Lädt die Daten des Bearbeiters
     * ACHTUNG: setzt die shop_id falls das noch nicht passiert ist, shop_id wird aus der Sesssion genommen
     * @deprecated nur noch mail::setEditor verwenden
     */
    public function bearbeiter()
    {
        if (!$this->shop_id) {
            $this->prase_daten['shop_id'] = $_SESSION['shop_id'];
        }
        $this->setUserId();
    }

    /**
     * Fügt einen neuen Wert für den Parser hinzu
     * @param string $key
     * @param string $value
     */
    public function assign($key, $value)
    {
        $this->prase_daten[$key] = $value;
    }

    /**
     * Fügt Werte aus ein assoziative Array den Parser hinzu
     * @param array $daten
     */
    public function assignArray(array $daten)
    {
        foreach ($daten as $key => $value) {
            $this->assign($key, $value);
        }
    }

    /**
     * Lädt eine Vorlage
     * @param string $mail_id
     * @return bool
     * @throws FileNotFoundException
     * @throws DevException
     */
    public function loadVorlage($mail_id)
    {
        if (!$this->shop_id) {
            throw new DevException('shop_id nicht festgelegt');
        }

        try {
            $mail_template = $this->mail_template_repository->loadMailTemplate($mail_id, $this->shop_id);
        } catch (SmartDataEntityNotFoundException $smart_data_entity_not_found_exception) {
            return false;
        }

        $this->mail_daten['absender_name'] = $mail_template->getAbsenderName();
        $this->mail_daten['absender_mail'] = $mail_template->getAbsenderMail();
        $this->mail_daten['betreff'] = $mail_template->getBetreff();
        $this->mail_daten['text_mail'] = $mail_template->getTextMail();
        $this->mail_daten['customer_memo_vorlage'] = $mail_template->getTextCustomerMemo();

        foreach ($mail_template->getAttachments() as $attachment) {
            $content = $this->fetchAttachmentContent($attachment['path']);
            $this->addAttachmentString($content, $attachment['name']);
        }

        if (!$this->editor_is_set) {
            $this->setUserId();
        }
        $this->parse();

        return (bool)$this->mail_daten;
    }

    public function setShopId($shop_id)
    {
        $this->prase_daten['shop_id'] = $shop_id;
        $this->shop_id = $shop_id;
    }

    public function parse()
    {
        $this->mail_daten['text_mail'] = $this->_parse($this->mail_daten['text_mail']);
        $this->mail_daten['customer_memo_vorlage'] = ($this->_parse($this->mail_daten['customer_memo_vorlage'] ?? ''));
        $this->mail_daten['absender_name'] = $this->_parse($this->mail_daten['absender_name']);
        $this->mail_daten['absender_mail'] = $this->_parse($this->mail_daten['absender_mail']);
        $this->mail_daten['betreff'] = $this->_parse($this->mail_daten['betreff']);
    }

    public function getMailBody()
    {
        return $this->mail_daten['text_mail'];
    }

    public function setMailBody($body)
    {
        $this->mail_daten['text_mail'] = $body;
    }

    public function getMailCustomerMemoTemplate(): ?string
    {
        return $this->mail_daten['customer_memo_vorlage'];
    }

    public function setMailCustomerMemoTemplate(string $customer_memo_vorlage): void
    {
        $this->mail_daten['customer_memo_vorlage'] = $customer_memo_vorlage;
    }

    public function getBetreff()
    {
        return $this->mail_daten['betreff'];
    }

    public function setBetreff($betreff)
    {
        $this->mail_daten['betreff'] = $betreff;
    }


    /**
     * legt fest ob im frontend getInlineEdit() eine Option angeboten werden soll zum senden/nicht senden
     *
     * ACHTUNG: diese Option wird nicht von dieser Klasse berücksichtigt!
     *
     * @param $optional_send
     */
    public function setOptionalSend($optional_send, $default = true)
    {
        $this->mail_daten['send_mail'] = (bool)$default;

        $this->form_optional_send = (bool)$optional_send;
    }

    public function shouldSend()
    {
        return $this->mail_daten['send_mail'];
    }

    //erzeugt formular elemente für mail bearbeitung
    public function get_inline_edit($name = false, $hide = false, $css_class = 'mail_editor')
    {
        if ($name === false) {
            $name = $this->form_name;
        }

        $return = '';

        if (!$this->reciver) {
            $this->reciver = $this->mail_daten['empfaenger_mail'];
        }

        $hide = $hide ? ' display: none;' : 'display: inline-block;';
        $return .= '<fieldset style="' . $hide . '; background-color: #fff; margin: 6px;" id="' . $name . '" class="mail_editor  ' . $css_class . '">';
        $return .= '<input type="hidden" name="' . $name . '[prase_daten]" value="' . base64_encode(json_encode($this->prase_daten)) . '" />';
        $return .= '<table>';
        $return .= '<tr>';
        $return .= '<td>Emfänger:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][empfaenger_mail]" value="' . $this->reciver . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>Absendername:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][absender_name]" value="' . $this->mail_daten['absender_name'] . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>Absenderemail:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][absender_mail]" value="' . $this->mail_daten['absender_mail'] . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>Betreff:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][betreff]" value="' . $this->mail_daten['betreff'] . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>E-Mail</td>';
        $return .= '<td><textarea name="' . $name . '[mail_daten][text_mail]" rows="20" cols="80">' . $this->mail_daten['text_mail'] . '</textarea></td>';
        $return .= '</tr>';
        $return .= '</table>';
        $return .= '</fieldset>';

        return $return;
    }

    public function getInlineEdit($name = false, $hide = false, $css_class = 'mail_editor')
    {
        if ($name === false) {
            $name = $this->form_name;
        }

        $return = '';

        if (!$this->reciver) {
            $this->reciver = $this->mail_daten['empfaenger_mail'];
        }

        $hide = $hide ? ' display: none;' : 'display: inline-block;';

        $style = 'style="' . $hide . '; background-color: #fff; margin: 6px;"';
        if ($css_class == 'form_element_mail_editor_form') {
            $style = '';
        }
        $return .= '<fieldset ' . $style . ' id="' . $name . '" class="mail_editor ' . $css_class . '">';
        if ($this->form_legend) {
            $return .= '<legend>' . $this->form_legend . '</legend>';
        }
        $return .= '<input type="hidden" name="' . $name . '[prase_daten]" value="' . base64_encode(json_encode($this->prase_daten)) . '" />';


        $show = true;

        if ($this->form_optional_send) {
            if (!$this->mail_daten['send_mail']) {
                $show = false;
            }

            $return .= '<div class="row">';
            $return .= '<label>Mail senden</label>';

            $checked = $this->mail_daten['send_mail'] ? 'checked="checked"' : '';

            $return .= '<input type="checkbox" name="' . $name . '[mail_daten][send_mail]" value="1" ' . $checked . ' onchange="this.checked?jQuery(\'#' . $name . '_inner\').show():jQuery(\'#' . $name . '_inner\').hide();">';

            $return .= '<input type="hidden" name="' . $name . '[mail_daten][send_mail_input]" value="1">';
            $return .= '</div>';
        }

        $show = $show ? '' : ' style="display: none;"';

        $return .= '<div id="' . $name . '_inner"' . $show . '>';
        $return .= '<div class="row">';
        $return .= '<label>Emfänger</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][empfaenger_mail]" value="' . $this->reciver . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>Absendername</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][absender_name]" value="' . $this->mail_daten['absender_name'] . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>Absenderemail</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][absender_mail]" value="' . $this->mail_daten['absender_mail'] . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>Betreff</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][betreff]" value="' . $this->mail_daten['betreff'] . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>E-Mail</label>';
        $return .= '<textarea name="' . $name . '[mail_daten][text_mail]" rows="20" cols="80">' . $this->mail_daten['text_mail'] . '</textarea>';
        $return .= '</div>';
        $return .= '</div>';

        $return .= '</fieldset>';

        return $return;
    }


    public function __toString()
    {
        return $this->get_inline_edit();
    }

    protected function _parse(string $string): string
    {
        if (!preg_match_all('#<!--(.*?)/-->#', $string, $daten)) {
            return $string;
        }

        $rerun = false;

        foreach ($daten[1] as $placeholder) {
            $type = 'variable';
            $pattern_value = $placeholder;

            if (strpos($placeholder, ':')) {
                list($type, $pattern_value) = explode(':', $placeholder);
            }

            $type_resolved = false;

            foreach ($this->placeholder_resolver as $resolver) {
                if ($resolver->getType() === $type) {
                    $type_resolved = true;

                    $value = $resolver->resolve($pattern_value);

                    if ($value !== null) {
                        $string = str_replace("<!--$placeholder/-->", $value, $string);

                        if (strpos($value, '<!--') !== false) {
                            $rerun = true;
                        }
                        break;
                    }
                }
            }

            if (!$type_resolved) {
                throw new FatalException('unknown placeholder ' . $placeholder);
            }
        }

        if ($rerun) {
            $string = $this->_parse($string);
        }

        return $string;
    }

    /**
     * Senden die Mail, speichert die Mail in der DB und gibt den Status als Bool zurück
     * @param bool $exceptions aktiviert das Exception handlung
     * @return bool Status
     * @throws Exception
     */
    public function send($exceptions = false)
    {
        if (!$this->reciver) {
            $this->reciver = $this->mail_daten['empfaenger_mail'];
        }


        if (str_contains($this->reciver, '<EMAIL>')) {
            //mitigation für 0815 galaxus bestellungen
            return false;
        }

        //letztes mal parsen
        $this->parse();

        $mailer = service_loader::getSymfonyMailer($this->mail_daten['absender_mail']);

        $message = new Email();
        $message->from(new SymfonyAddress($this->mail_daten['absender_mail'], $this->mail_daten['absender_name']));

        $recivers = [];

        if (config::getLegacy('system')->debug_mail_redirecting) {
            $recivers[] = config::getLegacy('system')->debug_mail_redirecting;
        } else {
            $recivers[] = $this->reciver;
        }

        if ($this->recivers) {
            foreach ($this->recivers as $reciver) {
                $recivers[] = $reciver;
            }
        }

        try {
            $message->to(...$recivers);
        } catch (RfcComplianceException $e) {
            //teilweise werden ungültige mail adressen genutzt das mails nicht rausgehen
            return false;
        }

        //ccs anfügen
        if (isset($this->mail_daten['cc_empfaenger']) && is_array($this->mail_daten['cc_empfaenger'])) {
            $message->cc(...$this->mail_daten['cc_empfaenger']);
        }

        if (isset($this->mail_daten['bcc_empfaenger']) && is_array($this->mail_daten['bcc_empfaenger'])) {
            $message->bcc(...$this->mail_daten['bcc_empfaenger']);
        }

        if ($this->reply_to) {
            $message->replyTo($this->reply_to);
        }

        $message->subject($this->mail_daten['betreff']);

        if ($this->body_is_html) {
            $body = $this->mail_daten['text_mail'];
        } else {
            $body = $this->text2html($this->mail_daten['text_mail'], $this->prase_daten['shop_id']);
        }

        $message->html($body);

        if (count($this->attachments) > 0) {
            foreach ($this->attachments as $att) {
                if (array_key_exists('file', $att)) {
                    $message->addPart(new DataPart(new SymfonyFile($att['file'], $att['name'])));
                } elseif (array_key_exists('string', $att)) {
                    $message->addPart(new DataPart($att['string'], $att['name']));
                }
            }
        }

        try {
            $mailer->send($message);
        } catch (TransportExceptionInterface $e) {
            if ($exceptions) {
                throw $e;
            }

            return false;
        }

        if ($this->auto_add_to_mailarchiv) {
            if (isset($this->prase_daten['customer_id']) && $this->prase_daten['customer_id']) {
                $mail = new MailArchivMail();
                $mail->setDatum(new DateObj());
                $mail->setOrderId($this->prase_daten['order_id']);
                $mail->setCustomerId($this->prase_daten['customer_id']);
                $mail->setEmailSender($this->mail_daten['absender_mail']);
                $mail->setEmailReciver($this->reciver);
                $mail->setBetreff($this->mail_daten['betreff']);
                $mail->setBody($this->mail_daten['text_mail']);
                $mail->setUserId(env::getUserId());
                $mail->save();
            }
        }

        return true;
    }

    /**
     * @return string
     * @todo das ist jetzt stark zusammengeschustert... wollte das aber nicht noch nach draus ziehen. (code ist doppelt in send() zu finden...)
     *
     */
    public function getMailBodyAsHtml(): string
    {
        if ($this->body_is_html) {
            $body = $this->mail_daten['text_mail'];
        } else {
            $body = $this->text2html($this->mail_daten['text_mail'], $this->prase_daten['shop_id']);
        }

        return $body;
    }

    //static helper function
    protected function text2html($string, $shop_id)
    {
        /*if(string_utils::isHtml($string)) {
            return $string;
        }*/

        $html = '';

        $string = preg_replace("~^(   |    )~m", '&nbsp; &nbsp; ', $string);

        if ($shop_id == Shop::ALLEGO) {
            $html .= '<html>';
            $html .= '<head>';
            $html .= '<style type="text/css">';
            $html .= 'body { background-color: #fff; font-family:Arial, Helvetica, sans-serif; }';
            $html .= '#logo { float: right; width: 240px; }';
            $html .= '</style>';
            $html .= '</head>';
            $html .= '<body>';
            $html .= '<img src="https://www.smartgoods.de/assets/logo_smartgoods.png" id="logo"><br><br style="clear:both;">';
            $html .= nl2br($this->replaceUrlsToLinks($string));
            $html .= '</body>';
            $html .= '</html>';
        } else {
            $html .= '<html>';
            $html .= '<head>';
            $html .= '<style type="text/css">';
            $html .= 'body { background-color: #fff; font-family:Arial, Helvetica, sans-serif; }';
            $html .= '</style>';
            $html .= '</head>';
            $html .= '<body>';
            $html .= nl2br($this->replaceUrlsToLinks($string));
            $html .= '</body>';
            $html .= '</html>';
        }

        return $html;
    }

    public function replaceUrlsToLinks($text)
    {
        $text = preg_replace_callback('~http://([-=_.a-z0-9!#&/\?+%@]*)~i', function ($token) {
            $url = $token[0];

            $domain = substr($url, 7);
            $pos = strpos($domain, '/');
            if ($pos) {
                $domain = substr($domain, 0, $pos);
            }
            return '<a href="' . $url . '">' . $domain . '</a>';
        }, $text);

        $text = preg_replace_callback('~https://([-=_.a-z0-9!#&/\?+%@]*)~i', function ($token) {
            $url = $token[0];

            $domain = substr($url, 8);
            $pos = strpos($domain, '/');
            if ($pos) {
                $domain = substr($domain, 0, $pos);
            }
            return '<a href="' . $url . '">' . $domain . '</a>';
        }, $text);

        return $text;
    }

    private function widerruf2html($string)
    {
        $string = '<div style="border: 2px solid #000; padding: 10px;">' . trim(preg_replace('/# (.*) #/', '\\1', $string)) . '</div>';

        return $string;
    }

    public function addAnschrift($anschrift)
    {
        $this->prase_daten['anschrift'] = $anschrift;
    }

    public function printer($shop_id = '', $params = [])
    {
        $this->parse();
        echo '<body>';
        /*
        echo '<script type="text/javascript">';
            echo 'window.open("/LIB/new_mail.php?cmd=print&mail=&shop_id='.$shop_id.'","printer","height=600,width=680,menubar=yes,resizable=yes,scrollbars=yes,status=yes,toolbar=yes");';
        echo '</script>';
        */


        /*if(isset($_REQUEST['cmd']) AND $_REQUEST['cmd']=='print') {
            include('bootstrap.php');
            $mail = unserialize(base64_decode(str_replace(' ','',$_POST['mail'])));
            $mail->doPrint($_POST['shop_id'],unserialize(base64_decode($_POST['params'])));
        }*/

        throw new DevException('leagancy code -> print funktion "controller" direkt im lib integriert!');

        echo '<form method="post" action="/LIB/business_logic/mails/new_mail.php?cmd=print" target="printer" id="printerform">';
        echo '<input type="hidden" name="mail" value="' . base64_encode(serialize($this)) . '" />';
        echo '<input type="hidden" name="shop_id" value="' . $shop_id . '" />';
        echo '<input type="hidden" name="params" value="' . base64_encode(serialize($params)) . '" />';
        echo '</form>';

        echo '<script type="text/javascript">';
        echo 'window.open("about:blank","printer","height=600,width=680,menubar=yes,resizable=yes,scrollbars=yes,status=yes,toolbar=yes");';
        echo 'document.getElementById("printerform").submit();';
        echo '</script>';
        echo '</body>';
        return true;
    }

    public function doPrint($shop_id = '', $params = [])
    {
        if ($shop_id == '') {
            $shop_id = $this->prase_daten['mitarbeiter_shop_id'];
        }

        $this->mail_daten['text_mail'] = strip_tags($this->mail_daten['text_mail']);

        //text
        $text = explode("\n", $this->mail_daten['text_mail']);

        $zpp = 40;

        $seiten_max = ceil(count($text) / $zpp);

        $shop = Shop::getInstance($shop_id);
        $footer_daten = $shop->getDocumentSignature();

        switch ($params['absender']) {
            case 'service':
                $shop_adr = '<u style="font-size: 11px;">' . $shop->getServiceAddress()->format(Address::FORMAT_SINGLE_LINE) . '</u>';
                break;
            default:
                $shop_adr = '<u style="font-size: 11px;">' . $shop->getAddress()->format(Address::FORMAT_SINGLE_LINE) . '</u>';
        }

        $footer = '<br /><br /><hr />';
        $footer .= DocumentRepository::getHtmlFooter($shop->getShopId());
        $footer .= '<div style=" page-break-after:always;"></div>';

        echo '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">';
        echo '<html>';
        echo '<head>';
        echo '<title>Drucken</title>';
        echo '<link REL="STYLESHEET" TYPE="text/css" HREF="/res/images/rechlogo/1.0.css">';
        echo '<style type="text/css">
                    @media screen {
                        body {
                            margin: 0;
                            font-size: 14px;
                            background-color: #ccc;
                        }

                        .page {
                            background-color: #fff;
                            padding: 40px;

                            position: relative;
                            top: -10px;
                            left: -10px;
                        }

                        .page_shadow {
                            margin-top: 20px;
                            margin-bottom: 20px;

                            background-color: #444;


                            position: relative;
                            top: 10px;
                            left: 10px;

                        }
                    }

                    @media print {
                        body {
                            font-size: 14px;
                        }
                    }
                      </style>';
        echo '</head>';
        echo '<body style="margin-left: 2.5%; margin-right: 2.5%;">';

        $cur_line = 0;
        for ($seite_akt = 0; $seite_akt <= $seiten_max - 1; $seite_akt++) {
            echo '<div class="page_shadow">';
            echo '<div class="page">';
            echo '<div style="float: right;">';
            echo '<img src="/res/images/logos/invoice_' . $shop_id . '_1.0.jpg" width="160" border="0" />';
            echo '<div style="text-align: center; font-size: 11px;">';
            echo 'Seite ' . ($seite_akt + 1) . ' von ' . ($seiten_max);
            echo '</div>';
            echo '</div>';


            if ($seite_akt == 0) {
                echo '<br /><br /><br />';
                echo $shop_adr;
                echo '<br /><br />';
                echo nl2br($this->prase_daten['anschrift']);

                echo '<br /><br /><br /><br />';

                echo '<div style="float: right;">Dresden, den ' . date('d.m.Y') . '</div><br /><br />';
                echo '<b>' . $this->mail_daten['betreff'] . '</b>';

                echo '<br /><br />';
            } else {
                echo '<br /><br /><br /><br /><br /><br /><br /><br /><br />';
            }

            $zpp_ = $seite_akt == 0 ? $zpp - 8 : $zpp;

            for ($i = 0; $i <= $zpp_; $i++) {
                echo preg_replace("~^(    |  )~", "&nbsp; &nbsp; &nbsp;", $text[$cur_line++]) . '<br />';
            }

            echo $footer;
            echo '</div>';
            echo '</div>';
        }


        if ($this->auto_add_to_mailarchiv) {
            if ($this->prase_daten['customer_id']) {
                $mail = new MailArchivMail();
                $mail->setDatum(new DateObj());
                $mail->setOrderId($this->prase_daten['order_id']);
                $mail->setCustomerId($this->prase_daten['customer_id']);
                $mail->setEmailSender('per Post');
                $mail->setEmailReciver('per Post / ' . $this->reciver);
                $mail->setBetreff($this->mail_daten['betreff']);
                $mail->setBody($this->mail_daten['text_mail']);
                $mail->setUserId(env::getUserId());
                $mail->save();
            }
        }
    }

    /**
     * Setzt den Empfänger
     * @param string $email_adresse
     */
    public function setEmpf($email_adresse)
    {
        $this->mail_daten['empfaenger_mail'] = $email_adresse;

        $this->reciver = $email_adresse;
    }

    public function addEmpf($address)
    {
        if (!$this->reciver) {
            $this->setEmpf($address);
        } else {
            $this->recivers[] = $address;
        }
    }

    public function getReciverEmail(): string
    {
        return $this->reciver ?: $this->mail_daten['empfaenger_mail'];
    }

    public function setEmpfByConfigString($address_string)
    {
        $dest = MailUtils::parseAddressConfigString($address_string);

        foreach ($dest['to'] as $address) {
            $this->addEmpf($address);
        }

        foreach ($dest['cc'] as $address) {
            $this->addCCEmpf($address);
        }

        foreach ($dest['bcc'] as $address) {
            $this->addBCCEmpf($address);
        }
    }

    public function setCCEmpf($email_adresse)
    {
        $this->addCCEmpf($email_adresse);
    }

    public function addCCEmpf($address)
    {
        if (!$this->mail_daten['cc_empfaenger']) {
            $this->mail_daten['cc_empfaenger'] = [];
        }

        $this->mail_daten['cc_empfaenger'][] = $address;
    }

    public function addBCCEmpf($address)
    {
        if (!$this->mail_daten['bcc_empfaenger']) {
            $this->mail_daten['bcc_empfaenger'] = [];
        }

        $this->mail_daten['bcc_empfaenger'][] = $address;
    }

    public function setAbsender($mail, $name = false)
    {
        $this->mail_daten['absender_mail'] = $mail;
        $this->mail_daten['absender_name'] = $name !== false ? $name : $mail;
    }

    public function setReplyTo($mail)
    {
        $this->reply_to = $mail;
    }

    public function addAttachment($file, $name)
    {
        $this->attachments[] = ['file' => $file, 'name' => $name];
    }

    public function addAttachmentString($string, $name)
    {
        $this->attachments[] = ['string' => $string, 'name' => $name];
    }

    public function hasAttachments(): bool
    {
        return count($this->attachments) > 0;
    }

    /**
     * @return array [filename, body]
     */
    public function getAttachmentsWithContent(): array
    {
        $result = [];

        foreach ($this->attachments as $att) {
            if (array_key_exists('file', $att)) {
                $result[] = ['filename' => $att['name'], 'body' => file_get_contents($att['file'])];
            } elseif (array_key_exists('string', $att)) {
                $result[] = ['filename' => $att['name'], 'body' => $att['string']];
            }
        }

        return $result;
    }

    /**
     * @return null
     */
    public function getFormLegend()
    {
        return $this->form_legend;
    }

    /**
     * @param null $form_legend
     */
    public function setFormLegend($form_legend)
    {
        $this->form_legend = $form_legend;
    }

    public function setAutoAddToMailarchiv($status)
    {
        $this->auto_add_to_mailarchiv = (bool)$status;
    }

    public function setSignatureType($signature_type)
    {
        $this->signatur = $signature_type;
    }

    /**
     * @param $attachment_path
     * @return string
     * @throws FileNotFoundException
     */
    public function fetchAttachmentContent($attachment_path)
    {
        if ($this->mail_template_repository->isStorageAttachment($attachment_path)) {
            return $this->mail_template_repository->fetchAttachmentContent($attachment_path);
        }

        throw new DevException('unknown attachment type');
    }

    public function getValue(string $key): mixed
    {
        return $this->prase_daten[$key] ?? null;
    }

    public function getShopId(): ?int
    {
        return $this->shop_id;
    }
}
