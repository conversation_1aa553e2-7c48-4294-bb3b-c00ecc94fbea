<?php

namespace wws\Mails;

class MailContentAnalyse
{
    public function isStorno(string $betreff, string $body): bool
    {
        return $this->checkStorno($betreff . ' ' . $body);
    }

    public function isWiderruf(string $betreff, string $body): bool
    {
        return $this->checkWiderruf($betreff . ' ' . $body);
    }

    public function checkStorno(string $message): bool
    {
        static $keywords = [
            'Stornierung',
            'storno',
            'Storno',
            'STORNO',
            'storniert',
            'Stornowunsch',
            'storniere',
            'Bestellstornierungs-Anforderung',
            'einen Kauf abbrechen'
        ];

        static $false_keywords = [
            'Ihr Auftrag wurde storniert',
            '(bitte stornieren Sie stattdessen',
            'Lieferverzug, Stornierungen und Retoureneingänge an:'
        ];

        $status = $this->areKeywordsInText($message, $keywords);

        if ($status) {
            $status = !$this->areKeywordsInText($message, $false_keywords);
        }

        return $status;
    }

    public function checkWiderruf(string $message): bool
    {
        static $keywords = [
            'Widerruf',
            'widerruf',
            'Wiederruf',
            'wiederruf',
            'Rücktritt vom Kaufvertrag',
            'Rückabwicklung'
        ];

        static $false_keywords = [
            'Widerrufsbelehrung und Widerrufsformular',
            'Ihrem Widerspruchsrecht Gebrauch machen und die Einwilligung'
        ];

        $status = $this->areKeywordsInText($message, $keywords);

        if ($status) {
            $status = !$this->areKeywordsInText($message, $false_keywords);
        }

        return $status;
    }


    private function areKeywordsInText(string $message, array $keywords, array $ignore = []): bool
    {
        if ($ignore) {
            $message = str_replace($ignore, '', $message);
        }

        foreach ($keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }
}
