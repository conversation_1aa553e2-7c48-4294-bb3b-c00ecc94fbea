<?php

namespace wws\Mails\Event;

use bqp\Event\Event;

abstract class EventMailOrder extends Event
{
    /**
     * @var int
     */
    private $order_id;

    /**
     * @var int
     */
    private $mail_id;

    public function __construct(int $mail_id, int $order_id)
    {
        $this->mail_id = $mail_id;
        $this->order_id = $order_id;
    }

    public function getMessage(): array
    {
        return ['mail_id' => $this->mail_id, 'order_id' => $this->order_id];
    }
}
