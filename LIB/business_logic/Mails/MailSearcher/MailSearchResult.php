<?php

namespace wws\Mails\MailSearcher;

class MailSearchResult
{
    /**
     * @var bool
     */
    private $abort = false;

    /**
     * @var int
     */
    private $customer_id = 0;

    /**
     * @var int
     */
    private $order_id = 0;

    /**
     * @var int
     */
    private $search_count = 0;

    public function isAbort(): bool
    {
        return $this->abort;
    }

    /**
     * search plugin kann den suchvorgang beenden
     */
    public function abort(): void
    {
        $this->abort = true;
    }

    public function isSuccess(): bool
    {
        return $this->getCustomerId() > 0;
    }

    /**
     * @return int
     */
    public function getOrderId(): int
    {
        return $this->order_id;
    }

    public function setOrderId(int $order_id): void
    {
        $this->order_id = $order_id;
    }

    public function getCustomerId(): int
    {
        return $this->customer_id;
    }

    public function setCustomerId(int $customer_id): void
    {
        $this->customer_id = $customer_id;
    }

    public function incSearchCount(): void
    {
        $this->search_count++;
    }
}