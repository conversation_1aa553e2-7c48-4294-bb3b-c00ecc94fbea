<?php

namespace wws\Mails\MailSearcher;

use bqp\db\db_generic;
use wws\Customer\CustomerConst;

class MailSearchPluginWwsCustomerMailAddress implements MailSearchPlugin
{
    private $shop_id;

    private db_generic $db;
    private array $domain_blacklist = [];

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function setShopId($shop_id): void
    {
        $this->shop_id = $shop_id;
    }

    public function setDomainBlackist($domain_blacklist): void
    {
        $this->domain_blacklist = $domain_blacklist;
    }

    public function isResponsible(MailSearcher $searcher): bool
    {
        return true;
    }

    public function isMailAddressBacklisted(string $mail_address): bool
    {
        $domain = substr(strstr($mail_address, '@'), 1);

        return in_array($domain, $this->domain_blacklist);
    }

    public function search(MailSearcher $searcher): void
    {
        $search_result = $searcher->getSearchResult();

        $mail_address = $searcher->getSenderEmailAddress();

        if ($this->isMailAddressBacklisted($mail_address)) {
            return;
        }

        if (
            $searcher->getSearchModus() === MailSearcher::SEARCH_MODUS_FULL ||
            $searcher->getSearchModus() === MailSearcher::SEARCH_MODUS_SENDER_MAIL ||
            $searcher->getSearchModus() === MailSearcher::SEARCH_MODUS_RECIVER_MAIL
        ) {
            if ($searcher->getSearchModus() === MailSearcher::SEARCH_MODUS_RECIVER_MAIL) {
                $mail_address = $searcher->getReciverEmailAddress();
            }

            //in kunden tabelle nach absender email adresse suchen
            if ($mail_address) {
                $this->searchByMailAddress($mail_address, $search_result);
            }
        }
    }

    public function searchByMailAddress(string $mail_address, MailSearchResult $search_result): void
    {
        $daten = $this->db->singleQuery("
                SELECT
                    customers.customer_id
                FROM
                    customers
                WHERE
                    customers.email LIKE '" . $this->db->escape($mail_address) . "' AND
                    customers.shop_id = " . (int)$this->shop_id . "
                ORDER BY
                    FIND_IN_SET(customer_status, '" . CustomerConst::CUSTOMER_STATUS_OK . "," . CustomerConst::CUSTOMER_STATUS_DELETE . "'),
                    customers.customer_id DESC
                LIMIT
                    1
        ");

        if ($daten) {
            $search_result->setCustomerId($daten['customer_id']);

            //wenn kunde nur eine bestellung hat und diese nicht älter als 3 monate
            //oder der kunden hat eine bestellung innerhalb der letzten stunde
            $daten = $this->db->singleQuery("
                    SELECT
                        MAX(orders.order_id) AS order_id,
                        COUNT(orders.order_id) AS quantity,
                        MAX(orders.added) > DATE_SUB(NOW(), INTERVAL 3 MONTH) AS is_fresh,
                        MAX(orders.added) > DATE_SUB(NOW(), INTERVAL 1 HOUR) AS is_from_last_hour
                    FROM
                        orders
                    WHERE
                        orders.customer_id = '" . $daten['customer_id'] . "'
                ");

            if ($daten['quantity'] == 1 and $daten['is_fresh']) {
                $search_result->setOrderId($daten['order_id']);
            } elseif ($daten['order_id'] and $daten['is_from_last_hour']) {
                $search_result->setOrderId($daten['order_id']);
            }
        }
    }
}