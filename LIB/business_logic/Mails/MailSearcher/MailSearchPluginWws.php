<?php

namespace wws\Mails\MailSearcher;

use bqp\db\db_generic;
use db;
use order_repository;

class MailSearchPluginWws implements MailSearchPlugin
{
    private $shop_id;
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function setShopId($shop_id)
    {
        $this->shop_id = $shop_id;
    }

    public function isResponsible(MailSearcher $searcher): bool
    {
        return true;
    }

    public function search(MailSearcher $searcher): void
    {
        $search_result = $searcher->getSearchResult();

        // Im Betreff nach Kunden oder Auftragsnummer suchen
        if ($searcher->getSubject()) {
            $result = order_repository::searchAuftnrCustomerIdInTextTolerantAutoFill($searcher->getSubject());

            /**
             * Wenn Kundennummer und Auftragsnummer gefunden wurden
             * prüfen ob diese im Zusammenhang existieren
             */
            if ($this->isValidCustomerIdWithAuftNr($result['customer_id'], $result['auftnr'])) {
                $search_result->setCustomerId($result['customer_id']);
                $this->setOrderIdAndCustomerIdByAuftnr($search_result, $result['auftnr']);
                return;
            }

            /**
             * Ansonsten nur CustomerId oder Auftnr/Orderid zuweisen
             */
            if ($this->isValidCustomerId((int)$result['customer_id'])) {
                $search_result->setCustomerId($result['customer_id']);
            } elseif ($this->isValidAuftnr($result['auftnr'])) {
                $this->setOrderIdAndCustomerIdByAuftnr($search_result, $result['auftnr']);
            }
        }

        // Im Body suchen
        $body = $searcher->getBody();

        if ($body) {
            $body = preg_replace('~(HRB|HRA)(?: )[0-9]{6}\b~', '', $body); //handelsregister nummern raus kicken -> wird sonst u.U. als kundennummer genommen

            $result = order_repository::searchAuftnrCustomerIdInTextTolerantAutoFill($body);

            if ($this->isValidCustomerId($result['customer_id'])) {
                $search_result->setCustomerId($result['customer_id']);
            }

            if ($this->isValidAuftnr($result['auftnr'])) {
                $this->setOrderIdAndCustomerIdByAuftnr($search_result, $result['auftnr']);
            }
        }
    }

    private function isValidCustomerIdWithAuftNr($customer_id, $auftnr): bool
    {
        if (!$customer_id || !$auftnr) {
            return false;
        }

        $result = $this->db->prepare("
            SELECT
                customers.customer_id
            FROM
                customers INNER JOIN
                orders ON(orders.customer_id=customers.customer_id)
            WHERE
                customers.customer_id = :customer_id AND
                orders.auftnr = :auftnr AND
                customers.shop_id = :shop_id
            ")->executeField([
                'customer_id' => (int)$customer_id,
                'auftnr' => $auftnr,
                'shop_id' => (int)$this->shop_id,
            ]
        );

        return $result ? true : false;
    }

    private function setOrderIdAndCustomerIdByAuftnr(MailSearchResult $mail_search_result, string $auftnr): void
    {
        $row = $this->db->singleQuery("
            SELECT
                orders.order_id,
                orders.customer_id
            FROM
                orders
            WHERE
                orders.auftnr = '" . db::getInstance()->escape($auftnr) . "'
        ");

        if (!$row) {
            return;
        }

        $mail_search_result->setCustomerId($row['customer_id']);
        $mail_search_result->setOrderId($row['order_id']);
    }


    private function isValidCustomerId($customer_id): bool
    {
        if (!$customer_id) {
            return false;
        }

        $result = $this->db->prepare("
            SELECT
                customers.customer_id
            FROM
                customers
            WHERE
                customers.customer_id = :customer_id AND
                customers.shop_id = :shop_id
        ")->executeField([
            'customer_id' => (int)$customer_id,
            'shop_id' => (int)$this->shop_id,
        ]);

        return $result ? true : false;
    }


    private function isValidAuftnr($auftnr): bool
    {
        if (!$auftnr) {
            return false;
        }

        $result = $this->db->prepare("
            SELECT
                orders.order_id,
                orders.customer_id
            FROM
                orders
            WHERE
                orders.auftnr = :auftnr AND 
                orders.shop_id = :shop_id
            ")->executeField([
                'auftnr' => $auftnr,
                'shop_id' => (int)$this->shop_id,
            ]
        );

        return $result ? true : false;
    }
}