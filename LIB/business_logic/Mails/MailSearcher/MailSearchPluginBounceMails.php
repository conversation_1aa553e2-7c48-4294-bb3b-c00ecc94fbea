<?php

namespace wws\Mails\MailSearcher;

use bqp\db\db_generic;

class MailSearchPluginBounceMails implements MailSearchPlugin
{
    private db_generic $db;
    private MailSearchPluginWwsCustomerMailAddress $wws_mail_searcher;

    public function __construct(db_generic $db, MailSearchPluginWwsCustomerMailAddress $wws_mail_searcher)
    {
        $this->db = $db;
        $this->wws_mail_searcher = $wws_mail_searcher;
    }

    public function isResponsible(MailSearcher $searcher): bool
    {
        return strpos($searcher->getSubject(), 'Undelivered Mail Returned to Sender') !== false && strpos($searcher->getSenderEmailAddress(), 'ecom-dresden.de') !== false;
    }


    public function search(MailSearcher $searcher): void
    {
        $body = $searcher->getBody();

        $pos = strpos($body, 'The mail system');

        if ($pos === false) {
            return;
        }

        $body = trim(substr($body, $pos + 15));

        if (!preg_match('~<([^@]{3,32}@[^>]{4,})>~', $body, $match)) {
            return;
        }

        $mail = $match[1];

        $this->wws_mail_searcher->searchByMailAddress($mail, $searcher->getSearchResult());
    }
}