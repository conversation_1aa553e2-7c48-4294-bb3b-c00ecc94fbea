<?php

namespace wws\Mails\MailSearcher;

use bqp\extern\Amazon\Wws\MailSearchPluginAmazon;
use bqp\extern\Check24\MailSearchPluginCheck24;
use bqp\extern\Concardis\MailSearchPluginConcardis;
use bqp\extern\Crowdfox\MailSearchPluginCrowdfox;
use bqp\extern\Ebay\MailSearchPluginEbay;
use bqp\extern\idealo\MailSearchPluginIdealo;
use bqp\extern\Kaufland\MailSearchPluginKaufland;
use bqp\extern\Paypal\MailSearchPluginPaypal;
use db;
use env;

class MailSearcherWws extends MailSearcher
{
    protected ?int $shop_id = null;

    public function __construct()
    {
        parent::__construct();

        $db = db::getInstance();

        if (env::isK11()) {
            $this->addSearchPlugin(new MailSearchPluginAmazon($db));
            $this->addSearchPlugin(new MailSearchPluginWws($db));
            $this->addSearchPlugin(new MailSearchPluginEbay($db));
            $this->addSearchPlugin(new MailSearchPluginPaypal($db));
        }

        if (env::isEcom()) {
            $this->addSearchPlugin(new MailSearchPluginAmazon($db));
            $this->addSearchPlugin(new MailSearchPluginCheck24($db));
            $this->addSearchPlugin(new MailSearchPluginCrowdfox($db));
            $this->addSearchPlugin(new MailSearchPluginWws($db));
            $this->addSearchPlugin(new MailSearchPluginEbay($db));
            $this->addSearchPlugin(new MailSearchPluginConcardis($db));
            $this->addSearchPlugin(new MailSearchPluginPaypal($db));
            $this->addSearchPlugin(new MailSearchPluginKaufland($db));
            $this->addSearchPlugin(new MailSearchPluginIdealo($db));
        }

        $mail_address_plugin = new MailSearchPluginWwsCustomerMailAddress($db);
        $mail_address_plugin->setDomainBlackist([
            'allego.de',
            'smartgoods.de',
            'ersatzteilshop.de'
        ]);
        $this->addSearchPlugin($mail_address_plugin);

        $this->addSearchPlugin(new MailSearchPluginBounceMails($db, $mail_address_plugin));
    }

    public function setShopId(int $shop_id): void
    {
        $this->shop_id = $shop_id;

        $this->refreshShopIdInPlugins();
    }

    private function refreshShopIdInPlugins(): void
    {
        foreach ($this->search_plugins as $plugin) {
            if (method_exists($plugin, 'setShopId')) {
                $plugin->setShopId($this->shop_id);
            }
        }
    }
}
