<?php

namespace wws\Mails\MailSearcher;

use bqp\Mail\MimeMail;
use bqp\Utils\MailUtils;
use wws\Mailsystem\MailsystemMail;

/**
 * versucht anhand von maildaten ein auftrag und kunden zu finden
 */
class MailSearcher
{
    const SEARCH_MODUS_FULL = 'full';
    const SEARCH_MODUS_WITHOUT_MAIL = 'without_mail';
    const SEARCH_MODUS_SENDER_MAIL = 'sender_mail';
    const SEARCH_MODUS_RECIVER_MAIL = 'reciver_mail';

    const DIRECTION_IN = 'in';
    const DIRECTION_OUT = 'out';

    protected $direction = self::DIRECTION_IN;
    protected $reciver_email_address;
    protected $sender_email_address;
    protected $subject;
    protected $body;

    protected $search_modus = self::SEARCH_MODUS_FULL;

    /**
     * @var MailSearchResult
     */
    protected $search_result;

    /**
     * @var MailSearchPlugin[]
     */
    protected $search_plugins = [];

    public function __construct()
    {
        $this->search_result = new MailSearchResult();
    }

    public function addSearchPlugin(MailSearchPlugin $plugin)
    {
        $this->search_plugins[] = $plugin;
    }

    public function setByMimeMail(MimeMail $mail)
    {
        $this->setSenderEmailAddress($mail->getSenderMail());
        $this->setReciverEmailAddress($mail->getEmpfaengerMail());
        $this->setBody($mail->getBody());
        $this->setSubject($mail->getBetreff());
    }

    public function setByMailsystemMail(MailsystemMail $mail)
    {
        $this->setReciverEmailAddress(MailUtils::extractMailAddress($mail->getEmpfaenger()));
        $this->setSenderEmailAddress($mail->getSenderEmail());
        $this->setBody($mail->getBody());
        $this->setSubject($mail->getBetreff());
    }

    /**
     * @return MailSearchResult
     */
    public function getSearchResult()
    {
        return $this->search_result;
    }

    /**
     * @return MailSearchResult
     */
    public function search()
    {
        foreach ($this->search_plugins as $plugin) {
            if (!$plugin->isResponsible($this)) {
                continue;
            }

            $plugin->search($this);

            if ($this->search_result->isAbort()) {
                break;
            }
        }

        return $this->search_result;
    }


    /**
     * @return mixed
     */
    public function getReciverEmailAddress()
    {
        return $this->reciver_email_address;
    }

    /**
     * @param mixed $reciver_email_address
     */
    public function setReciverEmailAddress($reciver_email_address)
    {
        $this->reciver_email_address = $reciver_email_address;
    }

    /**
     * @return mixed
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * @param mixed $subject
     */
    public function setSubject($subject)
    {
        $this->subject = $subject;
    }

    /**
     * @return mixed
     */
    public function getBody()
    {
        return $this->body;
    }

    /**
     * @param mixed $body
     */
    public function setBody($body)
    {
        $this->body = $body;
    }

    /**
     * @return mixed
     */
    public function getSenderEmailAddress()
    {
        return $this->sender_email_address;
    }

    /**
     * @param mixed $sender_email_address
     */
    public function setSenderEmailAddress($sender_email_address)
    {
        $this->sender_email_address = $sender_email_address;
    }

    /**
     * @return string
     */
    public function getSearchModus()
    {
        return $this->search_modus;
    }

    /**
     * @param string $search_modus
     */
    public function setSearchModus($search_modus)
    {
        $this->search_modus = $search_modus;
    }
}
