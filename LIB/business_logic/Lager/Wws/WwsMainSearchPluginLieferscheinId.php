<?php

namespace wws\Lager\Wws;

use bqp\db\db_generic;
use output;
use wws\core\WwsMainSearchPlugin;
use wws\Users\UserSession;

class WwsMainSearchPluginLieferscheinId implements WwsMainSearchPlugin
{
    private db_generic $db;
    private UserSession $user;

    public function __construct(db_generic $db, UserSession $user)
    {
        $this->db = $db;
        $this->user = $user;
    }

    public function searchWithRedirect(string $search_term): void
    {
        if (!preg_match("/^LID([0-9]{5,7})$/i", $search_term, $match)) {
            return;
        }

        $lieferschein_id = (int)$match[1];

        $order_id = $this->db->fieldQuery("
            SELECT
                warenausgang_lieferschein.order_id
            FROM
                warenausgang_lieferschein INNER JOIN
                orders ON (warenausgang_lieferschein.order_id = orders.order_id)
            WHERE
                warenausgang_lieferschein.lieferschein_id = " . $lieferschein_id . " AND
                orders.shop_id = '" . $this->user->getShopId() . "'
        ");

        if (!$order_id) {
            return;
        }

        output::redirect_iframe('/ax/auftraege/order_delivery/order_delivery_slips/?order_id=' . $order_id . '&lieferschein_id=' . $lieferschein_id);
    }
}
