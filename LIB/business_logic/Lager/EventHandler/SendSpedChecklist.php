<?php

namespace wws\Lager\EventHandler;

use bqp\db\db_generic;
use wws\Lager\Event\EventWarenausgangLieferscheinErledigt;
use wws\Lager\WarenausgangLieferschein;
use wws\Mails\Mail;
use wws\Shipment\ShipmentRepository;

class SendSpedChecklist
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function handleEvent(EventWarenausgangLieferscheinErledigt $event): void
    {
        $lieferschein = $event->getWarenausgangLieferschein();
        $this->process($lieferschein);
    }

    public function process(WarenausgangLieferschein $lieferschein): void
    {
        if (!ShipmentRepository::isSpedShipment($lieferschein)) {
            return;
        }

        $order = $lieferschein->getOrder();

        //sped checkliste
        $mail = new Mail();
        $mail->setOrder($order);
        $mail->loadVorlage('checkliste_sped');
        $mail->send();

        //waschmaschine/waschtrocker transportsicherung (nicht als getrennter event, weil die prüfung die gleichen sind)
        $product_ids = [];
        foreach ($order->getOrderItems() as $order_item) {
            $product_ids[] = $order_item->getProductId();
        }

        $quantity = $this->db->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                product
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ") AND
                product.cat_id IN (4, 5)
        ");

        if ($quantity > 0) {
            $mail = new Mail();
            $mail->setOrder($order);
            $mail->loadVorlage('versand_wama');
            $mail->send();
        }
    }
}
