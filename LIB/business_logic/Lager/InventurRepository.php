<?php

namespace wws\Lager;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;

class InventurRepository
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }


    public static function tableHelper_bestandsabweichung($daten, $key)
    {
        $abweichung = $daten[$key];

        if ($abweichung == 0) {
            return ['value' => '<b>' . $abweichung . '</b>'];
        }

        if ($abweichung > 0) {
            return ['value' => '<b>' . $abweichung . '</b>', 'style' => 'background-color: #00ff00;'];
        }

        if ($abweichung < 0) {
            return ['value' => '<b>' . $abweichung . '</b>', 'style' => 'background-color: red;'];
        }
    }

    /**
     * @param int $inventur_id
     * @return Inventur
     * @throws SmartDataEntityNotFoundException
     */
    public function load(int $inventur_id): Inventur
    {
        return new Inventur($inventur_id);
    }

    public function createNewInventur(int $lager_id, ?InventurFilter $filter): Inventur
    {
        if (!$filter) {
            $filter = new InventurFilter();
        }

        $description = 'Lager - ' . LagerRepository::getLagerName($lager_id);

        $filter_text = $filter->getAsText();
        if ($filter_text) {
            $description .= ' (Filter ' . $filter_text . ')';
        }

        //@todo über inventur entity erzeugen
        //inventur erzeugen
        $this->db->query("
            INSERT INTO
                inventur
            SET
                inventur.inventur_start = NOW(),
                inventur.inventur_status = 'active',
                inventur.lager_id = '$lager_id',
                inventur.description = '" . $this->db->escape($description) . "'
        ");

        $inventur_id = $this->db->insert_id();

        $where = '';
        if ($filter->hasBrandIds()) {
            $where .= ' AND product.brand_id IN (' . $this->db->in($filter->getBrandIds()) . ') ';
        }

        //alle bestände und ek preise
        $this->db->query("
            INSERT INTO
                inventur_bestand
                (
                    inventur_id,
                    product_id,
                    bestand_soll,
                    ek_netto
                )
            SELECT
                $inventur_id,
                product_lager_detail.product_id,
                product_lager_detail.lager_real,
                product_ek.ek_netto
            FROM
                product_lager_detail LEFT JOIN
                product_ek ON (product_lager_detail.product_id = product_ek.product_id AND product_ek.ek_fav = 1) INNER JOIN
                product ON (product_lager_detail.product_id = product.product_id) INNER JOIN
                product_warenkorb_types ON (product.product_warenkorb_typ = product_warenkorb_types.product_warenkorb_typ)
            WHERE
                product_lager_detail.lager_id = '$lager_id' AND
                product_lager_detail.lager_real != 0 AND
                product_warenkorb_types.bestand_count = 1
                $where
        ");

        return new Inventur($inventur_id);
    }
}
