<?php

namespace wws\Lager;

use db;

class LagerGroup
{
    protected $daten = [];

    public function __construct($lager_group_id = null)
    {
        if ($lager_group_id !== null) {
            $this->load($lager_group_id);
        }
    }

    public function load($lager_group_id)
    {
        $daten = db::getInstance()->singleQuery("
                SELECT
                    einst_lager_group.lager_group_id,
                    einst_lager_group.lager_group_name,
                    einst_lager_group.lager_group_quickview,
                    einst_lager_group.lager_group_pos
                FROM
                    einst_lager_group
                WHERE
                    einst_lager_group.lager_group_id = '$lager_group_id'
        ");

        $this->loadByDaten($daten);
    }

    public function loadByDaten($daten)
    {
        $this->daten = $daten;
    }


    public function getLagerGroupId()
    {
        return $this->daten['lager_group_id'];
    }

    public function getLagerGroupName()
    {
        return $this->daten['lager_group_name'];
    }

    public function isLagerGroupQuickview()
    {
        return $this->daten['lager_group_quickview'];
    }

    public function isLagerGroupQuickviewNoEmpty(): bool
    {
        return $this->getLagerGroupId() == 2;
    }

    /**
     * @return Lager[]
     */
    public function getLagers()
    {
        $lagers = [];

        foreach (LagerRepository::getLagers() as $lager) {
            if ($lager->getLagerGroupId() != $this->getLagerGroupId()) {
                continue;
            }

            $lagers[] = $lager;
        }

        usort($lagers, function (Lager $a, Lager $b) {
            return $a->getLagerPos() <=> $b->getLagerPos();
        });

        return $lagers;
    }
}
