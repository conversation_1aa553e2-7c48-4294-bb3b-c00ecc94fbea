<?php

namespace wws\Lager;

use db;
use Exception;

class LagerRepository
{
    public const LAGER_TYPE_NORMAL = 'normal';
    public const LAGER_TYPE_GROSSIST_MANUAL = 'gros_man';


    protected static $laeger = [];

    /**
     * gibt das entstprechende lager zurück
     * @param int $lager_id
     * @return Lager
     */
    public static function load($lager_id)
    {
        if (!isset(self::$laeger[$lager_id])) {
            self::$laeger[$lager_id] = new Lager($lager_id);
        }

        return self::$laeger[$lager_id];
    }

    public static function getLagerTypeNames()
    {
        return [
            'normal' => 'normales Lager',
            'virtuell' => 'virtuelles Lager',
            'template' => 'Vorlage',
            'gros' => 'Grossisten Lager - automatisch',
            'gros_man' => 'Grossisten Lager - händig',
            'customer' => 'Techniker'
        ];
    }

    public static function getCalcIntakes()
    {
        return [
            'gs_orders' => 'offene Lieferanten Bestellungen',
            'none' => 'kein Zugang berechnen',
            'customer_orders' => 'offene Bestellungen zugeordneter Techniker bzw. Kunden (Placos)'
        ];
    }

    public static function getVersandHandlers(): array
    {
        return [
            'none' => 'kein Versand',
            'manual' => 'manueller Versand'
        ];
    }

    /**
     * @return Lager[]
     * @throws Exception
     */
    public static function getLagers(): array
    {
        static $lagers = [];

        if (!$lagers) {
            $result = db::getInstance()->query("
                SELECT
                    " . LagerRepository::getSelectFieldsForLager() . "
                FROM
                    einst_lager
            ");

            foreach ($result as $daten) {
                $lager = Lager::getInstanceByDaten($daten);
                $lagers[$lager->getLagerId()] = $lager;
            }
        }

        return $lagers;
    }

    public static function getSelectFieldsForLager(): string
    {
        return "
                einst_lager.lager_id,
                einst_lager.lager_name,
                einst_lager.lager_typ,
                einst_lager.lager_group_id,
                einst_lager.lager_pos,
                einst_lager.template_lager_id,
                einst_lager.versand_handler,
                einst_lager.lager_transfer,
                einst_lager.speditionen,
                einst_lager.wareneingang,
                einst_lager.warenausgang,
                einst_lager.supplier_id,

                einst_lager.use_serials,
                einst_lager.ignore_csv,
                einst_lager.auto_booking,
                einst_lager.book_negative,
                einst_lager.book_direct,
                einst_lager.calc_intake,
                einst_lager.archived,

                einst_lager.firma,
                einst_lager.name,
                einst_lager.strasse,
                einst_lager.plz,
                einst_lager.ort,
                einst_lager.country_id
        ";
    }

    public static function getAllLager($modus = 'name')
    {
        $db = db::getInstance();

        //dirty
        $query = "
                SELECT
                    einst_lager.lager_id,
                    ";

        if ($modus == 'detail') {
            $query .= 'einst_lager.*';
        } else {
            $query .= 'einst_lager.lager_name';
        }

        $query .= "
                FROM
                    einst_lager
                ORDER BY
                    einst_lager.lager_id
            ";

        if ($modus == 'detail') {
            $return = [];

            $result = $db->query($query);

            foreach ($result as $daten) {
                $return[$daten['lager_id']] = $daten;

                $speditionen = @unserialize($daten['speditionen']);

                $return[$daten['lager_id']]['speditionen'] = $speditionen ? $speditionen : [];
            }

            return $return;
        } else {
            return $db->query($query)->asSingleArray('lager_id');
        }
    }

    /**
     * @return LagerGroup[]
     * @throws Exception
     */
    public static function getLagerGroupes(): array
    {
        $groupes = [];

        $result = db::getInstance()->query("
                SELECT
                    einst_lager_group.lager_group_id,
                    einst_lager_group.lager_group_name,
                    einst_lager_group.lager_group_quickview,
                    einst_lager_group.lager_group_pos
                FROM
                    einst_lager_group
                ORDER BY
                    einst_lager_group.lager_group_pos
        ");

        foreach ($result as $daten) {
            $lager_group = new LagerGroup();
            $lager_group->loadByDaten($daten);

            $groupes[$lager_group->getLagerGroupId()] = $lager_group;
        }

        return $groupes;
    }

    public static function getLagerGroupNames(): array
    {
        return db::getInstance()->query("
                SELECT
                    einst_lager_group.lager_group_id,
                    einst_lager_group.lager_group_name
                FROM
                    einst_lager_group
                ORDER BY
                    einst_lager_group.lager_group_pos
        ")->asSingleArray('lager_group_id');
    }

    /**
     * @return LagerGroup[]
     */
    public static function getLagerQuickviewGroupes(): array
    {
        $groupes = [];

        foreach (self::getLagerGroupes() as $group) {
            if (!$group->isLagerGroupQuickview()) {
                continue;
            }

            $groupes[] = $group;
        }

        return $groupes;
    }


    public static function getLagerWarenausgang($modus = 'name')
    {
        $lager = LagerRepository::getAllLager('detail');

        $return = [];

        foreach ($lager as $lager_id => $lager_daten) {
            if (!$lager_daten['warenausgang']) {
                continue;
            }

            switch ($modus) {
                case 'name':
                    $return[$lager_id] = $lager_daten['lager_name'];
                    break;
                case 'detail':
                    $return[$lager_id] = $lager_daten;
                    break;
            }
        }

        return $return;
    }


    /**
     * Gibt die Lager als Assosiativen Array zurück (id => name)
     * @param string $filter
     * @return array
     */
    public static function getLagersAsArray($filter = 'none')
    {
        static $daten = [];
        if (!array_key_exists($filter, $daten)) {
            switch ($filter) {
                case 'none':
                    $daten[$filter] = db::getInstance()->query("
                        SELECT
                            einst_lager.lager_id,
                            einst_lager.lager_name
                        FROM
                            einst_lager
                    ")->asSingleArray('lager_id');
                    break;
                case 'lager_transfer':
                    $daten[$filter] = db::getInstance()->query("
                        SELECT
                            einst_lager.lager_id,
                            einst_lager.lager_name
                        FROM
                            einst_lager
                        WHERE
                            einst_lager.lager_transfer = 1
                    ")->asSingleArray('lager_id');
                    break;
            }
        }
        return $daten[$filter];
    }


    public static function tableHelper_lager($daten, $field)
    {
        static $laeger = null;

        if ($laeger === null) {
            $laeger = self::getLagersAsArray();
        }

        return $laeger[$daten[$field]];
    }

    public static function getLagerTemplatesAsNames(): array
    {
        return db::getInstance()->query("
            SELECT
                einst_lager.lager_id,
                einst_lager.lager_name
            FROM
                einst_lager
            WHERE
                einst_lager.lager_typ = 'template'
        ")->asSingleArray('lager_id');
    }

    /**
     * prüft ob das Lager Seriennummern nutzt und ggf. das Produkt auch ($product_id = optional)
     * @param int $lager_id
     * @param int|null $product_id
     * @return bool
     */
    public static function isSerialsRequierd(int $lager_id, ?int $product_id = null): bool
    {
        $use_serials = (bool)db::getInstance()->fieldQuery("
            SELECT
                einst_lager.use_serials
            FROM
                einst_lager
            WHERE
                einst_lager.lager_id = '" . (int)$lager_id . "'
        ");

        if (!$use_serials) {
            return false;
        }

        if ($product_id === null) {
            return $use_serials;
        }

        $use_serials = (bool)db::getInstance()->fieldQuery("
            SELECT
                product.product_with_serials
            FROM
                product
            WHERE
                product.product_id = '$product_id'
        ");

        return $use_serials;
    }

    public static function getLagerName(int $lager_id): string
    {
        $lagers = self::getLagersAsArray();

        return $lagers[$lager_id];
    }

    public static function getLagerTyp(int $lager_id): string
    {
        return db::getInstance()->fieldQuery("
            SELECT
                einst_lager.lager_typ
            FROM
                einst_lager
            WHERE
                einst_lager.lager_id = '$lager_id'
        ");
    }

    public static function isIgnoreCsv(int $lager_id): bool
    {
        static $cache = null;

        if ($cache === null) {
            $cache = db::getInstance()->query("
                SELECT
                    einst_lager.lager_id,
                    einst_lager.ignore_csv
                FROM
                    einst_lager
            ")->asSingleArray('lager_id');
        }

        return (bool)$cache[$lager_id];
    }

    public static function isBookNegative(int $lager_id): bool
    {
        static $cache = null;

        if ($cache === null) {
            $cache = db::getInstance()->query("
                SELECT
                    einst_lager.lager_id,
                    einst_lager.book_negative
                FROM
                    einst_lager
            ")->asSingleArray('lager_id');
        }

        return (bool)$cache[$lager_id];
    }

    /**
     * Gibt für ein Lager die "standardmässig" genutzte $sped_id zurück...
     *
     * Achtung: "standardmässig" ist derzeit erst ausgewählte Spedition.
     * Das ist konzeptionell auch nicht durchgedacht, es nervt nur grade das in jeder Automatik eine $lager_id und
     * $sped_id fest kodiert sind.
     * Für den simplen Direktversand Case reicht das aus.
     *
     * @param int $lager_id
     * @return int
     */
    public static function getDefaultSpedId(int $lager_id): int
    {
        static $cache = [];

        if (!array_key_exists($lager_id, $cache)) {
            $lager = self::load($lager_id);

            $sped_ids = $lager->getSpeditionen();

            $cache[$lager_id] = $sped_ids ? reset($sped_ids) : null;
        }

        if (!$cache[$lager_id]) {
            throw new Exception('Keine Direktspedition für Lager ' . $lager_id . ' gefunden.');
        }

        return $cache[$lager_id];
    }
}
