<?php

namespace wws\Lager;

use bqp\Date\DateObj;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Pdf\PdfMerge;
use bqp\Utils\ArrayUtils;
use db;
use debug;
use Exception;
use output;
use service_loader;
use SmartyException;
use wws\business_structure\Shop;
use wws\core\DocumentTemplateSys;
use wws\core\SmartyWws;
use wws\Documents\DocumentPdfService;
use wws\Documents\DocumentPdfServiceException;
use wws\Mails\Mail;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderRepository;
use wws\Product\ProductWarenkorbTypesRepository;
use wws\Shipment\ShipmentRepository;
use wws\Tracking\TrackingInformation;
use wws\Tracking\TrackingLegacy;

class WarenausgangLieferscheinUtils
{
    public const MODUS_HTML = 'html';
    public const MODUS_HTML_INLINE = 'html_inline';
    public const MODUS_PDF_BLOB = 'pdf_string';

    /**
     * erzeugt ein Warenausganglieferschein anhand der noch offenen Position einer Bestellung
     * @param Order $order
     * @return WarenausgangLieferschein
     */
    public static function createWarenausgangLieferscheinByOrder(Order $order): WarenausgangLieferschein
    {
        $lieferschein = self::initWarenausgangLieferscheinByOrder($order);
        $lieferschein->save();

        return $lieferschein;
    }

    public static function initWarenausgangLieferscheinByOrder(Order $order): WarenausgangLieferschein
    {
        $lieferschein = new WarenausgangLieferschein();
        self::preFillByOrder($lieferschein, $order);

        foreach ($order->getOrderItemsSorted() as $order_item) {
            if ($order_item->getStatus() > OrderConst::STATUS_ZUSTELLUNG_BEENDET) {
                continue;
            }

            if ($order_item->getQuantityOpen() < 1) {
                continue;
            }

            if (!ProductWarenkorbTypesRepository::isDelivery($order_item->getTyp())) {
                continue;
            }

            $item = $lieferschein->createItem();
            $item->setOrderItemId($order_item->getOrderItemId());
            $item->setProductId($order_item->getProductId());
            $item->setAnzahl($order_item->getQuantityOpen());
        }

        return $lieferschein;
    }

    /**
     * füllt den Lieferschein anhand der $order_id mit Daten. Es werden keinen Positionen übernommen!
     */
    public static function preFillByOrder(WarenausgangLieferschein $lieferschein, Order $order): void
    {
        $lieferschein->setCustomerId($order->getCustomerId());
        $lieferschein->setAuftnr($order->getAuftnr());
        $lieferschein->setShopId($order->getShopId());
        $lieferschein->setOrderId($order->getOrderId());
        $lieferschein->setSpedId($order->getSpedId());
        $lieferschein->setLagerId($order->getLagerId());
        $lieferschein->setDatum(new DateObj());

        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_REWE) {
            $lieferschein->setDocumentTemplate('warenausgang_lieferschein_rewe');
        }

        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_GALAXUS) {
            $lieferschein->setDocumentTemplate('warenausgang_liefers_galaxus');
        }
    }


    /**
     * lieferschein erledigt
     */
    public static function erledigt($lieferschein_id)
    {
        db::getInstance()->begin();

        $lieferschein = new WarenausgangLieferschein($lieferschein_id);
        $lieferschein->buchen();

        db::getInstance()->commit();

        return true;
    }

    /**
     * bucht ein Lieferschein wieder ein
     * @param int $lieferschein_id
     */
    public static function lateStorno(int $lieferschein_id): void
    {
        $lieferschein = new WarenausgangLieferschein($lieferschein_id);

        if ($lieferschein->getStatus() !== $lieferschein::STATUS_ERLEDIGT) {
            throw new FatalException('lieferschein muss beendet sein');
        }

        $lieferschein->setStatus($lieferschein::STATUS_LATE_STORNO);
        $lieferschein->save();
    }

    /**
     * storniert den noch nicht ausgelösten Lieferschein und setzt den Auftrag auf "Auslieferung verschoben"
     */
    public static function storno(int $lieferschein_id, ?int $status = null): bool
    {
        $lieferschein = new WarenausgangLieferschein($lieferschein_id);

        if ($lieferschein->getStatus() == WarenausgangLieferschein::STATUS_STORNIERT) {
            return false;
        }

        $lieferschein->setStatus(WarenausgangLieferschein::STATUS_STORNIERT);
        $lieferschein->save();

        self::stornoHandleOrder($lieferschein_id, $status);

        return true;
    }

    public static function stornoHandleOrder(int $lieferschein_id, ?int $status = null): void
    {
        $lieferschein = new WarenausgangLieferschein($lieferschein_id);

        $order = $lieferschein->getOrder();

        $status = $status ?: OrderConst::STATUS_AUSLIEFERUNG_VERSCHOBEN;

        //@todo hier müssten eigentlich explizit die Lieferschein Positionen berücksichtig werden
        foreach ($order->getOrderItems() as $order_item) {
            if ($lieferschein->getStatus() === $lieferschein::STATUS_STORNIERT) {
                if ($order_item->getStatus() !== OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST) {
                    continue;
                }
            }
            if ($lieferschein->getStatus() === $lieferschein::STATUS_LATE_STORNO) {
                if ($order_item->getStatus() !== OrderConst::STATUS_ZUSTELLUNG_BEENDET) {
                    continue;
                }
            }

            $order_item->setStatus($status);
            if ($status == OrderConst::STATUS_AB_LEERFELD) {
                $order_item->setSupplierId(0);
            }
        }

        $order->save();
    }


    public static function displayByLieferscheinId($lieferschein_id, string $modus = self::MODUS_HTML)
    {
        $lieferschein = new WarenausgangLieferschein($lieferschein_id);

        return self::display($lieferschein, $modus);
    }

    /**
     * @param WarenausgangLieferschein $lieferschein
     * @param string $modus
     * - pdf_string -> smarty template wird geladen, variablen zugewiesen, per pdf service html2pdf wird ein pdf
     * erzeugt und der PDF Content als return der methode zurückgegeben
     * - html -> smarty template wird geladen, variablen zugewiesen, und das html wird ausgegeben.
     * Die Methode liefert in diesem Fall einen leeren String zurück
     * - html_inline -> smarty template wird geladen, variablen zugewiesen, und das (inline?) html wird ausgegeben.
     * Die Methode liefert in diesem Fall einen leeren String zurück
     *
     * ggf. leerer string ($modus 'html', $modus 'html_inline'
     * ggf. gefüllter string mit PDF Content ($modus 'pdf_string)
     * @throws SmartyException
     * @throws DocumentPdfServiceException
     * @throws FatalException
     */
    public static function display(WarenausgangLieferschein $lieferschein, string $modus = self::MODUS_HTML)
    {
        //@todo rücksende formular thematik -> in zukunft zerlegen in ShippingDocuments
        //      der markup der hier erzeugt wird, ist ja auch dummfug. mehrmals display() aufzurufen ist ja richtig bullshit


        $delivery_note_template = self::getDeliveryNoteTemplateSys($lieferschein);
        $delivery_note_template_file = static::getDeliveryNoteTemplateFilename($lieferschein->getDocumentTemplate());

        $append_return_note = false;
        if ($lieferschein->getShopId() == Shop::ERSATZTEILSHOP && $lieferschein->getSpedId() != ShipmentRepository::SPED_POST) {
            $return_note_template = self::getReturnNoteTemplateSys($lieferschein);
            $return_note_template_file = 'dokumente/ruecksendung_ersatzteilshop.tpl';

            $append_return_note = true;
        }

        switch ($modus) {
            case self::MODUS_HTML:
                $delivery_note_template->dokumentDisplay($delivery_note_template_file);
                if ($append_return_note) {
                    $return_note_template->dokumentDisplay($return_note_template_file);
                }
                break;
            case self::MODUS_HTML_INLINE:
                $delivery_note_template->display($delivery_note_template_file);
                if ($append_return_note) {
                    $return_note_template->display($return_note_template_file);
                }
                break;
            case self::MODUS_PDF_BLOB:
                $pdf_blob = self::htmlToPdf($delivery_note_template->dokumentFetchForWkhtmlToPdf($delivery_note_template_file));

                if ($append_return_note) {
                    $return_note_pdf_blob = self::htmlToPdf($return_note_template->dokumentFetchForWkhtmlToPdf($return_note_template_file));

                    $merge = new PdfMerge();
                    $merge->addPdfBlob($pdf_blob);
                    $merge->addPdfBlob($return_note_pdf_blob);

                    $pdf_blob = $merge->getAsBlobAsA5();
                }

                return $pdf_blob;
            default:
                throw new FatalException('Displaymodus wird nicht unterstützt.');
        }
    }

    /**
     * @param string $html
     * @return string
     * @throws DocumentPdfServiceException
     */
    private static function htmlToPdf(string $html): string
    {
        $pdf_service = new DocumentPdfService();
        return $pdf_service->html2pdf($html);
    }

    /**
     * @param string $template
     * @return string
     */
    private static function getDeliveryNoteTemplateFilename(string $template): string
    {
        if (!$template) {
            throw new DevException('template ist not setted');
        }

        return 'dokumente/' . $template . '.tpl';
    }

    public static function sendVersandMail(int $lieferschein_id): bool
    {
        $lieferschein = new WarenausgangLieferschein($lieferschein_id);

        //prüfen ob für spedition eine versandmail hinterlegt
        $mail_template_id = ShipmentRepository::getVersandmailId($lieferschein->getSpedId());

        if (!$mail_template_id) {
            return false;
        }

        $order = service_loader::get(OrderRepository::class)->loadCached($lieferschein->getOrderId());

        //prüfen ob mail gesendet werden soll
        $order_mail_profile = service_loader::getOrderMailProfile();
        $order_mail_profile->setOrder($order);

        if (!$order_mail_profile->isMail($order_mail_profile::MAIL_TYPE_VERSAND)) {
            return false;
        }

        //mail senden
        $mail = new Mail();
        $mail->setOrder($order);
        $mail->loadVorlage($mail_template_id);

        //tracking
        $tracking = TrackingLegacy::getTrackingIdsByLieferscheinId($lieferschein->getLieferscheinId());

        if ($tracking) {
            $tracking_ids = ArrayUtils::extract($tracking, 'tracking_id');

            $tracking_ids = "\n" . implode(", ", $tracking_ids) . "\n";

            $mail->assign('tracking_ids', $tracking_ids);

            $tracking_ids_urls = "\n" . implode("\n", array_map(function (TrackingInformation $tracking) {
                    return $tracking->getTrackingUrl() ?: $tracking->getTrackingId();
                }, $tracking)) . "\n";

            $mail->assign('tracking', $tracking_ids_urls);
            $mail->assign('tracking_ids_links', $tracking_ids_urls);
        } else {
            $mail->assign('tracking', '');
            $mail->assign('tracking_ids', '');
            $mail->assign('tracking_ids_links', '');
        }

        $mail->assign('tracking_provider_hint', ''); //k11 comba

        $mail->send();

        return true;
    }

    /**
     * @param WarenausgangLieferschein $lieferschein
     * @return SmartyWws
     */
    private static function getDeliveryNoteTemplateSys(WarenausgangLieferschein $lieferschein): SmartyWws
    {
        $template = DocumentTemplateSys::getInstance();
        $template->assign('lieferschein', $lieferschein);
        $template->assign('address', $lieferschein->getLieferAddress());
        $template->assign('shop', new Shop($lieferschein->getShopId()));
        $template->assign('order', $lieferschein->getOrder());

        $template->setTitle('Lieferschein');
        return $template;
    }

    public static function createWarenausgangLieferscheinPdf(int $lieferschein_id): void
    {
        $storage_mount_manger = service_loader::getStorageFactory()->get('wws');

        try {
            $pdf = WarenausgangLieferscheinUtils::displayByLieferscheinId($lieferschein_id, WarenausgangLieferscheinUtils::MODUS_PDF_BLOB);
            $storage_mount_manger->write('wws://orders/warenausgang_lieferscheine/' . $lieferschein_id . '.pdf', $pdf);
        } catch (Exception $e) {
            debug::dump('$lieferschein_id: ' . $lieferschein_id);
            debug::dumpException($e);
        }
    }


    /**
     * @param WarenausgangLieferschein $lieferschein
     * @return SmartyWws
     * @throws FatalException
     */
    private static function getReturnNoteTemplateSys(WarenausgangLieferschein $lieferschein): SmartyWws
    {
        $order = $lieferschein->getOrder();
        $tpl = output::tpl(); //@todo sieht fishy aus... das liefert die instanz vom wws
        $tpl->assign('order', $order);
        $tpl->assign('lieferschein', $lieferschein);
        $items = self::getItemsExtended($lieferschein, $order->getOrderItems());
        $tpl->assign('items', $items);
        return $tpl;
    }

    /**
     * @param WarenausgangLieferschein $lieferschein
     * @param array $order_items
     * @return array
     * @throws FatalException
     */
    public static function getItemsExtended(WarenausgangLieferschein $lieferschein, array $order_items): array
    {
        $items = [];
        foreach ($lieferschein->getItemsExtended() as $shipping_item) {
            $rq = 1;

            foreach ($order_items as $order_item) {
                if ($order_item->getOrderItemId() == $shipping_item['order_item_id']) {
                    //Prüfen ob über Krempl retournierbar
                    //@todo noch relevant? / doppelt und inkonsistent zu \bqp\extern\krempl\KremplProductReturn::extendLieferscheinItemsForReturnNote
                    switch ($order_item->getSpedId()) {
                        case ShipmentRepository::SPED_DIREKT_KREMPL:
                        case ShipmentRepository::SPED_DIREKT_SONSTIGE:
                            // UND EK über 5€ => 2 UND Hans Krempl Nummer im Artikel
                            if ($order_item->getEkNetto() > 5
                                && strpos($shipping_item['delivery_slip_hint'], 'HK: ') !== false) {
                                $rq = 2;
                                break 2;
                            }
                            break;
                    }
                }
            }

            $shipping_item['delivery_slip_hint'] .= ' - RQ: ' . $rq;
            $items[] = $shipping_item;
        }

        return $items;
    }
}
