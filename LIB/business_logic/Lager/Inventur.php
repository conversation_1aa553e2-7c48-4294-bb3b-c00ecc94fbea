<?php

namespace wws\Lager;

use bqp\Date\DateObj;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use db;

class Inventur
{
    protected SmartDataObj $daten;

    public function __construct($inventur_id = null)
    {
        $this->daten = new SmartDataObj($this);

        if ($inventur_id !== null) {
            $this->load($inventur_id);
        } else {
            $this->setDescription('');
        }
    }

    public function load(int $inventur_id): void
    {
        $db = db::getInstance();

        $daten = $db->singleQuery("
            SELECT
                inventur.inventur_id,
                inventur.lager_id,
                inventur.description,
                inventur.inventur_start,
                inventur.user_id,
                inventur.inventur_end,
                inventur.inventur_status
            FROM
                inventur
            WHERE
                inventur.inventur_id = '" . $inventur_id . "'
        ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException('inventur (' . $inventur_id . ') konnte nicht geladen werden.');
        }

        $this->daten->loadDaten($daten);
    }

    public function save()
    {
        if ($this->daten->getObjStatus() != SmartDataObj::STATUS_UPDATE) {
            return;
        }

        $db = db::getInstance();

        $changes = $this->daten->getChanges();

        $sql = [];

        foreach ($changes as $key => $value) {
            $sql[] = "inventur." . $key . " = '" . $db->escape($value) . "'";
        }

        $db->query("
            UPDATE
                inventur
            SET
                " . implode(',', $sql) . "
            WHERE
                inventur.inventur_id = '" . $this->getInventurId() . "'
        ");
    }

    public function inventurEnd()
    {
        if ($this->daten['inventur_status'] != 'active') {
            return;
        }

        $this->setInventurEnd(new DateObj());
        //$this->calculate();

        $this->setInventurStatus('ende');
    }

    public function inventurAbort()
    {
        if ($this->daten['inventur_status'] != 'active') {
            return;
        }

        $this->setInventurEnd(new DateObj());
        $this->setInventurStatus('aborte');
    }

    public function setInventurStatus($inventur_status)
    {
        $status = $this->daten->setter('inventur_status', $inventur_status);

        return $status;
    }

    public function getInventurStatus()
    {
        return $this->daten['inventur_status'];
    }

    public function getDescription(): string
    {
        return $this->daten['description'];
    }

    public function setDescription(string $description): bool
    {
        return $this->daten->setter('description', $description);
    }

    public function setInventurEnd(DateObj $date)
    {
        return $this->daten->setter('inventur_end', $date->db());
    }

    public function getInventurStart(): DateObj
    {
        return new DateObj($this->daten['inventur_start']);
    }

    public function getLagerId(): int
    {
        return $this->daten->getter('lager_id');
    }

    public function calculate()
    {
        $db = db::getInstance();

        $db->query("
            UPDATE
                inventur_bestand
            SET
                inventur_bestand.bestand_ist = 0
            WHERE
                inventur_bestand.inventur_id = '" . $this->getInventurId() . "'
        ");

        $result = $db->query("
            SELECT
                inventur_lists_items.product_id,
                SUM(inventur_lists_items.anzahl) AS anzahl
            FROM
                inventur_lists INNER JOIN
                inventur_lists_items ON (inventur_lists.inventur_list_id = inventur_lists_items.inventur_list_id)
            WHERE
                inventur_lists.inventur_id = '" . $this->getInventurId() . "'
            GROUP BY
                inventur_lists_items.product_id
        ");

        foreach ($result as $daten) {
            $found = $db->fieldQuery("
                SELECT
                    inventur_bestand.product_id
                FROM
                    inventur_bestand
                WHERE
                    inventur_bestand.inventur_id = '" . $this->getInventurId() . "' AND
                    inventur_bestand.product_id = '" . $daten['product_id'] . "'
            ");

            if ($found) {
                $db->query("
                    UPDATE
                        inventur_bestand
                    SET
                        inventur_bestand.bestand_ist = '" . $daten['anzahl'] . "'
                    WHERE
                        inventur_bestand.inventur_id = '" . $this->getInventurId() . "' AND
                        inventur_bestand.product_id = '" . $daten['product_id'] . "'
                ");
            } else {
                //ek auslesen
                $ek_netto = $this->searchEkNetto($daten['product_id']);

                $db->query("
                    INSERT INTO
                        inventur_bestand
                    SET
                        inventur_bestand.inventur_id = '" . $this->getInventurId() . "',
                        inventur_bestand.product_id = '" . $daten['product_id'] . "',
                        inventur_bestand.bestand_ist = '" . $daten['anzahl'] . "',
                        inventur_bestand.bestand_soll = 0,
                        inventur_bestand.ek_netto = $ek_netto
                ");
            }
        }
    }

    public function refreshEk(int $product_id): void
    {
        $ek_netto = $this->searchEkNetto($product_id);

        if (!$ek_netto) {
            return;
        }

        db::getInstance()->query("
            UPDATE
                inventur_bestand
            SET
                inventur_bestand.ek_netto = " . $ek_netto . "
            WHERE
                inventur_bestand.product_id = '" . $product_id . "' AND
                inventur_bestand.inventur_id = '" . $this->getInventurId() . "'
        ");
    }

    private function searchEkNetto(int $product_id): float
    {
        $db = db::getInstance();

        $ek_netto = $db->fieldQuery("
            SELECT
                product_lager_detail.lager_ek
            FROM
                product_lager_detail
            WHERE
                product_lager_detail.product_id = '" . $product_id . "' AND
                product_lager_detail.lager_id = '" . $this->getLagerId() . "'
        ");

        if (!$ek_netto) {
            $ek_netto = $db->fieldQuery("
                SELECT
                    product_ek.ek_netto
                FROM
                    product_ek
                WHERE
                    product_ek.product_id = '" . $product_id . "' AND
                    product_ek.ek_fav = 1
            ");
        }

        return $ek_netto;
    }

    public function getInventurId()
    {
        return $this->daten['inventur_id'];
    }


    public function getLists()
    {
        $db = db::getInstance();

        $lists = $db->query("
            SELECT
                inventur_lists.inventur_list_id,
                inventur_lists.inventur_id,
                inventur_lists.list_name,
                inventur_lists.user_id,
                inventur_lists.added
            FROM
                inventur_lists
            WHERE
                inventur_lists.inventur_id = '" . $this->getInventurId() . "'
        ")->asArray();

        return $lists;
    }

    public function addNewList($list_name)
    {
        $db = db::getInstance();

        $db->query("
            INSERT INTO
                inventur_lists
            SET
                inventur_lists.inventur_id = '" . $this->getInventurId() . "',
                inventur_lists.list_name = '" . $db->escape($list_name) . "',
                inventur_lists.added = NOW()
        ");

        return $db->insert_id();
    }

    public function addBemerkungForProduct($product_id, $bemerkung)
    {
        $db = db::getInstance();

        $db->query("
            UPDATE
                inventur_bestand
            SET
                inventur_bestand.bemerkung = '" . $db->escape($bemerkung) . "'
            WHERE
                inventur_bestand.inventur_id = '" . $this->getInventurId() . "' AND
                inventur_bestand.product_id = '" . (int)$product_id . "'
        ");
    }

    public function addItem(int $inventur_list_id, int $product_id, int $quantity): int
    {
        $db = db::getInstance();

        $db->query("
            INSERT INTO
                inventur_lists_items
            SET
                inventur_lists_items.inventur_list_id = '$inventur_list_id',
                inventur_lists_items.product_id = '" . $product_id . "',
                inventur_lists_items.anzahl = '" . $quantity . "'
        ");

        return $db->insert_id();
    }

    public function deleteItem(int $inventur_list_item_id): void
    {
        db::getInstance()->query("
            DELETE FROM
                inventur_lists_items
            WHERE
                inventur_lists_items.inventur_list_item_id = '" . $inventur_list_item_id . "'
        ");
    }

    public function updateItem(int $inventur_list_item_id, int $product_id, int $quantity): void
    {
        db::getInstance()->query("
            UPDATE
                inventur_lists_items
            SET
                inventur_lists_items.product_id = '" . $product_id . "',
                inventur_lists_items.anzahl = '" . $quantity . "'
            WHERE
                inventur_lists_items.inventur_list_item_id = '" . $inventur_list_item_id . "'
        ");
    }
}
