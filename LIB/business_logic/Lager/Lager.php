<?php

namespace wws\Lager;

use bqp\Address\Address;
use bqp\Exceptions\InputException;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use bqp\Utils\StringUtils;
use db;
use wws\Country\CountryConst;
use wws\Shipment\ShipmentAutomatic\ShipmentAutomatic;
use wws\Shipment\ShipmentConst;

class Lager
{
    const INTAKE_CALC_CUSTOMER_ORDERS = 'customer_orders';
    const INTAKE_CALC_GROS_ORDERS = 'gs_orders';

    public const LAGER_TYPE_TEMPLATE = 'template';

    /**
     * @var Lager[]
     */
    private static $instances = [];

    private SmartDataObj $daten;

    public function __construct(?int $lager_id = null)
    {
        //@todo teilweise wird noch 0 reingegeben...
        if (!$lager_id) {
            $lager_id = null;
        }

        $this->daten = new SmartDataObj($this);

        if ($lager_id !== null) {
            $this->load($lager_id);
        } else {
            $this->loadDefaults();
        }
    }

    public function load(int $lager_id): void
    {
        $daten = db::getInstance()->singleQuery("
            SELECT
                " . LagerRepository::getSelectFieldsForLager() . "
            FROM
                einst_lager
            WHERE
                einst_lager.lager_id = '$lager_id'
        ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException();
        }

        $this->daten->loadDaten($daten);
    }

    public function loadDefaults(): void
    {
        $this->setCountryId(CountryConst::COUNTRY_ID_DE);
        $this->setLagerPos(0);
    }

    public function validate()
    {
        $error = new InputException();

        if (!$this->daten['lager_name']) {
            $error->add('lager_name', 'Bitte geben Sie ein Lagernamen an.');
        }

        $address = $this->getAddress();

        foreach ($address->validate() as $key => $message) {
            if ($key === 'anrede') {
                continue;
            }

            $error->add($key, $message);
        }


        $error->check();
    }


    public function save(): int
    {
        $this->validate();

        $db = db::getInstance();

        $sql = [];

        foreach ($this->daten->getAsArray() as $key => $value) {
            $sql[] = "einst_lager.$key = '" . $db->escape($value) . "'";
        }

        if (!$this->daten['lager_id']) {
            $db->query("
                INSERT INTO
                    einst_lager
                SET
                    " . implode(', ', $sql) . "
            ");

            $this->daten->setterDirect('lager_id', $db->insert_id());
            $this->daten->setterDirect('lager_pos', $this->getLagerId());

            $db->query("
                UPDATE
                    einst_lager
                SET
                    einst_lager.lager_pos = " . $this->getLagerPos() . "
                WHERE
                    einst_lager.lager_id = " . $this->getLagerId() . "
            ");
        } else {
            $db->query("
                UPDATE
                    einst_lager
                SET
                    " . implode(', ', $sql) . "
                WHERE
                    einst_lager.lager_id = '" . $this->daten['lager_id'] . "'
            ");
        }

        return $this->getLagerId();
    }

    /**
     * Gibt eine Objekt für das Lager zurück
     * @param int $lager_id
     * @return Lager
     */
    public static function getInstance($lager_id)
    {
        if (!isset(self::$instances[$lager_id])) {
            self::$instances[$lager_id] = new self($lager_id);
        }

        return self::$instances[$lager_id];
    }


    public static function getInstanceByDaten($daten)
    {
        $lager_id = $daten['lager_id'];

        if (!isset(self::$instances[$lager_id])) {
            self::$instances[$lager_id] = new self();
            self::$instances[$lager_id]->daten->loadDaten($daten);
        }

        return self::$instances[$lager_id];
    }


    /**
     * Lager mit Seriennummernführung oder ohne
     * @return bool
     */
    public function isWithSerials()
    {
        return $this->daten['use_serials'] == 1;
    }

    /**
     * Gibt ein address Objekt für das Lager zurück
     * @return Address
     */
    public function getAddress(): Address
    {
        return new Address($this->daten->getAsArray());
    }

    public function setLagerId(int $lager_id): void
    {
        $this->daten['lager_id'] = $lager_id;
    }

    /**
     * setzt das Feld lager_name
     * @param $lager_name
     * @return boolean
     */
    public function setLagerName($lager_name)
    {
        $this->daten['lager_name'] = $lager_name;
    }

    /**
     * setzt das Feld lager_typ
     * @param $lager_typ
     * @return boolean
     */
    public function setLagerTyp($lager_typ)
    {
        $this->daten['lager_typ'] = $lager_typ;
    }

    /**
     * setzt das Feld versand_handler
     * @param $versand_handler
     * @return boolean
     */
    public function setVersandHandler($versand_handler)
    {
        $this->daten['versand_handler'] = $versand_handler;
    }

    /**
     * setzt das Feld lager_transfer
     * @param $lager_transfer
     * @return boolean
     */
    public function setLagerTransfer($lager_transfer)
    {
        $this->daten['lager_transfer'] = $lager_transfer;
    }

    /**
     * setzt das Feld speditionen
     * @param array $speditionen
     * @return bool
     */
    public function setSpeditionen(array $speditionen): bool
    {
        $raw = '';
        if ($speditionen) {
            $raw = serialize($speditionen);
        }

        return $this->daten->setter('speditionen', $raw);
    }

    /**
     * setzt das Feld wareneingang
     * @param $wareneingang
     * @return boolean
     */
    public function setWareneingang($wareneingang)
    {
        $this->daten['wareneingang'] = $wareneingang;
    }

    /**
     * setzt das Feld warenausgang
     * @param $warenausgang
     * @return boolean
     */
    public function setWarenausgang($warenausgang)
    {
        $this->daten['warenausgang'] = $warenausgang;
    }

    /**
     * setzt das Feld firma
     * @param $firma
     * @return boolean
     */
    public function setFirma($firma)
    {
        $this->daten['firma'] = $firma;
    }

    /**
     * setzt das Feld name
     * @param $name
     * @return boolean
     */
    public function setName($name)
    {
        $this->daten['name'] = $name;
    }

    /**
     * setzt das Feld strasse
     * @param $strasse
     * @return boolean
     */
    public function setStrasse($strasse)
    {
        $this->daten['strasse'] = $strasse;
    }

    /**
     * setzt das Feld plz
     * @param $plz
     * @return boolean
     */
    public function setPlz($plz)
    {
        $this->daten['plz'] = $plz;
    }

    /**
     * setzt das Feld ort
     * @param $ort
     * @return boolean
     */
    public function setOrt($ort)
    {
        $this->daten['ort'] = $ort;
    }

    public function setCountryId(int $country_id): void
    {
        $this->daten['country_id'] = $country_id;
    }

    public function setTemplateLagerId($lager_id)
    {
        $this->daten['template_lager_id'] = $lager_id;
    }


    public function setCalcIntake($calc)
    {
        $this->daten['calc_intake'] = $calc;
    }

    public function getCalcIntake()
    {
        return $this->daten['calc_intake'];
    }

    /**
     * lager kann ins negative gebucht werden
     * @param bool $status
     */
    public function setBookNegative($status)
    {
        $this->daten['book_negative'] = $status ? '1' : '0';
    }

    /**
     * lager kann ins negative gebucht werden
     * @return bool
     */
    public function getBookNegative()
    {
        return (bool)$this->daten['book_negative'];
    }

    /**
     * @param bool $status
     */
    public function setBookDirect($status)
    {
        $this->daten['book_direct'] = $status ? '1' : '0';
    }

    /**
     * @return bool
     */
    public function getBookDirect(): bool
    {
        return (bool)$this->daten['book_direct'];
    }


    /**
     * gibt das Feld lager_id zurück
     * @return string lager_id
     */
    public function getLagerId()
    {
        return $this->daten['lager_id'];
    }

    /**
     * gibt das Feld lager_name zurück
     * @return string lager_name
     */
    public function getLagerName()
    {
        return $this->daten['lager_name'];
    }

    /**
     * Gibt den Typ des Lagers zurück
     * @return string lager_typ
     */
    public function getLagerTyp()
    {
        return $this->daten['lager_typ'];
    }

    /**
     * gibt das Feld versand_handler zurück
     * @return string versand_handler
     */
    public function getVersandHandler()
    {
        return $this->daten['versand_handler'];
    }

    /**
     * gibt das Feld lager_transfer zurück
     * @return string lager_transfer
     */
    public function getLagerTransfer()
    {
        return $this->daten['lager_transfer'];
    }

    public function isLagerTransfer(): bool
    {
        return (bool)$this->daten['lager_transfer'];
    }

    /**
     * gibt das Feld speditionen zurück
     * @return string speditionen
     */
    public function getSpeditionen(): array
    {
        $raw = $this->daten->getter('speditionen');

        if (!$raw) {
            return [];
        }

        return unserialize($this->daten['speditionen']);
    }

    /**
     * gibt das Feld wareneingang zurück
     * @return string wareneingang
     */
    public function getWareneingang()
    {
        return $this->daten['wareneingang'];
    }

    /**
     * gibt das Feld warenausgang zurück
     * @return string warenausgang
     */
    public function getWarenausgang()
    {
        return $this->daten['warenausgang'];
    }

    /**
     * gibt das Feld firma zurück
     * @return string firma
     */
    public function getFirma()
    {
        return $this->daten['firma'];
    }

    /**
     * gibt das Feld name zurück
     * @return string name
     */
    public function getName()
    {
        return $this->daten['name'];
    }

    /**
     * gibt das Feld strasse zurück
     * @return string strasse
     */
    public function getStrasse()
    {
        return $this->daten['strasse'];
    }

    /**
     * gibt das Feld plz zurück
     * @return string plz
     */
    public function getPlz()
    {
        return $this->daten['plz'];
    }

    /**
     * gibt das Feld ort zurück
     * @return string ort
     */
    public function getOrt()
    {
        return $this->daten['ort'];
    }

    public function getCountryId()
    {
        return $this->daten['country_id'];
    }

    public function getLagerGroupId()
    {
        return $this->daten['lager_group_id'];
    }

    public function setLagerGroupId($lager_group_id)
    {
        $this->daten['lager_group_id'] = $lager_group_id;
    }

    public function getTemplateLagerId()
    {
        return $this->daten['template_lager_id'];
    }

    public function useTemplate()
    {
        return (bool)$this->getTemplateLagerId();
    }

    public function getSupplierId()
    {
        return $this->daten['supplier_id'];
    }

    public function getArchived()
    {
        return $this->daten['archived'];
    }

    public function setArchived($archived)
    {
        $this->daten['archived'] = $archived;
    }

    //Methoden

    /**
     * @todo raus damit!
     *
     * führt den Versand für das Lager aus
     */
    public function doVersand()
    {
        if ($this->getVersandHandler() == ShipmentConst::SHIPMENT_HANDLER_NONE) {
            return;
        }

        if (strpos($this->getVersandHandler(), '\\') !== false) {
            $class_name = $this->getVersandHandler();
        } else {
            $class_name = 'wws\\shipment\\ShipmentAutomatic\\ShipmentAutomatic' . StringUtils::camelCaseToStudlyCaps($this->getVersandHandler());
        }

        $versand = ShipmentAutomatic::getInstance($class_name, $this->getLagerId());
        $versand->prepareOrders();
        $versand->executePipe();
    }

    public function isSerialnumbers()
    {
    }

    public function isIgnoreCsv()
    {
        return (bool)$this->daten['ignore_csv'];
    }

    public function setIgnoreCsv($ignore_csv)
    {
        $this->daten['ignore_csv'] = $ignore_csv ? 1 : 0;
    }

    public function isAutoBooking()
    {
        return (bool)$this->daten['auto_booking'];
    }

    public function setAutoBooking($auto_booking)
    {
        $this->daten['auto_booking'] = $auto_booking ? 1 : 0;
    }

    public function getLagerPos(): int
    {
        return (int)$this->daten['lager_pos'];
    }

    public function setLagerPos(int $lager_pos): void
    {
        $this->daten['lager_pos'] = $lager_pos;
    }
}
