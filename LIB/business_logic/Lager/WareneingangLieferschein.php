<?php

namespace wws\Lager;

use bqp\Date\DateObj;
use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataObj;
use service_loader;

class WareneingangLieferschein implements ProvideSmartDataObj
{
    const STATUS_DRAFT = 0;
    const STATUS_PRE_BOOK = 1;
    const STATUS_BOOKED = 2;

    private SmartDataObj $daten;

    /**
     * @var null|WareneingangLieferscheinItem[]
     */
    public $items = null;

    public function __construct()
    {
        $this->daten = new SmartDataObj($this);
        $this->loadDefaults();
    }

    private function loadDefaults(): void
    {
        $this->setBemerkung('');
        $this->setStatus(self::STATUS_DRAFT);
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }

    public function createItem(): WareneingangLieferscheinItem
    {
        $item = new WareneingangLieferscheinItem();
        $this->addItem($item);

        return $item;
    }

    public function addItem(WareneingangLieferscheinItem $item): void
    {
        $this->getItems();

        $this->items[] = $item;
    }

    /**
     * @return WareneingangLieferscheinItem[]
     */
    public function getItems(): array
    {
        if ($this->items === null) {
            $this->items = [];
            WareneingangLieferscheinRepository::loadItems($this);
        }

        return array_filter($this->items, fn($item) => $item->getSmartDataObj()->getObjStatus() !== SmartDataObj::STATUS_DEL);
    }

    /**
     * @return null|WareneingangLieferscheinItem[]
     */
    public function __getItemsDirect(): ?array
    {
        return $this->items;
    }

    public function getSupplierId(): int
    {
        return (int)$this->daten['supplier_id'];
    }

    public function getLieferscheinId(): int
    {
        return (int)$this->daten['lieferschein_id'];
    }

    public function setLieferscheinNr(string $lieferschein_nr): bool
    {
        return $this->daten->setter('lieferschein_nr', $lieferschein_nr);
    }

    public function setStatus(string $status): bool
    {
        return $this->daten->setter('status', $status);
    }

    public function setSupplierId(int $supplier_id): bool
    {
        return $this->daten->setter('supplier_id', $supplier_id);
    }

    public function setLagerId(int $lager_id): bool
    {
        return $this->daten->setter('lager_id', $lager_id);
    }

    public function setBemerkung(string $bemerkung): bool
    {
        return $this->daten->setter('bemerkung', $bemerkung);
    }

    public function getLieferscheinNr(): string
    {
        return (string)$this->daten->getter('lieferschein_nr');
    }

    public function getLieferscheinDatum(): DateObj
    {
        return new DateObj($this->daten->getter('datum'));
    }

    public function getBemerkung(): string
    {
        return $this->daten->getter('bemerkung');
    }

    public function getLagerId(): int
    {
        return (int)$this->daten->getter('lager_id');
    }

    public function getStatus(): int
    {
        return $this->daten->getter('status');
    }

    public function addSupplierOrderId(int $supplier_order_id): bool
    {
        if (!$supplier_order_id) {
            return false;
        }

        $supplier_order_ids = $this->getSupplierOrderIds();

        if (!in_array($supplier_order_id, $supplier_order_ids)) {
            $supplier_order_ids[] = $supplier_order_id;

            return $this->setSupplierOrderIds($supplier_order_ids);
        }

        return false;
    }

    private function setSupplierOrderIds(array $supplier_order_ids): bool
    {
        return $this->daten->setter('supplier_order_ids', implode(',', $supplier_order_ids));
    }

    public function getSupplierOrderIds(): array
    {
        if (!$this->daten['supplier_order_ids']) {
            return [];
        }

        return explode(',', $this->daten['supplier_order_ids']);
    }

    public function setDatum(DateObj $datum): bool
    {
        return $this->daten->setter('datum', $datum->db());
    }


    public function doPreBook(): void
    {
        $booking_service = service_loader::get(WareneingangLieferscheinBookingService::class);
        $booking_service->preBook($this);
    }


    public function doBook(): void
    {
        $booking_service = service_loader::get(WareneingangLieferscheinBookingService::class);
        $booking_service->doBook($this);
    }
}
