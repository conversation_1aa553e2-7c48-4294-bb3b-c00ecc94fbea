<?php

namespace wws\Lager;

use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataObj;

class WareneingangLieferscheinItem implements ProvideSmartDataObj
{
    private SmartDataObj $daten;

    public function __construct()
    {
        $this->daten = new SmartDataObj($this);
        $this->loadDefaults();
    }

    private function loadDefaults(): void
    {
        $this->setBemerkung('');
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }

    public function setProductId($product_id)
    {
        return $this->daten->setter('product_id', $product_id);
    }

    public function setAnzahl($anzahl)
    {
        return $this->daten->setter('anzahl', $anzahl);
    }

    public function setBemerkung($bemerkung)
    {
        return $this->daten->setter('bemerkung', $bemerkung);
    }

    public function setLieferscheinId($lieferschein_id)
    {
        return $this->daten->setter('lieferschein_id', $lieferschein_id);
    }

    public function getItemId()
    {
        return $this->daten['item_id'];
    }


    public function getProductId()
    {
        return $this->daten['product_id'];
    }

    public function getAnzahl()
    {
        return $this->daten['anzahl'];
    }

    public function getBemerkung()
    {
        return $this->daten['bemerkung'];
    }

    public function setSerials($serials)
    {
    }

    public function delete()
    {
        $this->daten->setObjStatus(SmartDataObj::STATUS_DEL);
    }

    public function getModelPk()
    {
        return $this->getItemId();
    }
}
