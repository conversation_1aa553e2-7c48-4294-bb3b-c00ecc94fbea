<?php

namespace wws\Lager;

use bqp\Date\DateObj;
use bqp\db\db_exception;
use bqp\Exceptions\DevException;
use bqp\Exceptions\InputException;
use db;
use service_loader;
use wws\Lager\Event\EventWareneingangAnnounceCreaded;
use wws\Product\ProductConst;

class WareneingangAnnounce
{
    private int $announce_id;
    private int $supplier_id = 0;
    private string $lieferschein_nr;
    protected $lieferschein_datum;

    private string $note = '';

    private array $supplier_order_ids = [];
    private array $items = [];

    /**
     * @throws InputException
     * @throws WareneingangAnnounceDuplicateException
     */
    public function save(): int
    {
        $this->validate();

        if (!isset($this->announce_id)) {
            $this->saveNew();
        } else {
            throw new DevException('nicht implementiert');
        }

        return $this->getAnnounceId();
    }

    public function validate(): void
    {
        $e = new InputException();

        if (!$this->getSupplierId()) {
            $e->add('supplier_id', 'Geben Sie einen Lieferanten an.');
        }
        if (!$this->items) {
            $e->add('items', 'Bitte geben Sie mindesten eine Position an.');
        }

        if ($this->supplier_order_ids) {
            $db = db::getInstance();

            $known_supplier_order_ids = $db->query("
                SELECT
                    supplier_order.supplier_order_id
                FROM
                    supplier_order
                WHERE
                    supplier_order.supplier_order_id IN (" . $db->in($this->supplier_order_ids) . ")
            ")->asSingleArray();

            $unknown_supplier_order_ids = array_diff($this->supplier_order_ids, $known_supplier_order_ids);

            if ($unknown_supplier_order_ids) {
                $e->add('supplier_order_ids', 'Es wurde ein oder mehrere ungültige $supplier_order_ids angegeben. (' . implode(',', $unknown_supplier_order_ids) . ')');
            }
        }

        $e->check();
    }

    /**
     * @throws WareneingangAnnounceDuplicateException
     */
    private function saveNew(): int
    {
        $db = db::getInstance();

        try {
            $db->query("
                INSERT INTO
                    wareneingang_announces
                SET
                    wareneingang_announces.supplier_id = '" . $db->escape($this->getSupplierId()) . "',
                    wareneingang_announces.lieferschein_nr = '" . $db->escape($this->lieferschein_nr) . "',
                    wareneingang_announces.lieferschein_datum = '" . $db->escape($this->lieferschein_datum) . "',
                    wareneingang_announces.note = '" . $db->escape($this->note) . "'
            ");
        } catch (db_exception $e) {
            if (strpos($e->getMessage(), 'SQLDuplicate') !== false) {
                throw new WareneingangAnnounceDuplicateException('Delivery slip number already exists ' . $this->lieferschein_nr . ' from ' . $this->getSupplierId());
            }

            throw $e;
        }

        $this->announce_id = $db->insert_id();

        foreach ($this->items as $item) {
            $db->query("
                INSERT INTO
                    wareneingang_announce_items
                SET
                    wareneingang_announce_items.announce_id = $this->announce_id,
                    wareneingang_announce_items.gros_product_id = '" . $db->escape($item['gros_product_id']) . "',
                    wareneingang_announce_items.gros_product_name = '" . $db->escape($item['gros_product_name']) . "',
                    wareneingang_announce_items.product_id = '" . $db->escape($item['product_id']) . "',     
                    wareneingang_announce_items.anzahl = '" . (int)$item['anzahl'] . "'
            ");
        }

        if ($this->supplier_order_ids) {
            foreach ($this->supplier_order_ids as $supplier_order_id) {
                $db->query("
                    INSERT INTO
                        wareneingang_announce_supplier_order_ids
                    SET
                        wareneingang_announce_supplier_order_ids.announce_id = $this->announce_id,
                        wareneingang_announce_supplier_order_ids.supplier_order_id = $supplier_order_id 
                ");
            }
        }

        $event = new EventWareneingangAnnounceCreaded($this);
        service_loader::getEventDispatcher()->dispatch($event);

        return $this->getAnnounceId();
    }


    public function _setAnnounceId(int $announce_id): void
    {
        $this->announce_id = $announce_id;
    }

    public function getAnnounceId(): int
    {
        return $this->announce_id;
    }

    public function setSupplierId(int $supplier_id): void
    {
        $this->supplier_id = $supplier_id;
    }

    public function getSupplierId(): int
    {
        return $this->supplier_id;
    }

    public function setLieferscheinNr(string $lieferschein_nr): void
    {
        $this->lieferschein_nr = $lieferschein_nr;
    }

    public function getLieferscheinNr(): string
    {
        return (string)$this->lieferschein_nr;
    }

    public function setNote(string $note): void
    {
        $this->note = $note;
    }

    public function getNote(): string
    {
        return $this->note;
    }

    public function setLieferscheinDatum(DateObj $lieferschein_datum): void
    {
        $this->lieferschein_datum = $lieferschein_datum->db();
    }

    public function getLieferscheinDatum(): DateObj
    {
        return new DateObj($this->lieferschein_datum);
    }


    /**
     * "null" für die einfacherer benutzung. int 0 und null werden ignoeriert
     *
     * @param int|null $supplier_order_id
     */
    public function addSupplierOrderId(?int $supplier_order_id): void
    {
        if (!$supplier_order_id) {
            return;
        }

        $this->supplier_order_ids[$supplier_order_id] = $supplier_order_id;
    }

    /**
     * @return int[]
     */
    public function getSupplierOrderIds(): array
    {
        return $this->supplier_order_ids;
    }

    public function addItem(string $gros_product_id, int $anzahl, string $gros_product_name = '', ?int $product_id = null): void
    {
        if (!$product_id) {
            $product_id = $this->searchProductIdByGrosProductId($gros_product_id);
        }

        $this->items[] = [
            'gros_product_id' => $gros_product_id,
            'gros_product_name' => $gros_product_name,
            'anzahl' => $anzahl,
            'product_id' => (int)$product_id
        ];
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function searchProductIdByGrosProductId(string $gros_product_id): ?int
    {
        $db = db::getInstance();

        if ($this->supplier_order_ids) {
            //primär über die supplier order suchen, damit bekommen wir sicher die bestellte product_id zurück, auch wenn das Angebot mehrfach existiert
            $product_id = $db->fieldQuery("
                SELECT
                    supplier_order_items.product_id
                FROM
                    supplier_order_items
                WHERE
                    supplier_order_items.supplier_order_id IN (" . $db->in($this->supplier_order_ids) . ") AND
                    supplier_order_items.gros_product_id LIKE '" . $db->escape($gros_product_id) . "'
            ");

            if ($product_id) {
                return $product_id;
            }
        }

        return $db->fieldQuery("
            SELECT
                product_ek.product_id
            FROM
                product_ek INNER JOIN
                product ON (product_ek.product_id = product.product_id)                
            WHERE
                product_ek.supplier_id = '" . $this->getSupplierId() . "' AND
                product_ek.gros_product_id LIKE '" . $db->escape($gros_product_id) . "'
            ORDER BY
                IF(product.product_status = " . ProductConst::PRODUCT_STATUS_NORMAL . ",0,1)
        ");
    }
}
