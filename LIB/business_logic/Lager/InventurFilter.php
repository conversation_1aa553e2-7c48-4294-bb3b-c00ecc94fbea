<?php

namespace wws\Lager;

use wws\Product\ProductBrand\ProductBrandRepository;

class InventurFilter
{
    /**
     * @var array
     */
    private $brand_ids = [];

    public function addBrandId(int $brand_id): void
    {
        $this->brand_ids[] = $brand_id;
    }

    public function setBrandIds(array $brand_ids): void
    {
        $this->brand_ids = $brand_ids;
    }

    public function hasBrandIds(): bool
    {
        return (bool)$this->brand_ids;
    }

    /**
     * @return int[]
     */
    public function getBrandIds(): array
    {
        return $this->brand_ids;
    }

    public function getAsText(): string
    {
        $text = '';

        if ($this->brand_ids) {
            $text .= 'Hersteller: ';

            $brand_names = array_map(function (int $brand_id) {
                return ProductBrandRepository::getBrandName($brand_id);
            }, $this->brand_ids);

            $text .= implode(', ', $brand_names);
        }

        return $text;
    }
}
