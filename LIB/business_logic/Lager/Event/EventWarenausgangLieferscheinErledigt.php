<?php

namespace wws\Lager\Event;

use bqp\Event\Event;
use wws\Lager\WarenausgangLieferschein;

class EventWarenausgangLieferscheinErledigt extends Event
{

    /**
     * @var WarenausgangLieferschein
     */
    private $warenausgang_lieferschein;

    public function __construct(WarenausgangLieferschein $warenausgang_lieferschein)
    {

        $this->warenausgang_lieferschein = $warenausgang_lieferschein;
    }

    public function getWarenausgangLieferschein(): WarenausgangLieferschein
    {
        return $this->warenausgang_lieferschein;
    }

    public function getMessage(): array
    {
        return [
            'lieferschein_id' => $this->warenausgang_lieferschein->getLieferscheinId()
        ];
    }
}
