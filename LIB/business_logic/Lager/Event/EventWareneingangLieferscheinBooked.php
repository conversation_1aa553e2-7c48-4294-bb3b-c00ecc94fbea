<?php

namespace wws\Lager\Event;

use bqp\Event\Event;
use wws\Lager\WareneingangLieferschein;

class EventWareneingangLieferscheinBooked extends Event
{
    /**
     * @var WareneingangLieferschein
     */
    private $wareneingang_lieferschein;

    public function __construct(WareneingangLieferschein $wareneingang_lieferschein)
    {
        $this->wareneingang_lieferschein = $wareneingang_lieferschein;
    }

    public function getWareneingangLieferschein(): WareneingangLieferschein
    {
        return $this->wareneingang_lieferschein;
    }
}
