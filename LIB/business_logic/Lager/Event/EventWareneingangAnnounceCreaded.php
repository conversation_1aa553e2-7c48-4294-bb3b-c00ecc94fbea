<?php

namespace wws\Lager\Event;

use bqp\Event\Event;
use wws\Lager\WareneingangAnnounce;

class EventWareneingangAnnounceCreaded extends Event
{
    /**
     * @var WareneingangAnnounce
     */
    private $wareneingang_announce;

    public function __construct(WareneingangAnnounce $wareneingang_announce)
    {
        $this->wareneingang_announce = $wareneingang_announce;
    }

    public function getWareneingangAnnounce(): WareneingangAnnounce
    {
        return $this->wareneingang_announce;
    }
}
