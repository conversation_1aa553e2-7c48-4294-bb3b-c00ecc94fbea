<?php

namespace wws\Lager;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;

class WareneingangAnnounceRepository
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * @param int $announce_id
     * @return WareneingangAnnounce
     * @throws SmartDataEntityNotFoundException
     */
    public function load(int $announce_id): WareneingangAnnounce
    {
        $data = $this->db->singleQuery("
            SELECT
                wareneingang_announces.announce_id,
                wareneingang_announces.supplier_id,
                wareneingang_announces.lieferschein_nr,
                wareneingang_announces.lieferschein_datum,
                wareneingang_announces.note
            FROM
                wareneingang_announces
            WHERE
                wareneingang_announces.announce_id = $announce_id
        ");

        if (!$data) {
            throw new SmartDataEntityNotFoundException();
        }

        $announce = new WareneingangAnnounce();
        $announce->_setAnnounceId($data['announce_id']);
        $announce->setSupplierId($data['supplier_id']);
        $announce->setLieferscheinNr($data['lieferschein_nr']);
        $announce->setLieferscheinDatum(new DateObj($data['lieferschein_datum']));
        $announce->setNote($data['note']);

        $result = $this->db->query("
            SELECT
                wareneingang_announce_items.announce_item_id,
                wareneingang_announce_items.gros_product_id,
                wareneingang_announce_items.gros_product_name,
                wareneingang_announce_items.product_id,
                wareneingang_announce_items.anzahl                
            FROM
                wareneingang_announce_items
            WHERE
                wareneingang_announce_items.announce_id = $announce_id
        ");

        foreach ($result as $row) {
            $announce->addItem($row['gros_product_id'], $row['anzahl'], $row['gros_product_name'], $row['product_id']);
        }

        $result = $this->db->query("
            SELECT
                wareneingang_announce_supplier_order_ids.supplier_order_id               
            FROM
                wareneingang_announce_supplier_order_ids
            WHERE
                wareneingang_announce_supplier_order_ids.announce_id = $announce_id
        ");

        foreach ($result as $row) {
            $announce->addSupplierOrderId($row['supplier_order_id']);
        }

        return $announce;
    }

    public function searchAnnounceIdsBySupplierOrderId(int $supplier_order_id): array
    {
        return $this->db->query("
            SELECT
                wareneingang_announce_supplier_order_ids.announce_id
            FROM
                wareneingang_announce_supplier_order_ids
            WHERE
                wareneingang_announce_supplier_order_ids.supplier_order_id = '" . $supplier_order_id . "'
        ")->asSingleArray();
    }

    public function getSupplierOrderIdsByAnnounceId(int $announce_id): array
    {
        return $this->db->query("
            SELECT
                wareneingang_announce_supplier_order_ids.supplier_order_id
            FROM
                wareneingang_announce_supplier_order_ids
            WHERE
                wareneingang_announce_supplier_order_ids.announce_id = '" . $announce_id . "'
        ")->asSingleArray();
    }

    public function getAnnounceAsArray(int $announce_id): array
    {
        $row = $this->db->singleQuery("
            SELECT
                wareneingang_announces.announce_id,
                wareneingang_announces.supplier_id,
                wareneingang_announces.lieferschein_nr,
                wareneingang_announces.lieferschein_datum,
                wareneingang_announces.note,

                supplier.supplier_name
            FROM
                wareneingang_announces INNER JOIN
                supplier ON (wareneingang_announces.supplier_id = supplier.supplier_id) 
            WHERE
                wareneingang_announces.announce_id = " . $announce_id . "
        ");

        if (!$row) {
            throw new SmartDataEntityNotFoundException();
        }

        $row['lieferschein_datum'] = new DateObj($row['lieferschein_datum']);

        return $row;
    }

    public function isDeliverySlipKnown(int $supplier_id, string $lieferschein_nr): bool
    {
        return (bool)$this->db->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                wareneingang_announces
            WHERE
                wareneingang_announces.supplier_id = $supplier_id AND
                wareneingang_announces.lieferschein_nr = '" . $this->db->escape($lieferschein_nr) . "'
        ");
    }
}
