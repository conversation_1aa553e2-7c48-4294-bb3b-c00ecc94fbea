<?php

namespace wws\Lager;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\extern\Google\VertexAI\VertexAI;
use bqp\Json;
use db;
use Exception;
use service_loader;
use wws\Supplier\SupplierOrderRepository;

class WareneingangAnnounceOcr
{
    private db_generic $db;
    private VertexAI $vertex;

    public function __construct()
    {
        $this->db = db::getInstance();

        $config = service_loader::getConfigRegistry()->get('k11/google_vertex_ai');

        $this->vertex = new VertexAI($config);
        $this->vertex->setModelId('gemini-1.5-pro');
        //$this->vertex->setModelId('gemini-1.5-flash');
        $this->vertex->setRetries(0);
        $this->vertex->setTemperature(0.1);

        $this->vertex->setJsonResponseSchema([
            'type' => 'object',
            'required' => ['delivery_slip_number', 'delivery_slip_date', 'positions_delivered'],
            'properties' => [
                'supplier' => [
                    'type' => 'object',
                    'properties' => [
                        'company' => ['type' => 'string'],
                        'ustidnr' => ['type' => 'string'],
                        'gln' => ['type' => 'string'],
                    ]
                ],
                'delivery_slip_number' => ['type' => 'string'],
                'delivery_slip_date' => ['type' => 'string'],
                'our_references' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ]
                ],
                'positions_delivered' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'object',
                        'required' => ['quantity_delivered', 'supplier_article_number', 'position_text'],
                        'properties' => [
                            'position_number' => ['type' => 'number'],
                            'quantity_delivered' => ['type' => 'number'],
                            'supplier_article_number' => ['type' => 'string'],
                            'position_text' => ['type' => 'string'],
                            'ean' => ['type' => 'string'],
                        ]
                    ]
                ]
            ],
        ]);
    }

    public function processPdfBlob(string $pdf_blob, ?int $supplier_id = null): WareneingangAnnounce
    {
        $warnings = [
            'benutzte bei den Lieferanten Groupe SEB, WMF, Silit, Tefal unbedingt den Wert aus der Spalte Artikelnummer als supplier_article_number. Die Nummer, die direkt vor der Artikelbezeichnung steht, ist Teil der Artikelbezeichnung.
            Z.B. "3201002555  1294106040 Set 4 Kids" -> supplier_article_number: 3201002555, position_text: 1294106040 Set 4 Kids',
        ];

        $prompt_text = "Das übergebene Dokument ist ein Lieferschein von einem Lieferanten an uns.
Bitte extrahiere folgendes:
1. den Lieferanten (Name/Company, Ustidnr, etc)
2. die Lieferscheinnummern (delivery slip number)
3. unsere Bestellnummern, teilweise auch als Referenznummern oder Kommission bezeichnet. Diese fangen i.d.R. mit B an und von 5 bis 7 Ziffern gefolgt. (our_references)
4. Eine Tabelle mit allen gelieferten Positionen. Zu jeder Position wird die Artikelnummer, die Anzahl, die Bezeichnung und falls verfügbar EAN benötigt.
    Die Artikelnummer steht i.d.R. in einer separaten Spalte. Nummern in der Bezeichnung/Produktnamen sind Teil dieser und nicht die gesuchte Artikelnummer.";

        if ($warnings) {
            $prompt_text .= "\nAchtung:\n-" . implode("\n-", $warnings);
        }

        $prompt = [
            [
                'inlineData' => [
                    'mimeType' => 'application/pdf',
                    'data' => base64_encode($pdf_blob),
                ]
            ],
            [
                'text' => $prompt_text
            ]
        ];

        $text_result = $this->vertex->promptAdv($prompt)->getPlainText();

        $result = Json::decode($text_result);

        $supplier_order_ids = $this->resolveSupplierOrderIds($result['our_references'] ?? []);

        if (!$supplier_id) {
            $supplier_id = $this->searchSupplierId($supplier_order_ids, $result['supplier'] ?? []);
        }

        $we_announce = new WareneingangAnnounce();
        $we_announce->setSupplierId($supplier_id);
        $we_announce->setLieferscheinNr($result['delivery_slip_number'] . '-OCR');

        $date = new DateObj($result['delivery_slip_date']);
        $we_announce->setLieferscheinDatum($date);

        foreach ($supplier_order_ids as $supplier_order_id) {
            $we_announce->addSupplierOrderId($supplier_order_id);
        }

        foreach ($result['positions_delivered'] as $item) {
            $we_announce->addItem($item['supplier_article_number'], $item['quantity_delivered'], $item['position_text']);
        }

        $we_announce->setNote('ACHTUNG: dieser Lieferankündigung wurde per OCR erstellt. Prüfe die Daten genau!');
        $we_announce->save();

        return $we_announce;
    }

    public function searchSupplierId(array $supplier_order_ids, array $supplier_data): int
    {
        if ($supplier_order_ids) {
            $result = $this->db->query("
                SELECT
                    supplier_order.supplier_id,
                    COUNT(*) AS found_supplier_orders
                FROM
                    supplier_order
                WHERE
                    supplier_order.supplier_order_id IN (" . $this->db->in($supplier_order_ids) . ")
                GROUP BY
                    supplier_order.supplier_id
                ORDER BY
                    found_supplier_orders DESC
            ");

            return $result->first()['supplier_id'];
        }

        throw new Exception('Lieferant konnte nicht bestimmt werden.');
    }

    private function resolveSupplierOrderIds(array $our_references): array
    {
        $supplier_order_ids = [];
        foreach ($our_references as $our_reference) {
            $supplier_order_ids = array_merge($supplier_order_ids, SupplierOrderRepository::searchSupplierOrderIds($our_reference));
        }

        return $supplier_order_ids;
    }
}
