<?php

namespace wws\Lager;

use bqp\db\db_generic;
use bqp\db\db_mysqli;
use bqp\Exceptions\DevException;
use db;
use Exception;
use service_loader;
use wws\Lager\Event\EventWareneingangLieferscheinBooked;
use wws\ProductStock\ProductStockBookingService;

class WareneingangLieferscheinBookingService
{
    private db_mysqli|db_generic $db;
    private ProductStockBookingService $product_stock_booking_service;

    public function __construct()
    {
        $this->db = db::getInstance();
        $this->product_stock_booking_service = service_loader::getDiContainer()->get(ProductStockBookingService::class);
    }

    public function preBook(WareneingangLieferschein $lieferschein): void
    {
        $lieferschein->setStatus($lieferschein::STATUS_PRE_BOOK);

        //seriennummern einbuchen

        WareneingangLieferscheinRepository::save($lieferschein);
    }

    public function doBook(WareneingangLieferschein $lieferschein): void
    {
        if (!$lieferschein->getLagerId()) {
            throw new DevException('lieferschein ohne $lager_id');
        }

        //lieferschein prüfen und daten auslesen
        if ($lieferschein->getStatus() != $lieferschein::STATUS_PRE_BOOK) {
            throw new Exception('Lieferschein schon eingebucht!');
        }


        //artikel in lager tabelle einbuachen
        foreach ($lieferschein->getItems() as $item) {
            //preis kann nicht 1:1 aus bestellung übernommen werden -> skonto und eventuell weitere abzüge sind nicht berücksichtigt
            /*$ek_netto = $db->fieldQuery("
                    SELECT
                        supplier_order_items.ek_netto
                    FROM
                        supplier_order_items
                    WHERE
                        supplier_order_items.supplier_order_id IN (".$db->makeIn($this->getSupplierOrderIds()).") AND
                        supplier_order_items.product_id = ".$db->quote($item->getProductId())."
                UNION
                    (
                        SELECT
                            product_ek.ek_netto
                        FROM
                            product_ek
                        WHERE
                            product_ek.product_id = '".$item->getProductId()."'
                        ORDER BY
                            IF(product_ek.supplier_id = '".$this->getSupplierId()."', 0, 1),
                            product_ek.ek_fav DESC
                    )
            ");*/

            $ek_netto = $this->db->fieldQuery("
                SELECT
                    product_ek.ek_netto
                FROM
                    product_ek
                WHERE
                    product_ek.product_id = '" . $item->getProductId() . "'
                ORDER BY
                    IF(product_ek.supplier_id = '" . $lieferschein->getSupplierId() . "', 0, 1),
                    product_ek.ek_fav DESC
                LIMIT
                    1
            ");

            $grund = 'Wareneingang (LID_E:' . $lieferschein->getLieferscheinId() . ')';
            if ($item->getBemerkung()) {
                $grund .= ' ' . $item->getBemerkung();
            }

            $this->product_stock_booking_service->book($item->getProductId(), $lieferschein->getLagerId(), $item->getAnzahl(), $grund, $ek_netto);

            $this->db->query("
                UPDATE
                    product_lager_items
                SET
                    product_lager_items.ek_netto = '" . $ek_netto . "'
                WHERE
                    product_lager_items.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "' AND
                    product_lager_items.product_id = '" . $item->getProductId() . "'
            ");
        }

        //labels updaten und auf richtiges lager setzen
        $this->db->query("
            UPDATE
                product_lager_items
            SET
                product_lager_items.lager_id = '" . $lieferschein->getLagerId() . "'
            WHERE
                product_lager_items.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "'
        ");

        //lieferschein status setzen
        $lieferschein->setStatus($lieferschein::STATUS_BOOKED);
        WareneingangLieferscheinRepository::save($lieferschein);

        $event = new EventWareneingangLieferscheinBooked($lieferschein);
        service_loader::getEventDispatcher()->dispatch($event);
    }
}
