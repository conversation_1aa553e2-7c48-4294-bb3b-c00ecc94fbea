<?php

namespace wws\Lager;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use bqp\Model\EntityChanges;
use bqp\Model\SmartDataObj;
use db;

class WareneingangLieferscheinRepository
{
    const STATUS_UNVOLLSTAENDIG = 0;
    const STATUS_OFFEN = 1;
    const STATUS_EINGEBUCHT = 2;

    public static $statuse = [
        0 => 'unvollständig',
        1 => 'offen',
        2 => 'eingebucht'
    ];

    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public static function getStatuse()
    {
        return self::$statuse;
    }

    public static function table_helper_status($daten, $key)
    {
        return self::$statuse[$daten[$key]];
    }

    /**
     * lädt ein Wareneingangs Lieferschein
     * @param int $lieferschein_id
     * @return WareneingangLieferschein
     */
    public static function load($lieferschein_id)
    {
        $daten = db::getInstance()->singleQuery("
                SELECT
                    wareneingang_lieferschein.lieferschein_id,
                    wareneingang_lieferschein.lieferscheinnr AS lieferschein_nr,
                    wareneingang_lieferschein.supplier_id,
                    wareneingang_lieferschein.supplier_order_ids,
                    wareneingang_lieferschein.datum,
                    wareneingang_lieferschein.status,
                    wareneingang_lieferschein.bemerkung,
                    wareneingang_lieferschein.bearbeiter,
                    wareneingang_lieferschein.lager_id
                FROM
                    wareneingang_lieferschein
                WHERE
                    wareneingang_lieferschein.lieferschein_id = '" . (int)$lieferschein_id . "'
        ");

        $lieferschein = new WareneingangLieferschein();
        $lieferschein->getSmartDataObj()->loadDaten($daten);

        return $lieferschein;
    }

    public static function loadItems(WareneingangLieferschein $lieferschein)
    {
        $lieferschein_id = $lieferschein->getLieferscheinId();

        if (!$lieferschein_id) {
            return;
        }

        $result = db::getInstance()->query("
                SELECT
                    wareneingang_lieferschein_items.item_id,
                    wareneingang_lieferschein_items.lieferschein_id,
                    wareneingang_lieferschein_items.product_id,
                    wareneingang_lieferschein_items.anzahl,
                    wareneingang_lieferschein_items.bemerkung,
                    wareneingang_lieferschein_items.pakete,
                    wareneingang_lieferschein_items.product_id_org,
                    wareneingang_lieferschein_items.bestell_item_id
                FROM
                    wareneingang_lieferschein_items
                WHERE
                    wareneingang_lieferschein_items.lieferschein_id = '" . $lieferschein_id . "'
        ");
        foreach ($result as $daten) {
            $lieferschein->createItem()->getSmartDataObj()->loadDaten($daten);
        }
    }

    /**
     * @param WareneingangLieferschein $lieferschein
     */
    public static function getAllChanges(WareneingangLieferschein $lieferschein)
    {
        $entity_changes = new EntityChanges('warenausgang_lieferschein');

        $changes = $lieferschein->getSmartDataObj()->getChanges(SmartDataObj::CHANGES_BOTH_VALUES);

        foreach ($changes as $key => $daten) {
            $entity_changes->add($key, $daten['new'], $daten['old'], 'stamm');
        }

        if ($lieferschein->__getItemsDirect()) {
            foreach ($lieferschein->__getItemsDirect() as $item) {
                $changes = $item->getSmartDataObj()->getChanges(SmartDataObj::CHANGES_BOTH_VALUES);

                switch ($item->getSmartDataObj()->getObjStatus()) {
                    case SmartDataObj::STATUS_NEW:
                        $entity_changes->addNew('item', $item);
                        break;
                    case SmartDataObj::STATUS_DEL:
                        $entity_changes->addRemove('item', $item);
                        break;
                }

                foreach ($changes as $key => $daten) {
                    $entity_changes->add($key, $daten['new'], $daten['old'], 'item', $item);
                }
            }
        }

        return $entity_changes;
    }

    public static function save(WareneingangLieferschein $lieferschein)
    {
        $protokoll = self::getAllChanges($lieferschein);

        $daten = $lieferschein->getSmartDataObj();

        if ($daten->getObjStatus() !== SmartDataObj::STATUS_LOADED) {
            if ($daten->getObjStatus() === SmartDataObj::STATUS_NEW) {
                $lieferschein->setDatum(new DateObj());
            }


            $db = db::getInstance();

            $sql = [];

            foreach ($daten->getChanges() as $field => $value) {
                switch ($field) {
                    case 'lieferschein_nr':
                        $sql[] = "wareneingang_lieferschein.lieferscheinnr = '" . $db->escape($value) . "'";
                        break;
                    default:
                        $sql[] = "wareneingang_lieferschein.$field = '" . $db->escape($value) . "'";
                }
            }

            switch ($daten->getObjStatus()) {
                case SmartDataObj::STATUS_NEW:
                    $db->query("
                        INSERT INTO
                            wareneingang_lieferschein
                        SET
                            " . implode(',', $sql) . "
                    ");

                    $daten->setterDirect('lieferschein_id', $db->insert_id());
                    break;
                case SmartDataObj::STATUS_UPDATE:
                    $db->query("
                        UPDATE
                            wareneingang_lieferschein
                        SET
                            " . implode(',', $sql) . "
                        WHERE
                            wareneingang_lieferschein.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "'
                    ");
                    break;
            }
            $daten->setSaved();
        }

        self::saveItems($lieferschein);

        $protokoll->setEntityId($lieferschein->getLieferscheinId());
        $protokoll->save();

        return $lieferschein->getLieferscheinId();
    }

    private static function saveItems(WareneingangLieferschein $lieferschein): void
    {
        if (!$lieferschein->__getItemsDirect()) {
            return;
        }

        $db = db::getInstance();

        foreach ($lieferschein->__getItemsDirect() as $item) {
            $daten = $item->getSmartDataObj();

            if ($daten->getObjStatus() == SmartDataObj::STATUS_NEW) {
                $item->setLieferscheinId($lieferschein->getLieferscheinId());
            }

            if ($daten->getObjStatus() == SmartDataObj::STATUS_LOADED) {
                continue;
            }

            $sql = [];

            foreach ($daten->getChanges() as $field => $value) {
                switch ($field) {
                    default:
                        $sql[] = "wareneingang_lieferschein_items.$field = '" . $db->escape($value) . "'";
                }
            }

            switch ($daten->getObjStatus()) {
                case SmartDataObj::STATUS_NEW:
                    $db->query("
                        INSERT INTO
                            wareneingang_lieferschein_items
                        SET
                            " . implode(',', $sql) . "
                    ");

                    $daten->setterDirect('item_id', $db->insert_id());
                    $daten->setSaved();
                    break;
                case SmartDataObj::STATUS_UPDATE:
                    $db->query("
                        UPDATE
                            wareneingang_lieferschein_items
                        SET
                            " . implode(',', $sql) . "
                        WHERE
                            wareneingang_lieferschein_items.item_id = '" . $item->getItemId() . "' AND
                            wareneingang_lieferschein_items.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "'
                    ");
                    $daten->setSaved();
                    break;
                case SmartDataObj::STATUS_DEL:
                    $db->query("
                        DELETE FROM
                            wareneingang_lieferschein_items
                        WHERE
                            wareneingang_lieferschein_items.item_id = '" . $item->getItemId() . "' AND
                            wareneingang_lieferschein_items.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "'
                    ");

                    break;
            }
        }
    }

    public function getDuplicatedDeliverySlips(string $lieferscheinnr, int $supplier_id): array
    {
        $result = $this->db->query("
			SELECT
				wareneingang_lieferschein.lieferschein_id,
			    wareneingang_lieferschein.datum
			FROM
				wareneingang_lieferschein
			WHERE
				wareneingang_lieferschein.lieferscheinnr = '" . $this->db->escape($lieferscheinnr) . "' AND
			    wareneingang_lieferschein.supplier_id = '" . $supplier_id . "'
			ORDER BY
			    wareneingang_lieferschein.datum
			LIMIT
			    10
		")->asArray();

        if (count($result) > 5) {
            return [];
        }

        return $result;
    }

    /**
     *
     * @return WareneingangLieferschein
     */
    public static function create(): WareneingangLieferschein
    {
        return new WareneingangLieferschein();
    }

    public static function delete(WareneingangLieferschein $lieferschein): void
    {
        if ($lieferschein->getStatus() != $lieferschein::STATUS_PRE_BOOK) {
            throw new FatalException('Dieser Lieferschein kann nicht gelöscht werden.');
        }

        $db = db::getInstance();

        $db->query("
            DELETE FROM
                product_lager_items
            WHERE
                product_lager_items.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "'
        ");

        $db->query("
            DELETE FROM
                wareneingang_lieferschein_items
            WHERE
                wareneingang_lieferschein_items.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "'
        ");

        $db->query("
            DELETE FROM
                wareneingang_lieferschein
            WHERE
                wareneingang_lieferschein.lieferschein_id = '" . $lieferschein->getLieferscheinId() . "'
        ");
    }
}
