<?php

namespace wws\Lager\Table;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\db\SqlUtils;
use bqp\Exceptions\InputException;
use bqp\table\DataSource\TableObjectDataSourceWws;
use bqp\table\field\table_object_field_callback;
use bqp\table\TableObjectPersistent;
use wws\Lager\WarenausgangLieferschein;
use wws\Tracking\TrackingLinkBuilder;

class TableBuilderWarenausgangLieferscheine
{
    const MODUS_NORMAL = 'normal';
    const MODUS_TRACKING = 'tracking';

    /**
     * @var null|string
     */
    public $product_name;

    /**
     * @var null|int
     */
    public $product_id;

    /**
     * @var null|DateObj
     */
    public $date_till;

    /**
     * @var null|DateObj
     */
    public $date_from;

    /**
     * @var null|int[]
     */
    public $lager_ids;

    /**
     * @var null|array
     */
    public $statuse;

    /**
     * @var null|int[]
     */
    public $sped_ids;

    /**
     * @var null|int
     */
    private $order_id;

    /**
     * @var null|int
     */
    private $shop_id;

    /**
     * @var string
     */
    private $modus;

    private db_generic $db;

    /**
     * @var array
     */
    private $order_origin_ids;

    /**
     * @var string
     */
    private $tracking_carrier_name = '';

    public function __construct(db_generic $db)
    {
        $this->setModus(self::MODUS_NORMAL);
        $this->db = $db;
    }

    public function build(?string $filter_id = null): TableObjectPersistent
    {
        $where = '1';

        if ($this->shop_id) {
            $where .= " AND orders.shop_id = '" . (int)$this->shop_id . "' ";
        }

        if ($this->statuse) {
            $where .= " AND warenausgang_lieferschein.status IN (" . $this->db->in($this->statuse) . ")";
        }

        if ($this->sped_ids) {
            $where .= " AND warenausgang_lieferschein.sped_id IN (" . $this->db->in($this->sped_ids) . ")";
        }

        if ($this->lager_ids) {
            $where .= " AND warenausgang_lieferschein.lager_id IN (" . $this->db->in($this->lager_ids) . ")";
        }

        if ($this->date_from) {
            $where .= " AND warenausgang_lieferschein.datum >= '" . $this->date_from->db('begin') . "'";
        }

        if ($this->date_till) {
            $where .= " AND warenausgang_lieferschein.datum <= '" . $this->date_till->db('end') . "'";
        }

        if ($this->order_id) {
            $where .= " AND warenausgang_lieferschein.order_id = '" . (int)$this->order_id . "'";
        }

        if ($this->order_origin_ids) {
            $where .= " AND orders.order_origin_id IN (" . $this->db->in($this->order_origin_ids) . ") ";
        }

        if ($this->tracking_carrier_name) {
            if ($this->modus === self::MODUS_TRACKING) {
                $where .= " AND tracking_carrier.tracking_carrier_name LIKE '%" . $this->db->escape($this->tracking_carrier_name) . "%'";
            } else {
                throw new InputException('"Tracking - Spedition" wird nur in der Tracking-Ansicht unterstützt');
            }
        }

        $products_sql = '';
        if ($this->product_id) {
//            $products_sql = " INNER JOIN (
//				SELECT
//					warenausgang_lieferschein_items.lieferschein_id
//				FROM
//					warenausgang_lieferschein_items
//				WHERE
//					warenausgang_lieferschein_items.product_id = '".(int)$values['product']['product_id']."'
//			) AS t ON (t.lieferschein_id = warenausgang_lieferschein.lieferschein_id)";

//            $where .= " AND warenausgang_lieferschein.lieferschein_id IN (
//                        SELECT
//                            warenausgang_lieferschein_items.lieferschein_id
//                        FROM
//                            warenausgang_lieferschein_items
//                        WHERE
//                            warenausgang_lieferschein_items.product_id = '".(int)$values['product']['product_id']."'
//                   )
//			";

            //man... ohne straight join kommt grade kein sinnvoller execution plan in der live db zustande -> also per Hand:
            $lieferschein_ids = $this->db->query("
                SELECT
                    warenausgang_lieferschein_items.lieferschein_id
                FROM
                    warenausgang_lieferschein_items
                WHERE
                    warenausgang_lieferschein_items.product_id = '" . (int)$this->product_id . "'
            ")->asSingleArray();

            $where .= " AND warenausgang_lieferschein.lieferschein_id IN (" . $this->db->in($lieferschein_ids) . ")";
        } elseif ($this->product_name) {
//            $products_sql = " INNER JOIN (
//				SELECT
//					warenausgang_lieferschein_items.lieferschein_id
//				FROM
//					product INNER JOIN
//					warenausgang_lieferschein_items ON (product.product_id = warenausgang_lieferschein_items.product_id)
//				WHERE
//					product.product_name LIKE '".SqlUtils::parseSearchTerm($values['product']['product_name'])."'
//			) AS t ON (t.lieferschein_id = warenausgang_lieferschein.lieferschein_id)";

            $lieferschein_ids = $this->db->query("
                SELECT
					warenausgang_lieferschein_items.lieferschein_id
				FROM
					product INNER JOIN
					warenausgang_lieferschein_items ON (product.product_id = warenausgang_lieferschein_items.product_id)
				WHERE
					product.product_name LIKE '" . SqlUtils::searchString($this->product_name) . "'
            ")->asSingleArray();

            $where .= " AND warenausgang_lieferschein.lieferschein_id IN (" . $this->db->in($lieferschein_ids) . ")";
        }

        if ($this->modus === self::MODUS_TRACKING) {
            $sql = "
				SELECT
					warenausgang_lieferschein.lieferschein_id,
					MAKRO.customers.customer_nr,
					MAKRO.customers.customer_name_firma,
					warenausgang_lieferschein.auftnr,
					warenausgang_lieferschein.supplier_order_id,
					MAKRO.order_tags_small,
				    orders.order_id,
					warenausgang_lieferschein.datum,
					warenausgang_lieferschein.lager_id,

					warenausgang_lieferschein.sped_id,

					warenausgang_lieferschein.status,

					warenausgang_lieferschein.exportiert,

					[
						tracking_carrier.tracking_carrier_id,
						tracking_carrier.tracking_carrier_name,
						warenausgang_lieferschein_tracking_ids.tracking_id,
						warenausgang_lieferschein_tracking_ids.tracking_url
					] AS tracking
				FROM
					warenausgang_lieferschein INNER JOIN
					customers ON (customers.customer_id = warenausgang_lieferschein.customer_id) INNER JOIN
					orders ON (warenausgang_lieferschein.order_id = orders.order_id) LEFT JOIN
					warenausgang_lieferschein_tracking_ids ON (warenausgang_lieferschein.lieferschein_id = warenausgang_lieferschein_tracking_ids.lieferschein_id) LEFT JOIN
					tracking_carrier ON (warenausgang_lieferschein_tracking_ids.tracking_carrier_id = tracking_carrier.tracking_carrier_id)
					$products_sql
				WHERE
					$where
				GROUP BY
					warenausgang_lieferschein.lieferschein_id
			";

            $ds = new TableObjectDataSourceWws($this->db, $sql);

            $table = new TableObjectPersistent($ds, $filter_id);

            $table->getFieldByKey('tracking')->setName('Tracking');
            $table->getFieldByKey('tracking_carrier_name')->setName('Spedition');
            $table->removeFieldByKey('tracking_url');
            $table->removeFieldByKey('tracking_carrier_id');

            $field = $table->getFieldByKey('tracking_id')->convert('callback');
            $field->setName('Sendungsnnummer');
            $field->setCallback(function (array $row) {
                if (!$row['tracking_carrier_id']) {
                    return '';
                }

                $return = '';

                if (!empty($row['tracking_url'])) {
                    $link = $row['tracking_url'];
                } else {
                    $link = TrackingLinkBuilder::buildTrackingUrlWws($row['tracking_carrier_id'], $row['tracking_id']);
                }

                if ($link) {
                    $return .= '<a href="' . $link . '" target="_blank">' . $row['tracking_id'] . '</a>';
                } else {
                    $return .= $row['tracking_id'];
                }

                if ($row['tracking_id'] && $row['tracking_id'] === $row['tracking'][0]['tracking_id']) {
                    $return .= '&nbsp; &nbsp; &nbsp;<a href="/ax/lager/tracking/tracking_details/?lieferschein_id=' . $row['lieferschein_id'] . '" style="color: #999; font-size: 10px;" onclick="popup_large(event)">Details</a>';
                }

                return $return;
            });
        } else {
            $sql = "
				SELECT
					warenausgang_lieferschein.lieferschein_id,
					MAKRO.customers.customer_nr,
					MAKRO.customers.customer_name_firma,
					warenausgang_lieferschein.auftnr,
					MAKRO.order_tags_small,
				    orders.order_id,
					warenausgang_lieferschein.datum,
					warenausgang_lieferschein.lager_id,
					orders.zahlungs_id,
					MAKRO.warenausgang_lieferschein.grouped_products,
					warenausgang_lieferschein.sped_id,

					warenausgang_lieferschein.status,

					warenausgang_lieferschein.exportiert,
					1 AS has_tracking
				FROM
					warenausgang_lieferschein INNER JOIN
					warenausgang_lieferschein_items ON (warenausgang_lieferschein.lieferschein_id = warenausgang_lieferschein_items.lieferschein_id) INNER JOIN
					customers ON (customers.customer_id = warenausgang_lieferschein.customer_id) LEFT JOIN
					order_item ON (warenausgang_lieferschein_items.order_item_id = order_item.order_item_id) INNER JOIN
					orders ON (warenausgang_lieferschein.order_id = orders.order_id)
					$products_sql
				WHERE
					$where
				GROUP BY
					warenausgang_lieferschein.lieferschein_id
			";

            $ds = new TableObjectDataSourceWws($this->db, $sql);
            $ds->setDataCallback(function (array $result) {
                if (!$result) {
                    return $result;
                }

                //Lieferscheine kennezichnen für die Tracking Infromationen existierten. Durch den join auf order_item ist das so effektiver.
                $lieferschein_ids = array_column($result, 'lieferschein_id');

                $found = $this->db->query("
                    SELECT
                        DISTINCT
                        warenausgang_lieferschein_tracking_ids.lieferschein_id,
                        1
                    FROM
                        warenausgang_lieferschein_tracking_ids
                    WHERE
                        warenausgang_lieferschein_tracking_ids.lieferschein_id IN (" . $this->db->in($lieferschein_ids) . ")
                ")->asSingleArray('lieferschein_id');

                foreach ($result as $key => $row) {
                    $result[$key]['has_tracking'] = isset($found[$row['lieferschein_id']]);
                }

                return $result;
            });


            $table = new TableObjectPersistent($ds, $filter_id);

            $field = new table_object_field_callback('tracking_url', 'Tracking');
            $field->setCallback(function (array $row) {
                if ($row['has_tracking']) {
                    return '<a href="/ax/lager/tracking/tracking_details/?lieferschein_id=' . $row['lieferschein_id'] . '" onclick="popup_large(event)"><i class="fa fa-truck" style="scale: 1.5; color: #005992;"></i></a>';
                }

                return '';
            });
            $field->setAlign('center');
            $table->addFieldAfter($field, 'sped_id');

            $table->removeFieldByKey('has_tracking');
        }

        $table->removeFieldByKey('order_id');
        $table->setExportEnabled(true);
        $table->setCaption('Lieferscheine');

        $table->removeFieldByKey('exportiert');
        $table->addField($this->table_lieferschein_actions());

        return $table;
    }

    public function table_lieferschein_actions()
    {
        $field = new table_object_field_callback('actrion', 'Aktion');
        $field->setCallback(function ($daten) {
            global $_user;

            $return = '<nobr>';
            $return .= '<a href="/ax/auftraege/lieferschein/lieferschein/?lieferschein_id=' . $daten['lieferschein_id'] . '" class="link_aktion_green" target="zpop" onclick="openpop(1000,600,\'yes\')">Anzeigen</a>';
            $return .= '<a href="/ax/auftraege/lieferschein/pdf/?lieferschein_id=' . $daten['lieferschein_id'] . '" class="link_aktion_green" target="zpop"> (PDF)</a>';

            if ($daten['status'] === WarenausgangLieferschein::STATUS_OFFEN) {
                $return .= '&nbsp; &nbsp; <a href="/av/lager/index.php?page=warenausgang&modul=lieferschein&lieferschein_id=' . $daten['lieferschein_id'] . '" class="link_aktion_green">Versenden</a>';
            }

            $return .= '&nbsp; &nbsp; <a href="/ax/lager/shipping_label/?lieferschein_id=' . $daten['lieferschein_id'] . '" class="link_aktion_blue" onclick="popup_standard(event)">Export</a>';

            if ($_user->hasRight('rights.stornoWarenausgangLieferschein')) {
                if ($daten['status'] === WarenausgangLieferschein::STATUS_OFFEN) {
                    $return .= '&nbsp; &nbsp; <a href="/ax/lager/warenausgang_lieferscheine/storno_lieferschein/?lieferschein_id=' . $daten['lieferschein_id'] . '" class="link_aktion_red" target="inline">Stornieren</a>';
                }
            }

            if ($daten['status'] === WarenausgangLieferschein::STATUS_ERLEDIGT && $_user->hasRight('rights.lateStornoWarenausgangLieferschein')) {
                $return .= '&nbsp; &nbsp; <a href="/ax/lager/warenausgang_lieferscheine/storno_lieferschein/?lieferschein_id=' . $daten['lieferschein_id'] . '" class="link_aktion_red" target="inline">Nachträglich stornieren</a>';
            }

            $return .= '</nobr>';

            return $return;
        });

        return $field;
    }


    public function setModus(string $modus): void
    {
        $this->modus = $modus;
    }

    public function setShopId(int $shop_id): void
    {
        $this->shop_id = $shop_id;
    }

    public function setStatuse(array $statuse): void
    {
        $this->statuse = $statuse;
    }

    public function setSpedIds(array $sped_ids): void
    {
        $this->sped_ids = $sped_ids;
    }

    public function setLagerIds(array $lager_ids): void
    {
        $this->lager_ids = $lager_ids;
    }

    public function setDateFrom(DateObj $date_from): void
    {
        $this->date_from = $date_from;
    }

    public function setDateTill(DateObj $date_till): void
    {
        $this->date_till = $date_till;
    }

    public function setProductId(int $product_id): void
    {
        $this->product_id = $product_id;
    }

    public function setProductName(string $product_name): void
    {
        $this->product_name = $product_name;
    }

    public function setOrderId(int $order_id): void
    {
        $this->order_id = $order_id;
    }

    public function setOrderOriginIds(array $order_origin_ids): void
    {
        $this->order_origin_ids = $order_origin_ids;
    }

    public function setTrackingCarrierName(string $tracking_carrier_name): void
    {
        $this->tracking_carrier_name = $tracking_carrier_name;
    }
}
