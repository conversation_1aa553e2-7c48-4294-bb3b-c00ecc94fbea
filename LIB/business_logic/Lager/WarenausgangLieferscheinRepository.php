<?php

namespace wws\Lager;

use db;
use order_repository;

class WarenausgangLieferscheinRepository
{

    /**
     * gibt ein asoc array mit den möglichen Lieferscheinbstatusen zurück
     * status => name
     * @return array
     */
    public static function getLieferscheinStatusNames(): array
    {
        return [
            WarenausgangLieferschein::STATUS_OFFEN => 'offen',
            WarenausgangLieferschein::STATUS_ERLEDIGT => 'erledigt',
            WarenausgangLieferschein::STATUS_STORNIERT => 'storniert',
            WarenausgangLieferschein::STATUS_TRANSFER => 'transfer',
            WarenausgangLieferschein::STATUS_LATE_STORNO => 'nachträglich storniert'
        ];
    }

    public static function tableHelper_status($daten, $field)
    {
        $status_names = self::getLieferscheinStatusNames();

        return $status_names[$daten[$field]];
    }


    public static function parseWarenausgangLieferscheinRef($ref)
    {
        $return = [
            'auftnr' => null,
            'lieferschein_id' => null
        ];

        if (preg_match('~' . order_repository::getAuftnrRegexp() . '~', $ref, $temp)) {
            $return['auftnr'] = $temp[1];
        }

        if (preg_match('~LID(?:|:)([0-9]{4,6})~', $ref, $temp)) {
            $return['lieferschein_id'] = $temp[1];
        }

        return $return;
    }


    /**
     * @param int $lieferschein_id
     * @return int $order_id
     */
    public static function getOrderIdByLieferscheinId($lieferschein_id)
    {
        $order_id = db::getInstance()->fieldQuery("
            SELECT
                warenausgang_lieferschein.order_id
            FROM
                warenausgang_lieferschein
            WHERE
                warenausgang_lieferschein.lieferschein_id = '" . (int)$lieferschein_id . "'
        ");

        return $order_id;
    }


    public static function getLastLieferscheinIdByOrderId($order_id)
    {
        $lieferschein_id = db::getInstance()->fieldQuery("
            SELECT
                MAX(warenausgang_lieferschein.lieferschein_id)
            FROM
                warenausgang_lieferschein
            WHERE
                warenausgang_lieferschein.order_id = '" . (int)$order_id . "'
        ");

        return $lieferschein_id;
    }

    public static function getOpenLieferscheinIdByOrderId(int $order_id): ?int
    {
        $lieferschein_id = db::getInstance()->fieldQuery("
            SELECT
                warenausgang_lieferschein.lieferschein_id
            FROM
                warenausgang_lieferschein
            WHERE
                warenausgang_lieferschein.order_id = '" . (int)$order_id . "' AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_OFFEN . "'
        ");

        return $lieferschein_id ?: null;
    }
}
