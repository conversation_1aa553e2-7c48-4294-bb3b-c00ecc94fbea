<?php

namespace wws\Lager;

use bqp\Csv\TableReader;
use bqp\Csv\TableReaderCsvSource;
use bqp\db\db_generic;
use bqp\table\DataSource\TableObjectDataSourceArray;
use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_currency;
use bqp\table\field\table_object_field_integer;
use bqp\table\Renderer\TableObjectRendererPhpSpreadsheet;
use bqp\table\TableObject;
use config;
use InvalidArgumentException;

class WarehouseValue
{

    private db_generic $db;
    private string $archive_directory;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
        $this->archive_directory = config::getLegacy('system')->archiv_dir . '/lagerbestand/';
    }

    public function buildAllReports(): void
    {
        $lager_result = $this->db->query("
            SELECT
                einst_lager.lager_id
            FROM
                einst_lager
        ")->asSingleArray();

        foreach ($lager_result as $lager_id) {
            $this->buildReport($lager_id);
        }
    }

    public function buildReport(int $lager_id): void
    {
        $result = $this->db->query("
            SELECT
                product_lager_detail.product_id,
    
                product_lager_detail.lager_real,
                product_lager_detail.lager_abgang,
                product_lager_detail.lager_ek,
                
                product_ek.ek_netto,
                product.lager_qualifikation
            FROM
                product_lager_detail LEFT JOIN
                product_ek ON (product_lager_detail.product_id = product_ek.product_id AND product_ek.ek_fav = 1) INNER JOIN
                product ON (product.product_id = product_lager_detail.product_id) INNER JOIN
                product_warenkorb_types ON (product.product_warenkorb_typ = product_warenkorb_types.product_warenkorb_typ)
            WHERE
                product_lager_detail.lager_id = '$lager_id' AND
                product_lager_detail.lager_real > 0 AND
                product_warenkorb_types.bestand_count = 1
        ")->asArray();

        if (count($result) == 0) {
            return;
        }

        $this->writeReportArchiveFile($lager_id, $result);
        $this->writeReportSummary($lager_id, $result);
    }

    private function writeReportArchiveFile(int $lager_id, array $result): void
    {
        $path = $this->archive_directory . '/' . date('Ym') . '/';
        if (!file_exists($path)) {
            mkdir($path);
        }

        $csv_blob = "product_id;lager_real;lager_abgang;ek_netto;lager_qualifikation;lager_ek\n";
        foreach ($result as $daten) {
            $csv_blob .= $daten['product_id'] . ';' . $daten['lager_real'] . ';' . $daten['lager_abgang'] . ';' . round($daten['ek_netto'], 2) . ';' . $daten['lager_qualifikation'] . ';' . $daten['lager_ek'] . "\n";
        }

        $gz = gzopen($path . date('Ymd') . '_' . $lager_id . '.csv.gz', 'w9');
        gzputs($gz, $csv_blob);
        gzclose($gz);
    }

    private function writeReportSummary(int $lager_id, array $result): void
    {
        $anzahl = 0;
        $wert = 0;
        $wert_abgang = 0;
        $wert_lager_ek = 0;

        foreach ($result as $daten) {
            $anzahl += $daten['lager_real'];
            $wert += $daten['lager_real'] * $daten['ek_netto'];
            $wert_abgang += $daten['lager_abgang'] * $daten['ek_netto'];
            $wert_lager_ek += $daten['lager_real'] * $daten['lager_ek'];
        }

        $this->db->query("
            INSERT INTO
                statistik_lagerwert
            SET
                statistik_lagerwert.datum = '" . date('Ymd') . "',
                statistik_lagerwert.lager_id = '$lager_id',
                statistik_lagerwert.wert = '" . round($wert, 2) . "',
                statistik_lagerwert.anzahl = '" . round($anzahl) . "',
                statistik_lagerwert.wert_abgang = '" . round($wert_abgang, 2) . "',
                statistik_lagerwert.wert_lager_ek = '" . round($wert_lager_ek, 2) . "'
        ");
    }

    public function getReportCsvBlob(int $lager_id, string $date): string
    {
        $date = str_replace('-', '', $date);

        if (strlen($date) !== 8) {
            throw new InvalidArgumentException('date must be yyyy-mm-dd');
        }

        $file = $this->archive_directory . '/' . substr($date, 0, 6) . '/' . $date . '_' . $lager_id . '.csv.gz';

        return gzdecode(file_get_contents($file));
    }

    public function getReportDataExtended(int $lager_id, string $date_key): array
    {
        $csv_blob = $this->getReportCsvBlob($lager_id, $date_key);

        $src = new TableReaderCsvSource();
        $src->setCsvString($csv_blob, 'UTF-8');

        $table = new TableReader($src);
        $table->setInputFormatStatic([
            'product_id' => ['type' => 'string', 'checkHeader' => "product_id"],
            'lager_real' => ['type' => 'int', 'checkHeader' => "lager_real"],
            'lager_abgang' => ['type' => 'int', 'checkHeader' => "lager_abgang"],
            'ek_netto' => ['type' => 'float', 'checkHeader' => "ek_netto"],
            'lager_qualifikation' => ['type' => 'string', 'checkHeader' => "lager_qualifikation"],
            'lager_ek' => ['type' => 'float', 'checkHeader' => "lager_ek"],
        ]);

        $data = $table->getAsArray();

        $product_ids = array_column($data, 'product_id');

        $product_data = $this->db->query("
            SELECT
                product.product_id,
                product.product_nr,
                product.product_name,
                product.product_type
            FROM
                product   
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ")        
        ")->asArray('product_id');

        $table->addDataCallback(function (array $row) use ($product_data) {
            $row['product_name'] = $product_data[$row['product_id']]['product_name'] ?? '';
            $row['product_nr'] = $product_data[$row['product_id']]['product_nr'] ?? '';
            $row['product_type'] = $product_data[$row['product_id']]['product_type'] ?? '';

            return $row;
        });

        return $table->getAsArray();
    }

    public function getReportAsXlsx(int $lager_id, string $date): string
    {
        $data = $this->getReportDataExtended($lager_id, $date);

        $src = new TableObjectDataSourceArray($data);

        $table = new TableObject($src);
        $table->setEntriesPerPage(100000);


        $field = new table_object_field('product_id', 'Product-Id');
        $table->addField($field);

        $field = new table_object_field('product_nr', 'Produkt-Nr.');
        $table->addField($field);

        $field = new table_object_field('product_type', 'Produktgruppe');
        $table->addField($field);

        $field = new table_object_field('product_name', 'Produktname');
        $table->addField($field);

        $field = new table_object_field('lager_qualifikation', 'Lager-Qualifikation');
        $table->addField($field);

        $field = new table_object_field_integer('lager_real', 'Bestand');
        $table->addField($field);

        $field = new table_object_field_integer('lager_abgang', 'Bestand im Abgang');
        $table->addField($field);

        $field = new table_object_field_currency('lager_ek', 'Lager-EK netto pro Stk.');
        $table->addField($field);

        $field = new table_object_field_currency('ek_netto', 'FAV-EK netto pro Stk.');
        $table->addField($field);

        $renderer = new TableObjectRendererPhpSpreadsheet($table);

        $xlsx = $renderer->render();

        return $xlsx;
    }

    public function foobar()
    {
        $xlsx = $this->getReportAsXlsx(1, '20221231');

        var_dump($xlsx);

    }
}
