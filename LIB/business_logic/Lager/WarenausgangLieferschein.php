<?php

namespace wws\Lager;

use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Exceptions\InputException;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use config;
use db;
use DomainException;
use env;
use Laminas\Barcode\Barcode;
use service_loader;
use UnexpectedValueException;
use wws\Lager\Event\EventWarenausgangLieferscheinCreated;
use wws\Lager\Event\EventWarenausgangLieferscheinErledigt;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderRepository;
use wws\Product\ProductLagerItem;
use wws\ProductStock\ProductStockBookingService;
use wws\Shipment\ShipmentRepository;

class WarenausgangLieferschein
{
    const STATUS_ERLEDIGT = 'erledigt';
    const STATUS_OFFEN = 'offen';
    const STATUS_STORNIERT = 'storniert';
    const STATUS_LATE_STORNO = 'late_storno';
    const STATUS_ENTWURF = 'entwurf';
    const STATUS_TRANSFER = 'transfer';

    private SmartDataObj $daten;

    /**
     * @var array WarenausgangLieferscheinItem[]
     */
    private array $items = [];

    /**
     * @var ProductLagerItem[]
     */
    private array $serials = [];

    /**
     * serieals deren zuordnung aufgehoben werden soll
     * @var ProductLagerItem[]
     */
    private array $serials_remove = [];

    // <editor-fold defaultstate="collapsed" desc="Speichern/Laden">
    public function __construct(?int $lieferschein_id = null)
    {
        $this->daten = new SmartDataObj($this);

        if ($lieferschein_id !== null) {
            $this->load($lieferschein_id);
        } else {
            $this->loadDefaults();
        }
    }

    public function loadDefaults(): void
    {
        $this->daten->setter('status', self::STATUS_OFFEN);
        $this->daten->setter('document_template', config::documents('warenausgang_lieferschein'));
        $this->setExportDatensatz('');
        $this->setVersandParameter('');
        $this->setVersandlabel('');
        $this->daten->setter('assigned_serials', '');
        $this->setSupplierOrderId(0);
        $this->setUserId(env::getUserId());
    }

    /**
     * @param int $lieferschein_id
     * @throws SmartDataEntityNotFoundException
     */
    public function load(int $lieferschein_id): void
    {
        $db = db::getInstance();

        $daten = $db->singleQuery("
                SELECT
                    warenausgang_lieferschein.lieferschein_id,
                    warenausgang_lieferschein.customer_id,
                    warenausgang_lieferschein.order_id,
                    warenausgang_lieferschein.user_id,
                    warenausgang_lieferschein.auftnr,
                    warenausgang_lieferschein.datum,
                    warenausgang_lieferschein.shop_id,
                    warenausgang_lieferschein.status,
                    warenausgang_lieferschein.exportiert,
                    warenausgang_lieferschein.export_datensatz,
                    warenausgang_lieferschein.sped_id,
                    warenausgang_lieferschein.lager_id,
                    warenausgang_lieferschein.supplier_order_id,
                    warenausgang_lieferschein.versandmail_status,
                    warenausgang_lieferschein.versand_parameter,
                    warenausgang_lieferschein.datum_erledigt,
                    warenausgang_lieferschein.versand_pipe,
                    warenausgang_lieferschein.versandlabel,
                    warenausgang_lieferschein.assigned_serials,
                    warenausgang_lieferschein.document_template
                FROM
                    warenausgang_lieferschein
                WHERE
                    warenausgang_lieferschein.lieferschein_id = '" . $db->escape($lieferschein_id) . "'
            ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException('Lieferschein mit $lieferschein_id ' . $lieferschein_id . ' konnte nicht geladen werden.');
        }

        $this->daten->loadDaten($daten);
        $this->parseAssignedSerials();
    }

    public function loadItems(): void
    {
        if (!$this->getLieferscheinId()) {
            return;
        }
        if ($this->items) {
            return;
        }

        $db = db::getInstance();

        $result = $db->query("
                SELECT
                    warenausgang_lieferschein_items.lieferschein_item_id,
                    warenausgang_lieferschein_items.lieferschein_id,
                    warenausgang_lieferschein_items.product_id,
                    warenausgang_lieferschein_items.order_item_id,
                    warenausgang_lieferschein_items.anzahl,
                    warenausgang_lieferschein_items.lager_id,
                    warenausgang_lieferschein_items.item_status
                FROM
                    warenausgang_lieferschein_items
                WHERE
                    warenausgang_lieferschein_items.lieferschein_id = '" . $this->getLieferscheinId() . "'
            ");

        foreach ($result as $daten) {
            $item = new WarenausgangLieferscheinItem($this, $daten);
            $this->items[] = $item;
        }
    }

    protected function parseAssignedSerials()
    {
        $temp = $this->daten['assigned_serials'];
        if (!$temp) {
            return;
        }
        $temp = explode('||', $temp);

        foreach ($temp as $serial) {
            $this->serials[] = new ProductLagerItem($serial);
        }
    }

    public function validate()
    {
        $e = new InputException();
        if (!$this->getSpedId()) {
            $e->add('sped_id', 'Keine Spedition ausgewählt.');
        }
        if (!$this->getLagerId()) {
            $e->add('lager_id', 'Keine Lager ausgewählt.');
        }
        if (!$this->getOrderId()) {
            $e->add('order_id', 'Keine Bestellung ausgewählt.');
        }

        if (count($this->getItems()) == 0) {
            $e->add('items', 'Keine Lieferschein Position ausgewählt.');
        }

        $e->check();
    }

    public function save()
    {
        $this->validate();

        db::getInstance()->begin();

        $changes = $this->daten->getChanges(SmartDataObj::CHANGES_BOTH_VALUES);

        if (isset($changes['status'])) {
            $this->beforeStatusChange($changes['status']['old'], $changes['status']['new']);
        }

        $this->saveStamm();
        $this->saveItems();
        $this->saveSerials();

        if (isset($changes['status'])) {
            $this->statusChanged($changes['status']['old'], $changes['status']['new']);
        }

        db::getInstance()->commit();

        if (isset($changes['status'])) {
            switch ($this->getStatus()) {
                case self::STATUS_ERLEDIGT:
                    $event = new EventWarenausgangLieferscheinErledigt($this);
                    break;
                case self::STATUS_OFFEN:
                    $event = new EventWarenausgangLieferscheinCreated($this);
                    break;
                default:
                    $event = null;
            }

            if ($event) {
                service_loader::getEventDispatcher()->dispatch($event);
            }
        }

        return $this->getLieferscheinId();
    }

    protected function beforeStatusChange($old_status, $new_status)
    {
        //illegale status änderungen
        if (
            (!$old_status && $new_status == self::STATUS_ERLEDIGT) ||
            (!$old_status && $new_status == self::STATUS_STORNIERT) ||
            ($old_status == self::STATUS_ENTWURF && $new_status == self::STATUS_ERLEDIGT) ||
            ($old_status == self::STATUS_ERLEDIGT && $new_status != self::STATUS_LATE_STORNO) ||
            $old_status == self::STATUS_STORNIERT ||
            $old_status == self::STATUS_LATE_STORNO ||
            ($old_status == self::STATUS_OFFEN && $new_status == self::STATUS_ENTWURF)
        ) {
            throw new WarenausgangLieferscheinException('Illegaler Status-Wechsel. LID:' . $this->getLieferscheinId());
        }

        //bestände prüfen
        if ($new_status == self::STATUS_OFFEN) {
            if (!LagerRepository::isBookNegative($this->getLagerId())) {
                foreach ($this->getItems() as $item) {
                    $lager_bestand = db::getInstance()->fieldQuery("
                                SELECT
                                    product_lager_detail.lager_bestand
                                FROM
                                    product_lager_detail
                                WHERE
                                    product_lager_detail.product_id = '" . $item->getProductId() . "' AND
                                    product_lager_detail.lager_id = '" . $this->getLagerId() . "'
                            ");

                    if ($item->getAnzahl() > $lager_bestand) {
                        throw new WarenausgangLieferscheinException('Nicht genügend Geräte am Lager. (' . $item->getProductId() . ') (' . $lager_bestand . ') (order_id:' . $this->getOrderId() . ')');
                    }
                }
            }
        }

        //
        if ($new_status == self::STATUS_ERLEDIGT) {
            if (!$this->isSerialsComplete()) {
                throw new WarenausgangLieferscheinException('Dieser Lieferschein kann erst gebucht werden wenn alle benötigten Seriennummern erfasst wurden.');
            }
        }
    }

    protected function statusChanged($old_status, $new_status)
    {
        $booking_service = service_loader::getDiContainer()->get(ProductStockBookingService::class);

        //die prüfung ob der status wechsel möglich ist wird in self::beforeStatusChange() gemacht, damit kann hier darauf verzichtet werden
        $order = service_loader::get(OrderRepository::class)->loadCached($this->getOrderId());

        $grund = 'Lieferung für ' . $order->getAuftnr() . ' (LID_A:' . $this->getLieferscheinId() . ')';

        if ($old_status == self::STATUS_ENTWURF) {
            foreach ($this->getItems() as $item) {
                $order_item = $order->getOrderItemByOrderItemId($item->getOrderItemId());
                $order_item->chgQuantityPlaned(-$item->getAnzahl());
            }
        }

        if ($old_status == self::STATUS_OFFEN) {
            foreach ($this->getItems() as $item) {
                $order_item = $order->getOrderItemByOrderItemId($item->getOrderItemId());
                $order_item->chgQuantityProgress(-$item->getAnzahl());

                $booking_service->bookShippingReservation($item->getProductId(), $this->getLagerId(), -$item->getAnzahl());
            }
        }

        if ($new_status == self::STATUS_ENTWURF) {
            foreach ($this->getItems() as $item) {
                $order_item = $order->getOrderItemByOrderItemId($item->getOrderItemId());
                $order_item->chgQuantityPlaned($item->getAnzahl());
            }
        }

        if ($new_status == self::STATUS_OFFEN) {
            foreach ($this->getItems() as $item) {
                $order_item = $order->getOrderItemByOrderItemId($item->getOrderItemId());
                $order_item->chgQuantityProgress($item->getAnzahl());

                $booking_service->bookShippingReservation($item->getProductId(), $this->getLagerId(), $item->getAnzahl());
            }

            if ($order->isCompleteDeliverd()) {
                $order->setStatus(OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST);
            }
        }

        if ($new_status == self::STATUS_ERLEDIGT) {
            foreach ($this->getItems() as $item) {
                $order_item = $order->getOrderItemByOrderItemId($item->getOrderItemId());
                $order_item->chgQuantityCompleted($item->getAnzahl());

                $booking_service->book($item->getProductId(), $this->getLagerId(), -$item->getAnzahl(), $grund);
            }

            //lager items ausbuchen
            foreach ($this->serials as $product_lager_item) {
                $product_lager_item->setStatus(ProductLagerItem::STATUS_AUS, $grund);
            }

            $this->setStatus(self::STATUS_ERLEDIGT);

            if ($order->isCompleteDeliverd()) {
                $order->setStatus(OrderConst::STATUS_ZUSTELLUNG_BEENDET);
            }
        }

        if ($new_status == self::STATUS_LATE_STORNO) {
            foreach ($this->getItems() as $item) {
                $booking_service->book($item->getProductId(), $this->getLagerId(), $item->getAnzahl(), $order->getAuftnr() . ' Lieferschein nachträglich storniert (LID:' . $this->getLieferscheinId() . ')');

                $order_item = $order->getOrderItemByOrderItemId($item->getOrderItemId());
                $order_item->chgQuantityCompleted(-$item->getAnzahl());
            }
        }

        $order->save();
    }

    /**
     * bucht den Lieferschein aus, je nach order_type wird der Auftrag beendet oder auf zustellenung aktiv gesetzt
     */
    public function buchen()
    {
        $this->setStatus(self::STATUS_ERLEDIGT);
        $this->save();
    }


    protected function saveStamm()
    {
        if (!$this->daten->isChange()) {
            return;
        }

        if ($this->daten->getObjStatus() == SmartDataObj::STATUS_NEW and !$this->daten['datum']) {
            $this->setDatum(new DateObj());
        }

        $changes = $this->daten->getChanges();

        $db = db::getInstance();


        $sql = [];
        foreach ($changes as $field => $value) {
            switch ($field) {
                default:
                    $sql[] = "warenausgang_lieferschein.$field = '" . $db->escape($value) . "'";
            }
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db->query("
                        INSERT INTO
                            warenausgang_lieferschein
                        SET
                            " . implode(',', $sql) . "
                    ");

                $this->setLieferscheinId($db->insert_id());
                break;
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                        UPDATE
                            warenausgang_lieferschein
                        SET
                            " . implode(',', $sql) . "
                        WHERE
                            warenausgang_lieferschein.lieferschein_id = '" . $this->getLieferscheinId() . "'
                    ");
                break;
        }

        $this->daten->setSaved();
    }

    protected function saveItems()
    {
        if (!$this->items) {
            return;
        }

        $db = db::getInstance();

        foreach ($this->items as $item_key => $item) {
            $obj = $item->getSmartDataObj();

            $sql = [];
            foreach ($obj->getChanges() as $field => $value) {
                switch ($field) {
                    default:
                        $sql[] = "warenausgang_lieferschein_items.$field = '" . $db->escape($value) . "'";
                }
            }

            switch ($obj->getObjStatus()) {
                case SmartDataObj::STATUS_NEW:
                    $sql[] = "warenausgang_lieferschein_items.lieferschein_id = '" . $db->escape($this->getLieferscheinId()) . "'";

                    $db->query("
                            INSERT INTO
                                warenausgang_lieferschein_items
                            SET
                                " . implode(',', $sql) . "
                        ");

                    $obj->setterDirect('lieferschein_item_id', $db->insert_id());

                    break;
                case SmartDataObj::STATUS_UPDATE:
                    $db->query("
                            UPDATE
                                warenausgang_lieferschein_items
                            SET
                                " . implode(',', $sql) . "
                            WHERE
                                warenausgang_lieferschein_items.lieferschein_item_id = '" . $item->getLieferscheinItemId() . "'
                        ");
                    break;
                case SmartDataObj::STATUS_DEL:
                    $db->query("
                            DELETE FROM
                                warenausgang_lieferschein_items
                            WHERE
                                warenausgang_lieferschein_items.lieferschein_item_id = '" . $item->getLieferscheinItemId() . "'
                        ");

                    unset($this->items[$item_key]);

                    break;
            }

            $obj->setSaved();
        }
    }

    protected function saveSerials()
    {
        //seriennummern entfernen
        foreach ($this->serials_remove as $product_lager_item) {
            if ($product_lager_item->getStatus() == 'assigned') {
                $product_lager_item->setStatus('frei');
                $product_lager_item->setLieferscheinId(0);
                $product_lager_item->setOrderItemId(0);
            }

            $product_lager_item->save();
        }

        $this->serials_remove = [];

        //seriennummern speichern
        $item_ids = [];

        foreach ($this->serials as $product_lager_item) {
            $item_ids[] = $product_lager_item->getItemId();

            if ($product_lager_item->getStatus() == 'frei') {
                $product_lager_item->setStatus('assigned');
            }
            if (!$product_lager_item->getOrderItemId()) {
                $product_lager_item->setOrderItemId($this->saveSerials_getOrderItemIdForProductId($product_lager_item->getProductId()));
            }

            $product_lager_item->setLieferscheinId($this->getLieferscheinId());

            $product_lager_item->save();
        }

        $item_ids = implode('||', $item_ids);

        $db = db::getInstance();

        $db->query("
            UPDATE
                warenausgang_lieferschein
            SET
                warenausgang_lieferschein.assigned_serials = '" . $db->escape($item_ids) . "'
            WHERE
                warenausgang_lieferschein.lieferschein_id = '" . $this->getLieferscheinId() . "'
        ");
    }

    /**
     * versucht die $order_item_id für eine $product_id heraus zufinden (kann aber nicht ordentlich funcktionieren wenn ein produkt mehrmals in der Bestellung enthalten ist)
     *
     * nur noch aus kompatipilitäts gründen enthalten
     * @depracted
     * @param int $product_id
     * @return int $order_item_id
     */
    protected function saveSerials_getOrderItemIdForProductId($product_id)
    {
        $db = db::getInstance();

        $order_item_id = $db->fieldQuery("
            SELECT
                order_item.order_item_id
            FROM
                orders INNER JOIN
                order_item ON (orders.order_id = order_item.order_id)
            WHERE
                orders.order_id = '" . $db->escape($this->getOrderId()) . "' AND
                order_item.product_id = '" . $product_id . "'
        ");

        return $order_item_id;
    }

    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Setter">

    /**
     * setzt das Feld lieferschein_id
     * @param $lieferschein_id
     * @return boolean
     */
    public function setLieferscheinId($lieferschein_id)
    {
        return $this->daten->setter('lieferschein_id', $lieferschein_id);
    }

    /**
     * setzt das Feld customer_id
     * @param $customer_id
     * @return boolean
     */
    public function setCustomerId($customer_id)
    {
        return $this->daten->setter('customer_id', $customer_id);
    }

    /**
     * setzt das Feld order_id
     * @param $order_id
     * @return boolean
     */
    public function setOrderId($order_id)
    {
        return $this->daten->setter('order_id', $order_id);
    }

    public function setUserId(int $user_id): bool
    {
        return $this->daten->setter('user_id', $user_id);
    }

    /**
     * setzt das Feld auftnr
     * @param $auftnr
     * @return boolean
     */
    public function setAuftnr($auftnr)
    {
        return $this->daten->setter('auftnr', $auftnr);
    }

    /**
     * setzt das Feld datum
     * @param DateObj $datum
     * @return bool
     */
    public function setDatum(DateObj $datum): bool
    {
        return $this->daten->setter('datum', $datum->db());
    }

    /**
     * setzt das Feld shop_id
     * @param $shop_id
     * @return bool
     */
    public function setShopId($shop_id): bool
    {
        return $this->daten->setter('shop_id', $shop_id);
    }

    /**
     * setzt das Feld status
     * @param string $status
     * @return bool
     */
    public function setStatus(string $status): bool
    {
        $change = $this->daten->setter('status', $status);

        if ($change && $status == self::STATUS_ERLEDIGT) {
            $this->setDatumErledigt(new DateObj());
        }
        if ($change && $status == self::STATUS_STORNIERT) {
            $this->setVersandPipe(0);
        }

        return $change;
    }

    /**
     * setzt das Feld exportiert
     * @param $exportiert
     * @return boolean
     */
    public function setExportiert($exportiert)
    {
        return $this->daten->setter('exportiert', $exportiert);
    }

    /**
     * setzt das Feld export_datensatz
     * @param $export_datensatz
     * @return boolean
     */
    public function setExportDatensatz($export_datensatz)
    {
        return $this->daten->setter('export_datensatz', $export_datensatz);
    }

    /**
     * setzt das Feld sped_id
     * @param $sped_id
     * @return boolean
     */
    public function setSpedId($sped_id)
    {
        return $this->daten->setter('sped_id', $sped_id);
    }

    public function setLagerId(int $lager_id): bool
    {
        $state = $this->daten->setter('lager_id', $lager_id);

        foreach ($this->getItems() as $item) {
            $item->_setLagerId($lager_id);
        }

        return $state;
    }

    /**
     * setzt das Feld versandmail_status
     * @param $versandmail_status
     * @return boolean
     */
    public function setVersandmailStatus($versandmail_status)
    {
        return $this->daten->setter('versandmail_status', $versandmail_status);
    }

    /**
     * setzt das Feld versand_parameter
     * @param $versand_parameter
     * @return boolean
     */
    public function setVersandParameter($versand_parameter)
    {
        return $this->daten->setter('versand_parameter', $versand_parameter);
    }

    /**
     * setzt das Feld datum_erledigt
     * @param DateObj $datum_erledigt
     * @return bool
     */
    public function setDatumErledigt(DateObj $datum_erledigt): bool
    {
        return $this->daten->setter('datum_erledigt', $datum_erledigt->db());
    }

    /**
     * setzt das Feld versand_pipe was angibt ob der Versandauftrag noch übertragen werden muss
     * @param int $versand_pipe
     * @return boolean
     */
    public function setVersandPipe($versand_pipe)
    {
        return $this->daten->setter('versand_pipe', $versand_pipe);
    }

    /**
     * setzt das Feld versandlabel
     * @param $versandlabel
     * @return boolean
     */
    public function setVersandlabel($versandlabel)
    {
        return $this->daten->setter('versandlabel', $versandlabel);
    }

    public function setDocumentTemplate($document_template)
    {
        return $this->daten->setter('document_template', $document_template);
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Getter">

    /**
     * gibt das Feld lieferschein_id zurück
     * @return int lieferschein_id
     */
    public function getLieferscheinId(): int
    {
        return (int)$this->daten->getter('lieferschein_id');
    }

    public function getLieferscheinIdAsBarcode(): string
    {
        return 'LID' . $this->getLieferscheinId();
    }

    public function getBarcodeAsHtml()
    {
        //$barcode = \Laminas\Barcode\Barcode::factory('ean13', 'image', array('text' => $this->getLieferscheinIdAsBarcode()), []);
        $barcode = Barcode::factory('code39', 'image', ['text' => $this->getLieferscheinIdAsBarcode(), 'barHeight' => 40, 'drawText' => false], []);

        ob_start();
        $image = $barcode->draw();
        imagepng($image);
        $image_data = ob_get_contents();
        ob_end_clean();

        $image_data_base64 = base64_encode($image_data);

        return '<img alt="' . $this->getLieferscheinIdAsBarcode() . '" src="data:image/png;base64,' . $image_data_base64 . '">';
    }


    /**
     * gibt das Feld customer_id zurück
     * @return string customer_id
     */
    public function getCustomerId()
    {
        return $this->daten->getter('customer_id');
    }

    /**
     * gibt das Feld order_id zurück
     * @return string order_id
     */
    public function getOrderId()
    {
        return $this->daten->getter('order_id');
    }

    public function getUserId(): int
    {
        return (int)$this->daten->getter('user_id');
    }

    /**
     * gibt die order zurück
     * @return Order $order
     */
    public function getOrder()
    {
        return service_loader::get(OrderRepository::class)->loadCached($this->getOrderId());
    }

    /**
     * gibt das Feld auftnr zurück
     * @return string auftnr
     */
    public function getAuftnr()
    {
        return $this->daten->getter('auftnr');
    }

    /**
     * gibt das Feld datum zurück
     * @return DateObj datum
     */
    public function getDatum(): DateObj
    {
        return new DateObj($this->daten->getter('datum'));
    }

    /**
     * gibt das Feld shop_id zurück
     * @return string shop_id
     */
    public function getShopId()
    {
        return $this->daten->getter('shop_id');
    }

    /**
     * gibt das Feld status zurück
     * @return string status
     */
    public function getStatus()
    {
        return $this->daten->getter('status');
    }

    /**
     * gibt das Feld exportiert zurück
     * @return string exportiert
     */
    public function getExportiert()
    {
        return $this->daten->getter('exportiert');
    }

    /**
     * gibt das Feld export_datensatz zurück
     * @return string export_datensatz
     */
    public function getExportDatensatz()
    {
        return $this->daten->getter('export_datensatz');
    }

    /**
     * gibt das Feld sped_id zurück
     * @return string sped_id
     */
    public function getSpedId()
    {
        return $this->daten->getter('sped_id');
    }

    /**
     * gibt das Feld lager_id zurück
     * @return string lager_id
     */
    public function getLagerId()
    {
        return $this->daten->getter('lager_id');
    }


    public function setSupplierOrderId(int $supplier_order_id): bool
    {
        return $this->daten->setter('supplier_order_id', $supplier_order_id);
    }

    public function getSupplierOrderId(): int
    {
        return $this->daten->getter('supplier_order_id');
    }


    /**
     * gibt das Feld versandmail_status zurück
     * @return string versandmail_status
     */
    public function getVersandmailStatus()
    {
        return $this->daten->getter('versandmail_status');
    }

    /**
     * gibt das Feld versand_parameter zurück
     * @return string versand_parameter
     */
    public function getVersandParameter()
    {
        return $this->daten->getter('versand_parameter');
    }

    /**
     * gibt das Feld datum_erledigt zurück
     * @return string datum_erledigt
     */
    public function getDatumErledigt()
    {
        return $this->daten->getter('datum_erledigt');
    }

    /**
     * gibt das Feld versand_pipe zurück was angibt ob der Versandauftrag noch übertragen werden muss
     * @return bool
     */
    public function getVersandPipe()
    {
        return $this->daten->getter('versand_pipe');
    }

    /**
     * gibt das Feld versandlabel zurück
     * @return string versandlabel
     */
    public function getVersandlabel()
    {
        return $this->daten->getter('versandlabel');
    }

    public function getLieferbedingung()
    {
        //if(!$daten['firma']) $daten['lieferbedingung'] = 'Frei Haus';

        return 'Frei Haus';
    }


    public function getVersandart()
    {
        return ShipmentRepository::getSpedName($this->getSpedId());
    }

    public function getSpedNamePublic()
    {
        return ShipmentRepository::getSpedNamePublic($this->getSpedId());
    }

    public function getDocumentTemplate()
    {
        return $this->daten->getter('document_template');
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Lieferschein Positionen">
    /**
     * @return WarenausgangLieferscheinItem[]
     */
    public function getItems()
    {
        $this->loadItems();

        return $this->items;
    }

    public function getItem($lieferschein_item_id)
    {
        foreach ($this->getItems() as $item) {
            if ($item->getLieferscheinItemId() == $lieferschein_item_id) {
                return $item;
            }
        }

        return false;
    }

    public function getItemsExtended()
    {
        $product_extended = db::getInstance()->query("
                SELECT
                    order_item.order_item_id,
                    order_item.product_id,
                    order_item.product AS product_name,
                    order_item.preis,
                    order_item.unit_code,
                    product.product_nr,
                    product.ean,
                    product.mpn,
                    product.delivery_slip_hint
                FROM
                    order_item INNER JOIN
                    product ON (order_item.product_id = product.product_id)
                WHERE
                    order_item.order_id = '" . $this->getOrderId() . "'
            ")->asArray('order_item_id');

        $items = [];
        foreach ($this->getItems() as $item) {
            $item = $item->asArray();

            if ($item['order_item_id']) {
                if (!$product_extended[$item['order_item_id']]) {
                    throw new FatalException("Ein Produkt auf dem Lieferschein ist nicht in der Bestellung vorhanden. (order_item_id {$item['order_item_id']})");
                }

                $items[] = array_merge($item, $product_extended[$item['order_item_id']]);
            } else {
                $t = [];
                foreach ($product_extended as $product) {
                    if ($product['product_id'] == $item['product_id']) {
                        $t = $product;
                        break;
                    }
                }

                if (!$t) {
                    throw new FatalException("Ein Produkt auf dem Lieferschein ist nicht in der Bestellung vorhanden. (product_id {$item['product_id']})");
                }

                $items[] = array_merge($item, $t);
            }
        }

        return $items;
    }

    public function createItem()
    {
        $item = new WarenausgangLieferscheinItem($this);
        $item->_setLagerId($this->getLagerId());
        $this->addItem($item);

        return $item;
    }

    protected function addItem(WarenausgangLieferscheinItem $item)
    {
        $this->loadItems();
        $this->items[] = $item;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Seriennummern Handling">
    /**
     * prüft ob Seriennummern für diesen Lieferschein erfasst werden müssen
     * @return bool
     */
    public function isNeedSerials()
    {
        //lager prüfen ob seriennummer erfassung gemacht werden
        if (!Lager::getInstance($this->getLagerId())->isWithSerials()) {
            return false;
        }

        //produkte prüfen ob seriennummern erfasst werden müssen
        foreach ($this->getItems() as $item) {
            if ($item->getProduct()->isWithSerials()) {
                return true;
            }
        }

        return false;
    }

    /**
     * prüft ob noch Seriennummern für diesen Lieferschein erfasst werden müssen
     */
    public function isSerialsComplete()
    {
        $needs = $this->getProductsWithNotAssignedSerials();

        if (!$needs) {
            return true;
        }

        foreach ($needs as $need) {
            if ($need['count'] > 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * fügt eine Seriennummer den Lieferschein hinzu
     * @param string $item_id
     */
    public function assignSerial($item_id)
    {
        $this->assignProductLagerItem(new ProductLagerItem($item_id));
    }

    /**
     * fügt mehrere Seriennummern dem Lieferschein hinzu
     */
    public function assignSerials(array $item_ids)
    {
        foreach ($item_ids as $item_id) {
            $this->assignSerial($item_id);
        }
    }

    /**
     * fügt ein product_lager_item dem leiferschein hinzu
     */
    public function assignProductLagerItem(ProductLagerItem $item)
    {
        if (!$this->isValidLagerItemForLieferschein($item)) {
            throw new UnexpectedValueException('Diese Seriennummer kann dem Lieferschein nicht zugeordnet werden. (' . $this->valid_info . ')');
        }

        $this->serials[] = $item;
    }

    protected $valid_info = '';

    /**
     * prüft ob das product_lager_item den Lieferschein hinzugefügt werden
     * speichert den evtl. Grund für ein negatives ergebniss in $valid_info
     * @param ProductLagerItem $item
     * @return bool
     */
    public function isValidLagerItemForLieferschein(ProductLagerItem $item)
    {
        $this->valid_info = '';
        $needs = $this->getProductsWithNotAssignedSerials();

        if (!$needs[$item->getProductId()]) {
            $this->valid_info = 'product_not_included';
            return false;
        }

        if ($item->getLagerId() != $this->getLagerId()) {
            $this->valid_info = 'other_lager';
            return false;
        }

        if ($needs[$item->getProductId()]['count'] < 1) {
            $this->valid_info = 'product_too_many';
            return false;
        }

        if ($item->getStatus() != ProductLagerItem::STATUS_FREI) {
            $this->valid_info = 'serial_not_free';
            return false;
        }

        foreach ($this->serials as $serial_item) {
            if ($serial_item->getItemId() == $item->getItemId()) {
                $this->valid_info = 'item_already_assigned';
                return false;
            }
        }

        return true;
    }

    /**
     * gibt ein Array zurück mit product_ids und wieviele Seriennummern nocht benötigt werden
     * @return array $needs
     */
    public function getProductsWithNotAssignedSerials()
    {
        if (!$this->isNeedSerials()) {
            return [];
        }

        $needs = [];

        //komplett bedarf ermitteln
        foreach ($this->getItems() as $item) {
            if (!$item->getProduct()->isWithSerials()) {
                continue;
            }

            if ($needs[$item->getProductId()]) {
                $needs[$item->getProductId('product_id')]['count'] += $item->getAnzahl();
            } else {
                $needs[$item->getProductId('product_id')] = [
                    'product_id' => $item->getProductId(),
                    'count' => $item->getAnzahl()
                ];
            }
        }

        //das was wir schon haben abziehen
        foreach ($this->serials as $serial_item) {
            if (!$needs[$serial_item->getProductId()]) {
                throw new DevException('warenausgang_lieferschein ' . $this->getLieferscheinId() . ' ist ein product_lager_item zugeordnet was nicht benötigt wird');
            }

            $needs[$serial_item->getProductId()]['count']--;
        }

        return $needs;
    }

    /**
     * entfernt eine die zugeordnung zur Seriennummer
     * @param string $item_id
     */
    public function removeSerial($item_id)
    {
        foreach ($this->serials as $index => $product_lager_item) {
            if ($product_lager_item->getItemId() != $item_id) {
                continue;
            }

            if ($product_lager_item->getStatus() !== 'frei' and $product_lager_item->getStatus() !== 'assigned') {
                throw new DomainException("Diese Seriennummer kann nicht entfernt werden. Status muss frei oder assigned sein.");
            }

            $this->serials_remove[] = $product_lager_item;
            unset($this->serials[$index]);
            return true;
        }

        throw new DomainException("Diese Seriennummer ist dem Lieferschein nicht zugeordnet.");
    }
    // </editor-fold>

    /**
     *
     * @return Address
     */
    public function getLieferAddress()
    {
        return $this->getOrder()->getLieferAddress();
    }


    public function isMultiWarning()
    {
        foreach ($this->getItems() as $item) {
            if ($item->getAnzahl() > 1) {
                return true;
            }
        }

        return false;
    }

    /**
     * Gäfgen braucht den Tel. Avis groß auf dem Lieferschein
     *
     * @return bool
     * @todo dreck
     */
    public function printLargeAvis(): bool
    {
        if ($this->getLagerId() == 23) {
            return true;
        }

        return false;
    }
}
