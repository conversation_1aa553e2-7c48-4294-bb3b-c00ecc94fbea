<?php

namespace wws\Lager;

use bqp\Exceptions\FatalException;
use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataObj;
use db;
use service_loader;
use wws\Product\Product;
use wws\Product\ProductRepository;

class WarenausgangLieferscheinItem implements ProvideSmartDataObj
{
    private WarenausgangLieferschein $lieferschein;
    private SmartDataObj $daten;
    private ?Product $product = null;

    public function __construct(WarenausgangLieferschein $lieferschein, ?array $daten = null)
    {
        $this->lieferschein = $lieferschein;

        $this->daten = new SmartDataObj($this);
        if ($daten !== null) {
            $this->daten->loadDaten($daten);
        }
    }

    public function loadByOrderItemId(int $order_item_id): void
    {
        $daten = db::getInstance()->singleQuery("
            SELECT
                order_item.order_item_id,
                order_item.product_id,
                order_item.quantity
            FROM
                order_item
            WHERE
                order_item.order_item_id = '" . (int)$order_item_id . "'
        ");

        if (!$daten) {
            throw new FatalException('order_item eintrag nicht gefunden');
        }

        $this->setProductId($daten['product_id']);
        $this->setOrderItemId($daten['order_item_id']);
        $this->setAnzahl($daten['quantity']);
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }

    public function delete(): void
    {
        $this->daten->setObjStatus(SmartDataObj::STATUS_DEL);
    }

    /**
     * gibt das Produkt zurück
     * @return Product
     */
    public function getProduct(): Product
    {
        if (!isset($this->product)) {
            $this->product = service_loader::get(ProductRepository::class)->loadProduct($this->getProductId());
        }

        return $this->product;
    }

    public function asArray(): array
    {
        return $this->daten->getAsArray();
    }

    public function setProductId(int $product_id): bool
    {
        return $this->daten->setter('product_id', $product_id);
    }

    /**
     * setzt das Feld anzahl
     * @param $anzahl
     * @return boolean
     */
    public function setAnzahl($anzahl)
    {
        return $this->daten->setter('anzahl', $anzahl);
    }

    /**
     * NICHT VERWENDEN! Nur für WarenausgangLieferschein!
     *
     * setzt das Feld lager_id
     * @param $lager_id
     * @return boolean
     */
    public function _setLagerId($lager_id)
    {
        return $this->daten->setter('lager_id', $lager_id);
    }

    /**
     * setzt das Feld item_status
     * @param $item_status
     * @return boolean
     */
    public function setItemStatus($item_status)
    {
        return $this->daten->setter('item_status', $item_status);
    }

    /**
     * @param int $order_item_id
     * @return bool
     */
    public function setOrderItemId($order_item_id)
    {
        return $this->daten->setter('order_item_id', $order_item_id);
    }


    /**
     * gibt das Feld lieferschein_item_id zurück
     * @return string lieferschein_item_id
     */
    public function getLieferscheinItemId()
    {
        return $this->daten->getter('lieferschein_item_id');
    }

    /**
     * gibt das Feld lieferschein_id zurück
     * @return string lieferschein_id
     */
    public function getLieferscheinId()
    {
        return $this->daten->getter('lieferschein_id');
    }

    public function getProductId(): int
    {
        return $this->daten->getter('product_id');
    }

    /**
     * gibt das Feld anzahl zurück
     * @return int anzahl
     */
    public function getAnzahl()
    {
        return $this->daten->getter('anzahl');
    }

    /**
     * gibt das Feld item_status zurück
     * @return string item_status
     */
    public function getItemStatus()
    {
        return $this->daten->getter('item_status');
    }

    public function getOrderItemId(): int
    {
        return (int)$this->daten->getter('order_item_id');
    }
}
