<?php

namespace wws\Product\Ebay\Event;

use bqp\Event\Event;

class EventProductEbayCatChange extends Event
{
    private int $product_id;
    private ?int $ebay_cat_id;

    public function __construct(int $product_id, ?int $ebay_cat_id = null)
    {
        $this->product_id = $product_id;
        $this->ebay_cat_id = $ebay_cat_id;
    }

    public function getMessage(): array
    {
        return [
            'product_id' => $this->product_id,
            'ebay_cat_id' => $this->ebay_cat_id
        ];
    }
}
