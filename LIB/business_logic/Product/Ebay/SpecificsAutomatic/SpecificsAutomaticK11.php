<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use bqp\db\db_generic;
use bqp\db\db_mysqli;
use db;
use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticK11 implements SpecificsAutomatic
{
    private db_mysqli|db_generic $db;

    public function __construct()
    {
        $this->db = db::getInstance();
    }

    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            $cat_names = $this->db->query('
                SELECT
                    product_cat.cat_name,
                    parent_cat.cat_name as parent_cat_name
                FROM
                    product_cat LEFT JOIN
                    product_cat parent_cat ON (product_cat.parent_cat_id = parent_cat.cat_id)
                WHERE
                    product_cat.cat_id = ' . $product->getCatId() . '
            ')->first();

            if ($cat_names['parent_cat_name']) {
                $value = $cat_names['cat_name'] . ' für ' . $cat_names['parent_cat_name'];
            } else {
                $value = $cat_names['cat_name'];
            }

            if ($value) {
                $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        if ($specifics->isAutoOverrideSpecific('Markenkompatibilität')) {
            $brands = SpecificAutomaticUtils::getDeviceBrandsFuerFormated($product->getProductId(), 65);

            if ($brands) {
                $specifics->setValue('Markenkompatibilität', $brands, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        //klar... Modellkompatibiltät, sonst noch wünsche auf 100 Zeichen? -> ich knall da den markennamen mit rein und gut ist
        if ($specifics->isAutoOverrideSpecific('Modellkompatibilität')) {
            $brands = SpecificAutomaticUtils::getDeviceBrandsFuerFormated($product->getProductId(), 65);

            if ($brands) {
                $specifics->setValue('Modellkompatibilität', $brands, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        // eigentlich ist das für geräte gedacht, aber da manche ersatzteile bei uns in kategorien landen,
        // wo diese angabe erforderlich ist, nehme ich die MPN
        if ($specifics->isAutoOverrideSpecific('Modell')) {
            $mpn = $product->getMpn();

            if ($mpn) {
                $specifics->setValue('Modell', $mpn, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        // eigentlich ist das für geräte gedacht, aber da manche ersatzteile bei uns in kategorien landen,
        // wo diese angabe erforderlich ist, schreibe ich einfach Ersatzteil rein
        if ($specifics->isAutoOverrideSpecific('Installationsart')) {
            $specifics->setValue('Installationsart', 'Ersatzteil', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
        }
    }
}
