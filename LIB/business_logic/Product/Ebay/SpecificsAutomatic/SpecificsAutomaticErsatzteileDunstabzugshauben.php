<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;
use wws\Product\ProductConst;

class SpecificsAutomaticErsatzteileDuns<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> implements SpecificsAutomatic
{

    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($product->getProductType() !== ProductConst::PRODUCT_TYPE_XET) {
            return;
        }

        if ($specifics->getEbayCatId() !== 71253) {
            return;
        }

        $specifics->setValue('Produktart', 'Ersatzteil', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
        $specifics->setValue('Installationsart', 'Einbau', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
        $specifics->setValue('Modell', $product->getMpn() ?: 'N/A', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
    }
}
