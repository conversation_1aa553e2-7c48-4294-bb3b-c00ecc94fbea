<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticErsatzteileKuehlen implements SpecificsAutomatic
{

    static $produktart_cat_id_mapping = [
        796 => 'Tür',
        1318 => 'Tür',
        1319 => 'Tür',
        1323 => 'Türablage',
        1320 => 'Schublade',
        1317 => 'Schublade',
        1450 => 'Thermostat',
        1339 => 'Thermostat',
        792 => 'Thermostat',
        794 => 'Thermostat',
        790 => 'Thermostat',
        793 => 'Thermostat',
        1205 => 'Thermostat',
        791 => 'Thermostat',
        1203 => 'Thermostat',
        1322 => 'Regal',
        785 => 'Regal',
        1343 => 'Dichtung',
        1321 => 'Dichtung',
        1314 => 'Steuerplatine',
        1316 => 'Steuerplatine',
        1325 => 'Türgriff',
        1326 => 'Türgriff',
        784 => 'Steuerplatine',
        1334 => 'Kompressor',
        1333 => 'Kompressor',
        787 => 'Kompressor',
        1202 => 'Kompressor',
        1342 => 'Scharnier',
        1344 => 'Scharnier',
        1330 => 'Schlauch',
        797 => 'Ventilator',
        1345 => 'Ventilator',
        1346 => 'Ventilator',
        1221 => 'Ventilator',
        789 => 'Lampe',
        1145 => 'Lampe',
        1114 => 'Lampe',
        1338 => 'Lampe',
        1337 => 'Lampe',
        1659 => 'Türablage',
        1564 => 'Wasserfilter',
        1565 => 'Schalter',
        1348 => 'Eiswürfelschale',
        1328 => 'Ventil',
        1327 => 'Kältmittel',
        1451 => 'Kältmittel',
        786 => 'Kältmittel',
        1566 => 'Gehäuseteile',
        1569 => 'Blende',
        795 => 'Trockner',
        1341 => 'Trockner',
        799 => 'Zubehör',
        1331 => 'Zubehör',
        1349 => 'Zubehör',
        1201 => 'Zubehör',
        572 => 'Zubehör',
        798 => 'Sonstiges',
        1347 => 'Sonstiges',
        1204 => 'Sonstiges',
        788 => 'Sonstiges',



    ];

    static $produktart_keyword_mapping = [
        'schleppscharniere' => 'Schanier',
        'temperaturregler' => 'Thermostat',
        'kühlthermometer' => 'Thermometer',
        'stopfen' => 'Stopfen',
        'frontblende' => 'Blende',
        'gerätefuß' => 'Fuß',
        'lüftungsgitter' => 'Luftfilter',
        'flaschentrenner' => 'Flaschenfach',
        'flaschenkamm' => 'Flaschenfach',
        'deckeldichtung' => 'Dichtung',
        'butterdose' => 'Zubehör',
        'haken' => '',
        'käsefach' => 'Zubehör',
        'deckelgriffunterteil' => 'Türgriff',
        'deckelgriff' => 'Türgriff',
        'schubkasten' => 'Regal',
        'eisschale' => 'Eiswürfelbehälter',
        'thermostatgehäuse' => 'Thermostat',
        'türanschlag' => 'Tür',
        'abdeckkappe' => '',
        'scharnierbuchse' => 'Scharnier',
        'türscharnierset' => 'Scharnier',
        'türbefestigungsschiene' => 'Tür',
        'schalter' => 'Schalter',
        'lampenmodul' => 'Beleuchtung',
        'rohreinstechventil' => 'Wasserventil',
        'ablageplatte' => 'Regal',
        'flaschen' => 'Flaschenfach',
        'steuerelektronik' => 'Elektronik',
        'programmiert' => 'Elektronik',
        'steuergerät' => 'Elektronik',
        'bedienmodul' => 'Elektronik',
        'kältemittel' => 'Kältemittel',
        'ventilatorenflügel' => 'Lüfter',
        'display' => 'Elektronik',
        'magnetventil' => 'Elektronik',
        'motor' => 'Lüfter',
        'magnetdichtung' => 'Dichtung',
        'magnettürdichtung' => 'Dichtung',
        'kühlthermostat' => 'Thermostat',
        'drehknopf' => 'Bedienknopf',
        'gefrierfachklappenscharnier' => 'Scharnier',
        'innentür' => 'Tür',
        'abdeckplatte' => '',
        'ablage' => 'Regal',
        'hebelgriff' => 'Türgriff',
        'zulaufschlauch' => 'Schlauch',
        'festtüre' => 'Tür',
        'klixon' => 'Thermostat',
        'anlaufrelais' => 'Elektronik',
        'heizpatrone' => '',
        'anschlusskabel' => 'Elektronik',
        'schiene' => '',
        'schieber' => '',
        'gefrierfachklappenhalter' => 'Tür',
        'gemüsebehaelter' => 'Gemüsefach',
        'gefrierkorb' => 'Korb',
        'druckschalter' => 'Schalter',
        'kältegenerator' => '',
        'eisform' => 'Eiswürfelbehälter',
        'türdämpfer' => 'Tür',
        'kühlteiltüre' => 'Tür',
        'flaschenfachseitenteil' => 'Flaschenfach',
        'seitenteil' => '',
        'gehäuse' => '',
        'glasplattenauflage' => 'Regal',
        'lebensmittelfach' => 'Regal',
        'inverter' => 'Elektronik',
        'anlaufvorrichtung' => 'Elektronik',
        'flaschenhaltebügel' => 'Flaschenfach',
        'lüfter' => 'Lüfter',
        'leiste' => '',
        'lagerbock' => 'Kompressor',
        'schale' => 'Regal',
        'abstellboden' => 'Regal',
        'drahtkorb' => 'Korb',
        'ventilatormotor' => 'Lüfter',
        'lüftermotor' => 'Lüfter',
        'türgrifffeder' => 'Türgriff',
        'butterfachdeckel' => '',
        'thermosicherung' => '',
        'türe' => 'Tür',
        'schubladendeckel' => '',
        'ablageschale' => 'Regal',
        'wassertank' => 'Wasserspender',
        'kohlefilter' => 'Luftfilter',
        'tastenschalter' => 'Schalter',
        'halterung' => '',
        'türverriegelung' => 'Tür',
        'türgriffsatz' => 'Tür',
        'ntc' => 'Thermostat',
        'gefrierschubladenblende' => 'Regal',
        'griffleiste' => 'Türgriff',
        'haltebügel' => '',
        'kupplung' => '',
        'feder' => '',
        'halter' => '',
        'steuerungsmodul' => 'Elektronik',
        'lichtschalter' => 'Schalter',
        'luftfilter' => 'Luftfilter',
        'schlepptür' => 'Tür',
        'lampenfassung' => 'Beleuchtung',
        'knebel' => 'Bedienknopf',
        'kondensator' => 'Elektronik',
        'butterfachklappe' => '',
        'führungsschiene' => '',
        'integralplatine' => 'Elektronik',
        'flügel' => 'Lüfter',
        'trockner' => '',
        'türmontageset' => 'Tür',
        'anlassvorrichtung' => 'Elektronik',
        'sensor' => 'Thermostat',
        'eiereinsatz' => 'Eiswürfelbehälter',
        'gefrierbehälter' => 'Schublade',
        'eiswürfelbereiter' => 'Eiswürfelbehälter',
        'abtauheizung' => '',
        'fühler' => 'Thermostat',
        'eiswürfelschale' => 'Eiswürfelbehälter',
        'kabel' => 'Elektronik',
        'heizelement' => 'Heizung',
        'flaschenablage' => 'Flaschenfach',
        'verdampfer' => '',
        'scharnier' => 'Scharnier',
        'halteleiste' => '',
        'temperaturregler' => 'Thermostat',
        'e14' => 'Beleuchtung',
        'halterahmen' => '',
        'butterfach' => 'Zubehöhr',
        'bedieneinheit' => 'Elektronik',
        'flaschenhalter' => 'Flaschenfach',
        'deckel' => '',
        'auszugsschiene' => '',
        'abdeckung' => '',
        'gemüsebehälter' => 'Gemüsefach',
        'klappe' => '',
        'blende' => '',
        'lokring' => '',
        'rahmen' => '',
        'leisten' => '',
        'absorberkühlschrank' => '',
        'lampe' => 'Beleuchtung',
        'flaschenfach' => 'Flaschenfach',
        'ventilator' => 'Lüfter',
        'türabsteller' => 'Türablage',
        'wasserfilter' => 'Wasserfilter',
        'gemüsefach' => 'Gemüsefach',
        'gemüseschublade' => 'Gemüsefach',
        'temperaturfühler' => 'Thermostat',
        'gefrierfachtüre' => 'Tür',
        'innenraumtüre' => 'Tür',
        'gefriergutbehälter' => '',
        'gefrierfachtür' => 'Tür',
        'dichtung' => 'Dichtung',
        'türscharnier' => 'Scharnier',
        'kompressor' => 'Kompressor',
        'griff' => 'Türgriff',
        'glasplattenleiste' => '',
        'gefrierfachklappe' => '',
        'elektronik' => 'Elektronik',
        'kapillarrohr' => 'Thermostat',
        'schubladenblende' => '',
        'absteller' => 'Regal',
        'türgriff' => 'Türgriff',
        'gemüseschale' => 'Gemüsefach',
        'türfach' => 'Türablage',
        'gefrierschublade' => 'Schublade',
        'flaschenabsteller' => 'Flaschenfach',
        'glasplatte' => 'Regal',
        'türdichtung' => 'Dichtung',
        'thermostat' => 'Thermostat',
        'schublade' => 'Schublade',
        'kühlschranktüre' => 'Tür',
        'abstellfach' => 'Regal'
    ];

    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 184666) {
            return;
        }

        $cat_ids = $product->getExtendedCatIds();
        $cat_ids[] = $product->getCatId();

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            foreach ($cat_ids as $cat_id) {
                if (array_key_exists($cat_id, self::$produktart_cat_id_mapping)) {
                    $value = self::$produktart_cat_id_mapping[$cat_id];
                    $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                    break;
                }
            }

            if (!$specifics->getSpecific('Produktart')->getValue()) {
                foreach (self::$produktart_keyword_mapping as $keyword => $value) {
                    if (!$value) {
                        continue;
                    }

                    if (mb_stripos($product->getProductName(), $keyword, 0, 'UTF-8') !== false) {
                        $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                        break;
                    }
                }
            }
        }

        if ($specifics->isAutoOverrideSpecific('Modell')) {
            $value = $product->getMpn();
            if ($value) {
                $specifics->setValue('Modell', $value, ProductEbaySpecificValue::VALUE_SETTED_FALLBACK);
            }
        }
    }
}
