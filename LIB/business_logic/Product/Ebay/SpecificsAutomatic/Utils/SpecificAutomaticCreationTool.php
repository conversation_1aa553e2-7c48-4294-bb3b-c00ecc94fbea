<?php

namespace wws\Product\Ebay\SpecificsAutomatic\Utils;

use bqp\db\db_generic;
use bqp\String\WordCounter;
use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Ebay\SpecificsAutomatic\SpecificsAutomatic;
use wws\Product\Product;
use wws\Product\ProductConst;
use wws\Product\ProductRepository;

/**
 * $tool = service_loader::getDiContainer()->get(SpecificAutomaticCreationTool::class);
 *
 * $tool->setEbayCatId(184666);//Gefriergeräte und Kühlschrankteile
 * $tool->setAutomatic(service_loader::getDiContainer()->get(\wws\Product\Ebay\SpecificsAutomatic\SpecificsAutomaticErsatzteileKuehlen::class));
 *
 * $tool->addSpecific('Produktart');
 * $tool->addSpecific('Modell');
 * $tool->setFilterNotSetted(true);
 *
 * $tool->displayCatIds();
 * $tool->displayProductNameWordTokens();
 * $tool->displaySpecifics();
 * $tool->displayProducts();
 *
 * $tool->execute();
 */
class SpecificAutomaticCreationTool
{
    /**
     * @var string[]
     */
    private array $specifics = [];
    private int $ebay_cat_id;
    private db_generic $db;
    private SpecificsAutomatic $automatic;
    private ProductRepository $product_repository;
    private bool $filter_not_setted = false;

    public function __construct(db_generic $db, ProductRepository $product_repository)
    {
        $this->db = $db;
        $this->product_repository = $product_repository;
    }

    public function setEbayCatId(int $ebay_cat_id): void
    {
        $this->ebay_cat_id = $ebay_cat_id;
    }

    public function setSpecific(string $specific): void
    {
        $this->setSpecifics([$specific]);
    }

    public function setSpecifics(array $specifics): void
    {
        $this->specifics = [];

        foreach ($specifics as $specific) {
            $this->addSpecific($specific);
        }
    }

    public function addSpecific(string $specific): void
    {
        $this->specifics[] = $specific;
    }

    public function setAutomatic(SpecificsAutomatic $automatic): void
    {
        $this->automatic = $automatic;
    }

    public function setFilterNotSetted(bool $filter_not_setted): void
    {
        $this->filter_not_setted = $filter_not_setted;
    }

    public function getSqlQuery(): string
    {
        $select = '';
        $tables = '';
        $where_filter_not_setted = [];
        $where = '';
        foreach ($this->specifics as $specific) {
            $specific_alias = preg_replace('~[^a-z0-9]~', '', strtolower($specific));

            $table = 'product_ebay_specific_' . $specific_alias;

            $tables .= " LEFT JOIN product_ebay_specific AS $table ON (product.product_id = $table.product_id AND $table.specific_name = '" . $this->db->escape($specific) . "') ";
            $select .= ", $table.specific_value AS " . $table . "_value";

            if ($this->filter_not_setted) {
                $where_filter_not_setted[] = "($table.specific_value IS NULL OR $table.specific_value = '')";
            }
        }

        if ($where_filter_not_setted) {
            $where = ' AND (' . implode(' OR ', $where_filter_not_setted) . ')';
        }

        $sql = "
            SELECT
                product.product_id,
                product.product_name
                $select
            FROM
                (
                        SELECT
                            product_ebay.product_id
                        FROM
                            product_ebay
                        WHERE
                            product_ebay.ebay_manual_cat_id = " . $this->ebay_cat_id . "
                    UNION
                        SELECT
                            product.product_id
                        FROM
                            product_cat INNER JOIN
                            product ON (product_cat.cat_id = product.cat_id) INNER JOIN
                            product_ebay ON (product.product_id = product_ebay.product_id AND product_ebay.ebay_manual_cat_id = 0)
                        WHERE
                            product_cat.ebay_cat_id = " . $this->ebay_cat_id . " AND
                            product.product_status = " . ProductConst::PRODUCT_STATUS_NORMAL . "
                ) AS t INNER JOIN
                product ON (product.product_id = t.product_id) INNER JOIN
                product_cache ON (product.product_id = product_cache.product_id)
                $tables 
            WHERE
                product.product_status = " . ProductConst::PRODUCT_STATUS_NORMAL . "
                $where
        ";

        return $sql;
    }


    public function displayProducts(): void
    {
        $sql = $this->getSqlQuery();

        $this->db->query($sql)->display();
    }

    public function displayCatIds(): void
    {
        $query = $this->getSqlQuery();

        $this->db->query("
            SELECT
                product_cat.cat_id,
                product_cat.cat_name,
                product_cat.cat_path_text,
                COUNT(*)
            FROM
                ($query) AS src INNER JOIN
                product_cat_product_mapping ON (src.product_id = product_cat_product_mapping.product_id) INNER JOIN
                product_cat ON (product_cat_product_mapping.cat_id = product_cat.cat_id)
            GROUP BY
                product_cat.cat_id
            ORDER BY
                COUNT(*) DESC
        ")->display();
    }

    public function displayProductNameWordTokens(): void
    {
        $word_counter = new WordCounter();

        $result = $this->db->query($this->getSqlQuery());

        foreach ($result as $row) {
            $word_counter->add($row['product_name']);
        }

        $word_counter->test();

        $words = $word_counter->getWords(5);
        $words = array_reverse($words); //damit die weniger vorkommenden oben stehen

        var_dump("'" . implode("' => '',\n'", $words) . "' => ");
    }

    public function displaySpecifics(): void
    {
        $cat_data = $this->db->singleQuery("
            SELECT
                ext_ebay_cats.cat_name,
                ext_ebay_cats.cat_level,
                ext_ebay_cats.ebay_specific_definitions 
            FROM
                ext_ebay_cats
            WHERE
                ext_ebay_cats.ebay_cat_id = " . (int)$this->ebay_cat_id . "
        ");

        var_dump($cat_data['cat_name']);
        var_dump($cat_data['cat_level']);

        foreach ($this->getSpecifics() as $specific) {
            var_dump($specific);

            echo '<pre>';
            foreach ($specific->getEnums() as $value) {
                echo "'" . $value . "'\n";
            }
            echo '</pre>';
        }
    }

    /**
     * @return ProductEbaySpecificValue[]
     */
    public function getSpecifics(): array
    {
        $cat_data = $this->db->singleQuery("
            SELECT
                ext_ebay_cats.cat_name,
                ext_ebay_cats.cat_level,
                ext_ebay_cats.ebay_specific_definitions 
            FROM
                ext_ebay_cats
            WHERE
                ext_ebay_cats.ebay_cat_id = " . (int)$this->ebay_cat_id . "
        ");

        $specifics = new ProductEbaySpecifics();
        $specifics->setEbayCatId($this->ebay_cat_id);

        $specifics->setSpecificDefinitionsSerialized($cat_data['ebay_specific_definitions']);

        $all_specifics = $specifics->getAllSpecifics();

        $result = [];

        foreach ($this->specifics as $specific_key) {
            $result[$specific_key] = $all_specifics[$specific_key];
        }

        return $result;
    }


    public function execute(): void
    {
        $config = $this->product_repository->getDefaultLoadConfig();
        $config->setExtendedCats(true);

        $products = $this->product_repository->searchBySqlExtra($this->getSqlQuery(), 250, $config);

        $products->walk(function (Product $product, array $extra) {
            $product_ebay = new ProductEbay($product->getProductId());
            $specifics = $product_ebay->getEbaySpecifics();

            $this->automatic->autoFillByProduct($product, $product_ebay, $specifics);

            $product_ebay->save();
        });
    }
}
