<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use bqp\db\db_generic;
use db;
use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticErsatzteileStaubsauger implements SpecificsAutomatic
{

    static $produktart_cat_id_mapping = [
        1188 => 'Motor',
        1207 => 'Schlauch',
        1367 => 'Teleskoprohr/Verlängerungsrohr',
        1368 => 'Düsen/Aufsätze',
        1369 => 'Schalter',
        1370 => 'Filter/Filterzubehör',
        1374 => 'Bürste/Bürsten-Rolle',
        1379 => 'Filter/Filterzubehör',
        1376 => 'Akku',
        1382 => 'Kabel',
        1390 => 'Teleskoprohr/Verlängerungsrohr',
        1384 => 'Räder',
        1394 => '<PERSON><PERSON><PERSON><PERSON>',
        1590 => 'Handgriff',
        1378 => 'Schalter',
        1398 => 'D<PERSON>sen/Aufsätze',
        1381 => 'Leiterplatte',
        1388 => 'Düsen/Aufsätze',
        1400 => 'Düsen/Aufsätze',
        1372 => 'Düsen/Aufsätze',
        1371 => 'Riemen',
        1377 => 'Behälter',
        1380 => 'Behälter',
//
//        1189	Sonstiges
//        1375	Sonstiges
//        1395	Sonstiges
//        1397	Sonstiges
//        1206	Staubsaugerbeutel
//        1383	Zubehör
//        1389	Reiniger
//        1393	Reiniger
//        1399	Reiniger
    ];

    static $produktart_keyword_mapping = [
        'staubbehälteroberteil' => 'Behälter',
        'filterbeutelhalter' => 'Basis',
        'gehäuseabdeckung' => 'Basis',
        'staubbehälter' => 'Behälter',
        'staubraumdeckel' => 'Behälter',
        'entstörschutz' => 'Motor',
        'elektronik' => 'Leiterplatte',
        'Saugpinsel' => 'Düsen/Aufsätze',
        'Hartbodendüse' => 'Düsen/Aufsätze',
        'Absaugdüse' => 'Düsen/Aufsätze',
        'Vliesfilterbeutel' => 'Staubsaugerbeutel',
        'Staubtüten' => 'Staubsaugerbeutel',
        'Filterbeutel' => 'Staubsaugerbeutel',
        'Gehäuseteil' => 'Abdeckung/Schild',
        'Gehäuseschale' => 'Abdeckung/Schild',
        'Wasserfilter' => 'Filter/Filterzubehör',
        'Abdeckung für Batterie' => 'Deckel',
        'Wischtuch' => 'Wischtuch',
        'Bodentuch' => 'Wischtuch',
        'Tücher' => 'Wischtuch',
        'Reinigungstuch' => 'Wischtuch',
        'Reinigungspad' => 'Wischtuch',
        'stoßstange' => 'Abdeckung/Schild',
        'fernbedienung' => 'Fernbedienung',
        'antriebsrad' => 'Getriebe',
        'Dichtung' => 'Dichtung',
        'Abdeckung' => 'Abdeckung/Schild',
        'Tank' => 'Tank',
        'Stromschalter' => 'Energieversorgung',
        'Netzschalter' => 'Energieversorgung',
        'Netzteil' => 'Energieversorgung',
        'Kabeltrommel' => 'Energieversorgung',
        'Ladegerät' => 'Energieversorgung',
        'Dockstation' => 'Energieversorgung',
        'Ladekabel' => 'Energieversorgung',
        'Netzkabel' => 'Energieversorgung',
        'Stromkabel' => 'Energieversorgung',
        'Staubsaugerschlauch' => 'Schlauch',
        'sturzsensor' => 'Sensor',
        'sensor' => 'Sensor',
        'lasereinheit' => 'Sensor',
        'infrarot' => 'Sensor',
        'ladestation' => 'Ladegerät',
        'taste' => 'Schalter',
        'hauptplatine' => 'Elektronik',
        'Main Board' => 'Elektronik',
        'Wi-Fi Modul' => 'Elektronik',
        'Innenplatine' => 'Elektronik',
        'platine' => 'Elektronik',
        'Steuerungsmodul' => 'Elektronik',
        'LDS ' => 'Elektronik',
        'antriebseinheit' => 'Antreib',
        'antrieb' => 'Antreib',
        'fahrgestell' => 'Räder',
        'batterie' => 'Batterie/Akku',
        'akkublock' => 'Batterie/Akku',
        'akku' => 'Batterie/Akku',
        'battery' => 'Batterie/Akku',
        'ladekontakte' => 'Energieversorgung',
        'ladeanschluss' => 'Energieversorgung',
        'Ladeeinheit' => 'Energieversorgung',
        'Steckverbindung' => 'Energieversorgung',
        'reinigungswerkzeug' => 'Staubsaugerbürste',
        'walze' => 'Staubsaugerbürste',
        'Bürste' => 'Staubsaugerbürste',
        'Polsterdüse' => 'Düsen/Aufsätze',
        'Fugendüse' => 'Düsen/Aufsätze',
        'wasserpumpe' => 'Pumpe',
        'universalrolle' => 'Räder',
        'Universal Rad' => 'Räder',
        'Universal Rolle' => 'Räder',
        'laufrolle' => 'Räder',
        'Laufrad' => 'Räder',
        'bodendüse' => 'Düsen/Aufsätze',
        'hauptbürste' => 'Staubsaugerbürste',
        'teleskoprohr' => 'Rohr',
        'verlängerungsrohr' => 'Rohr',
        'handgriff' => 'Griff',
        'laufrolleneinheit' => 'Räder',
        'Adapter' => 'Adapter',
        'Schwenkarm' => 'Schwenkarm',
        'Lautsprecher' => 'Lautsprecher',
        'Gehäuse' => 'Gehäuse',
        'Dämmplatte' => 'Gehäuse',
        'Gegengewicht' => 'Gehäuse',
        'Deckel' => 'Gehäuse',
        'Rahmen' => 'Gehäuse',
        'Abdeckhaube' => 'Gehäuse',
        'Ausblasgitter' => 'Gehäuse',
        'Dekorteil' => 'Gehäuse',
        'Chassis' => 'Gehäuse',
        'Grundplatte' => 'Gehäuse',
        'Bedienblende' => 'Gehäuse',
        'platte' => 'Gehäuse',
        'Gitter' => 'Gehäuse',
        'einlass' => 'Einlass',
        'Rad ' => 'Räder',
        'Staubsaugerduft' => 'Reiniger',
        'Reiniger' => 'Reiniger',
        'Vorreiniger' => 'Reiniger',
        'Reinigungsloesung' => 'Reiniger'
    ];

    private db_generic $db;

    public function __construct()
    {
        $this->db = db::getInstance();
    }

    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 42146) {
            return;
        }

        $cat_ids = $product->getExtendedCatIds();
        $cat_ids[] = $product->getCatId();

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            foreach ($cat_ids as $cat_id) {
                if (array_key_exists($cat_id, self::$produktart_cat_id_mapping)) {
                    $value = self::$produktart_cat_id_mapping[$cat_id];
                    $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                    break;
                }
            }

            if ($specifics->isAutoOverrideSpecific('Produktart')) {
                foreach (self::$produktart_keyword_mapping as $keyword => $value) {
                    if (mb_stripos($product->getProductName(), $keyword, 0, 'UTF-8') !== false) {
                        $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                        break;
                    }
                }
            }
        }

        if ($specifics->isAutoOverrideSpecific('Markenkompatibilität')) {
            $brands = SpecificAutomaticUtils::getDeviceBrandsFuerFormated($product->getProductId());

            if ($brands) {
                $specifics->setValue('Markenkompatibilität', $brands, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        //klar... Modellkompatibiltät, sonst noch wünsche auf 100 Zeichen? -> ich knall da den markennamen mit rein und gut ist
        if ($specifics->isAutoOverrideSpecific('Modellkompatibilität')) {
            $brands = SpecificAutomaticUtils::getDeviceBrandsFuerFormated($product->getProductId());

            if ($brands) {
                $specifics->setValue('Modellkompatibilität', $brands, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }
    }
}
