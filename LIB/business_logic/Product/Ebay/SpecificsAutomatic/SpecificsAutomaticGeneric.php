<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;
use wws\Product\ProductBrand\ProductBrandRepository;

class SpecificsAutomaticGeneric implements SpecificsAutomatic
{


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        foreach ($specifics->getAllSpecifics() as $specific) {
            if ($specific->getValueSetted() == ProductEbaySpecificValue::VALUE_SETTED_MANUAL) {
                continue;
            }

            $value = null;

            $product_features = $product->getFeatureContainer()->getFeatureValuesObject()->getAsLegacyFeatures();

            switch ($specific->getKey()) {
                case 'Marke':
                case 'Hersteller':
                case 'Herstellername':
                    $value = self::findValueInEnum($specific->getEnums(), ProductBrandRepository::getBrandName($product->getBrandId()));
                    break;
                case 'Herstellernummer':
                    $value = $product->getMpn();
                    if (!$value) {
                        $value = 'nicht zutreffend';
                    }
                    break;
                case 'Farbe':
                    $value = $product->getColor();
                    break;
                case 'Höhe':
                    $value = $this->searchFeatureValueByName($product_features, 'höhe');

                    if (!$value && $product->getSizeH()) {
                        $value = self::findValueInEnum($specific->getEnums(), $product->getSizeH());
                    }
                    break;
                case 'Tiefe':
                case 'Länge':
                    $value = $this->searchFeatureValueByName($product_features, 'tiefe');

                    if (!$value && $product->getSizeT()) {
                        $value = self::findValueInEnum($specific->getEnums(), $product->getSizeT());
                    }
                    break;
                case 'Breite':
                case 'Breite (cm)':
                    $value = $this->searchFeatureValueByName($product_features, 'breite');

                    if (!$value && $product->getSizeB()) {
                        $value = self::findValueInEnum($specific->getEnums(), $product->getSizeB());
                    }
                    break;
                case 'Anzahl der Maßgedecke':
                    if (isset($product_features['201'])) {
                        $value = $product_features['201']['value'];
                    }
                    break;
                case 'Geräuschpegel (dB)':
                    $value = $this->searchFeatureValueByName($product_features, 'schall');
                    break;
                case 'Spülprogramme':
                    $value = $this->searchFeatureValueByName($product_features, 'Anzahl Programme');
                    break;
                case 'Trocknungswirkungsklasse':
                case 'Waschwirkungsklasse':
                case 'Leistung':
                    $value = $this->searchFeatureValueByName($product_features, $specific->getKey());
                    break;
                case 'Energieeffizienzklasse':
                    $value = $this->searchFeatureValueByName($product_features, $specific->getKey());
                    break;
                case 'EEK Spektrum';
                    switch ($product->getEekScaleId()) {
                        case 'A-D':
                            $value = 'A – G';
                            break;
                        case 'A-G':
                            $value = 'A – G';
                            break;
                        case 'A-G-2020':
                            $value = 'A – G';
                            break;
                        case 'A2-E':
                            $value = 'A++ - E';
                            break;
                        case 'A2-G':
                            $value = 'A++ - G';
                            break;
                        case 'A3-D':
                            $value = 'A+++ - D';
                            break;
                        case 'A3-G':
                            $value = 'A+++ - G';
                            break;

                    }
                    break;
                case 'Modell':
                    $value = $this->searchFeatureValueByName($product_features, $specific->getKey());

                    if (!$value && strlen($product->getProductName()) < 30) {
                        $value = $product->getProductName();
                    }

                    break;
                default:
                    //$this->setValue($specific->getKey(), 'blub', product_ebay_specific_value::VALUE_SETTED_AUTO);
            }

            if ($value !== null) {
                $specifics->setValue($specific->getKey(), $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }
    }

    private function searchFeatureValueByName(array $product_features, string $name)
    {
        foreach ($product_features as $feature) {
            if (stripos($feature['feature_name'], $name) !== false && !$feature['hidden']) {
                return $feature['value'];

                //return self::findValueInEnum($specific->getEnums(), $feature['value']);
            }
        }

        return null;
    }

    private static function findValueInEnum($enum, $value, $strict = false)
    {
        if (!$enum) {
            return $value;
        }


        return $value;
    }
}
