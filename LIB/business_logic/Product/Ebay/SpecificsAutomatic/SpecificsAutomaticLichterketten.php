<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticLichterketten implements SpecificsAutomatic
{
    static $keywords_produktart = [
        'Lichterkette' => ['Lichterkette', 'Innenkette', 'Außenkette', 'Baumbeleuchtung'],
        'Lichternetz' => ['Lichternetz'],
        'Lichterschlauch' => ['Lichterschlauch', 'Leuchtschlauch'],
        'Lichtervorhang' => ['Lichtervorhang', 'Vorhang'],
        'Ersatzlämpchen' => ['Ersatztoplampen', 'Ersatzschaftlampen'],
        'Lichtskulptur' => ['Weidenzweige', 'Eisbär', 'Pinguin', 'Dekostern', 'Schneemann']
    ];


    public function __construct()
    {

    }


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 38229) {
            return;
        }

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            $value = $this->match(self::$keywords_produktart, $product->getProductName());

            if ($value) {
                $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }
    }

    public function match(array $keywords_all, string $value)
    {
        foreach ($keywords_all as $specific_value => $keywords) {
            foreach ($keywords as $keyword) {
                if (mb_stripos($value, $keyword) !== false) {
                    return $specific_value;
                }
            }
        }
        return null;
    }
}
