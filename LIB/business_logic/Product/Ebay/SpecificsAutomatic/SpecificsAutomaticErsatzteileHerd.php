<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticErsatzteileHerd implements SpecificsAutomatic
{

    static array $produktart_cat_id_mapping = [
        706 => 'Backheizelement',
        1104 => 'Rost',
        1105 => 'Pizzastein',
        1106 => 'Knopf',
        1108 => 'Knopf',
        1109 => 'Kontrollleuchte',
        1110 => 'Kontrollleuchte',
        1112 => 'Backheizelement',
        1103 => 'Backblech',
        1114 => 'Lampe',
        1116 => 'Schalter',
        1115 => 'Schalter',
        1117 => 'Dichtung',
        1118 => 'Thermostat',
        1121 => 'Zünder',
        1123 => 'Backheizelement',
        1124 => 'Isolierung',
        1127 => 'Scharnier',
        1128 => 'Thermostat',
        1129 => 'Tür',
        1361 => 'Tür',
        1130 => 'Türgriff',
        1131 => 'Motor',
        1135 => 'Steuerplatine',
        1136 => 'Dichtung',
        1137 => 'Kabel',
        1144 => 'Filter',
        1146 => 'Filter',
        1147 => 'Filter',
        1209 => 'Backheizelement',
        1337 => 'Lampe',
        1338 => 'Lampe',
        1353 => 'Lampe',
        1342 => 'Scharnier',
        1344 => 'Scharnier',
        707 => 'Heizspirale',
        721 => 'Filter',
        704 => 'Backblech',
        705 => 'Bedienfeld'
    ];

    static $produktart_keyword_mapping = [
        'filter' => 'Filter',
        'backblech' => 'Backblech',
        'universalpfanne' => 'Backblech',
        'griff ' => 'Türgriff',
        'pizzaform' => 'Pizzastein',
        'pizzastein' => 'Pizzastein',
        'fettpfanne' => 'Backblech',
        'bratrost' => 'Rost',
        'brotbackstein' => 'Pizzastein',
        'auszug' => 'Halterung',
        'flexirunners' => 'Halterung',
        'starterset' => 'Filter',
        'leistungsmodul' => 'Steuerplatine',
        'relaisplatine' => 'Steuerplatine',
        'glaskeramikfläche' => 'Keramik-Kochfeld',
        'glaskeramikplatte' => 'Keramik-Kochfeld',
        'dichtung' => 'Dichtung',
        'thermostat' => 'Thermostat',
        'heißluftventilator' => 'Umluftofenheizelement',
        'lüfterflügel' => 'Umluftofenheizelement',
        'stecker' => 'Kabel',
        'kabel' => 'Kabel',
        'schraube' => 'Schrauben',
        'anzeige' => 'Bedienfeld',
        'düsensatz' => 'Brenner',
        'brenner' => 'Brenner',
        'halter' => 'Halter',
        'scheibe' => 'Glas',
        'fenster' => 'Glas',
        'glas' => 'Glas',
        'einbaurahmen' => 'Einbaurahmen',
        'wassertank' => 'Wassertank',
        'taste' => 'Schalter',
        'rahmen' => 'Rahmen',
        'dichtband' => 'Dichtung',
        'reiniger' => 'Reiniger',
        'reinigung' => 'Reiniger',
        'fettlöser' => 'Reiniger',
        'klingen' => 'Reiniger',
        'scharnier' => 'Scharnier',
        'bedienmodul' => 'Bedienmodul'
    ];


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 43566) {
            return;
        }

        $cat_ids = $product->getExtendedCatIds();
        $cat_ids[] = $product->getCatId();

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            foreach ($cat_ids as $cat_id) {
                if (array_key_exists($cat_id, self::$produktart_cat_id_mapping)) {
                    $value = self::$produktart_cat_id_mapping[$cat_id];
                    $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                    break;
                }
            }

            if ($specifics->isAutoOverrideSpecific('Produktart')) {
                foreach (self::$produktart_keyword_mapping as $keyword => $value) {
                    if (mb_stripos($product->getProductName(), $keyword, 0, 'UTF-8') !== false) {
                        $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                        break;
                    }
                }
            }
        }
    }
}
