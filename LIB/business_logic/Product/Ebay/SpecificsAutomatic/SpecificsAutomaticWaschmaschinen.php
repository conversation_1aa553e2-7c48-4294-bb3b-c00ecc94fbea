<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticWaschmaschinen implements SpecificsAutomatic
{

    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 71256) {
            return;
        }

        if ($specifics->isAutoOverrideSpecific('Installationsart')) {
            $specifics->setValue('Installationsart', 'Tragbar', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
        }

        if ($specifics->isAutoOverrideSpecific('Betriebsart')) {
            $specifics->setValue('Betriebsart', 'Netzstrom', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
        }
    }
}
