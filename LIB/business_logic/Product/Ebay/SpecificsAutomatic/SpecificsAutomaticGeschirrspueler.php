<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticGeschirrspueler implements SpecificsAutomatic
{
    static $produktart_mapping = [
        'Standgerät 45 cm' => 'Freistehend',
        'Standgerät 60 cm' => 'Freistehend',
        'integrierbar 45 cm' => 'Teilintegriert',
        'integrierbar 60 cm' => 'Teilintegriert',
        'vollintegrierbar 45 cm' => 'Vollintegriert',
        'vollintegrierbar 60 cm' => 'Vollintegriert',
        'unterbau 45 cm (ohne Arbeitsplatte)' => 'Teilintegriert',
        'unterbau 60 cm (ohne Arbeitsplatte)' => 'Teilintegriert',
        'Kompaktgerät Stand' => 'Tragbar',
        'Kompaktgerät Einbau' => 'Teilintegriert',
    ];

    const FEATURE_ID_BAUART = 194;


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 116023) {
            return;
        }

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            $feature_values = $product->getFeatureContainer()->getFeatureValuesObject()->getValues();

            $feature_value = $feature_values[self::FEATURE_ID_BAUART] ?? null;

            $value = self::$produktart_mapping[$feature_value] ?? null;

            if ($value) {
                $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        //FEATURE_ID_BAUART

        //var_dump($specifics->getAllSpecifics());

//        var_dump($specifics->getSpecific('Produktart'));

//        if (!$specifics->hasManuelValue('Produktart')) {
//            $value = $this->match(self::$keywords_produktart, $product->getProductName());
//
//            if ($value) {
//                 $specifics->setValue('Produktart', $value, product_ebay_specific_value::VALUE_SETTED_AUTO);
//            }
//        }
    }

    public function match(array $keywords_all, string $value)
    {
        foreach ($keywords_all as $specific_value => $keywords) {
            foreach ($keywords as $keyword) {
                if (mb_stripos($value, $keyword) !== false) {
                    return $specific_value;
                }
            }
        }
        return null;
    }
}
