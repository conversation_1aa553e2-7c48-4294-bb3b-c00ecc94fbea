<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticKuehlen implements SpecificsAutomatic
{
    static $produktart_feature_geraetetyp_mapping = [
//        Kühl-/Gefrierkombinationen
//        Kühlgeräte
//        Gefrierschränke
//        Doppeltürgeräte
//        Side-by-Side
//        Frenchdoor-Kombinationen (mit Gefrierschubladen)
//        Vorratszentrum
    ];

    static $ebay_cat_id_geraetetyp_mapping = [
        'Kühl-/Gefrierkombinationen' => 20713,
        'Kühl-/Gefrierkombinationen (Gefrierteil unten)' => 20713,
        'Kühlgeräte' => 71262,
        'Tischkühlgeräte' => 71262,
        'Gefrierschränke' => 71260,
        'Do<PERSON>türgeräte' => 20713,
        'Do<PERSON>türgeräte (Gefrierteil oben)' => 20713,
        'Side-by-Side' => 20713,
        'Side-by-Side Geräte' => 20713,
        'Frenchdoor-Kombinationen (mit Gefrierschubladen)' => 20713,
        'Frenchdoor-Kombinationen' => 20713,
        'Vorratszentrum' => 20713,
        'Flaschenkühlschrank' => 71262,
    ];

    static $ebay_cat_id_keyword_mapping = [
        'gefrierfach' => 71262,
        'gefrierkomb' => 20713,
        'gaskühlschrank' => 71262,
        'kühlautomat' => 71262,
        'gefrierbox' => 71260,
        'kühlbox' => 71262,
        'wein' => 177750,
        'standkühlschrank' => 71262,
        'flaschenkühlschrank' => 71262,
        'tischkühlschrank' => 71262,
        'side-by-side' => 20713,
    ];


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() == 71258) {
            $this->updateEbayCat($product, $product_ebay, $specifics);
            //wenn die kategorie geändert wird, dann muss product_ebay_specifics neu geladen werden. "neu laden" würde aber eine neues objekt erzeugen :|
            //in dem run kann also nix mehr geändert werden -> abbruch und im nächsten dann...
            return;
        }

        if ($specifics->getEbayCatId() == 20713) {
            $this->fillKombinationen($product, $product_ebay, $specifics);
        }

        if ($specifics->getEbayCatId() == 71262) {
            $this->fillKuehlschrank($product, $product_ebay, $specifics);
        }

        if ($specifics->getEbayCatId() == 71260) {
            $this->fillKGefrier($product, $product_ebay, $specifics);
        }
    }

    private function fillKGefrier(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        $type = SpecificAutomaticUtils::getFeatureValue($product, 89, 124); //Gerätetyp von Einbau und Stand

        $is_einbau = $product->getCatId() == 10;

        if ($specifics->isAutoOverrideSpecific('Installationsart')) {
            if ($is_einbau) {
                //Freistehend, Integriert, Vollintegriert
                $specifics->setValue('Installationsart', 'Eingebaut', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            } else {
                $specifics->setValue('Installationsart', 'Freistehend', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }

            if ($type === 'Tischkühlgeräte') {
                $specifics->setValue('Installationsart', 'Tischgerät', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            if ($product->getCatId() == 107) {
                $value = 'Tiefkühltruhe';
            } else {
                $type = SpecificAutomaticUtils::getFeatureValue($product, 89, 124); //Gerätetyp von Einbau und Stand

                switch ($type) {
                    case 'Gefrierschränke':
                        $value = 'Aufrecht';
                        break;
                    case 'Tischkühlgeräte':
                        $value = 'Tischgerät';
                        break;
                    default:
                        $value = null;
                }
            }

            if ($value) {
                $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        $this->updateFarbe($product, $product_ebay, $specifics);
    }

    private function fillKuehlschrank(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        $type = SpecificAutomaticUtils::getFeatureValue($product, 89, 124); //Gerätetyp von Einbau und Stand

        $is_einbau = $product->getCatId() == 10;

        if ($specifics->isAutoOverrideSpecific('Installationsart')) {
            if ($is_einbau) {
                //Freistehend, Integriert, Vollintegriert
                $specifics->setValue('Installationsart', 'Eingebaut', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            } else {
                $specifics->setValue('Installationsart', 'Freistehend', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }

            if ($type === 'Tischkühlgeräte') {
                $specifics->setValue('Installationsart', 'Tischgerät', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            switch ($type) {
                case 'Flaschenkühlschrank':
                case 'Kühlgeräte':
                    $value = 'Kühlschrank';
                    break;
                case 'Tischkühlgeräte':
                    $value = 'Tischgerät';
                    break;
                case 'Kühlbox':
                    $value = 'Tragbarer Kühlschrank';
                    break;
                default:
                    $value = null;
            }

            if ($value) {
                $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        $this->updateFarbe($product, $product_ebay, $specifics);
    }

    private function fillKombinationen(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {


        $is_einbau = $product->getCatId() == 10;

        if ($specifics->isAutoOverrideSpecific('Installationsart')) {
            if ($is_einbau) {
                //Freistehend, Integriert, Vollintegriert
                $specifics->setValue('Installationsart', 'Eingebaut', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            } else {
                $specifics->setValue('Installationsart', 'Freistehend', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            if ($product->getCatId() == 304) {
                //'Amerikanische Side-by-Side Kühl-Gefrier-Kombination' / 'Europäische Side-by-Side Kühl-Gefrier-Kombination'
                $specifics->setValue('Produktart', 'Amerikanische Side-by-Side Kühl-Gefrier-Kombination', ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            } else {
                $type = SpecificAutomaticUtils::getFeatureValue($product, 89, 124); //Gerätetyp von Einbau und Stand

                switch ($type) {
                    case 'Kühl-/Gefrierkombinationen (Gefrierteil unten)':
                    case 'Kühl-/Gefrierkombinationen':
                        if ($is_einbau) {
                            $value = 'Einbau-Kühl-Gefrier-Kombination mit Gefrierteil unten';
                        } else {
                            $value = 'Freistehende Kühl-Gefrier-Kombination';
                        }
                        break;
                    case 'Doppeltürgeräte (Gefrierteil oben)':
                    case 'Doppeltürgeräte':
                        if ($is_einbau) {
                            $value = 'Einbau-Kühl-Gefrier-Kombination mit Gefrierteil oben';
                        } else {
                            $value = 'Freistehende Kühl-Gefrier-Kombination';
                        }
                        break;
                    case 'Side-by-Side Geräte':
                    case 'Side-by-Side':
                        $value = 'Amerikanische Side-by-Side Kühl-Gefrier-Kombination';
                        break;
                    case 'Vorratszentrum':
                    case 'Frenchdoor-Kombinationen':
                    case 'Frenchdoor-Kombinationen (mit Gefrierschubladen)':
                        $value = 'French-Door-Kühlschrank';
                        break;
                    case 'Kühlgeräte':
                    case 'Tischkühlgeräte':
                    case 'Gefrierschränke':
                    case 'Flaschenkühlschrank':
                    case 'Kühlbox':
                    default:
                        $value = null;
                }

                if ($value) {
                    $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                }
            }
        }

        $this->updateFarbe($product, $product_ebay, $specifics);
    }

    private function updateFarbe(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if (!$specifics->isAutoOverrideSpecific('Farbe')) {
            return;
        }

        $value = $product->getColor();

        if (!$value) {
            $value = SpecificAutomaticUtils::getFeatureValue($product, 151, 2076, 161, 183);
        }

        if ($value) {
            $specifics->setValue('Farbe', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
        }
    }

    /**
     * Sortiert kühlgeräte aus der Ebay Hauptkategorie in die entsprechenden unterkategorien
     *
     * @param Product $product
     * @param ProductEbay $product_ebay
     * @param ProductEbaySpecifics $specifics
     */
    public function updateEbayCat(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {

        if ($specifics->getEbayCatId() != 71258) {
            return;
        }

        if ($product->getCatId() == 11) { //weinkühlscränke
            $product_ebay->setEbayManualCatId(177750);
            return;
        }

        $type = SpecificAutomaticUtils::getFeatureValue($product, 89, 124); //Gerätetyp von Einbau und Stand

        if ($type === null) {
            return;
        }

        if (isset(self::$ebay_cat_id_geraetetyp_mapping[$type])) {
            $ebay_cat_id = self::$ebay_cat_id_geraetetyp_mapping[$type];
            $product_ebay->setEbayManualCatId($ebay_cat_id);

            return;
        }

        foreach (self::$ebay_cat_id_keyword_mapping as $keyword => $ebay_cat_id) {
            if (mb_stripos($product->getProductName(), $keyword, 0, 'UTF-8') !== false) {
                $product_ebay->setEbayManualCatId($ebay_cat_id);
                return;
            }
        }
    }
}
