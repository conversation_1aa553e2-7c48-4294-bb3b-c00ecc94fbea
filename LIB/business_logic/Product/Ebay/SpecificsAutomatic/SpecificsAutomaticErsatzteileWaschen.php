<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticErsatzteileWaschen implements SpecificsAutomatic
{
    private static $produktart_cat_id_mapping = [
        856 => 'Tür',
        1442 => 'Tür',
        841 => 'Hauptsteuerplatine',
        1427 => 'Hauptsteuerplatine',
        1402 => 'Hauptsteuerplatine',
        1195 => 'Türmanschette',
        1199 => 'Schlauch',
        851 => 'Schlauch',
        847 => 'Wasserpumpe',
        1433 => 'Laugenpumpe',
        1428 => 'Heizungselement',
        842 => 'Heizungselement',
        857 => 'Spülmittelspender',
        1441 => 'Türriegel',
        1421 => 'Spülmittelspender',
        844 => 'Lagersatz',
        1439 => 'Türgriff',
        1431 => 'Lagersatz',
        854 => 'Dämpfer',
        1437 => 'Dämpfer',
        834 => 'Tür',
        1434 => 'Keilriemen',
        1432 => 'Magnetventil',
        845 => 'Magnetventil',
        830 => 'Flusensieb',
        1413 => 'Tür',
        823 => 'Hauptsteuerplatine',
        839 => 'Trommel',
        846 => 'Kohlebürste',
        1440 => 'Tür',
        1456 => 'Kohlebürste',
        848 => 'Keilriemen',
        1198 => 'Flusensieb',
        1436 => 'Flusensieb',
        838 => 'Bedienfeld',
        824 => 'Heizungselement',
        1403 => 'Heizungselement',
        1401 => 'Bedienfeld',
        1406 => 'Keilriemen',
        1412 => 'Dichtung',
        1424 => 'Trommel',
        1196 => 'Motor',
        828 => 'Keilriemen',
        822 => 'Bedienfeld',
        843 => 'Kondensator',
        852 => 'Filter',
        1438 => 'Thermostat',
        1423 => 'Bedienknopf',
        1405 => 'Motor',
        832 => 'Thermostat',
        1429 => 'Kondensator',
        1411 => 'Thermostat',
        1426 => 'Deckelschalter',
        855 => 'Thermostat',
        826 => 'Gebläserad',
        840 => 'Druckschalter',
        1648 => 'Lagersatz',
        1562 => 'Türscharnier',
        1435 => 'Deckelschalter',
        833 => 'Trommelwalze',
        1425 => 'Trommelrippe',
        827 => 'Wasserpumpe',
        1192 => 'Wasserpumpe',
        850 => 'Deckelschalter',
        831 => 'Spannrolle',
        1404 => 'Lagersatz',
        825 => 'Lagersatz',
        1408 => 'Filter',
        1410 => 'Laufrolle',
    ];

    private static $produktart_keyword_mapping = [
        'kondensator' => 'Kondensator',
        'verbindungssatz' => 'Verbindungssatz',
        'zwischenbausatz' => 'Verbindungssatz',
        'transportsicherung' => 'Installationszubehör',
        'gerätedeckel' => 'Obere Abdeckung',
        'steckfahnen' => 'Hauptsteuerplatine',
        'magnetventil' => 'Magnetventil',
        'arbeitsplatte' => 'Obere Abdeckung',
        'zwischenbaurahmen' => 'Verbindungssatz',
        'wassertank' => 'Wasserbehälter',
        'deckel' => 'Obere Abdeckung',
        'wassertankventil' => 'Magnetventil',
        'zulaufschlauch' => 'Einlass-/Füllschlauch',
        'unterbausatz' => 'Verbindungssatz',
        'tastenschalter' => 'Bedienknopf',
        'kabel' => 'Netzkabel',
        'abluftschlauch' => 'Abluftschlauch',
        'lampe' => 'Licht',
        'vario-schublade' => 'Seifenschubladengriff',
        'sensor' => 'Sensor',
        'kabelbaum' => 'Netzkabel',
        'bottich' => 'Trommel',
        'feder' => 'Dämpfer',
        'abluftschlauchset' => 'Abluftschlauch',
    ];


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 99697) {
            return;
        }

        $cat_ids = $product->getExtendedCatIds();
        $cat_ids[] = $product->getCatId();

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            $value = null;
            foreach ($cat_ids as $cat_id) {
                if (array_key_exists($cat_id, self::$produktart_cat_id_mapping)) {
                    $value = self::$produktart_cat_id_mapping[$cat_id];
                    $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                    break;
                }
            }

            if (!$value) {
                foreach (self::$produktart_keyword_mapping as $keyword => $value) {
                    if (mb_stripos($product->getProductName(), $keyword, 0, 'UTF-8') !== false) {
                        $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                        break;
                    }
                }
            }
        }

        if ($specifics->isAutoOverrideSpecific('Modell')) {
            $value = $product->getMpn();
            if ($value) {
                $specifics->setValue('Modell', $value, ProductEbaySpecificValue::VALUE_SETTED_FALLBACK);
            }
        }
    }
}
