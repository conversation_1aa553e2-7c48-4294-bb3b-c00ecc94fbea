<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticErsatzteileGeschirrspueler implements SpecificsAutomatic
{
    private static $produktart_cat_id_mapping = [
        734 => 'Korb',
        744 => 'Tür',
        740 => 'Schlauch',
        1166 => 'Korb',
        1174 => 'Schlauch',
        742 => 'Wascharm',
        1176 => 'Wascharm',
        1171 => 'Umwälzpumpe',
        736 => 'Umwälzpumpe',
        731 => 'Steuerplatine',
        1162 => 'Steuerplatine',
        1402 => 'Steuerplatine',
        1178 => 'Tür',
        1663 => 'Tür',
        1159 => 'Knopf',
        735 => 'Ventil',
        1173 => 'Knopf',
        729 => 'Knopf',
        737 => 'Sam<PERSON>behälter',
        738 => 'Knopf',
        732 => 'Sammelbehälter',
        1172 => 'Sam<PERSON>behälter',
        733 => 'Heizelement',
        730 => 'Sammelbehälter',
        1164 => 'Heizelement',
        1163 => 'Sammelbehälter',
        1160 => 'Dosierkammer',
        741 => 'Filter',
        1175 => 'Filter',
        1662 => 'Türdichtung',
        1165 => 'Besteckkorb',
        1169 => 'Druckschalter',
        889 => 'Luftfalle',
        1167 => 'Luftfalle',
        1179 => 'Pumpe',
        1559 => 'Schublade',
        743 => 'Temperatursensor',
        1177 => 'Temperatursensor',
        1168 => 'Ventil',
        1170 => 'Sammelbehälter',
        1199 => 'Schlauch',
    ];

    private static $produktart_keyword_mapping = [
        'zulaufschlauch' => 'Schlauch',
        'wassermelder' => 'Druckschalter',
        'laufschienenanschlag' => 'Schienenkappe',
        'türscharnierfeder' => 'Türfeder',
        'schalter' => 'Schalter'
    ];


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 116026) {
            return;
        }

        $cat_ids = $product->getExtendedCatIds();
        $cat_ids[] = $product->getCatId();

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            $value = null;
            foreach ($cat_ids as $cat_id) {
                if (array_key_exists($cat_id, self::$produktart_cat_id_mapping)) {
                    $value = self::$produktart_cat_id_mapping[$cat_id];
                    $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                    break;
                }
            }
//
//            if (!$value) {
//                foreach (self::$produktart_keyword_mapping as $keyword => $value) {
//                    if (mb_stripos($product->getProductName(), $keyword, 0, 'UTF-8') !== false) {
//                        $specifics->setValue('Produktart', $value, product_ebay_specific_value::VALUE_SETTED_AUTO);
//                        break;
//                    }
//                }
//            }
        }

        if ($specifics->isAutoOverrideSpecific('Modell')) {
            $value = $product->getMpn();
            if ($value) {
                $specifics->setValue('Modell', $value, ProductEbaySpecificValue::VALUE_SETTED_FALLBACK);
            }
        }
    }
}
