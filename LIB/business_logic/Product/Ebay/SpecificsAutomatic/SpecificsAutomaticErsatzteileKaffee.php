<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;
use wws\ProductDevice\ProductDeviceReader;

class SpecificsAutomaticErsatzteileKaffee implements SpecificsAutomatic
{

    static array $produktart_cat_id_mapping = [
        765 => 'Gehäuseteile',
        768 => 'Ventil',
        1241 => 'Ventil',
        1233 => 'Dichtung',
        763 => 'Brüheinheit',
        1231 => 'Brüheinheit',
        764 => 'Dichtung',
        761 => 'Tropfschale',
        1236 => 'Schlauch',
        1225 => 'Wasserbehälter',
        769 => 'Schlauch',
        1186 => 'Silikonfett',
        1222 => 'Tropfschale',
        1238 => 'Milchschaumkännchen',
        1235 => 'Deckel',
        1249 => 'Heizelement',
        1234 => 'Elektronik',
        1244 => 'Ausguss',
        1185 => 'Wasserpumpe',
        1223 => 'Milchschaumkännchen',
        1248 => 'Mahlwerk',
        767 => 'Wasserpumpe',
        1239 => 'Gehäuseteile',
        890 => 'Elektronik',
        1224 => 'Kaffeekanne',
        1230 => 'Bohnenbehälter',
        1226 => 'Wasserfilter',
        1232 => 'Brüheinheit',
        1240 => 'Mikroschalter',
        1251 => 'Reinigungsset'
    ];


    static array $produktart_keyword_mapping = [
        'dampfrohr' => 'Dampfdüse',
        'milchaufschäumer' => 'Dampfdüse',
        'dampfdüse' => 'Dampfdüse',
        'dampfhahn' => 'Dampfdüse',
        'milchbehälter' => 'Milchbehälter',
        'wassertankdeckel' => 'Wasserbehälter',
        'wassertank' => 'Wasserbehälter',
        'wasserbehälter' => 'Wasserbehälter',
        'kaffeekanne' => 'Kaffeekanne',
        'thermokanne' => 'Kaffeekanne',
        'glaskannendeckel' => 'Kaffeekanne',
        'glaskanne' => 'Kaffeekanne',
        'kanne' => 'Kaffeekanne',
        'kaffeeauslaufverteiler' => 'Auslauf',
        'kaffeeauslauf' => 'Auslauf',
        'getränkeauslaufdüse' => 'Auslauf',
        'milchauslauf' => 'Auslauf',
        'auslauf' => 'Auslauf',
        'mahlwerk' => 'Mahlwerk',
        'heizelement' => 'Heizelement',
        'heizzone' => 'Heizelement',
        'thermosicherung' => 'Sensor',
        'temperaturfühler' => 'Sensor',
        'temperaturbegrenzer' => 'Sensor',
        'temperaturregler' => 'Sensor',
        'durchflussmelder' => 'Sensor',
        'membranregler' => 'Sensor',
        'sensor' => 'Sensor',
        'durchflussmenge' => 'Sensor',
        'schwimmer' => 'Sensor',
        'wasserfilter' => 'Wasserfilter',
        'padhalter' => 'Padhalter',
        'kaffeepads' => 'Padhalter',
        'kapselkäfig' => 'Padhalter',
        'kaffeekapselhalter' => 'Padhalter',
        'tank' => 'Wasserbehälter',
        'edelstahlpflege' => 'Reiniger',
        'putzstein' => 'Reiniger',
        'reinigungsschwamm' => 'Reiniger',
        'mikrofasertuch' => 'Reiniger',
        'teinigungs' => 'Reiniger',
        'reiniger' => 'Reiniger',
        'entkalkung' => 'Reiniger',
        'reinigungstabletten' => 'Reiniger',
        'entkalkungskartusche' => 'Reiniger',
        'bohnenbehälter' => 'Bohnenbehälter',
        'kaffeefilterbehälter' => 'Filter',
        'abdeckung' => 'Gehäuse',
        'pulverschublade' => 'Gehäuse',
        'absteller' => 'Gehäuse',
        'träger' => 'Gehäuse',
        'blende' => 'Gehäuse',
        'behälter' => 'Gehäuse',
        'gehäuse' => 'Gehäuse',
        'griff' => 'Gehäuse',
        'klappe' => 'Gehäuse',
        'deckel' => 'Gehäuse',
        'seitenwand' => 'Gehäuse',
        'gummifuss' => 'Gehäuse',
        'buchse' => 'Gehäuse',
        'gitter' => 'Gehäuse',
        'schale' => 'Gehäuse',
        'blech' => 'Gehäuse',
        'einsatz' => 'Gehäuse',
        'fuss' => 'Gehäuse',
        'entriegelungshebel' => 'Gehäuse',
        'pyramidenplatte' => 'Gehäuse',
        'platte' => 'Gehäuse',
        'halter' => 'Gehäuse',
        'sieb' => 'Gehäuse',
        'montagewinkel' => 'Gehäuse',
        'kaffeepulverschacht' => 'Gehäuse',
        'trichter' => 'Gehäuse',
        'spezialfett' => 'Silikonfett',
        'verteiler' => 'Mechanik',
        'verriegelungsschieber' => 'Mechanik',
        'kupplung' => 'Mechanik',
        'zahnrad' => 'Mechanik',
        'führung' => 'Mechanik',
        'lager' => 'Mechanik',
        'mitnehmer' => 'Mechanik',
        'spindel' => 'Mechanik',
        'zahnriemen' => 'Mechanik',
        'befestigungssatz' => 'Mechanik',
        'anschlusselement' => 'Mechanik',
        'bypass' => 'Mechanik',
        'kupplungsstück' => 'Mechanik',
        'winkel' => 'Mechanik',
        'schlauch' => 'Mechanik',
        'stöpsel' => 'Mechanik',
        'aufnahme' => 'Mechanik',
        'feder' => 'Mechanik',
        'getriebeeinheit' => 'Mechanik',
        'dosierspule' => 'Mechanik',
        'hülse' => 'Mechanik',
        'schraube' => 'Mechanik',
        'reguliereinheit' => 'Mechanik',
        'kaffeemehlschieber' => 'Mechanik',
        'ablaufpumpe' => 'Pumpe',
        'filterwechselanzeige' => 'Display',
        'rückschlagventil' => 'Ventil',
        'zulaufventil' => 'Ventil',
    ];

    private ProductDeviceReader $product_device_reader;


    public function __construct(ProductDeviceReader $product_device_reader)
    {
        $this->product_device_reader = $product_device_reader;
    }

    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        if ($specifics->getEbayCatId() != 99565) {
            return;
        }

        $cat_ids = $product->getExtendedCatIds();
        $cat_ids[] = $product->getCatId();

        if ($specifics->isAutoOverrideSpecific('Produktart')) {
            foreach ($cat_ids as $cat_id) {
                if (array_key_exists($cat_id, self::$produktart_cat_id_mapping)) {
                    $value = self::$produktart_cat_id_mapping[$cat_id];
                    $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                    break;
                }
            }

            if (!$specifics->getSpecific('Produktart')->getValue()) {
                foreach (self::$produktart_keyword_mapping as $keyword => $value) {
                    if (mb_stripos($product->getProductName(), $keyword, 0, 'UTF-8') !== false) {
                        $specifics->setValue('Produktart', $value, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
                        break;
                    }
                }
            }
        }

        if ($specifics->isAutoOverrideSpecific('Markenkompatibilität')) {
            $brands = SpecificAutomaticUtils::getDeviceBrandsFuerFormated($product->getProductId());

            if ($brands) {
                $specifics->setValue('Markenkompatibilität', $brands, ProductEbaySpecificValue::VALUE_SETTED_AUTO);
            }
        }
    }
}
