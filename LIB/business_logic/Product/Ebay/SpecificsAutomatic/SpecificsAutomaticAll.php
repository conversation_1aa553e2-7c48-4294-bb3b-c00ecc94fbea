<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use debug;
use env;
use service_loader;
use Throwable;
use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Product;

class SpecificsAutomaticAll implements SpecificsAutomatic
{
    /**
     * @var SpecificsAutomatic[]
     */
    private array $modules = [];

    private static SpecificsAutomaticAll $instance;

    public static function getInstance(): SpecificsAutomaticAll
    {
        if (!isset(self::$instance)) {
            self::$instance = new SpecificsAutomaticAll();
            self::$instance->addModule(new SpecificsAutomaticGeneric());

            if (env::isEcom()) {
                self::$instance->addModule(service_loader::getDiContainer()->get(SpecificsAutomaticScraper::class));
                self::$instance->addModule(new SpecificsAutomaticLichterketten());
                self::$instance->addModule(new SpecificsAutomaticGeschirrspueler());
                self::$instance->addModule(new SpecificsAutomaticWaschmaschinen());
                self::$instance->addModule(new SpecificsAutomaticErsatzteileHerd());
                self::$instance->addModule(new SpecificsAutomaticKuehlen());
                self::$instance->addModule(new SpecificsAutomaticErsatzteileStaubsauger());
                self::$instance->addModule(service_loader::get(SpecificsAutomaticErsatzteileKaffee::class));
                self::$instance->addModule(new SpecificsAutomaticErsatzteileGeschirrspueler());
                self::$instance->addModule(new SpecificsAutomaticErsatzteileWaschen());
                self::$instance->addModule(new SpecificsAutomaticErsatzteileDunstabzugshauben());
            }

            if (env::isK11()) {
                self::$instance->addModule(new SpecificsAutomaticK11());
            }
        }

        return self::$instance;
    }


    public function runProduct(Product $product, ProductEbay $product_ebay, ?int $ebay_cat_id = null): void
    {
        $specifics = $product_ebay->getEbaySpecifics($ebay_cat_id);

        $this->autoFillByProduct($product, $product_ebay, $specifics);
    }


    public function addModule(SpecificsAutomatic $specifics_automatic): void
    {
        $this->modules[] = $specifics_automatic;
    }


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        try {
            foreach ($this->modules as $module) {
                $module->autoFillByProduct($product, $product_ebay, $specifics);
            }
        } catch (Throwable $e) {
            debug::dumpException($e);
            debug::dump($product->getProductName(), $product->getProductId());

            debug::limitIterations();
        }
    }
}
