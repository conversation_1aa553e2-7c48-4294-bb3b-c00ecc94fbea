<?php

namespace wws\Product\Ebay\SpecificsAutomatic;

use bqp\Collection\KeyValueCounter;
use bqp\db\db_generic;
use bqp\extern\Ebay\OfferData\EbayDataScraperRepository;
use bqp\Json;
use wws\Product\Ebay\ProductEbay;
use wws\Product\Ebay\ProductEbaySpecifics;
use wws\Product\Ebay\ProductEbaySpecificValue;
use wws\Product\Product;

class SpecificsAutomaticScraper implements SpecificsAutomatic
{
    private db_generic $db;

    private array $auto_specifics = [];

    public function __construct(db_generic $db, EbayDataScraperRepository $repository)
    {
        $this->db = $db;

        foreach ($repository->getAutoSpecificsKeys() as $row) {
            $this->auto_specifics[$row['ebay_cat_id'] . $row['specific_key']] = true;
        }
    }


    public function autoFillByProduct(Product $product, ProductEbay $product_ebay, ProductEbaySpecifics $specifics): void
    {
        $values = $this->getSpecificValuesForProductId($product->getProductId());

        if (!$product_ebay->getEbayCatId() && isset($values['ebay_cat_id'])) {
            $product_ebay->setEbayManualCatId($values['ebay_cat_id']);
            return; //beim umsetzen der Kategorie, sind die specifics falsch
        }

        if (!$product_ebay->getEbayProductId() && isset($values['ebay_product_id'])) {
            $product_ebay->setEbayProductId($values['ebay_product_id']);
        }

        foreach ($specifics->getAllSpecifics() as $specific) {
            if (!$specific->isAutoOverrideSpecific()) {
                continue;
            }

            if (!array_key_exists($specific->getKey(), $values)) {
                continue;
            }

            if ($specific->getValue() == $values[$specific->getKey()]) {
                continue;
            }

            if (!$this->autoSetSpecific($specific, $specifics->getEbayCatId())) {
                continue;
            }

            $specific->setValue($values[$specific->getKey()]);
            $specific->setValueSetted($specific::VALUE_SETTED_SCRAPED);
        }
    }

    private function autoSetSpecific(ProductEbaySpecificValue $specific, int $ebay_cat_id): bool
    {
        if ($specific->getUsage() === ProductEbaySpecificValue::REQUIRED) {
            return true;
        }

        if ($specific->getKey() === 'Energieeffizienzklasse') {
            return true;
        }

        if (isset($this->auto_specifics[$ebay_cat_id . $specific->getKey()])) {
            return true;
        }

        return false;
    }


    private function getSpecificValuesForProductId(int $product_id): array
    {
        $status = $this->db->fieldQuery("
            SELECT
                ext_ebay_product_scraper.status
            FROM
                ext_ebay_product_scraper
            WHERE
                ext_ebay_product_scraper.product_id = $product_id
        ");

        if ($status != EbayDataScraperRepository::PRODUCT_STATUS_COMPLETE) {
            return [];
        }

        $result = $this->db->query("
            SELECT
                ext_ebay_product_scraper_item.item_id,
                ext_ebay_product_scraper_item.cat_id,
                ext_ebay_product_scraper_item.specifics,
                ext_ebay_product_scraper_item.ebay_product_id
            FROM
                ext_ebay_product_scraper_item
            WHERE
                ext_ebay_product_scraper_item.product_id = $product_id
        ");

        $collector = new KeyValueCounter();

        foreach ($result as $row) {
            $collector->add('ebay_cat_id', $row['cat_id']);
            $collector->add('ebay_product_id', $row['ebay_product_id']);

            if (!$row['specifics']) {
                continue;
            }

            $specifics = Json::decode($row['specifics']);

            foreach ($specifics as $key => $value) {
                if (!is_array($value)) {
                    $value = [$value];
                }

                foreach ($value as $value2) {
                    if ($this->validValue($value2)) {
                        $collector->add($key, $value2);
                    }
                }
            }
        }

        //$collector->display();
        return $collector->getMostUsedValues();
    }


    private function validValue($value): bool
    {
        if ($value == '-/-') {
            return false;
        }
        //    if ($value == 'nicht zutreffend') {
        //        return false;
        //    }


        return true;
    }
}
