<?php

namespace wws\Product\Ebay\EventHandler;

use bqp\Event\EventDispatcher;
use wws\Product\Ebay\Event\EventProductEbayCatChange;
use wws\Product\Event\EventProductChange;

class ProductChangeEbayHandler
{
    private EventDispatcher $event_dispatcher;

    public function __construct(EventDispatcher $event_dispatcher)
    {
        $this->event_dispatcher = $event_dispatcher;
    }

    public function handle(EventProductChange $product_change): void
    {
        $entity_changes = $product_change->getEntityChanges();

        if (!$entity_changes->isChange('stamm.cat_id')) {
            return;
        }

        $this->event_dispatcher->dispatch(new EventProductEbayCatChange($product_change->getProductId()));
    }
}
