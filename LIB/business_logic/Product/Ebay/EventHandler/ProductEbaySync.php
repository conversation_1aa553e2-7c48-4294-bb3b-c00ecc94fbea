<?php

namespace wws\Product\Ebay\EventHandler;

use bqp\Event\AsyncEvent;
use bqp\Event\AsyncEventHandler;
use wws\Product\actions\product_actions;
use wws\Product\ProductRepository;

class ProductEbaySync implements AsyncEventHandler
{
    private ProductRepository $product_repository;

    public function __construct(ProductRepository $product_repository)
    {
        $this->product_repository = $product_repository;
    }

    /**
     * @param AsyncEvent $async_event
     */
    public function processAsyncEvent(AsyncEvent $async_event): void
    {
        //das ganze sollte aber als bulk ausgeführt werden, bulk ist aber dereit nur mit individuellen listenern möglich :|

        $message = $async_event->getMessage();
        $product_id = (int)$message['product_id'];

        $product = $this->product_repository->loadProduct($product_id);

        if (product_actions::updateEbay($product)) {
            $this->product_repository->save($product);
        }
    }
}
