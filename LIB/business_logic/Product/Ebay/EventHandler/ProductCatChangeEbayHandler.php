<?php

namespace wws\Product\Ebay\EventHandler;

use bqp\db\db_generic;
use bqp\Event\EventDispatcherBulk;
use wws\Product\Ebay\Event\EventProductEbayCatChange;
use wws\Product\Event\EventProductCatChange;

class ProductCatChangeEbayHandler
{
    private EventDispatcherBulk $event_dispatcher_bulk;
    private db_generic $db;

    public function __construct(db_generic $db, EventDispatcherBulk $event_dispatcher_bulk)
    {
        $this->event_dispatcher_bulk = $event_dispatcher_bulk;
        $this->db = $db;
    }


    public function handle(EventProductCatChange $product_cat_change): void
    {
        $entity_changes = $product_cat_change->getEntityChanges();

        if (!$entity_changes->isChange('product_cat.ebay_cat_id')) {
            return;
        }

        $product_cat = $product_cat_change->getProductCat();

        $product_ids = $this->getProductIdsForCatId($product_cat->getCatId());

        foreach ($product_ids as $product_id) {
            $event = new EventProductEbayCatChange($product_id, $product_cat->getEbayCatId());
            $this->event_dispatcher_bulk->dispatchBulk($event);
        }

        $this->event_dispatcher_bulk->flushBulk();
    }

    private function getProductIdsForCatId(int $cat_id): array
    {
        //nur primär kategorie beachten
        return $this->db->query("
            SELECT
                product.product_id
            FROM
                product LEFT JOIN
                product_ebay ON (product.product_id = product_ebay.product_id)
            WHERE
                product.cat_id = $cat_id AND
                (
                    product_ebay.ebay_manual_cat_id IS NULL OR
                    product_ebay.ebay_manual_cat_id = 0
                )
        ")->asSingleArray();
    }
}
