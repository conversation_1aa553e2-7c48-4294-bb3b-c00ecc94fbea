<?php

namespace wws\Product\Actionbar;

use bqp\Actionbar\ActionbarElement;
use service_loader;
use wws\Product\ProductTypes\ProductType;
use wws\Product\ProductTypes\ProductTypeRepository;

class ActionbarProductTypes extends ActionbarElement
{
    /**
     * @var callable
     */
    private $url_callback;

    private ?string $product_type = null;

    /**
     * @var ProductType[]
     */
    private array $product_types;
    private bool $show_all = true;
    private array $extra_options = [];
    private ?array $quantities = null;

    public function __construct()
    {
        $this->product_types = service_loader::get(ProductTypeRepository::class)->getProductTypes();
    }

    public function setShowAllAsProductType(bool $show_all): void
    {
        $this->show_all = $show_all;
    }

    public function setUrl(callable $url_callback): void
    {
        $this->url_callback = $url_callback;
    }

    public function setProductType(string $product_type): void
    {
        $this->product_type = $product_type;
    }

    public function addExtraOption(string $key, string $label, string $color = null): void
    {
        $this->extra_options[] = [
            'label' => $label,
            'key' => $key,
            'color' => $color,
            'quantity' => null
        ];
    }

    public function setQuantities(array $quantities): void
    {
        $this->quantities = $quantities;
    }

    private function getOptions(): array
    {
        $options = [];

        if ($this->show_all) {
            $options[] = [
                'label' => 'Alle',
                'key' => 'all',
                'color' => null,
                'quantity' => null
            ];
        }

        foreach ($this->product_types as $product_type) {
            $option = [
                'label' => $product_type->getProductTypeName(),
                'key' => $product_type->getProductType(),
                'color' => $product_type->getProductTypeColor(),
                'quantity' => null
            ];

            if ($this->quantities !== null) {
                $option['quantity'] = 0;

                if (isset($this->quantities[$product_type->getProductType()])) {
                    $option['quantity'] = $this->quantities[$product_type->getProductType()];
                }
            }

            $options[] = $option;
        }

        foreach ($this->extra_options as $option) {
            $options[] = $option;
        }

        return $options;
    }

    public function render(): string
    {
        $return = '';

        $return .= '<ul class="product_type_select">';

        foreach ($this->getOptions() as $option) {
            $s = $this->product_type == $option['key'] ? ' style="font-weight: bold;"' : '';

            $c = '';
            if ($option['color']) {
                $c = '<div class="product_type_color" style="background-color:' . $option['color'] . ';"></div> &nbsp;';
            }

            $q = '';
            if ($option['quantity']) {
                $q = ' <small>(' . $option['quantity'] . ')</small>';
            }

            $url = call_user_func($this->url_callback, $option['key']);
            $return .= '<li style="display: inline-block; padding-left: 15px;">' . $c . ' <a href="' . $url . '"' . $s . '>' . $option['label'] . $q . '</a></li>';
        }

//        if ($this->show_all) {
//            $s = $this->product_type == 'all'?' style="font-weight: bold;"':'';
//
//            $url = call_user_func($this->url_callback, ['product_type' => 'all']);
//            $return .= '<li style="display: inline-block; padding: 4px;"><a href="'.$url.'"'.$s.'>Alle</a></li>';
//        }
//
//        foreach ($this->product_types as $product_type) {
//            $s = $this->product_type == $product_type->getProductType()?' style="font-weight: bold;"':'';
//
//            $url = call_user_func($this->url_callback, $product_type);
//            $return .= '<li style="display: inline-block; padding-left: 15px;"><div class="product_type_color" style="background-color:'.$product_type['product_type_color'].';"></div> &nbsp; <a href="'.$url.'"'.$s.'>'.$product_type['typ_name'].'</a></li>';
//        }
        $return .= '</ul>';

        return $return;
    }
}
