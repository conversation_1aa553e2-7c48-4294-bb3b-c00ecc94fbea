<?php

namespace wws\OrderPlatformLinking;

use bqp\Config\ConfigRegistry;
use bqp\extern\Amazon\Order\OrderPlatformLinkingModuleAmazon;
use bqp\extern\Aswo\Wws\OrderPlatformLinkingModuleAswo;
use bqp\extern\Ebay\OrderPlatformLinkingModuleEbay;
use bqp\extern\Galaxus\OrderPlatformLinkingModuleGalaxus;
use bqp\extern\idealo\OrderPlatformLinkingModuleIdealo;
use bqp\extern\Kaufland\OrderPlatformLinkingModuleKaufland;
use bqp\extern\Paypal\OrderPlatformLinkingModulePaypal;
use bqp\extern\Unzer\OrderPlatformLinkingModuleUnzer;
use env;
use wws\core\PlatformLinking\PlatformLink;
use wws\Order\Order;
use wws\Order\OrderItem;
use wws\Shopware\Wws\OrderPlatformLinkingModuleErsatzteilshop;
use wws\Supplier\SuppliersConst;

class OrderPlatformLinking
{
    /**
     * @var OrderPlatformLinkingModule[]
     */
    private array $modules = [];

    public function __construct(ConfigRegistry $config_registry)
    {
        if (env::isEcom()) {
            $this->modules[] = new OrderPlatformLinkingModuleAswo([SuppliersConst::SUPPLIER_ID_ASWO]);
        }

        if (env::isK11()) {
            $this->modules[] = new OrderPlatformLinkingModuleAswo([SuppliersConst::SUPPLIER_ID_EURAS]);
            $this->modules[] = new OrderPlatformLinkingModuleErsatzteilshop();
        }

        $this->modules[] = new OrderPlatformLinkingModulePaypal();
        $this->modules[] = new OrderPlatformLinkingModuleAmazon();
        $this->modules[] = new OrderPlatformLinkingModuleEbay();
        $this->modules[] = new OrderPlatformLinkingModuleKaufland();
        $this->modules[] = new OrderPlatformLinkingModuleIdealo();
        $this->modules[] = new OrderPlatformLinkingModuleUnzer();
        $this->modules[] = new OrderPlatformLinkingModuleGalaxus();
    }

    /**
     * @param Order $order
     * @return PlatformLink[]
     */
    public function getByOrder(Order $order): array
    {
        $links = [];

        foreach ($this->modules as $module) {
            $result = $module->getByOrder($order);
            foreach ($result as $link) {
                $links[] = $link;
            }
        }

//      Nicht mehr möglich: CSRF Token + Post (mail an check24... ansonsten webextension? (aber aufwand/nutzen!!!))
//        if ($order->getOrderOriginId() === 'c24') {
//            $date = clone $order->getBestellDatum();
//            $date_from = $date->subSimple('days', 4)->format('d.m.Y');
//            $date_to = $date->addSimple('days', 8)->format('d.m.Y');
//
//            $links[] = new PlatformLink('https://www.testsieger.de/app/partner/shop/order/list.html?status=all&date_from='.$date_from.'&date_to='.$date_to.'&list_search_field=order_number&list_search_value='.$order->getPaymentReferenz(), 'Auftrag bei Check24 öffnen');
//        }

        return $links;
    }

    /**
     * @param OrderItem $order_item
     * @return PlatformLink[]
     */
    public function getByOrderItem(OrderItem $order_item): array
    {
        return [];
    }
}
