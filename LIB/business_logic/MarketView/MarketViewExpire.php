<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use wws\MarketView\Import\MarketViewImportFactory;
use wws\MarketView\Import\MarketViewImportProduct;

class MarketViewExpire
{
    protected db_generic $db_mv;
    private MarketViewImportFactory $market_view_import_factory;

    public function __construct(db_generic $db_mv, MarketViewImportFactory $market_view_import_factory)
    {
        $this->db_mv = $db_mv;
        $this->market_view_import_factory = $market_view_import_factory;
    }

    public function calcExpiredOffers(): void
    {
        $result = $this->db_mv->query("
                SELECT
                    market_view_source.mvsrc_id,
                    market_view_source.source_product_validity,
                    market_view_source.source_availability_validity,
                    market_view_source.source_price_validity
                FROM
                    market_view_source
                WHERE
                    market_view_source.status = 'aktiv'
        ");

        foreach ($result as $row) {
            if ($row['source_product_validity']) {
                $this->expireOffers($row['mvsrc_id'], $row['source_product_validity']);
            }
            if ($row['source_availability_validity']) {
                $this->expireOfferAvailability($row['mvsrc_id'], $row['source_availability_validity']);
            }
            if ($row['source_price_validity']) {
                $this->expireOfferPrices($row['mvsrc_id'], $row['source_price_validity']);
            }
        }
    }

    private function expireOffers(int $mvsrc_id, int $source_product_validity): void
    {
        $import = $this->market_view_import_factory->getImporter($mvsrc_id);

        $mvsrc_product_ids = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $mvsrc_id . "' AND
                market_view_product.product_status = 'online' AND
                market_view_product.date_updated <= DATE_SUB(NOW(), INTERVAL " . $source_product_validity . " HOUR)
        ")->asSingleArray();

        foreach ($mvsrc_product_ids as $mvsrc_product_id) {
            $import->setProductOffline($mvsrc_product_id);
        }
    }

    private function expireOfferAvailability(int $mvsrc_id, int $source_product_validity): void
    {
        $import = $this->market_view_import_factory->getImporter($mvsrc_id);

        $mvsrc_product_ids = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $mvsrc_id . "' AND
                market_view_product.date_updated_availability_id <= DATE_SUB(NOW(), INTERVAL " . $source_product_validity . " HOUR) AND
                market_view_product.availability_id NOT IN (" . MarketViewConst::AVAILABILITY_ID_UNKNOWN . ", " . MarketViewConst::AVAILABILITY_ID_OFFLINE . ")
        ")->asSingleArray();

        foreach ($mvsrc_product_ids as $mvsrc_product_id) {
            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($mvsrc_product_id);
            $product->setAvailabilityId(MarketViewConst::AVAILABILITY_ID_UNKNOWN);
            $product->setInventory(0);
            $product->setMvAvailability('');

            $import->updateProduct($product);
        }
    }

    private function expireOfferPrices(int $mvsrc_id, int $source_price_validity): void
    {
        $import = $this->market_view_import_factory->getImporter($mvsrc_id);

        $mvsrc_product_ids = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $mvsrc_id . "' AND
                market_view_product.product_status = 'online' AND
                market_view_product.date_updated_vk_netto <= DATE_SUB(NOW(), INTERVAL " . $source_price_validity . " HOUR) AND
                market_view_product.vk_netto != 0
        ")->asSingleArray();

        foreach ($mvsrc_product_ids as $mvsrc_product_id) {
            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($mvsrc_product_id);
            $product->setVkNetto(0);

            $import->updateProduct($product);
        }
    }
}
