<?php

namespace wws\MarketView\History;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;
use wws\MarketView\Import\MarketViewImportExistingOffer;
use wws\MarketView\Import\MarketViewImportProduct;

class MarketViewHistoryWriterOldDb implements MarketViewHistoryWriter
{
    /**
     * @var ExtendedInsertQuery
     */
    private $insert_stmt;

    /**
     * @var int
     */
    private $auto_flush = 1000;

    /**
     * @var int
     */
    private $mvsrc_id;

    /**
     * @var string
     */
    private $date;

    public function __construct(int $mvsrc_id, db_generic $db_mv)
    {
        $this->mvsrc_id = $mvsrc_id;
        $this->date = date('Y-m-d');

        $this->insert_stmt = new ExtendedInsertQuery($db_mv, 'market_view_history', ['mvsrc_id', 'mvsrc_product_id', 'date_added', 'last_flag', 'vk_netto', 'availability_id']);
        $this->insert_stmt->setAutoexecute($this->auto_flush);
        $this->insert_stmt->setAutoUpdate(true);
    }

    public function addByImport(MarketViewImportProduct $market_view_import_product, ?MarketViewImportExistingOffer $existing_offer): void
    {
        //Durch das bestehende DB-Schema fällt das hier etwas umständlich aus.
        //Das aktuelle Schema setzt voraus, dass vk_netto und availability_id gemeinsam geschrieben werden, auch wenn sich nur ein Wert ändert.
        //(Bisher wurden die Änderungen über ein JOIN bestimmt und geschrieben.)

        $vk_netto = $market_view_import_product->issetKey('vk_netto') ? $market_view_import_product->getVkNetto() : null;
        $availability_id = $market_view_import_product->issetKey('availability_id') ? $market_view_import_product->getAvailabilityId() : null;

        //keine relevanten änderungen möglich
        if ($vk_netto === null && $availability_id === null) {
            return;
        }

        //neues angebot
        if (!$existing_offer) {
            $this->add($market_view_import_product->getMvsrcProductId(), $vk_netto ?? 0, $availability_id ?? 0);
            return;
        }

        $old_vk_netto = $existing_offer->getVkNetto();
        $old_availability_id = $existing_offer->getAvailabilityId();

        //prüfen was sich geändert hat
        $changed_vk = $vk_netto !== null && abs($vk_netto - $old_vk_netto) > 0.01;
        $changed_availability_id = $availability_id !== null && $availability_id !== $old_availability_id;

        //keine änderung
        if (!$changed_vk && !$changed_availability_id) {
            return;
        }

        //wenn sich der Wert geändert hat, den neuen Wert übernehmen, ansonsten den alten Wert.
        $this->add(
            $market_view_import_product->getMvsrcProductId(),
            $changed_vk ? $vk_netto : $old_vk_netto,
            $changed_availability_id ? $availability_id : $old_availability_id
        );
    }

    private function add(string $mvsrc_product_id, float $vk_netto, int $availability_id): void
    {
        $entry = [
            'mvsrc_id' => $this->mvsrc_id,
            'mvsrc_product_id' => $mvsrc_product_id,
            'date_added' => $this->date,
            'last_flag' => 0,
            'vk_netto' => $vk_netto,
            'availability_id' => $availability_id
        ];

        $this->insert_stmt->add($entry);
    }

    public function flush(): void
    {
        $this->insert_stmt->flush();
    }

    public function __destruct()
    {
        $this->flush();
    }
}
