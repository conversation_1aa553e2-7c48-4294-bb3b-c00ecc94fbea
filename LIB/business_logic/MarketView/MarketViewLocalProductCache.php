<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Model\EntityChanges;
use wws\Product\Product;
use wws\Product\ProductConst;
use wws\Product\ProductRepository;
use wws\Product\ProductRepositoryLegacy;

class MarketViewLocalProductCache
{
    private db_generic $db_mv;

    private ProductRepository $product_repository;

    public function __construct(db_generic $db_mv, ProductRepository $product_repository)
    {
        $this->db_mv = $db_mv;
        $this->product_repository = $product_repository;
    }

    public function productChangeHookHandler(Product $product, EntityChanges $changes): void
    {
        $update = $changes->isChange(['stamm.product_name', 'stamm.product_status', 'stamm.ean', 'stamm.product_tags']);

        if ($update) {
            $this->updateProduct($product);
        }
    }

    public function updateProduct(Product $product): void
    {
        $daten = [];

        $daten['product_id'] = $product->getProductId();
        $daten['product_name'] = $product->getProductName();
        $daten['ean'] = $product->getEan();

        $flags = $product->getProductTags();

        if ($product->getProductStatus() === ProductConst::PRODUCT_STATUS_PREADD) {
            $flags[] = ProductConst::TAG_PDM_CREATE;
        }

        $daten['product_tags'] = implode(',', $flags);
        $daten['product_is_active'] = $product->getProductStatus() === ProductConst::PRODUCT_STATUS_NORMAL ? 1 : 0;

        $this->db_mv->simpleInsertUpdate('market_view_local_product_cache', $daten);
    }

    public function updateAllProducts(): void
    {
        $load = $this->product_repository->getDefaultLoadConfig();
        $load->setProductDetailsDefault(false);

        $products = $this->product_repository->loadAllProducts(250, $load);

        foreach ($products as $product) {
            $this->updateProduct($product);
        }
    }

    public static function tableHelper_local_product_info(array $row): string
    {
        if (!$row['product_id']) {
            return '';
        }

        $flags_html = ProductRepositoryLegacy::getProductTagsAsHtml($row['product_tags']);

        if ($flags_html) {
            $flags_html = ' ' . $flags_html;
        }

        return '<a href="javascript:datenblatt(' . $row['product_id'] . ')" class="inline"><img src="/res/images/icons/book_next.png" style="vertical-align: middle;" alt=""> ' . $row['product_id'] . $flags_html . '</a>';
    }
}
