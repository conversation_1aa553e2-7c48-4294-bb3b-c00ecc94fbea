<?php

namespace wws\MarketView;

use bqp\GdImage\GdImage;
use bqp\Json;
use bqp\storage\storage_mount_manager;
use bqp\String\Base62;
use config;
use debug;
use service_loader;
use Throwable;

class MarketViewMediaCache
{
    public /*protected*/ string $url;


    protected MarketViewRepository $mv_repository;
    protected storage_mount_manager $storage;
    private string $thumbnail_cache_dir;

    public function __construct(MarketViewRepository $mv_repository)
    {
        $this->url = config::system('url');
        $this->thumbnail_cache_dir = config::system('temp_dir') . '/market_view_thumbs/';

        $this->mv_repository = $mv_repository;

        $this->storage = service_loader::getStorageFactory()->get('marketview');
    }

    public function isStorageUrl(string $url): bool
    {
        return str_starts_with($url, 'marketview://');
    }

    public function buildUrl(int $mvsrc_id, string $url): string
    {
        if (!$url) {
            return '';
        }

        if ($this->isStorageUrl($url)) {
            return $this->url . 'get_mv_media.php?p=' . $this->encodeUrl($url);
        }

        if ($this->mv_repository->isMediaCacheEnabledForMvsrcId($mvsrc_id)) {
            return $this->url . 'get_mv_media.php?p=' . $this->encodeUrl($url);
        }

        return $url;
    }


    public function buildThumbnailUrl(int $mvsrc_id, string $url, string $size = '300x300'): string
    {
        if ($this->isStorageUrl($url)) {
            return $this->url . 'get_mv_media.php?p=' . $this->encodeUrl($url, ['size' => $size]);
        }

        if ($this->mv_repository->isMediaCacheEnabledForMvsrcId($mvsrc_id)) {
            return $this->url . 'get_mv_media.php?p=' . $this->encodeUrl($url, ['size' => $size]);
        }

        return $url;
    }

    public function encodeUrl(string $url, array $extra = null): string
    {
        if (!$extra) {
            $extra = [];
        }
        $extra['url'] = $url;

        return Base62::encode(json_encode($extra));
    }

    public function decodeUrl(string $parameter): array
    {
        return Json::decode(Base62::decode($parameter));
    }

    public function fetchStorageUrlByMediaUrl(string $url): string
    {
        if ($this->isStorageUrl($url)) {
            return $url;
        }

        if (!$this->isCached($url)) {
            $file = $this->cacheUrl($url);
        } else {
            $file = $this->getCacheFilename($url);
        }

        return $file;
    }

    public function fetchByMediaUrl(string $url): string
    {
        $storage_url = $this->fetchStorageUrlByMediaUrl($url);

        return $this->storage->read($storage_url);
    }

    public function displayByRequest(string $raw_parameter): void
    {
        $parameter = $this->decodeUrl($raw_parameter);

        if (isset($parameter['size'])) {
            $this->displayPreview($parameter['url'], $parameter['size']);
            return;
        }

        try {
            $filename = $this->fetchStorageUrlByMediaUrl($parameter['url']);

            header('Cache-Control: max-age=86400', true);
            header("Expires: " . gmdate("D, d M Y H:i:s", time() + (60 * 60 * 24)) . " GMT", true);
            header('Pragma: cache', true);
            header("Content-Type: " . $this->getContentTypeByFilename($filename));
            echo $this->storage->read($filename);
        } catch (Throwable $e) {
            header("HTTP/1.0 500 Internal Server Error");
            debug::dumpException($e);
        }
    }

    private function getContentTypeByFilename(string $filename): string
    {
        if (str_ends_with($filename, 'pdf')) {
            return 'application/pdf';
        }

        return 'image/jpeg';
    }

    public function displayPreview(string $url, string $size): void
    {
        $cache_file = $this->thumbnail_cache_dir . md5($url . $size) . '.jpg';

        if (!file_exists($cache_file)) {
            $filename = $this->fetchStorageUrlByMediaUrl($url);
            $raw_image = $this->storage->read($filename);

            [$width, $height] = explode('x', $size);

            $image = new GdImage($raw_image);
            $image->resize()->maxHeight($height)->maxWidth($width)->execute();
            $image->save('jpg', $cache_file);
        }

        header('Cache-Control: max-age=86400', true);
        header("Expires: " . gmdate("D, d M Y H:i:s", time() + (60 * 60 * 24)) . " GMT", true);
        header('Pragma: cache', true);
        header("Content-Type: image/jpeg");
        readfile($cache_file);
    }

    public function cacheUrl(string $url): string
    {
        $filename = $this->getCacheFilename($url);

        $content = MarketViewMediaCollector::fetchExternalUrlAsBlob($url);

        $this->storage->put($filename, $content);

        return $filename;
    }

    public function isCached(string $url): bool
    {
        if (!$this->isStorageUrl($url)) {
            return false;
        }

        return $this->storage->has($this->getCacheFilename($url));
    }

    /**
     * @param $url
     * @return string
     */
    public function getCacheFilename(string $url): string
    {
        $file = $this->convertUrlToFilename($url);

        return 'marketview://cache/' . $file[0] . '/' . $file;
    }

    public function convertUrlToFilename(string $url): string
    {
        return md5($url);
    }
}
