<?php

namespace wws\MarketView;

use bqp\db\db_generic;

/**
 * Class market_view_text_creator
 *
 * sucht für eine $product_id texte in market_view und gibt diese als market_view_text_creator zurück
 * @package wws\market_view
 */
class MarketViewTextCreator
{
    private db_generic $db_mv;
    private MarketViewRepository $repository;
    private MarketViewProductCreator $product_creator;

    public function __construct(db_generic $db_mv, MarketViewRepository $repository, MarketViewProductCreator $product_creator)
    {
        $this->db_mv = $db_mv;
        $this->repository = $repository;

        $this->product_creator = $product_creator;
    }

    public function getTextForProductId(int $product_id): MarketViewTextCreatorResult
    {
        $daten = $this->db_mv->singleQuery("
            SELECT
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id
            FROM
                market_view_product INNER JOIN
                market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_product.product_id = " . $product_id . " AND
                market_view_source.text_data_source_id IS NOT NULL AND
                (
                    market_view_product.beschreibung != '' OR
                    market_view_product.features_struct != ''
                    
                )
            ORDER BY
                market_view_source.source_std_priority DESC
            LIMIT
                1
        ");

        $result = new MarketViewTextCreatorResult();

        if (!$daten) {
            return $result;
        }

        $mv_product = $this->repository->getMarketViewProduct($daten['mvsrc_id'], $daten['mvsrc_product_id']);

        $product = $this->product_creator->getMvsrcProductAsProduct($mv_product);

        $result->setProductName($product->getProductName());
        $result->setDescription($product->getBeschreibung());
        $result->setDescriptionDataSourceId($product->getBeschreibungQuelle());

        $this->db_mv->getDoctrine()->clear();

        return $result;
    }
}
