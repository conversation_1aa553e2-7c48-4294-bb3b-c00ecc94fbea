<?php

namespace wws\MarketView;

class MarketViewConst
{
    public const PRICE_TYP_NONE = 'none'; //Verfügbarkeit wird syncronsiert, wenn im Produkt bereits ein Angebot existiert. Neue Angebote werden nicht automatisch hinzugefügt.
    public const PRICE_TYP_EK_LISTE = 'ek_liste';
    public const PRICE_TYP_RNP = 'rnp';
    public const PRICE_TYP_SNP = 'snp';
    public const PRICE_TYP_MANUAL = 'manual'; //fügt auch neue Angebote hinzu, neue Angebote werden auf 0€ und ohne Bezug gesetzt. Preis werden bei bestehenden Angeboten nicht geändert.

    public const AVAILABILITY_ID_0 = 0;
    public const AVAILABILITY_ID_MORE_THEN_FIVE = 10;
    public const AVAILABILITY_ID_LESS_THEN_FIVE = 20;
    public const AVAILABILITY_ID_ZULAUF = 30;
    public const AVAILABILITY_ID_5_DAYS = 31;
    public const AVAILABILITY_ID_10_DAYS = 32;
    public const AVAILABILITY_ID_15_DAYS = 33;
    public const AVAILABILITY_ID_MORE_THEN_15_DAYS = 34;
    public const AVAILABILITY_ID_KEIN_BETAND = 40;
    public const AVAILABILITY_ID_45 = 45;
    public const AVAILABILITY_ID_NICHT_RELEVANT = 50;
    public const AVAILABILITY_ID_KEIN_BEZUG = 69;
    public const AVAILABILITY_ID_65 = 65;
    public const AVAILABILITY_ID_OFFLINE = 70;
    public const AVAILABILITY_ID_UNKNOWN = 60;
    public const AVAILABILITY_ID_AUF_BESTELLUNG = 45;

    public const METAL_SURCHARGE_STATE_UNKNOWN = 'unknown'; //unbekannt -> sollte nicht vorkommen. Das muss zu jedem Import bekannt sein. Nur für alte Angebotsdaten.
    public const METAL_SURCHARGE_STATE_INCLUDED_UNKNOWN = 'included_unknown'; //Preis enthält ggf. Metallzuschlag. Es ist aber nicht bekannt, ob für dieses Angebot ein Metallzuschlag anfällt.
    public const METAL_SURCHARGE_STATE_NO = 'no'; //Angebot unterliegt keinen Metallzuschlag
    public const METAL_SURCHARGE_STATE_INCLUDED = 'included'; //Angebot unterliegt einen Metallzuschlag, dieser ist im Preis enthalten
    public const METAL_SURCHARGE_STATE_EXCLUDED_UNKNOWN = 'excluded_unknown'; //Angebot unterliegt einen Metallzuschlag, ist uns aber unbekannt als € und nicht im Preis enthalt. (das wird als ungültiger EK behandelt)

    public const TREE_ID_DEFAULT = 1;

    public const MVSRC_ID_SONEPAR_SUED = 56;
    public const MVSRC_ID_SONEPAR_SUED_LANGWEID = 81;
    public const MVSRC_ID_DEG_GARBSEN = 22;
    public const MVSRC_ID_SONEPAR_CONTENT = 85;
    public const MVSRC_ID_ETS = 77;
    public const MVSRC_ID_SONEPAR_NORD_FULFILLMENT = 93;
    public const MVSRC_ID_SONEPAR_NORD_ONLY_WAREHOUSE = 134;

    public const MVSRC_ID_GAUTZSCH_MUENSTER = 80;
    public const MVSRC_ID_GAUTZSCH_PLEISSA = 10;
    public const MVSRC_ID_GAUTZSCH_PLEISSA_FULFILLMENT = 113;
    public const MVSRC_ID_GAUTZSCH_PLEISSA_BAU_FULFILLMENT = 127;
    public const MVSRC_ID_AMAZON = 110;
    public const MVSRC_ID_EP = 1;
    public const MVSRC_ID_EP_NUR_LAGER = 101;
    public const MVSRC_ID_EP_FULFILLMENT = 125;

    public const MVSRC_ID_FRANZ_KERSTIN = 28;

    public const MVSRC_ID_NEFF = 76;
    public const MVSRC_ID_BOSCH = 20;
    public const MVSRC_ID_SIEMENS = 31;
    public const MVSRC_ID_CONSTRUCTA = 72;
    public const MVSRC_ID_BEKO_BLOMBERG = 40;
    public const MVSRC_ID_WESCO = 34;
    public const MVSRC_ID_AMICA = 114;
    public const MVSRC_ID_ELECTROLUX_SPAREPARTS = 97;

    public const MVSRC_ID_EPREL = 115;
    public const MVSRC_ID_ENVKV = 116;
    public const MVSRC_ID_STADELBAUER = 14;
    public const MVSRC_ID_SENNHEISER = 12;
    public const MVSRC_ID_4 = 4;
    public const MVSRC_ID_36 = 36;
    public const MVSRC_ID_SILIT = 42;
    public const MVSRC_ID_HERSTELLER_BILDER = 79;
    public const MVSRC_ID_REXEL = 25;
    public const MVSRC_ID_FEGA = 100;
    public const MVSRC_ID_LORCH = 118;
    public const MVSRC_ID_BROEMMELHAUPT = 120;
    public const MVSRC_ID_ELEKTRO_WANDELT = 122;
    public const MVSRC_ID_ZAJADACZ = 121;
    public const MVSRC_ID_DGH = 2;
    public const MVSRC_ID_COMPUTERUNIVERSE = 109;
    public const MVSRC_ID_MAXCOM = 55;
    public const MVSRC_ID_MANOLYA = 16;
    public const MVSRC_ID_EBERHARD = 124;
    public const MVSRC_ID_KREMPL = 86;
    public const MVSRC_ID_BUERKLE = 131;
    public const MVSRC_ID_WMF = 78;
    public const MVSRC_ID_K11 = 136;

    public const MVSRC_ID_GORENJE = 17;

    public const MVSRC_ID_BACKUP = 9999;

    //k11
    public const MVSRC_ID_EURAS = 1021;
    public const MVSRC_BSH_SPARPARTS = 95;

    //ecom
    public const ECOM_MVSRC_ID_EURAS = 133;
    public const ECOM_MVSRC_ID_K11 = 136;

    public const MVSRC_TYPE_SUPLLIER = 'grossist';
    public const MVSRC_TYPE_SELF = 'self';
}
