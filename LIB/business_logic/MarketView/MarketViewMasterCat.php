<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Model\SmartDataObj;

class MarketViewMasterCat
{
    protected SmartDataObj $daten;

    public function __construct()
    {
        $this->daten = new SmartDataObj($this);
    }

    /**
     * @param int $mv_master_cat_id
     * @param db_generic $db_mv
     */
    public function load(int $mv_master_cat_id, db_generic $db_mv)
    {
        $daten = $db_mv->singleQuery("
            SELECT
                market_view_master_cat.mv_master_cat_id,
                market_view_master_cat.parent_mv_master_cat_id,
                market_view_master_cat.cat_name
            FROM
                market_view_master_cat
            WHERE
                market_view_master_cat.mv_master_cat_id = '" . $mv_master_cat_id . "'
        ");

        $this->daten->loadDaten($daten);
    }

    /**
     * @param db_generic $db
     * @return int $mv_master_cat_id
     */
    public function save(db_generic $db)
    {
        if ($this->daten->getObjStatus() == SmartDataObj::STATUS_LOADED) {
            return $this->getMvMasterCatId();
        }

        $sql = [];

        foreach ($this->daten->getChanges() as $key => $value) {
            $sql[] = "market_view_master_cat.$key = '" . $db->escape($value) . "'";
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                    UPDATE
                        market_view_master_cat
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        market_view_master_cat.mv_master_cat_id = '" . $this->getMvMasterCatId() . "'
                ");

                break;
            case SmartDataObj::STATUS_NEW:
                $db->query("
                    INSERT INTO
                        market_view_master_cat
                    SET
                        " . implode(',', $sql) . "
                ");

                $this->daten->setterDirect('mv_master_cat_id', $db->insert_id());
                break;
            case SmartDataObj::STATUS_DEL:
                $db->query("
                    DELETE FROM
                        market_view_master_cat
                    WHERE
                        market_view_master_cat.mv_master_cat_id = '" . $this->getMvMasterCatId() . "'
                ");
                break;
        }

        $this->daten->setSaved();

        return $this->getMvMasterCatId();
    }

    public function delete()
    {
        $this->daten->setObjStatus(SmartDataObj::STATUS_DEL);
    }

    public function canDelete()
    {
        return false;
    }

    public function setCatName($cat_name)
    {
        return $this->daten->setter('cat_name', $cat_name);
    }

    public function getCatName()
    {
        return $this->daten->getter('cat_name');
    }

    public function setParentMvMasterCatId($parent_mv_master_cat_id)
    {
        return $this->daten->setter('parent_mv_master_cat_id', $parent_mv_master_cat_id);
    }

    public function getMvMasterCatId()
    {
        return $this->daten['mv_master_cat_id'];
    }
}
