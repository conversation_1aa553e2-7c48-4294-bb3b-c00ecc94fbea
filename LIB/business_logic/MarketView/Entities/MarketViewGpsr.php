<?php

namespace wws\MarketView\Entities;

use bqp\Date\DateObj;
use wws\MarketView\Import\MarketViewImportGpsr;

class MarketViewGpsr extends MarketViewImportGpsr
{
    //lala... die MarketViewImportGpsr reicht hier erstmal um die daten von a nach b zu bringen

    private int $mv_gpsr_id;
    private string $date_added;

    public function __construct(?array $data = null)
    {
        if ($data) {
            foreach ($data as $key => $value) {
                if (property_exists($this, $key)) {
                    $this->$key = $value;
                }
            }
        }
    }

    public function getMvGpsrId(): int
    {
        return $this->mv_gpsr_id;
    }

    public function getDateAdded(): DateObj
    {
        return new DateObj($this->date_added);
    }
}
