<?php

namespace wws\MarketView\Entities;

use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataObj;

class MarketViewMasterProduct implements ProvideSmartDataObj
{
    private SmartDataObj $data;

    public function __construct()
    {
        $this->data = new SmartDataObj($this);
    }

    /**
     * @param string $ean
     * @return boolean
     */
    public function setEan(string $ean): bool
    {
        return $this->data->setter('ean', $ean);
    }

    /**
     * @param string $mpn
     * @return boolean
     */
    public function setMpn(string $mpn): bool
    {
        return $this->data->setter('mpn', $mpn);
    }

    /**
     * @param int $mv_hersteller_id
     * @return bool
     */
    public function setMvHerstellerId(int $mv_hersteller_id): bool
    {
        return $this->data->setter('mv_hersteller_id', $mv_hersteller_id);
    }

    /**
     * @param int $mv_master_cat_id
     * @return bool
     */
    public function setMvMasterCatId(int $mv_master_cat_id): bool
    {
        return $this->data->setter('mv_master_cat_id', $mv_master_cat_id);
    }

    /**
     * @param int $mv_master_product_id
     * @return bool
     */
    public function setMvMasterProductId(int $mv_master_product_id): bool
    {
        return $this->data->setter('mv_master_product_id', $mv_master_product_id);
    }

    /**
     * @param int $product_id
     * @return bool
     */
    public function setProductId(int $product_id): bool
    {
        return $this->data->setter('product_id', $product_id);
    }

    /**
     * @param string $product_name
     * @return boolean
     */
    public function setProductName(string $product_name): bool
    {
        return $this->data->setter('product_name', $product_name);
    }

    /**
     * @param $product_name_src
     * @return boolean
     */
    public function setProductNameSrc($product_name_src): bool
    {
        return $this->data->setter('product_name_src', $product_name_src);
    }

    /**
     * @param string $product_type
     * @return boolean
     */
    public function setProductType(string $product_type): bool
    {
        return $this->data->setter('product_type', $product_type);
    }

    /**
     * @param $sources_available
     * @return boolean
     */
    public function setSourcesAvailable($sources_available): bool
    {
        return $this->data->setter('sources_available', $sources_available);
    }

    /**
     * @param $sources_online
     * @return boolean
     */
    public function setSourcesOnline($sources_online): bool
    {
        return $this->data->setter('sources_online', $sources_online);
    }


    /**
     * @return string ean
     */
    public function getEan(): string
    {
        return $this->data->getter('ean');
    }

    /**
     * @return string mpn
     */
    public function getMpn(): string
    {
        return $this->data->getter('mpn');
    }

    /**
     * @return int mv_hersteller_id
     */
    public function getMvHerstellerId(): int
    {
        return (int)$this->data->getter('mv_hersteller_id');
    }

    /**
     * @return int mv_master_cat_id
     */
    public function getMvMasterCatId(): int
    {
        return (int)$this->data->getter('mv_master_cat_id');
    }

    /**
     * @return int mv_master_product_id
     */
    public function getMvMasterProductId(): int
    {
        return (int)$this->data->getter('mv_master_product_id');
    }

    /**
     * @return int product_id
     */
    public function getProductId(): int
    {
        return (int)$this->data->getter('product_id');
    }

    /**
     * @return string product_name
     */
    public function getProductName(): string
    {
        return $this->data->getter('product_name');
    }

    /**
     * @return string product_name_src
     */
    public function getProductNameSrc(): string
    {
        return $this->data->getter('product_name_src');
    }

    /**
     * @return string product_type
     */
    public function getProductType(): string
    {
        return $this->data->getter('product_type');
    }

    /**
     * @return string sources_available
     */
    public function getSourcesAvailable(): string
    {
        return $this->data->getter('sources_available');
    }

    /**
     * @return string sources_online
     */
    public function getSourcesOnline(): string
    {
        return $this->data->getter('sources_online');
    }

    public function getSmartDataObj(): SmartDataObj
    {
        return $this->data;
    }
}
