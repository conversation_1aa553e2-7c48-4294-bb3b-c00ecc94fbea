<?php

namespace wws\MarketView\Entities;

use bqp\String\Base62;

class MarketViewDevice
{
    public const DEVICE_STATUS_ONLINE = 'online';
    public const DEVICE_STATUS_OFFLINE = 'offline';

    private string $mvsrc_device_id;
    private $mv_hersteller_id = null;
    private ?int $mv_cat_id = null;
    private ?int $mv_cat_id_2 = null;
    private $model_name;
    private $code1 = '';
    private $code2 = '';
    private $code3 = '';
    private $code4 = '';
    private $code5 = '';
    private $url = '';

    /**
     * @var MarketViewDeviceMedia[]
     */
    private array $market_view_device_media = [];

    private int $device_id = 0;

    /**
     * @return string
     */
    public function getMvsrcDeviceId(): string
    {
        return $this->mvsrc_device_id;
    }

    /**
     * @param string $mvsrc_device_id
     */
    public function setMvsrcDeviceId(string $mvsrc_device_id): void
    {
        $this->mvsrc_device_id = $mvsrc_device_id;
    }


    public function autoCreateMvsrcDeviceId(string $brand_name, array $use_codes = [1, 2, 3, 4, 5]): string
    {
        $chars = $brand_name . $this->model_name;
        foreach ($use_codes as $id) {
            $code = 'code' . $id;
            $chars .= $this->$code;
        }

        $hash = md5($chars, true);
        $hash = Base62::encode($hash);

        $this->setMvsrcDeviceId($hash);

        return $hash;
    }

    /**
     * @return int
     */
    public function getMvHerstellerId(): int
    {
        return $this->mv_hersteller_id;
    }

    /**
     * @param int $mv_hersteller_id
     */
    public function setMvHerstellerId(int $mv_hersteller_id): void
    {
        $this->mv_hersteller_id = $mv_hersteller_id;
    }

    /**
     * @return null|int
     */
    public function getMvCatId(): ?int
    {
        return $this->mv_cat_id;
    }

    /**
     * @param int $mv_cat_id
     */
    public function setMvCatId(int $mv_cat_id): void
    {
        $this->mv_cat_id = $mv_cat_id;
    }

    /**
     * @return null|int
     */
    public function getMvCatId2(): ?int
    {
        return $this->mv_cat_id_2;
    }

    /**
     * @param int $mv_cat_id_2
     */
    public function setMvCatId2(int $mv_cat_id_2): void
    {
        $this->mv_cat_id_2 = $mv_cat_id_2;
    }


    /**
     * @return string
     */
    public function getModelName(): string
    {
        return $this->model_name;
    }

    /**
     * @param string $model_name
     */
    public function setModelName(string $model_name): void
    {
        $this->model_name = $model_name;
    }

    /**
     * @return string
     */
    public function getCode1(): string
    {
        return $this->code1;
    }

    /**
     * @param string $code1
     */
    public function setCode1(string $code1): void
    {
        $this->code1 = $code1;
    }

    /**
     * @return string
     */
    public function getCode2(): string
    {
        return $this->code2;
    }

    /**
     * @param string $code2
     */
    public function setCode2(string $code2): void
    {
        $this->code2 = $code2;
    }

    /**
     * @return string
     */
    public function getCode3(): string
    {
        return $this->code3;
    }

    /**
     * @param string $code3
     */
    public function setCode3(string $code3): void
    {
        $this->code3 = $code3;
    }

    /**
     * @return string
     */
    public function getCode4(): string
    {
        return $this->code4;
    }

    /**
     * @param string $code4
     */
    public function setCode4(string $code4): void
    {
        $this->code4 = $code4;
    }

    /**
     * @return string
     */
    public function getCode5(): string
    {
        return $this->code5;
    }

    /**
     * @param string $code5
     */
    public function setCode5(string $code5): void
    {
        $this->code5 = $code5;
    }

    /**
     * @return string
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @param string $url
     */
    public function setUrl(string $url): void
    {
        $this->url = $url;
    }

    public function addMedia(MarketViewDeviceMedia $media): void
    {
        $this->market_view_device_media[] = $media;
    }

    /**
     * @return MarketViewDeviceMedia[]
     */
    public function getMedia(): array
    {
        return $this->market_view_device_media;
    }

    public function getDeviceId(): int
    {
        return $this->device_id;
    }

    public function setDeviceId(int $device_id): void
    {
        $this->device_id = $device_id;
    }
}
