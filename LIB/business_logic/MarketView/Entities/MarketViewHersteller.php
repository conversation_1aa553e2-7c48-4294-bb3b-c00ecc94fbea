<?php

namespace wws\MarketView\Entities;

class MarketViewHersteller
{
    protected $mv_hersteller_id;
    protected $hersteller_name;
    protected $mapping_mv_hersteller_id;
    protected $brand_id = 0;

    public function getMvHerstellerId()
    {
        return $this->mv_hersteller_id;
    }

    public function getHerstellerName()
    {
        return $this->hersteller_name;
    }

    /**
     * product_brand.brand_id
     * @return mixed
     */
    public function getBrandId()
    {
        return $this->brand_id;
    }
}
