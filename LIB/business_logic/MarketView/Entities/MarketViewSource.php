<?php

namespace wws\MarketView\Entities;

use ReflectionMethod;
use service_loader;
use wws\MarketView\MarketViewRepository;
use wws\Product\Product;

class MarketViewSource
{
    protected $mvsrc_id;
    protected $mvsrc_name;
    protected $typ;
    protected $status;
    protected $supplier_id;
    protected $price_typ;
    protected $inventory_typ;
    protected $source_product_validity;
    protected $source_availability_validity;
    protected $product_creator;
    protected $image_data_source_id;
    protected $text_data_source_id;
    protected $ean_edit;
    protected $use_devices;
    private int $matching_ean;
    private int $matching_mpn;
    private string $matching_extra_class = '';

    public function getMvsrcId()
    {
        return $this->mvsrc_id;
    }

    public function getMvsrcName()
    {
        return $this->mvsrc_name;
    }

    public function getTyp()
    {
        return $this->typ;
    }

    public function getStatus()
    {
        return $this->status;
    }

    /**
     * gibt die Lieferanten Id ($supplier_id) zurück
     *
     * @return int
     */
    public function getSupplierId(): int
    {
        return $this->supplier_id;
    }


    public function getProductCreator()
    {
        return $this->product_creator;
    }

    public function getMvsrcProductAsProduct(MarketViewProduct $market_view_product, Product $product, MarketViewRepository $mv_repository)
    {
        if ($this->getProductCreator()) {
            $temp = explode('::', $this->getProductCreator());

            $MethodChecker = new ReflectionMethod($temp[0], $temp[1]);
            if ($MethodChecker->isStatic()) {
                $product = call_user_func($temp, $market_view_product, $product, $mv_repository);
            } else {
                $class = service_loader::getDiContainer()->get($temp[0]);

                $product = call_user_func([$class, $temp[1]], $market_view_product, $product, $mv_repository);
            }
        }

        return $product;
    }

    public function getImageDataSourceId()
    {
        return $this->image_data_source_id;
    }

    public function getTextDataSourceId()
    {
        return $this->text_data_source_id;
    }

    /**
     * gibt zurück, ob für die Datenquelle die EANs per Hand bearbeitet werden dürfen
     */
    public function canEanEdit(): bool
    {
        return (bool)$this->ean_edit;
    }

    public function isUseDevices(): bool
    {
        return (bool)$this->use_devices;
    }

    public function isMatchingEan(): bool
    {
        return (bool)$this->matching_ean;
    }

    public function isMatchingMpn(): bool
    {
        return (bool)$this->matching_mpn;
    }

    public function setMatchingMpn(bool $matching_mpn): void
    {
        $this->matching_mpn = $matching_mpn ? 0 : 1;
    }

    public function getMatchingExtraClass(): string
    {
        return $this->matching_extra_class;
    }

    public function setMatchingExtraClass(string $matching_extra_class): void
    {
        $this->matching_extra_class = $matching_extra_class;
    }
}
