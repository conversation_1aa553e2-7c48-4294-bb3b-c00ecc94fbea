<?php

namespace wws\MarketView\Entities;

use bqp\Json;
use bqp\Utils\StringUtils;

/**
 * Model um unstrukturierte Daten mit in market_view_product zu speichern
 */
class MarketViewSonstiges
{
    protected $data = [];

    /**
     * @param ?string $serialized_data json encoded
     * @return MarketViewSonstiges
     */
    public static function loadSerialized(?string $serialized_data): MarketViewSonstiges
    {
        $sonstiges = new self();

        if ($serialized_data) {
            $t = Json::decode($serialized_data);
            $sonstiges->setDataRaw($t);
        }

        return $sonstiges;
    }


    protected function setDataRaw($data)
    {
        $this->data = $data;
    }

    public function serialize()
    {
        return json_encode($this->data);
    }

    /**
     * fügt ein Eintrag hinzu
     * @param $key
     * @param $value
     */
    public function add($key, $value)
    {
        $this->data[$key] = $value;
    }

    /**
     * fügt ein Eintrag hinzu, solange die $value kein leerer String|null|false ist
     * @param $key
     * @param $value
     */
    public function addAuto($key, $value)
    {
        if ($value === "" || $value === null || $value === false) {
            return;
        }

        $this->data[$key] = $value;
    }

    public function getData()
    {
        return $this->data;
    }

    /**
     * @param $key
     * @return mixed
     */
    public function getValue($key)
    {
        return $this->data[$key] ?? false;
    }

    public function removeKey(string $key): void
    {
        unset($this->data[$key]);
    }

    public function has(string $key): bool
    {
        return array_key_exists($key, $this->data);
    }

    public function hasValues(): bool
    {
        return (bool)$this->data;
    }

    public function asHtml()
    {
        $result = '';
        foreach ($this->data as $key => $value) {


            $result .= '<b>' . $key . ':</b> ';

            if (is_bool($value)) {
                $result .= $value ? 'ja <i>(true)</i>' : 'nein <i>(false)</i>';
            } else {
                $result .= $value;
            }

            $result .= '<br>';
        }

        return $result;
    }

    public function extractFromArray(array $daten, bool $skip_empty = false): void
    {
        foreach ($daten as $key => $value) {
            if (!StringUtils::begins($key, 'sonstiges')) {
                continue;
            }
            if ($skip_empty && $value === "") {
                continue;
            }

            $key = substr($key, 10);
            $this->add($key, $value);
        }
    }
}
