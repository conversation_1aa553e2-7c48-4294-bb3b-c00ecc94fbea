<?php

namespace wws\MarketView\Entities;

use bqp\Utils\StringUtils;

class MarketViewProduct
{
    protected $mvsrc_id;
    protected $mvsrc_product_id;
    protected $mvsrc_product_id_alt;
    protected $date_added;
    protected $date_updated;
    protected $date_updated_availability_id;
    protected $date_updated_vk_netto;
    protected $update_flag;
    protected $product_status;
    protected $product_name;
    protected $product_line;
    protected $rank;
    protected $mv_cat_id;
    protected $mv_cat_id_2;
    protected $mv_hersteller_id;
    protected $mv_gpsr_id;
    protected $mv_gpsr_id_2;
    protected $vk_netto;
    protected $vk_netto_per_quantity;
    protected $vk_netto_max;
    protected $vk_netto_info;
    protected $vk_list;
    /**
     * @var string
     */
    protected $metal_surcharge_state;
    protected $versand_netto;
    protected $versand_classification;
    protected $dropshipping;
    protected $mv_vat_rate;
    protected $uvp;
    protected $evp;
    protected $grundpreis_einheit;
    protected $grundpreis_menge;
    protected $grundpreis_faktor;
    protected $mv_availability;
    protected $availability_id;
    protected $inventory;
    protected $vpe;
    protected $vpe_zwang;
    protected $mpn;
    protected $model_name;
    protected $ean;
    protected $color;
    protected $beschreibung;
    protected $beschreibung_2;
    protected $lieferumfang;
    protected $testergebnis;
    protected $features;
    protected $features_struct;
    protected $sonstiges;
    protected $sonstiges_struct;
    protected $keywords;
    protected $safety_information;
    protected $breite;
    protected $hoehe;
    protected $tiefe;
    protected $gewicht;
    protected $url;
    protected $eol;
    protected $genuine_part;
    protected $mv_master_product_id;
    protected $zolltarifnummer;

    protected $market_view_source;
    protected $market_view_cat;
    protected $market_view_cat_2;
    protected $market_view_hersteller;
    protected $market_view_availability;

    public function getMvsrcId()
    {
        return $this->mvsrc_id;
    }

    public function getMvsrcProductId()
    {
        return $this->mvsrc_product_id;
    }

    public function getMvsrcProductIdAlt()
    {
        return $this->mvsrc_product_id_alt;
    }

    public function getDateAdded()
    {
        return $this->date_added;
    }

    public function getDateUpdated()
    {
        return $this->date_updated;
    }

    public function getDateUpdatedAvailabilityId()
    {
        return $this->date_updated_availability_id;
    }

    public function getDateUpdatedVkNetto()
    {
        return $this->date_updated_vk_netto;
    }

    public function getProductStatus()
    {
        return $this->product_status;
    }

    public function getProductName()
    {
        return $this->product_name;
    }

    public function getProductLine()
    {
        return $this->product_line;
    }

    public function getRank()
    {
        return $this->rank;
    }

    public function getMvCatId()
    {
        return $this->mv_cat_id;
    }

    public function getMvCatId2()
    {
        return $this->mv_cat_id_2;
    }

    public function getMvHerstellerId()
    {
        return $this->mv_hersteller_id;
    }

    public function getVkNetto()
    {
        return $this->vk_netto;
    }

    public function isVkNettoPerQuantity(): bool
    {
        return $this->vk_netto_per_quantity !== null;
    }

    public function getVkNettoPerQuantity(): ?int
    {
        return $this->vk_netto_per_quantity;
    }


    public function getVkNettoMax()
    {
        return $this->vk_netto_max;
    }

    public function getVkNettoInfo()
    {
        return $this->vk_netto_info;
    }

    public function getVkList(): ?float
    {
        return $this->vk_list;
    }

    public function getVersandNetto()
    {
        return $this->versand_netto;
    }

    public function getVersandClassification(): ?string
    {
        return $this->versand_classification;
    }

    public function getDropshipping()
    {
        return $this->dropshipping;
    }

    public function getMvVatRate(): string
    {
        return $this->mv_vat_rate;
    }

    public function getUvp()
    {
        return $this->uvp;
    }

    public function getEvp()
    {
        return $this->evp;
    }

    public function getMvAvailability()
    {
        return $this->mv_availability;
    }

    public function getAvailabilityId()
    {
        return $this->availability_id;
    }

    public function getInventory()
    {
        return $this->inventory;
    }

    public function getVpe()
    {
        return $this->vpe;
    }

    public function getMpn()
    {
        return $this->mpn;
    }

    public function getModelName()
    {
        return $this->model_name;
    }


    public function getEan()
    {
        return $this->ean;
    }

    public function getColor()
    {
        return $this->color;
    }

    public function getBeschreibung(): string
    {
        return (string)$this->beschreibung;
    }

    public function getBeschreibungAsHtml(): string
    {
        $beschreibung = $this->getBeschreibung();

        if (!StringUtils::isHtml($beschreibung)) {
            $beschreibung = nl2br($beschreibung);
        }

        return $beschreibung;
    }

    public function getBeschreibungForProductAsHtml(): string
    {
        $beschreibung = trim($this->getBeschreibung());

        if (!StringUtils::isHtml($beschreibung)) {
            $beschreibung = nl2br($beschreibung);
        }

        return $beschreibung;
    }

    public function getBeschreibung2()
    {
        return $this->beschreibung_2;
    }

    public function getLieferumfang()
    {
        return $this->lieferumfang;
    }

    public function getTestergebnis()
    {
        return $this->testergebnis;
    }

    public function getFeatures()
    {
        return $this->features;
    }

    public function getSonstiges()
    {
        return $this->sonstiges;
    }


    public function getKeywords(): string
    {
        return (string)$this->keywords;
    }

    public function getBreite()
    {
        return $this->breite;
    }

    public function getHoehe()
    {
        return $this->hoehe;
    }

    public function getTiefe()
    {
        return $this->tiefe;
    }

    public function getGewicht()
    {
        return $this->gewicht;
    }

    /**
     * @return MarketViewSource
     */
    public function getMvsrc()
    {
        return $this->market_view_source;
    }

    /**
     * @return MarketViewCat
     */
    public function getCat()
    {
        return $this->market_view_cat;
    }

    /**
     * @return MarketViewCat
     */
    public function getCat2()
    {
        return $this->market_view_cat_2;
    }

    /**
     * @return MarketViewHersteller
     */
    public function getHersteller()
    {
        return $this->market_view_hersteller;
    }

    public function getAvailability()
    {
        return $this->market_view_availability;
    }

    public function getUrl()
    {
        return $this->url;
    }

    public function getEol()
    {
        return $this->eol;
    }

    /**
     * @return int|null
     */
    public function getVpeZwang()
    {
        return $this->vpe_zwang;
    }

    public function getGrundpreisEinheit()
    {
        return $this->grundpreis_einheit;
    }

    public function getGrundpreisMenge()
    {
        return $this->grundpreis_menge;
    }

    public function getGrundpreisFaktor()
    {
        return $this->grundpreis_faktor;
    }

    public function getMvFeatures(): MarketViewFeatures
    {
        return MarketViewFeatures::loadSerialized($this->features_struct);
    }

    public function getSonstigesStruct(): MarketViewSonstiges
    {
        return MarketViewSonstiges::loadSerialized($this->sonstiges_struct);
    }

    public function isGenuinePartSet(): bool
    {
        return $this->genuine_part !== null;
    }

    public function isGenuinePart(): bool
    {
        return (bool)$this->genuine_part;
    }

    public function getZolltarifnummer()
    {
        return $this->zolltarifnummer;
    }

    public function getMvMasterProductId(): int
    {
        return $this->mv_master_product_id;
    }

    /**
     * @return string
     */
    public function getMetalSurchargeState(): string
    {
        return $this->metal_surcharge_state;
    }

    public function getMvGpsrId(): int
    {
        return (int)$this->mv_gpsr_id;
    }

    public function setMvGpsrId(int $mv_gpsr_id): void
    {
        $this->mv_gpsr_id = $mv_gpsr_id;
    }

    public function getMvGpsrId2(): int
    {
        return (int)$this->mv_gpsr_id_2;
    }

    public function setMvGpsrId2(int $mv_gpsr_id): void
    {
        $this->mv_gpsr_id_2 = $mv_gpsr_id;
    }

    public function getSafetyInformation(): ?string
    {
        return $this->safety_information;
    }

    public function setSafetyInformation(?string $safety_information): void
    {
        $this->safety_information = $safety_information;
    }
}
