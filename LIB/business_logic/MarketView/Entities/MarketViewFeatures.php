<?php

namespace wws\MarketView\Entities;

class MarketViewFeatures
{
    private array $features = [];

    /**
     * @param string|null $serialized_features
     * @return MarketViewFeatures
     */
    public static function loadSerialized(?string $serialized_features): MarketViewFeatures
    {
        $features = new self();

        if ($serialized_features) {
            $values = unserialize($serialized_features, ['allowed_classes' => false]);
            $features->setFeaturesRaw($values);
        }

        return $features;
    }

    private function setFeaturesRaw(array $features): void
    {
        $this->features = $features;
    }

    public function asHtml(): string
    {
        $result = '<table>';

        $result .= '<tr>';
        $result .= '<th>Id</th>';
        $result .= '<th>Name</th>';
        $result .= '<th>Prefix</th>';
        $result .= '<th>Wert</th>';
        $result .= '<th>Suffix</th>';
        $result .= '<th>Typ</th>';
        $result .= '</tr>';

        foreach ($this->features as $feature) {
            $result .= '<tr>';
            $result .= '<td>' . $feature['id'] . '</td>';
            $result .= '<td>' . ($feature['name'] ?? '') . '</td>';
            $result .= '<td>' . ($feature['prefix'] ?? '') . '</td>';
            $result .= '<td>' . ($feature['value'] ?? '') . '</td>';
            $result .= '<td>' . ($feature['suffix'] ?? '') . '</td>';
            $result .= '<td>' . ($feature['typ'] ?? '') . '</td>';
            $result .= '</tr>';
        }

        $result .= '</table>';

        return $result;
    }

    public function asTextSimple(): string
    {
        $text = '';

        foreach ($this->features as $feature) {
            $name = $feature['name'] ?? $feature['id'];

            if ($name) {
                $text .= $name . ': ';
            }
            $text .= $feature['value'];

            if (!empty($feature['suffix'])) {
                $text .= ' ' . $feature['suffix'];
            }

            $text .= "\n";
        }

        return $text;
    }


    public function asHtmlSimple(): string
    {
        $text = '';

        foreach ($this->features as $feature) {
            $name = $feature['name'] ?? $feature['id'];

            if ($name) {
                $text .= '<b>' . $name . '</b>: ';
            }
            $text .= nl2br(trim($feature['value']));

            if (!empty($feature['suffix'])) {
                $text .= ' ' . $feature['suffix'];
            }

            $text .= "<br>";
        }

        return $text;
    }

    public function serialize(): string
    {
        return serialize($this->features);
    }

    public function isFeatures(): bool
    {
        return count($this->features) > 0;
    }

    public function hasValues(): bool
    {
        return $this->isFeatures();
    }

    public function isFeature($id): bool
    {
        return isset($this->features[$id]);
    }

    public function addFeature($id, $value, $name = null, $suffix = null, $prefix = null, $typ = null): void
    {
        $feature = [];
        $feature['id'] = $id;
        $feature['value'] = $value;
        if ($name !== null) {
            $feature['name'] = $name;
        }
        if ($suffix !== null) {
            $feature['suffix'] = $suffix;
        }
        if ($prefix !== null) {
            $feature['prefix'] = $prefix;
        }
        if ($typ !== null) {
            $feature['typ'] = $typ;
        }

        $this->features[$id] = $feature;
    }

    public function getFeatures(): array
    {
        return $this->features;
    }

    /**
     * @param string $key
     * @return null|array
     */
    public function getFeature(string $key): ?array
    {
        return $this->features[$key] ?? null;
    }

    public function getFeatureValue(string $key)
    {
        $feature = $this->getFeature($key);

        if ($feature) {
            return $feature['value'];
        }

        return null;
    }

    public function removeFeature(string $key): void
    {
        unset($this->features[$key]);
    }
}
