<?php

namespace wws\MarketView\Entities;

use bqp\String\Base62;
use wws\MarketView\Import\MarketViewImportPropertyNotSetException;

/**
 * Achtung: Die Werte der Entität sind nur vollständig, wenn die Entität aus der Datenbank geladen wurden.
 * Aktuell wird diese Entität aber auch beim Import leer initialisiert und nur mit Werten aus dem Import befüllt.
 * Z.B. ein Fingerprint, eine Vorschau, Dateigröße, etc. ist eventuell nicht in der Entität geladen, aber existiert in der Datenbank!
 * Das muss unbedingt bei anpassungen berücksichtigt werden. Entweder muss für den Import ein eigenes Modell genutzt werden,
 * oder im Import Prozess, beim resolven der identität, auch die fehlenden Daten aus der Datenbank nachgeladen werden.
 * Wobei letzteres vom Ressourcen Aspekt, alles andere als optimal ist.
 * Aktuell besteht hier aber noch kein Handlungsbedarf. Die Use Cases zwischen Import der Medien und verarbeiten der Medien sind klar getrennt.
 */
class MarketViewMedia
{
    public const MEDIA_STATUS_ONLINE = 'online';
    public const MEDIA_STATUS_OFFLINE = 'offline';
    public const MEDIA_STATUS_MISSING = 'missing';

    public const MEDIA_TYPE_IMAGE = 'image'; //achtung derzeit werden image und image_url identisch behandelt. konzeptionell bin ich noch am struggeln wie ich das handeln will. image_url wird wahrscheinlich rein passiv werden.
    public const MEDIA_TYPE_IMAGE_URL = 'image_url';
    public const MEDIA_TYPE_PDF = 'pdf';
    public const MEDIA_TYPE_VIDEO = 'video';
    public const MEDIA_TYPE_URL = 'url';
    public const MEDIA_TYPE_OTHER = 'other';


    public const MEDIA_TOPIC_UNKNOWN = '';
    public const MEDIA_TOPIC_ICON = 'icon';
    public const MEDIA_TOPIC_SCHEMA = 'schema';
    public const MEDIA_TOPIC_PRODUCT_VIDEO = 'video';
    public const MEDIA_TOPIC_PRODUCT_IMAGE = 'image';
    public const MEDIA_TOPIC_PRODUCT_DETAIL = 'detail';
    public const MEDIA_TOPIC_ENERGIE_LABEL = 'energlabel';
    public const MEDIA_TOPIC_EU_DATENBLATT = 'eudatblatt';
    public const MEDIA_TOPIC_DATENBLATT = 'datblatt';
    public const MEDIA_TOPIC_MANUAL = 'manual';
    public const MEDIA_TOPIC_ENERGIE_LABEL_2020 = 'energlabel2020';
    public const MEDIA_TOPIC_EU_DATENBLATT_2020 = 'eudatblatt2020';
    public const MEDIA_TOPIC_EU_CONFORMITY = 'eu_conformity';

    private $data = [];

    private $mv_media_id_resolved = false;

    public function __construct()
    {
        $this->data['media_status'] = self::MEDIA_STATUS_ONLINE;
        $this->data['mv_media_id'] = 0;
    }

    /**
     * gibt an, ob für diese Entität die mv_media_id ermittelt wurde
     * @see https://allego.myjetbrains.com/youtrack/issue/WWS-1687/MarketViewMedia-mvmediaid
     *
     * @return bool
     */
    public function isMvMediaIdResolved(): bool
    {
        return $this->data['mv_media_id'] || $this->mv_media_id_resolved;
    }

    /**
     * @param bool $mv_media_id_resolved
     * @return void
     */
    public function setMvMediaIdResolved(bool $mv_media_id_resolved): void
    {
        $this->mv_media_id_resolved = $mv_media_id_resolved;
    }

    public function getMvMediaId(): int
    {
        return $this->data['mv_media_id'];
    }

    public function setMvMediaId(int $mv_media_id): void
    {
        $this->data['mv_media_id'] = $mv_media_id;
    }

    public function setUrl(string $url): void
    {
        $this->data['url'] = $url;

        if (!isset($this->data['media_key'])) {
            $this->data['media_key'] = Base62::encode(md5($this->data['url'], true));
        }
    }

    /**
     * Wenn die Dateien nur als Datei vorliegen, kann das als alias für setUrl() verwendet werden.
     * Es wird ein local:// vorangestellt.
     *
     * @param string $filename
     * @return void
     */
    public function setLocalFilename(string $filename): void
    {
        $this->setUrl('local://' . $filename);
    }

    public function isLocalFilename(): bool
    {
        $url = $this->getUrl();
        return str_starts_with($url, 'local://');
    }

    public function getUrl(): string
    {
        $this->assertSetted('url');
        return $this->data['url'];
    }

    /**
     * bildet mit mvsrc_id, mvsrc_product_id den PK. Wird dieser nicht explizit angegeben, wird die URL verwendet.
     *
     * @param string $media_key
     */
    public function setMediaKey(string $media_key): void
    {
        $this->data['media_key'] = $media_key;
    }

    /**
     * @return string
     */
    public function getMediaKey(): string
    {
        $this->assertSetted('media_key');
        return $this->data['media_key'];
    }

    /**
     * @return string
     */
    public function getMediaType(): string
    {
        $this->assertSetted('media_type');
        return $this->data['media_type'];
    }

    /**
     * @param string $media_type
     */
    public function setMediaType(string $media_type): void
    {
        $this->data['media_type'] = $media_type;
    }

    /**
     * @return string
     */
    public function getTopic(): string
    {
        $this->assertSetted('topic');
        return $this->data['topic'];
    }

    /**
     * @param string $topic
     */
    public function setTopic(string $topic): void
    {
        $this->data['topic'] = $topic;
    }


    /**
     * explizite Sortierung
     * @return int
     */
    public function getPos(): ?int
    {
        $this->assertSetted('pos');
        return $this->data['pos'];
    }

    /**
     * @param int|null $pos
     */
    public function setPos(?int $pos): void
    {
        $this->data['pos'] = $pos;
    }

    /**
     * @param string $md5
     */
    public function setMd5(string $md5): void
    {
        $this->data['md5'] = $md5;
    }

    /**
     * @return string
     */
    public function getMd5(): string
    {
        $this->assertSetted('md5');
        return $this->data['md5'];
    }

    public function isMd5Set(): bool
    {
        return !empty($this->data['md5']);
    }

    /**
     * @return string
     */
    public function getFingerprint(): ?string
    {
        $this->assertSetted('fingerprint');
        return $this->data['fingerprint'];
    }

    /**
     * @param string $fingerprint
     */
    public function setFingerprint(string $fingerprint): void
    {
        $this->data['fingerprint'] = $fingerprint;
    }

    /**
     * @return int|null
     */
    public function getWidth(): ?int
    {
        $this->assertSetted('width');
        return $this->data['width'];
    }

    /**
     * @param int|null $width
     */
    public function setWidth(?int $width): void
    {
        $this->data['width'] = $width;
    }

    /**
     * @return int|null
     */
    public function getHeight(): ?int
    {
        $this->assertSetted('height');
        return $this->data['height'];
    }

    /**
     * @param int|null $height
     */
    public function setHeight(?int $height): void
    {
        $this->data['height'] = $height;
    }

    public function setDescription(string $description): void
    {
        $this->data['description'] = $description;
    }

    public function getDescription(): string
    {
        $this->assertSetted('description');
        return $this->data['description'];
    }

    public function setMediaStatus(?string $media_status): void
    {
        if ($media_status === null) {
            unset($this->data['media_status']);
        } else {
            $this->data['media_status'] = $media_status;
        }
    }

    public function getMediaStatus(): string
    {
        $this->assertSetted('media_status');
        return $this->data['media_status'];
    }

    public function getMvsrcId(): int
    {
        $this->assertSetted('mvsrc_id');
        return $this->data['mvsrc_id'];
    }

    public function getMvsrcProductId(): string
    {
        $this->assertSetted('mvsrc_product_id');
        return $this->data['mvsrc_product_id'];
    }

    public function getUrlStorage(): string
    {
        $this->assertSetted('url_storage');
        return $this->data['url_storage'];
    }

    public function setUrlStorage(string $url_storage): void
    {
        $this->data['url_storage'] = $url_storage;
    }

    public function _getRawData(): array
    {
        return $this->data;
    }

    public function fromArray(array $data): void
    {
        $this->data = array_merge($this->data, $data);
    }

    public function setMvsrcId(int $mvsrc_id): void
    {
        $this->data['mvsrc_id'] = $mvsrc_id;
    }

    public function setMvsrcProductId(string $mvsrc_product_id): void
    {
        $this->data['mvsrc_product_id'] = $mvsrc_product_id;
    }


    private function assertSetted(string $key): void
    {
        if (!array_key_exists($key, $this->data)) {
            throw new MarketViewImportPropertyNotSetException('Key ' . $key . ' is not setted');
        }
    }

    public function clearMetaData(): void
    {
        $this->setFingerprint('');
        $this->setMd5('');
        $this->setWidth(null);
        $this->setHeight(null);
    }
}
