<?php

namespace wws\MarketView\Entities;

use bqp\Json;

/**
 * Datenstruktur um Beziehung zwischen market_view_products abzubilden
 */
class MarketViewProductRelation
{
    public const TYPE_REPLACED_BY = 'replaced';
    public const TYPE_REPLACE = 'replace';
    public const TYPE_UNKNOWN = 'unknown';
    public const TYPE_PARENT = 'parent';
    public const TYPE_CHILD = 'child'; //ist Bestandteil/Komponente von dem Produkt dem diese Relation zugewiesen wird.
    public const TYPE_CROSS_SELL = 'cross_sell';
    public const TYPE_PROPER_FOR = 'proper_for'; //geeignet für
    public const TYPE_ALTERNATIVE = 'alternative';

    private array $relations = [];

    public function unserialize(?string $relations): void
    {
        if ($relations) {
            $this->relations = Json::decode($relations);
        } else {
            $this->relations = [];
        }
    }

    public function serialize(): string
    {
        return json_encode($this->relations);
    }

    /**
     * @param string $type
     * @param string $mvsrc_product_id
     * @param array|null $extra
     * @param string|null $raw
     */
    public function add(string $type, string $mvsrc_product_id, $text = null, $extra = null, $raw = null)
    {
        $relation = ['type' => $type, 'mvsrc_product_id' => $mvsrc_product_id];

        if ($text) {
            $relation['text'] = $text;
        }

        if ($extra) {
            $relation['extra'] = $extra;
        }

        if ($raw) {
            $relation['raw'] = $raw;
        }

        $this->relations[] = $relation;
    }


    public function isRelationKnown(string $type, string $mvsrc_product_id): bool
    {
        foreach ($this->relations as $relation) {
            if ($relation['type'] === $type && $relation['mvsrc_product_id'] == $mvsrc_product_id) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return array
     */
    public function getRelations(): array
    {
        return $this->relations;
    }

    public function hasRelations(): bool
    {
        return (bool)$this->relations;
    }
}
