<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use service_loader;
use wws\MarketView\MarketViewMediaRepository;
use wws\MarketView\MarketViewMediaStorage;

class MarketViewImportFactory
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    public function getImporter(int $mvsrc_id, ?MarketViewExistingOfferStates $market_view_offer_states = null): MarketViewImport
    {
        if (!$market_view_offer_states) {
            $market_view_offer_states = new MarketViewExistingOfferStatesSqlite($mvsrc_id, $this->db_mv, $this->getExternAvailabilityIds());
        }

        $gpsr_import = new MarketViewGpsrImport($mvsrc_id, $this->db_mv);

        return new MarketViewImport($mvsrc_id, $this->db_mv, $market_view_offer_states, $gpsr_import);
    }

    public function getTestImporter(int $mvsrc_id): MarketViewImport
    {
        $market_view_offer_states = new MarketViewExistingOfferStatesMarketViewDb($mvsrc_id, $this->db_mv, $this->getExternAvailabilityIds());

        return $this->getImporter($mvsrc_id, $market_view_offer_states);
    }

    public function getExternAvailabilityIds(): array
    {
        return $this->db_mv->query("
            SELECT
                market_view_availability.availability_id
            FROM
                market_view_availability
            WHERE
                market_view_availability.is_extern_availability = 1
        ")->asSingleArray();
    }

    public function getMediaImporter(int $mvsrc_id): MarketViewMediaImport
    {
        $storage = service_loader::getStorageFactory()->get('marketview');

        $media_importer = new MarketViewMediaImport($this->db_mv, $storage, service_loader::get(MarketViewMediaRepository::class), service_loader::get(MarketViewMediaStorage::class));
        $media_importer->setMvsrcId($mvsrc_id);

        return $media_importer;
    }
}
