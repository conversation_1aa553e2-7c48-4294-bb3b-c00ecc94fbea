<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use bqp\db\DbSqliteInMemory;
use bqp\db\ExtendedInsertQuery;
use bqp\db\prepared_query;
use PDO;

/**
 * Class MarketViewExistingOfferStatesSqlite
 *
 * Bildet den offer state in einer Sqlite Datenbank ab. Vermeidet unnötige Schreibzugriffe auf der MarketView Datenbank
 * und bringt die bessere Read-Performance.
 * Die native Implementierung in PHP skaliert nicht. Der Speicherbedarf von PHP Arrays/SplFixedArray ist um Faktor
 * 10-20 höher als Sqlite.
 *
 * @package wws\MarketView\Import
 */
class MarketViewExistingOfferStatesSqlite implements MarketViewExistingOfferStates
{
    private int $mvsrc_id;
    private db_generic $db_mv;

    private db_generic $db_sqlite;

    private array $extern_availability_ids;
    private prepared_query $mark_as_seen_stmt;

    public function __construct(int $mvsrc_id, db_generic $db_mv, array $extern_availability_ids)
    {
        $this->mvsrc_id = $mvsrc_id;
        $this->db_mv = $db_mv;
        $this->extern_availability_ids = $extern_availability_ids;

        $this->clear();
    }

    private function initSqlite(): void
    {
        if (isset($this->db_sqlite)) {
            $this->db_sqlite->close();
            unset($this->db_sqlite);
        }

        $this->db_sqlite = new DbSqliteInMemory("
            CREATE TABLE market_view_state (
                mvsrc_product_id TEXT PRIMARY KEY, 
                product_status INT, 
                availability_id INT, 
                vk_netto FLOAT,
                seen INT DEFAULT 0
            )
        ");

        $this->mark_as_seen_stmt = $this->db_sqlite->prepare("
            UPDATE
                market_view_state
            SET
                seen = 1
            WHERE
                market_view_state.mvsrc_product_id = ?
        ");
    }

    public function clear(): void
    {
        $this->initSqlite();

        $result = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id,
                market_view_product.availability_id,
                IF(market_view_product.product_status = 'online', 1, 0) AS product_status,
                market_view_product.vk_netto
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = " . $this->mvsrc_id . "
        ", 'auto', false);

        $insert = new ExtendedInsertQuery($this->db_sqlite, 'market_view_state', ['mvsrc_product_id', 'product_status', 'availability_id', 'vk_netto']);
        $insert->setAutoexecute(50000);
        foreach ($result as $row) {
            $insert->add($row);
        }
        $insert->end();
    }

    public function getExistingOffer(string $mvsrc_product_id): ?MarketViewImportExistingOffer
    {
        $offer = $this->db_sqlite->singleQuery("
            SELECT
                market_view_state.mvsrc_product_id,
                market_view_state.availability_id,
                market_view_state.vk_netto
            FROM
                market_view_state
            WHERE
                market_view_state.mvsrc_product_id = '" . $this->db_sqlite->escape($mvsrc_product_id) . "'
        ");

        if (!$offer) {
            return null;
        }

        return new MarketViewImportExistingOffer($offer['availability_id'], $offer['vk_netto']);
    }

    public function markAsSeen(string $mvsrc_product_id): void
    {
        $this->mark_as_seen_stmt->execute($mvsrc_product_id);
    }

    public function prepareUnseen(): void
    {
        //für diese klasse unnötig
    }

    public function getUnseenMvsrcProductIdsWithOnlineStatus(): array
    {
        // PDO fetch() (was db_generic nutzt) und sqlite haben ein Problem bei großen Datenmengen auf dem Live-System.
        // Bei 800.000 Datensätze bricht fetch() auf 100 bis 300 Datensätze/Sekunde eine und die Auslastung des Kerns geht auf 100%.
        // Problem ist nicht der Query und lässt sich auch nicht Lokal reproduzieren. (selbst mit xdebug sind es 100.000 bis 2.000.000 fetchs pro Sekunde!)
        // Workaround über fetchAll()

        $sql = "
            SELECT
                market_view_state.mvsrc_product_id
            FROM
                market_view_state
            WHERE
                market_view_state.product_status = 1 AND
                market_view_state.seen = 0
        ";

        $pdo = $this->db_sqlite->getDbHandle();
        if ($pdo instanceof PDO) {
            $stmt = $pdo->query($sql);

            return $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
        }

        return $this->db_sqlite->query($sql)->asSingleArray();
    }

    public function getUnseenMvsrcProductIdsWithAvailability(): array
    {
        return $this->db_sqlite->query("
            SELECT
                market_view_state.mvsrc_product_id
            FROM
                market_view_state
            WHERE
                market_view_state.availability_id IN (" . $this->db_sqlite->in($this->extern_availability_ids) . ") AND
                market_view_state.seen = 0
        ")->asSingleArray();
    }
}
