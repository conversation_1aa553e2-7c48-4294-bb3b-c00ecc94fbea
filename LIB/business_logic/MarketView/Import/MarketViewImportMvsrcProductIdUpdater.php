<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;

/**
 * Hilfsklasse um mvsrc_product_ids zu ändern, falls ein Lieferant seine Artikelnummern ändern sollte.
 *
 * Achtung: Überlegen ob es nicht sauberer ist eine neue Quelle anzulegen. Das Problem ist teilweise,
 * das eventuell ein Teil des Sortiments gar nicht mehr aktiv ist (offline). Es wird dann nur ein Teil
 * der Daten migriert. Teilweise kommt so eine migration auch nur häppchenweise. Was letztendlich zu
 * Kollisionen führen kann.
 *
 */
class MarketViewImportMvsrcProductIdUpdater
{
    private int $mvsrc_id;
    private db_generic $db_mv;

    public function __construct(int $mvsrc_id, db_generic $db_mv)
    {
        $this->mvsrc_id = $mvsrc_id;
        $this->db_mv = $db_mv;
    }

    public function changeMvsrcProductId(string $old_mvsrc_product_id, string $new_mvsrc_product_id): void
    {
        MarketViewImportUtils::validateMvsrcProductId($old_mvsrc_product_id);
        MarketViewImportUtils::validateMvsrcProductId($new_mvsrc_product_id);

        $db = $this->db_mv;

        $db->query("
            UPDATE
                market_view_cross_sell
            SET
                market_view_cross_sell.mvsrc_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_cross_sell.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_cross_sell.mvsrc_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");

        $db->query("
            UPDATE
                market_view_cross_sell
            SET
                market_view_cross_sell.mvsrc_zub_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_cross_sell.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_cross_sell.mvsrc_zub_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");

        $db->query("
            UPDATE
                market_view_history
            SET
                market_view_history.mvsrc_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_history.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_history.mvsrc_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");

        $db->query("
            UPDATE
                market_view_matching
            SET
                market_view_matching.mvsrc_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_matching.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_matching.mvsrc_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");

        $db->query("
            UPDATE
                market_view_media
            SET
                market_view_media.mvsrc_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_media.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_media.mvsrc_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");

        $db->query("
            UPDATE
                market_view_product_relation_struct
            SET
                market_view_product_relation_struct.mvsrc_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_product_relation_struct.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_product_relation_struct.mvsrc_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");

        $db->query("
            UPDATE
                market_view_product_cat
            SET
                market_view_product_cat.mvsrc_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_product_cat.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_product_cat.mvsrc_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");

        $db->query("
            UPDATE
                market_view_product
            SET
                market_view_product.mvsrc_product_id = '" . $db->escape($new_mvsrc_product_id) . "'
            WHERE
                market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_product.mvsrc_product_id = '" . $db->escape($old_mvsrc_product_id) . "'
        ");
    }
}
