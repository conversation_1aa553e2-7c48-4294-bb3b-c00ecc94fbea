<?php

namespace wws\MarketView\Import;

use bqp\extern\Google\VertexAI\VertexAI;
use bqp\Json;
use bqp\KeyValueStore\KeyValueStoreDb;
use db;
use service_loader;

/**
 * Hilfsklasse um mit den unstrukturierten GPSR-Daten von unseren Lieferanten umzugehen.
 * Baut aus unstrukturierten Kontaktdaten ein MarketViewImportGpsr Objekt.
 *
 * Daten werden über ein LLM extrahiert. Einmal extrahierte Daten werden permanent gecached, um die Konsistenz zu gewährleisten.
 */
class MarketViewGpsrImportTextExtractor
{
    private VertexAI $vertex;

    /**
     * @var MarketViewImportGpsr[]
     */
    private array $cached_entities = [];
    private KeyValueStoreDb $kv_store;

    public function __construct()
    {
        $this->kv_store = new KeyValueStoreDb(db::getInstance(), 'mv_gpsr_ai_ext');

        $config = service_loader::getConfigRegistry()->get('k11/google_vertex_ai');

        $this->vertex = new VertexAI($config);
        $this->vertex->setModelId('gemini-1.5-flash-002');
        $this->vertex->setJsonResponseSchema([
            'type' => 'object',
            'properties' => [
                'name1' => ['type' => 'string'],
                'name2' => ['type' => 'string'],
                'street' => ['type' => 'string'],
                'post_code' => ['type' => 'string'],
                'city' => ['type' => 'string'],
                'country' => ['type' => 'string'],
                'email' => ['type' => 'string'],
                'phone' => ['type' => 'string'],
                'web' => ['type' => 'string'],
            ],
        ]);
    }

    public function convertUnstructuredContactToEntity(string $contact): MarketViewImportGpsr
    {
        $key = md5($contact);

        if (isset($this->cached_entities[$key])) {
            return $this->cached_entities[$key];
        }

        if ($this->kv_store->has($key)) {
            $cache_entry = Json::decode($this->kv_store->get($key));

            $result = $cache_entry['result'];
        } else {
            $result = $this->promptAi($contact);

            $cache_entry = [
                'contact' => $contact,
                'result' => $result,
            ];

            $this->kv_store->put($key, Json::encode($cache_entry));
        }

        $result = Json::decode($result);

        $entity = new MarketViewImportGpsr();

        if (isset($result['name1'])) {
            $entity->setName1($result['name1']);
        }
        if (isset($result['name2'])) {
            $entity->setName2($result['name2']);
        }
        if (isset($result['street'])) {
            $entity->setStreet($result['street']);
        }
        if (isset($result['post_code'])) {
            $entity->setPostCode($result['post_code']);
        }
        if (isset($result['city'])) {
            $entity->setCity($result['city']);
        }
        if (isset($result['country'])) {
            $entity->setCountry($result['country']);
        }
        if (isset($result['email'])) {
            $entity->setEmail($result['email']);
        }
        if (isset($result['phone'])) {
            $entity->setPhone($result['phone']);
        }
        if (isset($result['web'])) {
            $entity->setWeb($result['web']);
        }

        $this->cached_entities[$key] = $entity;

        return $entity;
    }

    private function promptAi(string $contact): string
    {
        $result = $this->vertex->prompt("Du bekommst von mir Kontaktdaten einer Firma. Die Daten sind unstrukturiert und teilweise unvollständig.
Bitte extrahiere mit aus diesen Daten bitte folgende Informationen: Firma, Kontaktperson, Postfach und weitere Namensbestandteile. Benutze diese Information bitte als name1 und name2.
Achte darauf das du Firmennamen nicht mit der Kontaktperson. In der Regel existiert in diesen Daten immer eine Firma. Eine Kontaktperson ist nicht immer vorhanden.
Weitere Daten sind Ort, die Postleitzahl, das Land, die Strasse mit Hausnummer. Telefonnummer, E-Mail-Adresse, Webseite.
Falls das Land nicht in Deutsch angegeben ist oder nur als Abkürzung oder Code, übersetze es bitte ins Deutsche. Sollte kein Land angegeben sein, versuch das Land anhand der anderen Daten zu erraten.

Die Kontaktdaten: \"" . $contact . "\" 
");

        return $result->getPlainText();
    }
}
