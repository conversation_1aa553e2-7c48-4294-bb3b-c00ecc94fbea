<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;

class MarketViewImportProductDeviceRelationSaveHandler
{
    private ExtendedInsertQuery $statement;
    private int $mvsrc_id;

    public function __construct(int $mvsrc_id, db_generic $db_mv)
    {
        $this->mvsrc_id = $mvsrc_id;

        $this->statement = new ExtendedInsertQuery($db_mv, 'market_view_product_device', ['mvsrc_id', 'mvsrc_product_id', 'mvsrc_device_id', 'extra', 'is_online']);
        $this->statement->setAutoUpdate(true);
        $this->statement->setAutoexecute(10000);
    }

    public function saveRelation(string $mvsrc_product_id, string $mvsrc_device_id, string $extra = ''): void
    {
        $this->statement->add([
            'mvsrc_id' => $this->mvsrc_id,
            'mvsrc_product_id' => $mvsrc_product_id,
            'mvsrc_device_id' => $mvsrc_device_id,
            'extra' => $extra,
            'is_online' => 1
        ]);
    }

    public function end(): void
    {
        $this->statement->end();
    }
}
