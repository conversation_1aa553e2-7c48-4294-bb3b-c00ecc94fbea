<?php

namespace wws\MarketView\Import;

use bqp\Exceptions\DevException;
use debug;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\MarketView\Entities\MarketViewSonstiges;

/**
 * Class MarketViewImportProduct
 *
 * MarketViewProduct eignet sich hierfür nicht. Es ist für den aktuellen import essentiel zu erkennen, was gesetzt wurde und was nicht.
 * Das lässt sich
 */
class MarketViewImportProduct /*implements ArrayAccess*/
{
//wird implizit über MarketViewImport gesetzt:
//        mvsrc_id
//        date_added
//        date_updated
//        update_flag
//        product_status
//        date_updated_availability_id
//        date_updated_vk_netto

    /**
     * Werte die ungeprüft aus der alten Datenstruktur in diese Entität übernommen werden dürfen.
     * (Das Datentypen damit u.U. nicht passen ist OK. Die getter werden in dem Fall nicht benutzt
     * und die Persistenzschicht kommt auch mit den alten Daten klar)
     *
     * @var array
     */
    private static $allowed_legacy_fields = [
        'mvsrc_product_id' => 'mvsrc_product_id',
        'mvsrc_product_id_alt' => 'mvsrc_product_id_alt',
        'content_flag' => 'content_flag',
        'product_name' => 'product_name',
        'product_line' => 'product_line',
        'rank' => 'rank',
        'mv_cat_id' => 'mv_cat_id',
        'mv_cat_id_2' => 'mv_cat_id_2',
        'mv_hersteller_id' => 'mv_hersteller_id',
        'vk_netto_info' => 'vk_netto_info',
        'vk_netto_max' => 'vk_netto_max',
        'vk_nnn' => 'vk_nnn',
        'versand_netto' => 'versand_netto',
        'versand_classification' => 'versand_classification',
        'dropshipping' => 'dropshipping',
        'uvp' => 'uvp',
        'evp' => 'evp',
        'grundpreis_einheit' => 'grundpreis_einheit',
        'grundpreis_menge' => 'grundpreis_menge',
        'grundpreis_faktor' => 'grundpreis_faktor',
        'mv_availability' => 'mv_availability',
        'availability_id' => 'availability_id',
        'inventory' => 'inventory',
        'eol' => 'eol',
        'vpe' => 'vpe',
        'vpe_zwang' => 'vpe_zwang',
        'mpn' => 'mpn',
        'color' => 'color',
        'beschreibung' => 'beschreibung',
        'beschreibung_2' => 'beschreibung_2',
        'lieferumfang' => 'lieferumfang',
        'testergebnis' => 'testergebnis',
        'features' => 'features',
        'features_struct' => 'features_struct',
        'sonstiges' => 'sonstiges',
        'sonstiges_struct' => 'sonstiges_struct',
        'keywords' => 'keywords',
        'breite' => 'breite',
        'hoehe' => 'hoehe',
        'tiefe' => 'tiefe',
        'url' => 'url',
        'zolltarifnummer' => 'zolltarifnummer',
        'genuine_part' => 'genuine_part',
        'hersteller_name' => 'hersteller_name',
        'default_availability_id' => 'default_availability_id'
    ];

    private $data;

    public function __construct()
    {
        $this->data = [];
    }

    public function initWithLegacyArray(array $row): void
    {
        $this->data = array_intersect_key($row, self::$allowed_legacy_fields);

        if (array_key_exists('vk_netto', $row)) {
            $this->setVkNetto($row['vk_netto']);
        }

        if (array_key_exists('vk_netto_per_quantity', $row)) {
            $this->setVkNettoPerQuantity($row['vk_netto_per_quantity']);
        }

        if (array_key_exists('ean', $row)) {
            $this->setEan($row['ean']);
        }

        if (array_key_exists('model_name', $row)) {
            $this->setModelName($row['model_name']);
        }

        if (array_key_exists('breite_mm', $row)) {
            $this->setBreiteMm($row['breite_mm']);
        }

        if (array_key_exists('hoehe_mm', $row)) {
            $this->setHoeheMm($row['hoehe_mm']);
        }

        if (array_key_exists('tiefe_mm', $row)) {
            $this->setTiefeMm($row['tiefe_mm']);
        }

        if (array_key_exists('breite_cm,', $row)) {
            $this->setBreiteCm($row['breite_cm']);
        }

        if (array_key_exists('hoehe_cm', $row)) {
            $this->setHoeheCm($row['hoehe_cm']);
        }

        if (array_key_exists('tiefe_cm', $row)) {
            $this->setTiefeCm($row['tiefe_cm']);
        }

        if (array_key_exists('gewicht_g', $row)) {
            $this->setGewichtG($row['gewicht_g']);
        }

        if (array_key_exists('gewicht_kg', $row)) {
            $this->setGewichtKg($row['gewicht_kg']);
        }

        if (array_key_exists('gewicht', $row)) {
            $this->setGewichtKg($row['gewicht']);
        }

        if (array_key_exists('mwst', $row)) {
            $this->setMwst($row['mwst']);
        }

        if (array_key_exists('mv_vat_rate', $row)) {
            $this->setMvVatRate($row['mv_vat_rate']);
        }

        if (array_key_exists('metal_surcharge_state', $row)) {
            $this->setMetalSurchargeState($row['metal_surcharge_state']);
        }

        if (array_key_exists('mv_gpsr_id', $row)) {
            $this->setMvGpsrId($row['mv_gpsr_id']);
        }

        if (array_key_exists('mv_gpsr_id_2', $row)) {
            $this->setMvGpsrId2($row['mv_gpsr_id_2']);
        }

        if (array_key_exists('safety_information', $row)) {
            $this->setSafetyInformation($row['safety_information']);
        }

        if (array_key_exists('vk_list', $row)) {
            $this->setVkList($row['vk_list']);
        }
    }

    public function setMvsrcProductId(string $mvsrc_product_id): void
    {
        $this->data['mvsrc_product_id'] = $mvsrc_product_id;
    }


    public function setMvsrcProductIdAlt(string $mvsrc_product_id_alt): void
    {
        $this->data['mvsrc_product_id_alt'] = $mvsrc_product_id_alt;
    }


    public function setContentFlag(bool $content_flag): void
    {
        $this->data['content_flag'] = $content_flag;
    }


    public function setProductName(string $product_name): void
    {
        $this->data['product_name'] = $product_name;
    }


    public function setProductLine(string $product_line): void
    {
        $this->data['product_line'] = $product_line;
    }


    public function setRank(int $rank): void
    {
        $this->data['rank'] = $rank;
    }


    public function setMvCatId(int $mv_cat_id): void
    {
        $this->data['mv_cat_id'] = $mv_cat_id;
    }


    public function setMvCatId2(int $mv_cat_id_2): void
    {
        $this->data['mv_cat_id_2'] = $mv_cat_id_2;
    }


    public function setMvHerstellerId(int $mv_hersteller_id): void
    {
        $this->data['mv_hersteller_id'] = $mv_hersteller_id;
    }

    public function setMvGpsrId(int $mv_gpsr_id): void
    {
        $this->data['mv_gpsr_id'] = $mv_gpsr_id;
    }

    public function setMvGpsrId2(int $mv_gpsr_id): void
    {
        $this->data['mv_gpsr_id_2'] = $mv_gpsr_id;
    }

    public function setVkNetto(float $vk_netto): void
    {
        $this->data['vk_netto'] = round($vk_netto, 2);
    }


    public function setVkNettoPerQuantity(?int $vk_netto_per_quantity): void
    {
        $this->data['vk_netto_per_quantity'] = $vk_netto_per_quantity;
    }

    public function setVkNettoInfo(string $vk_netto_info): void
    {
        $this->data['vk_netto_info'] = $vk_netto_info;
    }

    /**
     * @param string $metal_surcharge_state
     * @return void
     * @see MarketviewConst::METAL_SURCHARGE_STATE_*
     *
     */
    public function setMetalSurchargeState(string $metal_surcharge_state): void
    {
        $this->data['metal_surcharge_state'] = $metal_surcharge_state;
    }

    public function setVkNettoMax(?float $vk_netto_max): void
    {
        $this->data['vk_netto_max'] = $vk_netto_max;
    }


    public function setVkNnn(float $vk_nnn): void
    {
        $this->data['vk_nnn'] = $vk_nnn;
    }

    public function setVkList(?float $vk_list): void
    {
        $this->data['vk_list'] = $vk_list;
    }

    public function setVersandNetto(?float $versand_netto): void
    {
        $this->data['versand_netto'] = $versand_netto;
    }


    public function setVersandClassification(?string $versand_classification): void
    {
        $this->data['versand_classification'] = $versand_classification;
    }

    /**
     * Achtung tristat mit int -1 (kein dropshipping) 0 (unbekannt) 1 (dropshipping)
     * @param int $dropshipping
     */
    public function setDropshipping(int $dropshipping): void
    {
        $this->data['dropshipping'] = $dropshipping;
    }

    public function setMwst(int $mwst): void
    {
        throw new DevException('field is deprecated / usw mv_vat_rate');
    }

    /**
     * @param string $mv_vat_rate
     * @see MarketViewVatRates
     *
     */
    public function setMvVatRate(string $mv_vat_rate): void
    {
        $this->data['mv_vat_rate'] = $mv_vat_rate;
    }


    public function setUvp(float $uvp): void
    {
        $this->data['uvp'] = $uvp;
    }


    public function setEvp(float $evp): void
    {
        $this->data['evp'] = $evp;
    }


    public function setGrundpreisEinheit(?string $grundpreis_einheit): void
    {
        $this->data['grundpreis_einheit'] = $grundpreis_einheit;
    }


    public function setGrundpreisMenge(?float $grundpreis_menge): void
    {
        $this->data['grundpreis_menge'] = $grundpreis_menge;
    }


    public function setGrundpreisFaktor(?float $grundpreis_faktor): void
    {
        $this->data['grundpreis_faktor'] = $grundpreis_faktor;
    }


    public function setMvAvailability(string $mv_availability): void
    {
        $this->data['mv_availability'] = $mv_availability;
    }


    public function setAvailabilityId(int $availability_id): void
    {
        $this->data['availability_id'] = $availability_id;
    }


    public function setInventory(int $inventory): void
    {
        $this->data['inventory'] = $inventory;
    }


    public function setEol(bool $eol): void
    {
        $this->data['eol'] = $eol;
    }


    public function setVpe(string $vpe): void
    {
        $this->data['vpe'] = $vpe;
    }


    public function setVpeZwang(?bool $vpe_zwang): void
    {
        $this->data['vpe_zwang'] = $vpe_zwang;
    }


    public function setMpn(string $mpn): void
    {
        $this->data['mpn'] = $mpn;
    }


    public function setModelName(string $model_name): void
    {
        $this->data['model_name'] = $model_name;
    }


    public function setEan(string $ean): void
    {
        $this->data['ean'] = MarketViewImportUtils::normalizeEan($ean);
    }


    public function setColor(string $color): void
    {
        $this->data['color'] = $color;
    }


    public function setBeschreibung(string $beschreibung): void
    {
        $this->data['beschreibung'] = $beschreibung;
    }


    public function setBeschreibung2(string $beschreibung_2): void
    {
        $this->data['beschreibung_2'] = $beschreibung_2;
    }


    public function setLieferumfang(string $lieferumfang): void
    {
        $this->data['lieferumfang'] = $lieferumfang;
    }


    public function setTestergebnis(string $testergebnis): void
    {
        $this->data['testergebnis'] = $testergebnis;
    }


    public function setFeatures(string $features): void
    {
        $this->data['features'] = $features;
    }


    public function setFeaturesStruct(MarketViewFeatures $features): void
    {
        $this->data['features_struct'] = $features;
    }


    public function setSonstiges(string $sonstiges): void
    {
        $this->data['sonstiges'] = $sonstiges;
    }


    public function setSonstigesStruct(MarketViewSonstiges $sonstiges): void
    {
        $this->data['sonstiges_struct'] = $sonstiges;
    }


    public function setKeywords(string $keywords): void
    {
        $this->data['keywords'] = $keywords;
    }


    public function setBreiteCm(float $breite): void
    {
        $this->data['breite'] = $breite;
    }


    public function setHoeheCm(float $hoehe): void
    {
        $this->data['hoehe'] = $hoehe;
    }


    public function setTiefeCm(float $tiefe): void
    {
        $this->data['tiefe'] = $tiefe;
    }


    public function setGewichtKg(float $gewicht): void
    {
        $this->data['gewicht'] = $gewicht; //es wurde bisher bei gewicht_kg auf 2 nachkommastellen gerundet... rausgenommen, ggf. beim anlegen beachten
    }


    public function setBreiteMm(float $breite): void
    {
        $this->data['breite'] = round($breite / 10, 2);
    }


    public function setHoeheMm(float $hoehe): void
    {
        $this->data['hoehe'] = round($hoehe / 10, 2);
    }


    public function setTiefeMm(float $tiefe): void
    {
        $this->data['tiefe'] = round($tiefe / 10, 2);
    }


    public function setGewichtG(float $gewicht): void
    {
        $this->data['gewicht'] = round($gewicht / 1000, 4);
    }


    public function setUrl(string $url): void
    {
        $this->data['url'] = $url;
    }


    public function setZolltarifnummer(string $zolltarifnummer): void
    {
        $this->data['zolltarifnummer'] = $zolltarifnummer;
    }

    public function setCountryOfOrigin(string $country_of_origin): void
    {
        $this->data['country_of_origin'] = $country_of_origin;
    }

    public function setGenuinePart(?bool $genuine_part): void
    {
        $this->data['genuine_part'] = $genuine_part;
    }


    public function setHerstellerName(string $hersteller_name): void
    {
        $this->data['hersteller_name'] = $hersteller_name;
    }


    public function setDefaultAvailabilityId(int $default_availability_id): void
    {
        $this->data['default_availability_id'] = $default_availability_id;
    }


    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMvsrcProductId(): string
    {
        $this->assertSetted('mvsrc_product_id');
        return $this->data['mvsrc_product_id'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMvsrcProductIdAlt(): string
    {
        $this->assertSetted('mvsrc_product_id_alt');
        return $this->data['mvsrc_product_id_alt'];
    }

    /**
     * @return bool
     * @throws MarketViewImportPropertyNotSetException
     */
    public function isContentFlag(): bool
    {
        $this->assertSetted('content_flag');
        return $this->data['content_flag'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getProductName(): string
    {
        $this->assertSetted('product_name');
        return $this->data['product_name'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getProductLine(): string
    {
        $this->assertSetted('product_line');
        return $this->data['product_line'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getRank(): int
    {
        $this->assertSetted('rank');
        return $this->data['int'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMvCatId(): int
    {
        $this->assertSetted('mv_cat_id');
        return $this->data['mv_cat_id'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMvCatId2(): int
    {
        $this->assertSetted('mv_cat_id_2');
        return $this->data['mv_cat_id_2'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMvHerstellerId(): int
    {
        $this->assertSetted('mv_hersteller_id');
        return $this->data['mv_hersteller_id'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVkNetto(): float
    {
        $this->assertSetted('vk_netto');
        return $this->data['vk_netto'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVkNettoInfo(): string
    {
        $this->assertSetted('vk_netto_info');
        return $this->data['vk_netto_info'];
    }

    /**
     * @return null|float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVkNettoMax(): ?float
    {
        $this->assertSetted('vk_netto_max');
        return $this->data['vk_netto_max'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVkNnn(): float
    {
        $this->assertSetted('vk_nnn');
        return $this->data['vk_nnn'];
    }

    /**
     * @return ?float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVkList(): ?float
    {
        $this->assertSetted('vk_list');
        return $this->data['vk_list'];
    }
    /**
     * @return null|float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVersandNetto(): ?float
    {
        $this->assertSetted('versand_netto');
        return $this->data['versand_netto'];
    }

    /**
     * @return null|string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVersandClassification(): ?string
    {
        $this->assertSetted('versand_classification');
        return $this->data['versand_classification'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getDropshipping(): int
    {
        $this->assertSetted('dropshipping');
        return $this->data['dropshipping'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMwst(): int
    {
        throw new DevException('field is deprecated / use mv_vat_rate');
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMvVatRate(): string
    {
        $this->assertSetted('mv_vat_rate');
        return $this->data['mv_vat_rate'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getUvp(): float
    {
        $this->assertSetted('uvp');
        return $this->data['uvp'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getEvp(): float
    {
        $this->assertSetted('evp');
        return $this->data['evp'];
    }

    /**
     * @return null|string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getGrundpreisEinheit(): ?string
    {
        $this->assertSetted('grundpreis_einheit');
        return $this->data['grundpreis_einheit'];
    }

    /**
     * @return null|float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getGrundpreisMenge(): ?float
    {
        $this->assertSetted('grundpreis_menge');
        return $this->data['grundpreis_menge'];
    }

    /**
     * @return null|float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getGrundpreisFaktor(): ?float
    {
        $this->assertSetted('grundpreis_faktor');
        return $this->data['grundpreis_faktor'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMvAvailability(): string
    {
        $this->assertSetted('mv_availability');
        return $this->data['mv_availability'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getAvailabilityId(): int
    {
        $this->assertSetted('availability_id');
        return $this->data['availability_id'];
    }

    /**
     * @return int
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getInventory(): int
    {
        $this->assertSetted('inventory');
        return $this->data['inventory'];
    }

    /**
     * @return bool
     * @throws MarketViewImportPropertyNotSetException
     */
    public function isEol(): bool
    {
        $this->assertSetted('eol');
        return $this->data['eol'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVpe(): string
    {
        $this->assertSetted('vpe');
        return $this->data['vpe'];
    }

    /**
     * @return bool|null
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getVpeZwang(): ?bool
    {
        $this->assertSetted('vpe_zwang');
        return $this->data['vpe_zwang'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMpn(): string
    {
        $this->assertSetted('mpn');
        return $this->data['mpn'];
    }

    /**
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getModelName(): string
    {
        $this->assertSetted('model_name');
        return $this->data['model_name'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getEan(): string
    {
        $this->assertSetted('ean');
        return $this->data['ean'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getColor(): string
    {
        $this->assertSetted('color');
        return $this->data['color'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getBeschreibung(): string
    {
        $this->assertSetted('beschreibung');
        return $this->data['beschreibung'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getBeschreibung2(): string
    {
        $this->assertSetted('beschreibung_2');
        return $this->data['beschreibung_2'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getLieferumfang(): string
    {
        $this->assertSetted('lieferumfang');
        return $this->data['lieferumfang'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getTestergebnis(): string
    {
        $this->assertSetted('testergebnis');
        return $this->data['testergebnis'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getFeatures(): string
    {
        $this->assertSetted('features');
        return $this->data['features'];
    }

    /**
     * @return MarketViewFeatures
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getFeaturesStruct(): MarketViewFeatures
    {
        $this->assertSetted('features_struct');
        return $this->data['features_struct'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getSonstiges(): string
    {
        $this->assertSetted('sonstiges');
        return $this->data['sonstiges'];
    }

    /**
     * @return MarketViewSonstiges
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getSonstigesStruct(): MarketViewSonstiges
    {
        $this->assertSetted('sonstiges_struct');
        return $this->data['sonstiges_struct'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getKeywords(): string
    {
        $this->assertSetted('keywords');
        return $this->data['keywords'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getBreiteCm(): float
    {
        $this->assertSetted('breite');
        return $this->data['breite'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getHoeheCm(): float
    {
        $this->assertSetted('hoehe');
        return $this->data['hoehe'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getTiefeCm(): float
    {
        $this->assertSetted('tiefe');
        return $this->data['tiefe'];
    }

    /**
     * @return float
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getGewichtKg(): float
    {
        $this->assertSetted('gewicht');
        return $this->data['gewicht'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getUrl(): string
    {
        $this->assertSetted('url');
        return $this->data['url'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getZolltarifnummer(): string
    {
        $this->assertSetted('zolltarifnummer');
        return $this->data['zolltarifnummer'];
    }

    /**
     * @return bool|null
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getGenuinePart(): ?bool
    {
        $this->assertSetted('genuine_part');
        return $this->data['genuine_part'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getHerstellerName(): string
    {
        $this->assertSetted('hersteller_name');
        return $this->data['hersteller_name'];
    }

    /**
     * @return string
     * @throws MarketViewImportPropertyNotSetException
     */
    public function getMetalSurchargeState(): string
    {
        $this->assertSetted('metal_surcharge_state');
        return $this->data['metal_surcharge_state'];
    }

    public function setProductStatus(string $product_status): void
    {
        $this->data['product_status'] = $product_status;
    }

    public function setSafetyInformation(?string $safety_information): void
    {
        $this->data['safety_information'] = $safety_information;
    }

    public function unsetKey(string $key): void
    {
        unset($this->data[$key]);
    }

    public function issetKey(string $key): bool
    {
        return array_key_exists($key, $this->data);
    }

    public function _getRawData(): array
    {
        return $this->data;
    }

    public function debug(): void
    {
        debug::dump($this->data);
    }

    private function assertSetted(string $key): void
    {
        if (!array_key_exists($key, $this->data)) {
            throw new MarketViewImportPropertyNotSetException('Key ' . $key . ' is not setted');
        }
    }
}
