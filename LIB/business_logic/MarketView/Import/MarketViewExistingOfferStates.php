<?php

namespace wws\MarketView\Import;

/**
 * Interface MarketViewExistingOfferStates
 *
 * Stellt den Import-Prozess Zugriff auf den den bestehende Zustand der Angebote zur verfügung.
 *
 * @package wws\MarketView\Import
 */
interface MarketViewExistingOfferStates
{
    public function clear(): void;

    public function getExistingOffer(string $mvsrc_product_id): ?MarketViewImportExistingOffer;

    public function markAsSeen(string $mvsrc_product_id): void;

    public function prepareUnseen(): void;

    public function getUnseenMvsrcProductIdsWithOnlineStatus(): array;

    public function getUnseenMvsrcProductIdsWithAvailability(): array;
}
