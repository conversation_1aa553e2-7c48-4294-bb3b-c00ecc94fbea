<?php

namespace wws\MarketView\Import;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;
use bqp\Exceptions\DevException;
use bqp\logger\list_logger;
use bqp\logger\list_logger_interface;
use bqp\Utils\StringUtils;
use bqp\Vat\VatRate;
use InvalidArgumentException;
use service_loader;
use system_protokoll;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\Entities\MarketViewProductRelation;
use wws\MarketView\MarketViewConst;
use wws\MarketView\MarketViewVatRate;

/*
DROP TRIGGER IF EXISTS `calc_unprocessed_essential_update`//
CREATE TRIGGER `calc_unprocessed_essential_update` BEFORE UPDATE ON `market_view_product`
 FOR EACH ROW BEGIN
    IF
        NEW.product_status != OLD.product_status OR
        NEW.vk_netto != OLD.vk_netto OR
        NEW.vk_nnn != OLD.vk_nnn OR
        NEW.vk_netto_info != OLD.vk_netto_info OR
        NOT(NEW.versand_netto <=> OLD.versand_netto) OR
        NEW.availability_id != OLD.availability_id OR
        NEW.mv_availability != OLD.mv_availability OR
        NEW.inventory != OLD.inventory OR
        NEW.product_id != OLD.product_id OR
        NOT(NEW.dropshipping <=> OLD.dropshipping) OR
        NOT(NEW.vpe_zwang <=> OLD.vpe_zwang) OR
        NOT(NEW.vpe <=> OLD.vpe)
    THEN
        IF NEW.product_id != 0 THEN
            INSERT INTO market_view_update_cache SET market_view_update_cache.mvsrc_id = OLD.mvsrc_id, market_view_update_cache.mvsrc_product_id = OLD.mvsrc_product_id ON DUPLICATE KEY UPDATE market_view_update_cache.mvsrc_id = VALUES(mvsrc_id);
        END IF;
    END IF;
END
//

*/

/**
 * Grundgerüst für ein einfachen MarketView Import. Angebotsliste laden, Angebote speichern, Import abschließen.
 * Für komplexere Imports, z.B. ein zeitgleicher Import für mehrere mvsrc_ids, ist dieses Grundgerüst nicht geeignet.
 *
 * Achtung: teilweise hängen hier noch ein paar Funktionen drin, die eher in MarketViewImport oder eine utils Methode gehören würden.
 *
 * @see MarketViewImport
 */
abstract class MarketViewImportSimple
{
    protected $config = [];

    protected db_generic $db_mv;
    protected int $mvsrc_id;

    protected list_logger_interface $list_logger;

    /**
     * @var system_protokoll
     */
    protected $protokoll = null;

    private array $cache = [
        'cat_ids' => [],
        'cross_sell_type' => null
    ];

    protected MarketViewImport $market_view_import;
    protected MarketViewMediaImport $media_import;

    public function __construct($config = [])
    {
        $this->config = $config;
        if (!isset($config['db_mv'])) {
            throw new DevException('db_mv is mandatory');
        }
        $this->db_mv = $config['db_mv'];
        $this->mvsrc_id = $config['mvsrc_id'];

        if (!$this->mvsrc_id) {
            throw new DevException('checken warum mvrsrc_id nicht gesetzt');
        }

        if (isset($this->config['protokoll'])) {
            if ($this->config['protokoll'] instanceof system_protokoll) {
                $this->protokoll = $this->config['protokoll'];
            } else {
                $this->protokoll = system_protokoll::getInstance($this->config['protokoll']);
            }
        }

        if (isset($this->config['list_logger'])) {
            $this->list_logger = $this->config['list_logger'];
        } else {
            $this->list_logger = new list_logger();
        }

        $market_view_import_factory = service_loader::getDiContainer()->get(MarketViewImportFactory::class);

        if (isset($config['test']) && $config['test']) {
            $this->market_view_import = $market_view_import_factory->getTestImporter($this->mvsrc_id);
        } else {
            $this->market_view_import = $market_view_import_factory->getImporter($this->mvsrc_id);
        }
        $this->market_view_import->setListLogger($this->list_logger);

        if (isset($this->config['override_product_names'])) {
            $this->setOverrideProductNames($this->config['override_product_names']);
        }

        $this->media_import = $market_view_import_factory->getMediaImporter($this->getMvsrcId());

        $this->init();
    }

    public function init(): void
    {
    }

    public function setOverrideProductNames(bool $override_product_names): void
    {
        $this->market_view_import->setOverrideProductNames($override_product_names);
    }

    public function getMvsrcId(): int
    {
        return $this->mvsrc_id;
    }

    public function updateComplete(): void
    {
        $this->market_view_import->updateComplete();
    }

    /**
     * @param array $row
     * @return void
     */
    protected function saveProduct(array $row): void
    {
        $product = new MarketViewImportProduct();
        $product->initWithLegacyArray($row);

        $this->market_view_import->saveProduct($product);
    }

    /**
     * @param array $row
     * @return void
     */
    protected function updateProduct(array $row): void
    {
        if (!$row['mvsrc_product_id']) {
            throw new InvalidArgumentException('no mvsrc_product_id');
        }

        $entity = new MarketViewImportProduct();
        $entity->initWithLegacyArray($row);

        $this->market_view_import->updateProduct($entity);
    }


    protected function fullUpdatePrepare(): void
    {
        $this->market_view_import->fullUpdatePrepare();
    }

    protected function fullUpdateEnd(bool $set_eol = false): void
    {
        $this->market_view_import->fullUpdateEnd($set_eol);
    }

    /**
     * Bereitet ein Bestandsupdate vor, alle nicht geupdateten Angebote mit Bestand werden mit
     * $this->fullStockUpdateEnd() auf "kein Bestand" gesetzt.
     */
    protected function fullStockUpdatePrepare(): void
    {
        $this->market_view_import->fullStockUpdatePrepare();
    }

    protected function fullStockUpdateEndLazy(int $availability_id = 40): void
    {
        $this->market_view_import->fullStockUpdateEndLazy($availability_id);
    }

    public function setProductOffline(string $mvsrc_product_id): void
    {
        $this->market_view_import->setProductOffline($mvsrc_product_id);
    }

    /**
     * speichert die mv_availability und gibt die availability zurück
     *
     * @param string $availability
     * @return int $mv_availability
     */
    protected function saveMvAvailability(string $availability): int
    {
        return $this->market_view_import->saveMvAvailability($availability);
    }

    /**
     * ermittelt für einen Bestand die $mv_availability
     *
     * @param int $inventory
     * @return int $mv_availability
     */
    protected function saveInventory(int $inventory): int
    {
        return $this->market_view_import->saveInventory($inventory);
    }

    /**
     * @param DateObj $date
     * @return int $mv_availability
     */
    protected function saveAvailabilityDate(DateObj $date): int
    {
        return $this->market_view_import->saveAvailabilityDate($date);
    }

    /**
     * speichert den Hersteller ab
     *
     * @param string $hersteller_name
     * @return int
     */
    public function saveMvHersteller(string $hersteller_name): int
    {
        return $this->market_view_import->saveMvHersteller($hersteller_name);
    }

    protected function isKnownHersteller(int $mv_hersteller_id): bool
    {
        static $hersteller = null;

        if ($hersteller === null) {
            $hersteller = $this->db_mv->query("
                SELECT
                    market_view_hersteller.mv_hersteller_id,
                    market_view_hersteller.brand_id
                FROM
                    market_view_hersteller
                WHERE
                    market_view_hersteller.brand_id != 0
            ")->asSingleArray('mv_hersteller_id');
        }

        return (bool)$hersteller[$mv_hersteller_id];
    }

    /**
     * @param int $tree_id
     * @param string|array $cat_name
     * @param string|null $mvsrc_cat_id
     * @return int $mv_cat_id
     * @see MarketViewConst::TREE_ID_DEFAULT
     * @see MarketViewImport::saveCategory
     *
     */
    public function saveCategory(int $tree_id, string|array $cat_name, ?string $mvsrc_cat_id = null): int
    {
        return $this->market_view_import->saveCategory($tree_id, $cat_name, $mvsrc_cat_id);
    }

    protected function getMvCatIdByMvsrcCatId(string $mvsrc_cat_id): int
    {
        if (!array_key_exists($mvsrc_cat_id, $this->cache['cat_ids'])) {
            $mv_cat_id = $this->db_mv->fieldQuery("
                SELECT
                    market_view_cat.mv_cat_id
                FROM
                    market_view_cat
                WHERE
                    market_view_cat.mvsrc_cat_id = '" . $this->db_mv->escape($mvsrc_cat_id) . "' AND
                    market_view_cat.mvsrc_id = '$this->mvsrc_id'
            ");

            $this->cache['cat_ids'][$mvsrc_cat_id] = $mv_cat_id ?: 0;
        }

        return $this->cache['cat_ids'][$mvsrc_cat_id];
    }

    protected function saveCrossSelling(string $mvsrc_product_id, array $ids, bool $force = false): void
    {
        $cross_sell_type_id = $this->saveCrossSellType('zub');

        if ($force) {
            $this->db_mv->query("
                DELETE FROM
                    market_view_cross_sell
                WHERE
                    market_view_cross_sell.mvsrc_id = '" . $this->mvsrc_id . "' AND
                    market_view_cross_sell.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
            ");
        }

        if (!$ids) {
            return;
        }

        //da zubehör beim import meist gleich bleibt -> prüfen ob es nicht existiert und dann ggf. schreiben
        $exists = $this->db_mv->query("
            SELECT
                market_view_cross_sell.mvsrc_zub_product_id
            FROM
                market_view_cross_sell
            WHERE
                market_view_cross_sell.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_cross_sell.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "' AND
                market_view_cross_sell.cross_sell_type_id = $cross_sell_type_id
        ")->asSingleArray();

        foreach ($ids as $id) {
            if (!in_array($id, $exists)) {
                $this->db_mv->query("
                    INSERT IGNORE INTO
                        market_view_cross_sell
                    SET
                        market_view_cross_sell.mvsrc_id = '" . $this->mvsrc_id . "',
                        market_view_cross_sell.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "',
                        market_view_cross_sell.mvsrc_zub_product_id = '" . $this->db_mv->escape($id) . "',
                        market_view_cross_sell.cross_sell_type_id = $cross_sell_type_id
                ");
            }
        }
    }

    protected function saveCrossSellType(string $cross_sell_type_name): int
    {
        if ($this->cache['cross_sell_type'] === null) {
            $this->cache['cross_sell_type'] = $this->db_mv->query("
                SELECT
                    market_view_cross_sell_type.cross_sell_type_name,
                    market_view_cross_sell_type.cross_sell_type_id
                FROM
                    market_view_cross_sell_type
            ")->asSingleArray('cross_sell_type_name');
        }

        if (!isset($this->cache['cross_sell_type'][$cross_sell_type_name])) {
            $this->db_mv->query("
                INSERT INTO
                    market_view_cross_sell_type
                SET
                    market_view_cross_sell_type.cross_sell_type_name = '" . $this->db_mv->escape($cross_sell_type_name) . "'
            ");

            $this->cache['cross_sell_type'][$cross_sell_type_name] = $this->db_mv->insert_id();
        }

        return $this->cache['cross_sell_type'][$cross_sell_type_name];
    }

    /**
     * @var MarketViewImportCrossSellSaveHandler
     */
    private $cross_sell_save_handler;

    /**
     * @return MarketViewImportCrossSellSaveHandler
     */
    public function getCrossSellSaveHandler(): MarketViewImportCrossSellSaveHandler
    {
        if (!$this->cross_sell_save_handler) {
            $this->cross_sell_save_handler = new MarketViewImportCrossSellSaveHandler($this->mvsrc_id, $this->db_mv);
        }

        return $this->cross_sell_save_handler;
    }

    public function addMarketViewMedia(MarketViewMedia $media): void
    {
        $this->media_import->saveMarketViewMedia($media);
    }

    /**
     * @var MarketViewImportDeviceSaveHandler
     */
    private $device_save_handler;

    /**
     * Gibt das Objekt zum importieren von Devices zurück. Da das recht umfangreich ist, als eigenständige Klasse ausgelagert.
     *
     * @return MarketViewImportDeviceSaveHandler
     */
    public function getDeviceSaveHandler(): MarketViewImportDeviceSaveHandler
    {
        if (!$this->device_save_handler) {
            $this->device_save_handler = new MarketViewImportDeviceSaveHandler($this, $this->db_mv);
        }

        return $this->device_save_handler;
    }

    /**
     * @var MarketViewImportProductDeviceRelationSaveHandler
     */
    private $product_device_relation_save_handler;

    /**
     * @return MarketViewImportProductDeviceRelationSaveHandler
     */
    public function getProductDeviceRelationSaveHandler(): MarketViewImportProductDeviceRelationSaveHandler
    {
        if (!$this->product_device_relation_save_handler) {
            $this->product_device_relation_save_handler = new MarketViewImportProductDeviceRelationSaveHandler($this->getMvsrcId(), $this->db_mv);
        }

        return $this->product_device_relation_save_handler;
    }

    /**
     * @param array $daten
     * @return void
     * @deprecated see addMarketViewMedia()
     *
     */
    protected function saveMedia(array $daten): void
    {
        if (isset($daten['image'])) {
            $daten['images'] = [trim($daten['image'])];
        }

        if (!isset($daten['images'])) {
            return;
        }
        if (!$daten['images']) {
            return;
        }

        foreach ($daten['images'] as $row) {
            if ($row instanceof MarketViewMedia) {
                $image = $row;
            } else {
                $image = new MarketViewMedia();
                $image->setMediaType($image::MEDIA_TYPE_IMAGE_URL);
                $image->setUrl($row);
            }
            $image->setMvsrcProductId($daten['mvsrc_product_id']);

            $this->addMarketViewMedia($image);
        }
    }

    /**
     * @param string $mvsrc_product_id
     * @param MarketViewProductRelation $relation
     */
    public function saveProductRelation(string $mvsrc_product_id, MarketViewProductRelation $relation): void
    {
        $this->market_view_import->saveProductRelation($mvsrc_product_id, $relation);
    }

    /**
     * @return ExtendedInsertQuery
     */
    private function getAddMarketViewProductCatStatment(): ExtendedInsertQuery
    {
        static $statement = null;

        if ($statement === null) {
            $statement = new ExtendedInsertQuery($this->db_mv, 'market_view_product_cat', ['mvsrc_id', 'mvsrc_product_id', 'mv_cat_id']);
            $statement->setAutoUpdate(true);
        }
        return $statement;
    }

    public function addMarketViewProductCatsStart(): void
    {
        $this->getAddMarketViewProductCatStatment()->setAutoexecute(2500);
    }

    public function addMarketViewProductCatsEnd(): void
    {
        $this->getAddMarketViewProductCatStatment()->end();
    }

    /**
     * bildet die Kategoriezuordnung über market_view_product_cat ab (n:m Beziehung)
     *
     * die SQL Statments können mit addMarketViewProductCatsStart() und addMarketViewProductCatsEnd() gebündelt ausgeführt werden
     *
     * @param string $mvsrc_product_id
     * @param int[] $mv_cat_ids
     */
    public function addMarketViewProductCats(string $mvsrc_product_id, array $mv_cat_ids): void
    {
        $insert = $this->getAddMarketViewProductCatStatment();

        foreach ($mv_cat_ids as $mv_cat_id) {
            $insert->add($this->mvsrc_id, $mvsrc_product_id, $mv_cat_id);
        }

        if (!$insert->isAutoexecute()) {
            $insert->execute();
        }
    }

    public function deleteMvsrcProductId(string $mvsrc_product_id): void
    {
        $db = $this->db_mv;

        $db->query("
            DELETE FROM
                market_view_cross_sell
            WHERE
                market_view_cross_sell.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_cross_sell.mvsrc_product_id = '" . $db->escape($mvsrc_product_id) . "'
        ");

        $db->query("
            DELETE FROM
                market_view_cross_sell
            WHERE
                market_view_cross_sell.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_cross_sell.mvsrc_zub_product_id = '" . $db->escape($mvsrc_product_id) . "'
        ");

        $db->query("
            DELETE FROM
                market_view_history
            WHERE
                market_view_history.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_history.mvsrc_product_id = '" . $db->escape($mvsrc_product_id) . "'
        ");

        $db->query("
            DELETE FROM
                market_view_matching
            WHERE
                market_view_matching.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_matching.mvsrc_product_id = '" . $db->escape($mvsrc_product_id) . "'
        ");

        $db->query("
            DELETE FROM
                market_view_media
            WHERE
                market_view_media.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_media.mvsrc_product_id = '" . $db->escape($mvsrc_product_id) . "'
        ");

        $db->query("
            DELETE FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_product.mvsrc_product_id = '" . $db->escape($mvsrc_product_id) . "'
        ");
    }

    /**
     * Extrahier alle Elemente aus einem Array die mit sonstiges_ beginnen und braut daraus ein Text für makret_view_product.sonstiges
     * @param array $daten
     * @param bool $skip_empty
     * @return string
     *
     * @depracated vorzugsweise MarketViewSonstiges->extractFromArray()
     */
    public static function aggregateSonstiges(array $daten, bool $skip_empty = false): string
    {
        $sonstiges = '';

        foreach ($daten as $key => $value) {
            if (!StringUtils::begins($key, 'sonstiges')) {
                continue;
            }
            if ($skip_empty && $value === "") {
                continue;
            }

            $key = substr($key, 10);

            $sonstiges .= $key . ": " . $value . "\n";
        }

        return $sonstiges;
    }

    public function getMvsrcProductIdByEan(string $ean): ?string
    {
        return $this->market_view_import->searchMvsrcProductIdByEan($ean);
    }

    public function copyFromMvsrc(int $src_mvsrc_id): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_product.*
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '$src_mvsrc_id' AND
                market_view_product.availability_id != " . MarketViewConst::AVAILABILITY_ID_OFFLINE . "
        ");

        foreach ($result as $daten) {
            self::saveProduct($daten);
        }

        self::updateComplete();
    }

    /**
     * gibt zur alternativen mvsrc_product_id ($mvsrc_product_id_alt) die eigentliche $mvsrc_product_id
     *
     * @param string $mvsrc_product_id_alt
     * @return string|null
     */
    public function mvsrcProductIdAltToMvsrcProductId(string $mvsrc_product_id_alt): ?string
    {
        return $this->market_view_import->mvsrcProductIdAltToMvsrcProductId($mvsrc_product_id_alt);
    }

    /**
     * @return string
     */
    public function getMarketViewMediaLokalBaseUrl(): string
    {
        return self::getMarketViewMediaLokalBaseUrlStatic();
    }

    /**
     * bullshit funktion -> erstmal das das alles zentrall ist, bis jetzt war das ziemlich wild über hard codierte urls und die configs
     * @return string
     */
    public static function getMarketViewMediaLokalBaseUrlStatic(): string
    {
        return 'marketview://';
    }

    /**
     * @return VatRate
     * @see \wws\MarketView\MarketViewVatRate->getDefaultImportVatRate()
     */
    public function getDefaultImportVatRate(): VatRate
    {
        static $market_view_vat_rate = null;

        if ($market_view_vat_rate === null) {
            $market_view_vat_rate = new MarketViewVatRate();
        }

        return $market_view_vat_rate->getDefaultImportVatRate();
    }
}
