<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;
use Exception;

/**
 * Stellt eine erweiterte Möglichkeit bereit um die Beziehung zwischen Produkten und Geräten zu verwalteten.
 * Pflegt die "is_online" flag mit. Beziehungen, die nicht mehr übergeben werden, werden mit is_online = 0 markiert.
 *
 * WICHTIG: Nicht generell einsetzbar. Funktioniert nur, wenn die Daten _SORTIERT NACH_ $mvsrc_product_id übergeben werden!
 */
class MarketViewImportProductDeviceRelationStateAwareSaveHandler
{
    private int $mvsrc_id;
    private db_generic $db_mv;

    private $check_out_of_order = true;
    private $out_of_order_mvsrc_product_ids = [];

    private string $mvsrc_product_id = '';
    private array $device_relations = [];
    private ExtendedInsertQuery $insert_statement;

    public function __construct(int $mvsrc_id, db_generic $db_mv)
    {
        $this->mvsrc_id = $mvsrc_id;
        $this->db_mv = $db_mv;

        $this->insert_statement = new ExtendedInsertQuery($db_mv, 'market_view_product_device', ['mvsrc_id', 'mvsrc_product_id', 'mvsrc_device_id', 'extra', 'is_online']);
        $this->insert_statement->setAutoexecute(1000);
    }

    public function setCheckOutOfOrder(bool $check_out_of_order): void
    {
        $this->check_out_of_order = $check_out_of_order;
    }

    public function saveRelation(string $mvsrc_product_id, string $mvsrc_device_id, string $extra = ''): void
    {
        if ($this->mvsrc_product_id !== $mvsrc_product_id) {
            $this->flushByMvsrcProductId();

            $this->mvsrc_product_id = $mvsrc_product_id;
        }

        $this->device_relations[$mvsrc_device_id] = [
            'mvsrc_device_id' => $mvsrc_device_id,
            'extra' => $extra
        ];
    }

    public function flushByMvsrcProductId(): void
    {
        if ($this->mvsrc_product_id === '') {
            return;
        }

        if ($this->check_out_of_order) {
            if (in_array($this->mvsrc_product_id, $this->out_of_order_mvsrc_product_ids)) {
                throw new Exception('Daten sind nicht nach $mvsrc_product_id sortiert. Erneut zu verarbeitende $mvsrc_product_id "' . $this->mvsrc_product_id . '". (Achtung: ein Teil der Daten wurde bereits verarbeitet!)');
            }
            $this->out_of_order_mvsrc_product_ids[] = $this->mvsrc_product_id;
        }

        $current_device_relations = $this->db_mv->query("
            SELECT
                market_view_product_device.mvsrc_device_id,
                market_view_product_device.extra,
                market_view_product_device.is_online
            FROM
                market_view_product_device
            WHERE
                market_view_product_device.mvsrc_id = " . $this->mvsrc_id . " AND 
                market_view_product_device.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "'
        ")->asArray('mvsrc_device_id');

        foreach ($this->device_relations as $mvsrc_device_id => $device_relation) {
            $current_device_relation = $current_device_relations[$mvsrc_device_id] ?? null;

            if ($current_device_relation === null) {
                $this->insert_statement->add([
                    'mvsrc_id' => $this->mvsrc_id,
                    'mvsrc_product_id' => $this->mvsrc_product_id,
                    'mvsrc_device_id' => $device_relation['mvsrc_device_id'],
                    'extra' => $device_relation['extra'],
                    'is_online' => 1
                ]);
            } elseif ($current_device_relation['extra'] != $device_relation['extra'] || $current_device_relation['is_online'] !== '1') {
                //batchen? -> ist erstmal ok so. ist nur ein problem, falls die datenquelle irgendwie instabil ist
                $this->db_mv->query("
                    UPDATE
                        market_view_product_device
                    SET
                        market_view_product_device.extra = '" . $this->db_mv->escape($device_relation['extra']) . "',
                        market_view_product_device.is_online = 1
                    WHERE
                        market_view_product_device.mvsrc_id = " . $this->mvsrc_id . " AND 
                        market_view_product_device.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "' AND
                        market_view_product_device.mvsrc_device_id = '" . $this->db_mv->escape($device_relation['mvsrc_device_id']) . "'
                    
                ");
                unset($current_device_relations[$mvsrc_device_id]);
            } else {
                //nix zu tun
                unset($current_device_relations[$mvsrc_device_id]);
            }
        }

        //nicht gesehen beziehungen auf is_online = 0 setzen
        foreach ($current_device_relations as $key => $current_device_relation) {
            if ($current_device_relation['is_online'] === '0') {
                unset($current_device_relations[$key]);
            }
        }

        if ($current_device_relations) {
            $this->db_mv->query("
                UPDATE
                    market_view_product_device
                SET
                    market_view_product_device.is_online = 0
                WHERE
                    market_view_product_device.mvsrc_id = " . $this->mvsrc_id . " AND 
                    market_view_product_device.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "' AND
                    market_view_product_device.mvsrc_device_id IN (" . $this->db_mv->in(array_keys($current_device_relations)) . ")
            ");
        }
        //

        $this->mvsrc_product_id = "";
        $this->device_relations = [];
    }

    public function end(): void
    {
        $this->flushByMvsrcProductId();
        $this->insert_statement->end();
    }
}
