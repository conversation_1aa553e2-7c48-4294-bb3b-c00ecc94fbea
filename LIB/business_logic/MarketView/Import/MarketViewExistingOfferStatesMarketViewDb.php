<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;

/**
 * Class MarketViewExistingOfferStatesSqlite
 *
 * ACHTUNG... die Klasse bildet das Interface nicht vollständig ab!
 * Die Klasse ist nur dafür gedacht um wenige updates auf größere Lieferanten zu machen (z.B. per Webservice)
 * Bei der Sqlite Version, müssen ansonsten jedes mal alle Angebote kopiert werden. Bei Lieferanten mit millionen Angebote,
 * ist das relativ teuer.
 *
 * @package wws\MarketView\Import
 */
class MarketViewExistingOfferStatesMarketViewDb implements MarketViewExistingOfferStates
{
    private int $mvsrc_id;
    private db_generic $db_mv;
    private array $extern_availability_ids;

    private array $seen_mvsrc_product_ids = [];

    public function __construct(int $mvsrc_id, db_generic $db_mv, array $extern_availability_ids = [])
    {
        $this->mvsrc_id = $mvsrc_id;
        $this->db_mv = $db_mv;
        $this->extern_availability_ids = $extern_availability_ids;
    }

    public function clear(): void
    {
        $this->seen_mvsrc_product_ids = [];
    }

    public function getExistingOffer(string $mvsrc_product_id): ?MarketViewImportExistingOffer
    {
        $offer = $this->db_mv->singleQuery("
            SELECT
                market_view_product.mvsrc_product_id,
                market_view_product.availability_id,
                market_view_product.vk_netto
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = $this->mvsrc_id AND
                market_view_product.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
        ");

        if (!$offer) {
            return null;
        }

        return new MarketViewImportExistingOffer($offer['availability_id'], $offer['vk_netto']);
    }

    public function markAsSeen(string $mvsrc_product_id): void
    {
        $this->seen_mvsrc_product_ids[$mvsrc_product_id] = $mvsrc_product_id;
    }

    public function prepareUnseen(): void
    {
        //für diese klasse unnötig
    }

    public function getUnseenMvsrcProductIdsWithOnlineStatus(): array
    {
        $mvsrc_product_ids = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = $this->mvsrc_id AND
                market_view_product.product_status = 'online'
        ")->asSingleArray();

        foreach ($mvsrc_product_ids as $key => $mvsrc_product_id) {
            if (array_key_exists($mvsrc_product_id, $this->seen_mvsrc_product_ids)) {
                unset($mvsrc_product_ids[$key]);
            }
        }

        return $mvsrc_product_ids;
    }

    public function getUnseenMvsrcProductIdsWithAvailability(): array
    {
        $mvsrc_product_ids = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = $this->mvsrc_id AND
                market_view_product.availability_id IN (" . $this->db_mv->in($this->extern_availability_ids) . ")
        ")->asSingleArray();

        foreach ($mvsrc_product_ids as $key => $mvsrc_product_id) {
            if (array_key_exists($mvsrc_product_id, $this->seen_mvsrc_product_ids)) {
                unset($mvsrc_product_ids[$key]);
            }
        }

        return $mvsrc_product_ids;
    }
}
