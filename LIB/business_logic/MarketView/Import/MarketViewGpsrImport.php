<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use Exception;

class MarketViewGpsrImport
{
    private array $cache = [];
    private array $cache_misses = [];

    private int $mvsrc_id;
    private db_generic $db_mv;

    public function __construct(int $mvsrc_id, db_generic $db_mv)
    {
        $this->mvsrc_id = $mvsrc_id;
        $this->db_mv = $db_mv;
    }

    public function getMvGpsrIdByExternalKey(string $external_key): int
    {
        if (!isset($this->cache[$external_key])) {
            $this->loadCacheByExternalKey($external_key, null);
        }

        return $this->cache[$external_key]['mv_gpsr_id'];
    }

    public function tryGetMvGpsrIdByExternalKey(string $external_key): ?int
    {
        if (array_key_exists($external_key, $this->cache_misses)) {
            return null;
        }

        try {
            if (!isset($this->cache[$external_key])) {
                $this->loadCacheByExternalKey($external_key, null);
            }

            return $this->cache[$external_key]['mv_gpsr_id'];
        } catch (Exception $e) {
            $this->cache_misses[$external_key] = true;
            return null;
        }
    }

    public function saveMvGpsr(MarketViewImportGpsr $mv_gpsr_entity): int
    {
        if ($mv_gpsr_entity->getExternalKey()) {
            $key = $mv_gpsr_entity->getExternalKey();
            if (!isset($this->cache[$key])) {
                $this->loadCacheByExternalKey($key, $mv_gpsr_entity);
            }
        } else {
            $key = $mv_gpsr_entity->getInternalKey();
            if (!isset($this->cache[$key])) {
                $this->loadCacheByInternalKey($mv_gpsr_entity);
            }
        }

        $mv_gpsr_data = $this->cache[$key];

        if ($mv_gpsr_data['internal_key'] !== $mv_gpsr_entity->getInternalKey()) {
            throw new Exception('external_key (' . $mv_gpsr_entity->getExternalKey() . ') mit unterschiedlichen Daten');
        }

        return $this->cache[$key]['mv_gpsr_id'];
    }

    private function loadCacheByInternalKey(MarketViewImportGpsr $mv_gpsr_entity): void
    {
        $internal_key = $mv_gpsr_entity->getInternalKey();

        $mv_gpsr_data = $this->db_mv->singleQuery("
            SELECT
                market_view_gpsr.mv_gpsr_id,
                market_view_gpsr.internal_key
            FROM
                market_view_gpsr
            WHERE
                market_view_gpsr.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_gpsr.internal_key = '" . $this->db_mv->escape($internal_key) . "'
        ");

        if (!$mv_gpsr_data) {
            $mv_gpsr_data = $this->writeMvImportGpsr($mv_gpsr_entity);
        }

        $this->cache[$internal_key] = $mv_gpsr_data;
    }

    private function loadCacheByExternalKey(string $external_key, ?MarketViewImportGpsr $mv_gpsr_entity): void
    {
        $mv_gpsr_data = $this->db_mv->singleQuery("
            SELECT
                market_view_gpsr.mv_gpsr_id,
                market_view_gpsr.internal_key
            FROM
                market_view_gpsr
            WHERE
                market_view_gpsr.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_gpsr.external_key = '" . $this->db_mv->escape($external_key) . "'
        ");

        if (!$mv_gpsr_data) {
            if ($mv_gpsr_entity) {
                $mv_gpsr_data = $this->writeMvImportGpsr($mv_gpsr_entity);
            } else {
                throw new Exception('mv_gpsr_id für "' . $external_key . '" nicht gefunden');
            }
        }

        if ($mv_gpsr_entity) {
            $internal_key = $mv_gpsr_entity->getInternalKey();

            if ($mv_gpsr_data['internal_key'] !== $internal_key) {
                $this->db_mv->query("
                    UPDATE
                        market_view_gpsr
                    SET
                        market_view_gpsr.internal_key = '" . $internal_key . "',
                        market_view_gpsr.external_key = " . $this->db_mv->quote($mv_gpsr_entity->getExternalKey()) . ",
                        market_view_gpsr.name1 = '" . $this->db_mv->escape($mv_gpsr_entity->getName1()) . "',
                        market_view_gpsr.name2 = '" . $this->db_mv->escape($mv_gpsr_entity->getName2()) . "',
                        market_view_gpsr.street = '" . $this->db_mv->escape($mv_gpsr_entity->getStreet()) . "',
                        market_view_gpsr.post_code = '" . $this->db_mv->escape($mv_gpsr_entity->getPostCode()) . "',
                        market_view_gpsr.city = '" . $this->db_mv->escape($mv_gpsr_entity->getCity()) . "',
                        market_view_gpsr.country = '" . $this->db_mv->escape($mv_gpsr_entity->getCountry()) . "',
                        market_view_gpsr.email = '" . $this->db_mv->escape($mv_gpsr_entity->getEmail()) . "',
                        market_view_gpsr.phone = '" . $this->db_mv->escape($mv_gpsr_entity->getPhone()) . "',
                        market_view_gpsr.web = '" . $this->db_mv->escape($mv_gpsr_entity->getWeb()) . "',
                        market_view_gpsr.misc = '" . $this->db_mv->escape($mv_gpsr_entity->getMisc()) . "'
                    WHERE
                        market_view_gpsr.mv_gpsr_id = " . $mv_gpsr_data['mv_gpsr_id'] . "
                ");
                $mv_gpsr_data['internal_key'] = $internal_key;
            }
        }

        $this->cache[$external_key] = $mv_gpsr_data;
    }

    private function writeMvImportGpsr(MarketViewImportGpsr $mv_gpsr_entity): array
    {
        $internal_key = $mv_gpsr_entity->getInternalKey();

        $this->db_mv->query("
            INSERT INTO
                market_view_gpsr
            SET
                market_view_gpsr.mvsrc_id = " . $this->mvsrc_id . ",
                market_view_gpsr.internal_key = '" . $this->db_mv->escape($internal_key) . "',
                market_view_gpsr.external_key = " . $this->db_mv->quote($mv_gpsr_entity->getExternalKey()) . ",
                market_view_gpsr.name1 = '" . $this->db_mv->escape($mv_gpsr_entity->getName1()) . "',
                market_view_gpsr.name2 = '" . $this->db_mv->escape($mv_gpsr_entity->getName2()) . "',
                market_view_gpsr.street = '" . $this->db_mv->escape($mv_gpsr_entity->getStreet()) . "',
                market_view_gpsr.post_code = '" . $this->db_mv->escape($mv_gpsr_entity->getPostCode()) . "',
                market_view_gpsr.city = '" . $this->db_mv->escape($mv_gpsr_entity->getCity()) . "',
                market_view_gpsr.country = '" . $this->db_mv->escape($mv_gpsr_entity->getCountry()) . "',
                market_view_gpsr.email = '" . $this->db_mv->escape($mv_gpsr_entity->getEmail()) . "',
                market_view_gpsr.phone = '" . $this->db_mv->escape($mv_gpsr_entity->getPhone()) . "',
                market_view_gpsr.web = '" . $this->db_mv->escape($mv_gpsr_entity->getWeb()) . "',
                market_view_gpsr.misc = '" . $this->db_mv->escape($mv_gpsr_entity->getMisc()) . "',
                market_view_gpsr.date_added = NOW()
        ");

        return [
            'mv_gpsr_id' => $this->db_mv->insert_id(),
            'internal_key' => $internal_key
        ];
    }
}
