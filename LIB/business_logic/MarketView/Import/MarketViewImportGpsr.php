<?php

namespace wws\MarketView\Import;

/**
 * Entität für den Import von GPSR Daten.
 */
class MarketViewImportGpsr
{
    protected int $mvsrc_id = 0;
    protected ?string $external_key = null;
    protected string $name1 = '';
    protected string $name2 = '';
    protected string $street = '';
    protected string $post_code = '';
    protected string $city = '';
    protected string $country = '';
    protected string $email = '';
    protected string $phone = '';
    protected string $web = '';
    protected string $misc = '';


    public function getInternalKey(): string
    {
        return md5($this->mvsrc_id . $this->external_key . $this->name1 . $this->name2 . $this->street . $this->post_code . $this->city . $this->country . $this->email . $this->phone . $this->web . $this->misc);
    }

    public function setMvsrcId(int $mvsrc_id): void
    {
        $this->mvsrc_id = $mvsrc_id;
    }

    public function setExternalKey(?string $external_key): void
    {
        $this->external_key = $external_key;
    }

    public function setName1(string $name1): void
    {
        $this->name1 = $name1;
    }

    public function setName2(string $name2): void
    {
        $this->name2 = $name2;
    }

    public function setStreet(string $street): void
    {
        $this->street = $street;
    }

    public function setPostCode(string $post_code): void
    {
        $this->post_code = $post_code;
    }

    public function setCity(string $city): void
    {
        $this->city = $city;
    }

    public function setCountry(string $country): void
    {
        $this->country = $country;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
    }

    public function setPhone(string $phone): void
    {
        $this->phone = $phone;
    }

    public function setWeb(string $web): void
    {
        $this->web = $web;
    }

    public function getMvsrcId(): int
    {
        return $this->mvsrc_id;
    }

    public function getExternalKey(): ?string
    {
        return $this->external_key;
    }

    public function getName1(): string
    {
        return $this->name1;
    }

    public function getName2(): string
    {
        return $this->name2;
    }

    public function getStreet(): string
    {
        return $this->street;
    }

    public function getPostCode(): string
    {
        return $this->post_code;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): string
    {
        return $this->phone;
    }

    public function getWeb(): string
    {
        return $this->web;
    }

    public function getMisc(): string
    {
        return $this->misc;
    }

    public function setMisc(string $misc): void
    {
        $this->misc = $misc;
    }
}
