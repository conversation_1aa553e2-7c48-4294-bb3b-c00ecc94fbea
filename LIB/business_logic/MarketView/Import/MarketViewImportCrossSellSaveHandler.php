<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;

class MarketViewImportCrossSellSaveHandler
{
    private ExtendedInsertQuery $statement;
    private int $mvsrc_id;

    public function __construct(int $mvsrc_id, db_generic $db_mv)
    {
        $this->mvsrc_id = $mvsrc_id;

        $this->statement = new ExtendedInsertQuery(
            $db_mv,
            'market_view_cross_sell',
            ['mvsrc_id', 'mvsrc_product_id', 'mvsrc_zub_product_id', 'cross_sell_type_id']
        );
        $this->statement->setAutoUpdate(true);
        $this->statement->setAutoexecute(10000);
    }

    public function saveCrossSell(string $mvsrc_product_id, string $mvsrc_zub_product_id, int $cross_sell_type_id): void
    {
        $this->statement->add([
            'mvsrc_id' => $this->mvsrc_id,
            'mvsrc_product_id' => $mvsrc_product_id,
            'mvsrc_zub_product_id' => $mvsrc_zub_product_id,
            'cross_sell_type_id' => $cross_sell_type_id
        ]);
    }

    public function end(): void
    {
        $this->statement->end();
    }
}
