<?php

namespace wws\MarketView\Import;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\db\prepared_query;
use bqp\logger\list_logger;
use Exception;
use InvalidArgumentException;
use LengthException;
use service_loader;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\MarketView\Entities\MarketViewProductRelation;
use wws\MarketView\Entities\MarketViewSonstiges;
use wws\MarketView\History\MarketViewHistoryWriter;
use wws\MarketView\History\MarketViewHistoryWriterOldDb;
use wws\MarketView\MarketViewConst;
use wws\MarketView\MarketViewDuplicateEntryException;
use wws\MarketView\MarketViewFactory;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatchingEanChange;

class MarketViewImport
{
    private int $mvsrc_id;
    private db_generic $db_mv;
    private list_logger $list_logger;

    /**
     * Legt fest ob ein Produktname überschreiben werden soll. Das ist mit hier gelandet,
     * weil für den Aufrufer nicht klar ist, ob das Produkt schon existiert.
     *
     * @todo zu speziell, rausnehmen und in den Aufruf verlagern (ggf. hier über old_data productExists() implementieren)
     */
    private bool $override_product_names = true;

    /**
     * unterdrückt die Ausgabe von Fehlermeldungen wenn neue Artikel mehrfach angelegt werden sollen
     * (unfähigkeit von Lieferanten, z.B.: EGH und Uni)
     */
    private bool $silent_duplicate_errors = false;

    private MarketViewExistingOfferStates $market_view_offer_states;
    private MarketViewHistoryWriter $history;

    private ?MarketViewImportDataChangeTracking $data_change_tracking = null;
    private MarketViewGpsrImport $gpsr_import;

    public function __construct(
        int $mvsrc_id,
        db_generic $db_mv,
        MarketViewExistingOfferStates $market_view_offer_states,
        MarketViewGpsrImport $gpsr_import,
    ) {
        $this->mvsrc_id = $mvsrc_id;
        $this->db_mv = $db_mv;

        $this->list_logger = new list_logger();

        $this->gpsr_import = $gpsr_import;
        $this->market_view_offer_states = $market_view_offer_states;

        $this->history = new MarketViewHistoryWriterOldDb($mvsrc_id, $db_mv);
    }

    public function setListLogger(list_logger $list_logger): void
    {
        $this->list_logger = $list_logger;
    }

    public function getListLogger(): list_logger
    {
        return $this->list_logger;
    }

    public function setMarketViewDataChangeTracking(?MarketViewImportDataChangeTracking $market_view_data_change_tracking): void
    {
        $this->data_change_tracking = $market_view_data_change_tracking;
    }


    public function setSilentDuplicateErrors(bool $silent_duplicate_errors): void
    {
        $this->silent_duplicate_errors = $silent_duplicate_errors;
    }

    public function setOverrideProductNames(bool $override_product_names): void
    {
        $this->override_product_names = $override_product_names;
    }


    public function updateComplete(): void
    {
        $matching = MarketViewFactory::getInstance()->getMarketViewMatching();

        $this->list_logger->addRuntimeMark('parent::updateComplete begin');

        $ean_matching = service_loader::getDiContainer()->get(ProductMatchingEanChange::class);
        $ean_matching->removeMatchingForSourceIfEnabled($this->mvsrc_id);

        $matching->matchLocalProducts($this->mvsrc_id);

        $this->list_logger->addRuntimeMark('parent::updateComplete local end');
    }

    public function fullUpdatePrepare(): void
    {
        $this->market_view_offer_states->prepareUnseen();
    }

    public function fullUpdateEnd(bool $set_eol = false): void
    {
        $mvsrc_product_ids = $this->market_view_offer_states->getUnseenMvsrcProductIdsWithOnlineStatus();

        foreach ($mvsrc_product_ids as $mvsrc_product_id) {
            $this->setProductOffline($mvsrc_product_id, $set_eol);
        }
    }

    public function setProductOffline(string $mvsrc_product_id, bool $set_eol = false): void
    {
        $product = new MarketViewImportProduct();
        $product->setProductStatus('offline');
        $product->setMvsrcProductId($mvsrc_product_id);
        $product->setAvailabilityId(MarketViewConst::AVAILABILITY_ID_OFFLINE);
        $product->setInventory(0);
        $product->setMvAvailability('');
        if ($set_eol) {
            $product->setEol(true);
        }

        $this->updateProduct($product);
    }

    /**
     * Bereitet ein Bestandsupdate vor.
     * - MarketViewImport::fullStockUpdateEndLazy() erlaubt es, alle nicht gesehen Angebote mit Bestand, auf "kein Bestand" zu setzen.
     * - MarketViewImport::fullStockUpdateEnd() erlaubt es, alle nicht gesehen Angebot die aktiv sind, auf "kein Bestand" zu setzen
     */
    public function fullStockUpdatePrepare(): void
    {
        $this->market_view_offer_states->prepareUnseen();
    }

    /**
     * Setzt alle nicht gesehen Angebote mit Bestand, auf "kein Bestand". Eignet sich für Importe wo
     * Preisliste und Bestandslisten getrennt kommen und im Prinzip das gleiche Angebot umschließen.
     *
     * Wenn in der Preisliste länger Angebote enthalten sind, die nicht in mehr den Bestandslisten auftauchen,
     * werden diese ggf. durch den expire Mechanismus auf "Unbekannt" gesetzt.
     *
     * @param int $availability_id
     * @return void
     */
    public function fullStockUpdateEndLazy(int $availability_id = 40): void
    {
        $mvsrc_product_ids = $this->market_view_offer_states->getUnseenMvsrcProductIdsWithAvailability();

        foreach ($mvsrc_product_ids as $mvsrc_product_id) {
            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($mvsrc_product_id);
            $product->setAvailabilityId($availability_id);
            $product->setInventory(0);
            $product->setMvAvailability('');

            $this->updateProduct($product);
        }
    }

    /**
     * Setzt alle aktiven Angebot, die nicht gesehen wurden, auf "kein Bestand". Eignet sich
     * für Importe, in den die Bestandslisten z.B. nur lagerende Angebote enthalten.
     *
     * Solange ein Angebot in einer Preisliste enthalten ist, wird diese auf "kein Bestand" gesetzt
     * und gehalten.
     *
     * Achtung: führt aber auch dazu, dass alle aktiven Angebote in irgendeiner Form aktualisiert werden!
     *
     * @param int $availability_id
     * @return void
     */
    public function fullStockUpdateEnd(int $availability_id = 40): void
    {
        $mvsrc_product_ids = $this->market_view_offer_states->getUnseenMvsrcProductIdsWithOnlineStatus();

        foreach ($mvsrc_product_ids as $mvsrc_product_id) {
            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($mvsrc_product_id);
            $product->setAvailabilityId($availability_id);
            $product->setInventory(0);
            $product->setMvAvailability('');

            $this->updateProduct($product);
        }
    }

    /**
     * @throws MarketViewDuplicateEntryException
     */
    public function saveProduct(MarketViewImportProduct $product, bool $update_only = false): void
    {
        MarketViewImportUtils::validateMvsrcProductId($product->getMvsrcProductId());

        $old_offer = $this->market_view_offer_states->getExistingOffer($product->getMvsrcProductId());

        $raw_data = $this->prepareProductDataToSave($product->_getRawData(), $old_offer);
        $sql_prepare = $this->convertProductDataToSql($raw_data);

        $this->market_view_offer_states->markAsSeen($product->getMvsrcProductId());

        if ($this->data_change_tracking) {
            $this->data_change_tracking->preloadState($product->getMvsrcProductId());
        }

        try {
            if ($old_offer) {
                $this->saveProduct_update_perPreparedSql($sql_prepare);
            } elseif (!$update_only) {
                $this->saveProduct_insert_perPreparedSql($sql_prepare);
            }

            $this->history->addByImport($product, $old_offer);

            $this->list_logger->addEntityOk();
        } catch (Exception $e) {
            $this->list_logger->addEntityFail($e->getMessage(), $product->getMvsrcProductId());

            if (str_contains($e->getMessage(), 'Duplicate entry')) {
                if ($this->silent_duplicate_errors) {
                    //nix machen
                } else {
                    throw new MarketViewDuplicateEntryException($e->getMessage(), $e->getCode(), $e);
                }
            } else {
                throw $e;
            }

            return; //damit data_change_tracking nicht mit aufgerufen wird (das will ich nicht in dem try-catch block haben)
        }

        if ($this->data_change_tracking) {
            $this->data_change_tracking->compareState($product->getMvsrcProductId(), $raw_data);
        }
    }

    /**
     * @param MarketViewImportProduct $product
     * @return void
     */
    public function updateProduct(MarketViewImportProduct $product): void
    {
        $this->saveProduct($product, true);
    }

    public function searchMvsrcProductIdByEan(string $ean): ?string
    {
        if (!$ean) {
            return null;
        }

        return $this->db_mv->fieldQuery("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $this->db_mv->escape($this->mvsrc_id) . "' AND
                market_view_product.ean = '" . $this->db_mv->escape($ean) . "'
            LIMIT
                1
        ");
    }

    public function saveMvGpsr(MarketViewImportGpsr $mv_gpsr_entity): int
    {
        return $this->gpsr_import->saveMvGpsr($mv_gpsr_entity);
    }

    public function getMvGpsrIdByExternalKey(string $external_key): int
    {
        return $this->gpsr_import->getMvGpsrIdByExternalKey($external_key);
    }

    public function tryGetMvGpsrIdByExternalKey(string $external_key): ?int
    {
        return $this->gpsr_import->tryGetMvGpsrIdByExternalKey($external_key);
    }

    private function prepareProductDataToSave(array $row, MarketViewImportExistingOffer $old_offer = null): array
    {
        if (array_key_exists('hersteller_name', $row) && !array_key_exists('mv_hersteller_id', $row)) {
            $row['mv_hersteller_id'] = $this->saveMvHersteller($row['hersteller_name']);
        }

        if (array_key_exists('vk_netto', $row) && !array_key_exists('vk_nnn', $row)) {
            $row['vk_nnn'] = $row['vk_netto'];
        }

        //verfügbarkeit/default verfügbarkeit
        if (!array_key_exists('availability_id', $row)) {
            $old_availability_id = $old_offer ? $old_offer->getAvailabilityId() : 0;
            if (!$old_availability_id || in_array($old_availability_id, [MarketViewConst::AVAILABILITY_ID_OFFLINE, MarketViewConst::AVAILABILITY_ID_UNKNOWN, MarketViewConst::AVAILABILITY_ID_0])) {
                if (array_key_exists('default_availability_id', $row)) {
                    if ($row['default_availability_id'] == MarketViewConst::AVAILABILITY_ID_UNKNOWN && $old_availability_id == MarketViewConst::AVAILABILITY_ID_UNKNOWN) {
                        //nix tun wenn unbekannt mit unbekannt überschreiben werden soll
                        //->das aktuallisiert ansonsten date_updated_availability_id und das ist in dem fall nicht gewünscht
                    } else {
                        $row['availability_id'] = $row['default_availability_id'];
                    }
                }
            }
        }

        //produktnamen
        if ($old_offer && !$this->override_product_names) { //wenn Produktnamen nicht überschreiben werden sollen prüfen ob neuer artikel
            unset($row['product_name']);
        }

        if (array_key_exists('availability_id', $row)) {
            $row['date_updated_availability_id'] = DateObj::init()->db();
        }

        if (array_key_exists('vk_netto', $row)) {
            $row['date_updated_vk_netto'] = DateObj::init()->db();
        }

        if (array_key_exists('vpe', $row) && $row['vpe'] === null) {
            $row['vpe'] = '';
        }

        if (array_key_exists('features_struct', $row)) {
            /* @var MarketViewFeatures $row ['features_struct'] */
            $row['features_struct'] = $row['features_struct']->serialize();
        }

        if (array_key_exists('sonstiges_struct', $row)) {
            /* @var MarketViewSonstiges $row ['sonstiges_struct'] */
            $row['sonstiges_struct'] = $row['sonstiges_struct']->serialize();
        }

        return $row;
    }

    private function convertProductDataToSql(array $row): array
    {
        $sql_prepare = [];

        $sql_prepare['product_status'] = 'online';
        if (array_key_exists('product_status', $row)) {
            $sql_prepare['product_status'] = $row['product_status'];
        }

        $sql_prepare['mvsrc_product_id'] = $row['mvsrc_product_id'];

        if (array_key_exists('product_name', $row)) {
            $sql_prepare['product_name'] = $row['product_name'];
        }
        if (array_key_exists('product_line', $row)) {
            $sql_prepare['product_line'] = $row['product_line'];
        }
        if (array_key_exists('mvsrc_product_id_alt', $row)) {
            $sql_prepare['mvsrc_product_id_alt'] = $row['mvsrc_product_id_alt'];
        }
        if (array_key_exists('vk_netto', $row)) {
            $sql_prepare['vk_netto'] = $row['vk_netto'];
        }
        if (array_key_exists('date_updated_vk_netto', $row)) {
            $sql_prepare['date_updated_vk_netto'] = $row['date_updated_vk_netto'];
        }
        if (array_key_exists('vk_netto_max', $row)) {
            $sql_prepare['vk_netto_max'] = $row['vk_netto_max'];
        }
        if (array_key_exists('vk_netto_info', $row)) {
            $sql_prepare['vk_netto_info'] = $row['vk_netto_info'];
        }
        if (array_key_exists('vk_nnn', $row)) {
            $sql_prepare['vk_nnn'] = $row['vk_nnn'];
        }
        if (array_key_exists('vk_list', $row)) {
            $sql_prepare['vk_list'] = $row['vk_list'];
        }
        if (array_key_exists('vk_netto_per_quantity', $row)) {
            $sql_prepare['vk_netto_per_quantity'] = $row['vk_netto_per_quantity'];
        }
        if (array_key_exists('metal_surcharge_state', $row)) {
            $sql_prepare['metal_surcharge_state'] = $row['metal_surcharge_state'];
        }
        if (array_key_exists('mwst', $row)) {
            $sql_prepare['mwst'] = $row['mwst'];
        }
        if (array_key_exists('mv_vat_rate', $row)) {
            $sql_prepare['mv_vat_rate'] = $row['mv_vat_rate'];
        }
        if (array_key_exists('versand_netto', $row)) {
            $sql_prepare['versand_netto'] = $row['versand_netto'];
        }
        if (array_key_exists('versand_classification', $row)) {
            $sql_prepare['versand_classification'] = $row['versand_classification'];
        }
        if (array_key_exists('mv_availability', $row)) {
            $sql_prepare['mv_availability'] = $row['mv_availability'];
        }
        if (array_key_exists('availability_id', $row)) {
            $sql_prepare['availability_id'] = $row['availability_id'];
        }
        if (array_key_exists('date_updated_availability_id', $row)) {
            $sql_prepare['date_updated_availability_id'] = $row['date_updated_availability_id'];
        }
        if (array_key_exists('inventory', $row)) {
            $sql_prepare['inventory'] = $row['inventory'];
        }
        if (array_key_exists('content_flag', $row)) {
            $sql_prepare['content_flag'] = (int)$row['content_flag'];
        }
        if (array_key_exists('sonstiges', $row)) {
            $sql_prepare['sonstiges'] = $row['sonstiges'];
        }
        if (array_key_exists('sonstiges_struct', $row)) {
            $sql_prepare['sonstiges_struct'] = $row['sonstiges_struct'];
        }
        if (array_key_exists('vpe', $row)) {
            $sql_prepare['vpe'] = $row['vpe'];
        }
        if (array_key_exists('vpe_zwang', $row)) {
            if ($row['vpe_zwang'] !== null) {
                $sql_prepare['vpe_zwang'] = $row['vpe_zwang'] ? 1 : 0;
            } else {
                $sql_prepare['vpe_zwang'] = null;
            }
        }
        if (array_key_exists('eol', $row)) {
            $sql_prepare['eol'] = $row['eol'] ? 1 : 0;
        }
        if (array_key_exists('uvp', $row)) {
            $sql_prepare['uvp'] = $row['uvp'];
        }
        if (array_key_exists('evp', $row)) {
            $sql_prepare['evp'] = $row['evp'];
        }
        if (array_key_exists('mv_cat_id', $row)) {
            $sql_prepare['mv_cat_id'] = $row['mv_cat_id'];
        }
        if (array_key_exists('mv_cat_id_2', $row)) {
            $sql_prepare['mv_cat_id_2'] = $row['mv_cat_id_2'];
        }
        if (array_key_exists('mv_hersteller_id', $row)) {
            $sql_prepare['mv_hersteller_id'] = $row['mv_hersteller_id'];
        }
        if (array_key_exists('mv_gpsr_id', $row)) {
            $sql_prepare['mv_gpsr_id'] = $row['mv_gpsr_id'];
        }
        if (array_key_exists('mv_gpsr_id_2', $row)) {
            $sql_prepare['mv_gpsr_id_2'] = $row['mv_gpsr_id_2'];
        }
        if (array_key_exists('dropshipping', $row)) {
            $sql_prepare['dropshipping'] = (int)$row['dropshipping'];
        }
        if (array_key_exists('mpn', $row)) {
            $sql_prepare['mpn'] = $row['mpn'];
        }
        if (array_key_exists('model_name', $row)) {
            $sql_prepare['model_name'] = $row['model_name'];
        }
        if (array_key_exists('ean', $row)) {
            $sql_prepare['ean'] = $row['ean'];
        }
        if (array_key_exists('genuine_part', $row)) {
            if ($row['genuine_part'] !== null) {
                $row['genuine_part'] = $row['genuine_part'] ? 1 : 0;
            } else {
                $sql_prepare['genuine_part'] = null;
            }
        }
        if (array_key_exists('grundpreis_einheit', $row)) {
            $sql_prepare['grundpreis_einheit'] = $row['grundpreis_einheit'];
        }
        if (array_key_exists('grundpreis_menge', $row)) {
            $sql_prepare['grundpreis_menge'] = $row['grundpreis_menge'];
        }
        if (array_key_exists('grundpreis_faktor', $row)) {
            $sql_prepare['grundpreis_faktor'] = $row['grundpreis_faktor'];
        } //produkt menge/menge = faktor

        if (array_key_exists('color', $row)) {
            $sql_prepare['color'] = $row['color'];
        }
        if (array_key_exists('beschreibung', $row)) {
            $sql_prepare['beschreibung'] = $row['beschreibung'];
        }
        if (array_key_exists('beschreibung_2', $row)) {
            $sql_prepare['beschreibung_2'] = $row['beschreibung_2'];
        }
        if (array_key_exists('lieferumfang', $row)) {
            $sql_prepare['lieferumfang'] = $row['lieferumfang'];
        }
        if (array_key_exists('testergebnis', $row)) {
            $sql_prepare['testergebnis'] = $row['testergebnis'];
        }
        if (array_key_exists('features', $row)) {
            $sql_prepare['features'] = $row['features'];
        }
        if (array_key_exists('features_struct', $row)) {
            $sql_prepare['features_struct'] = $row['features_struct'];
        }
        if (array_key_exists('breite', $row)) {
            $sql_prepare['breite'] = $row['breite'];
        } //in cm
        if (array_key_exists('hoehe', $row)) {
            $sql_prepare['hoehe'] = $row['hoehe'];
        } //in cm
        if (array_key_exists('tiefe', $row)) {
            $sql_prepare['tiefe'] = $row['tiefe'];
        } //in cm
        if (array_key_exists('gewicht', $row)) {
            $sql_prepare['gewicht'] = $row['gewicht'];
        } //in kg
        if (array_key_exists('url', $row)) {
            $sql_prepare['url'] = $row['url'];
        }
        if (array_key_exists('rank', $row)) {
            $sql_prepare['rank'] = $row['rank'];
        }
        if (array_key_exists('zolltarifnummer', $row)) {
            $sql_prepare['zolltarifnummer'] = $row['zolltarifnummer'];
        }
        if (array_key_exists('keywords', $row)) {
            $sql_prepare['keywords'] = $row['keywords'];
        }
        if (array_key_exists('safety_information', $row)) {
            $sql_prepare['safety_information'] = $row['safety_information'];
        }

        return $sql_prepare;
    }


    /**
     * @var prepared_query[]
     */
    private $update_stmt_cache = [];

    private function saveProduct_update_perPreparedSql(array $fields): void
    {
        $stmt_key = md5(implode('', array_keys($fields)));

        //statment bauen falls noch nicht vorhanden
        if (!isset($this->update_stmt_cache[$stmt_key])) {
            $sql_fields = [];

            foreach ($fields as $key => $value) {
                if ($key === 'mvsrc_product_id') {
                    continue;
                }

                $sql_fields[] = "market_view_product." . $key . " = :" . $key;
            }

            $sql_fields = implode(', ', $sql_fields);

            $query = "
                    UPDATE
                        market_view_product
                    SET
                        market_view_product.date_updated = NOW(),
                        $sql_fields
                    WHERE
                        market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                        market_view_product.mvsrc_product_id = :mvsrc_product_id
                ";

            $this->update_stmt_cache[$stmt_key] = $this->db_mv->prepare($query);
        }

        $this->update_stmt_cache[$stmt_key]->execute($fields);
    }

    /**
     * @var prepared_query[]
     */
    private $insert_stmt_cache = [];

    private function saveProduct_insert_perPreparedSql(array $fields): void
    {
        $stmt_key = md5(implode('', array_keys($fields)));

        //statment bauen falls noch nicht vorhanden
        if (!isset($this->insert_stmt_cache[$stmt_key])) {
            $sql_fields = [];

            foreach ($fields as $key => $value) {
                $sql_fields[] = "market_view_product." . $key . " = :" . $key;
            }

            $sql_fields = implode(', ', $sql_fields);

            $query = "
                    INSERT INTO
                        market_view_product
                    SET
                        market_view_product.mvsrc_id = '" . $this->mvsrc_id . "',
                        market_view_product.date_updated = NOW(),
                        market_view_product.date_added = NOW(),
                        $sql_fields
                ";

            $this->insert_stmt_cache[$stmt_key] = $this->db_mv->prepare($query);
        }

        $this->insert_stmt_cache[$stmt_key]->execute($fields);
    }

    public function clearOldDaten(): void
    {
        $this->market_view_offer_states->clear();
    }

    private $cache_mvsrc_product_ids_alt;

    public function mvsrcProductIdAltToMvsrcProductId(string $mvsrc_product_id_alt): ?string
    {
        if ($this->cache_mvsrc_product_ids_alt === null) {
            $this->cache_mvsrc_product_ids_alt = $this->db_mv->query("
                SELECT
                    market_view_product.mvsrc_product_id_alt,
                    market_view_product.mvsrc_product_id
                FROM
                    market_view_product
                WHERE
                    market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                    market_view_product.mvsrc_product_id_alt != ''
            ")->asSingleArray('mvsrc_product_id_alt');
        }

        if (!isset($this->cache_mvsrc_product_ids_alt[$mvsrc_product_id_alt])) {
            return null;
        }

        return $this->cache_mvsrc_product_ids_alt[$mvsrc_product_id_alt];
    }


    private $cache_hersteller = [];

    /**
     * speichert den Hersteller ab
     *
     * @param string $hersteller_name
     * @return int
     */
    public function saveMvHersteller(string $hersteller_name): int
    {
        $hersteller_name = trim($hersteller_name);

        if (strlen($hersteller_name) > 96) {
            throw new LengthException('$hersteller_name länger als 96 Zeichen');
        }

        if ($hersteller_name === '') {
            return 0;
        }

        if (!isset($this->cache_hersteller[$hersteller_name])) {
            $mv_hersteller_id = $this->db_mv->fieldQuery("
                SELECT
                    IF(
                        market_view_hersteller.mapping_mv_hersteller_id > 0,
                        market_view_hersteller.mapping_mv_hersteller_id,
                        market_view_hersteller.mv_hersteller_id
                    ) AS mv_hersteller_id
                FROM
                    market_view_hersteller
                WHERE
                    market_view_hersteller.hersteller_name LIKE '" . $this->db_mv->escape($hersteller_name) . "'
            ");

            if ($mv_hersteller_id === null) {
                $this->db_mv->query("
                    INSERT INTO
                        market_view_hersteller
                    SET
                        market_view_hersteller.hersteller_name = '" . $this->db_mv->escape($hersteller_name) . "'
                ");

                $mv_hersteller_id = $this->db_mv->insert_id();
            }

            $this->cache_hersteller[$hersteller_name] = $mv_hersteller_id;
        }

        return $this->cache_hersteller[$hersteller_name];
    }

    private $cache_cats = [];

    /**
     * @param int $tree_id
     * @param string|array $cat_name
     * @param string|null $mvsrc_cat_id
     * @param bool $override_name steurt ob die Kategorienamen überschreiben werden sollen, wenn mit $mvsrc_cat_id gearbeitet wird
     * @return int $mv_cat_id
     * @see MarketViewConst::TREE_ID_DEFAULT
     *
     */
    public function saveCategory(int $tree_id, string|array $cat_name, ?string $mvsrc_cat_id = null, bool $override_name = false): int
    {
        if (is_array($cat_name)) {
            $cat_name = array_map(function ($value) {
                return trim($value);
            }, $cat_name);
            $cat_name = array_filter($cat_name);
            $cat_name = implode(' :: ', $cat_name);
        }

        if (!is_string($cat_name)) {
            throw new InvalidArgumentException('$cat_name must be of type string or array');
        }

        //leeren namen immer rauskicken -> wir behandeln das mal ohne exception, weil sonst muss das wahrscheinlich in jeden zweiten consumer extra gemacht werden. (zuviel Datenmüll)
        if ($cat_name === "") {
            return 0;
        }

        if ($mvsrc_cat_id) {
            $cache_key = 'T' . $tree_id . 'T' . $mvsrc_cat_id;
        } else {
            $cache_key = 'T' . $tree_id . 'T' . $cat_name;
        }

        if (!isset($this->cache_cats[$cache_key])) {
            if ($mvsrc_cat_id) {
                $mv_cat_id = $this->db_mv->fieldQuery("
                    SELECT
                        market_view_cat.mv_cat_id
                    FROM
                        market_view_cat
                    WHERE
                        market_view_cat.mvsrc_cat_id = '" . $this->db_mv->escape($mvsrc_cat_id) . "' AND
                        market_view_cat.mvsrc_id = '$this->mvsrc_id' AND
                        market_view_cat.tree_id = '" . $tree_id . "'
                ");

                if (!$mv_cat_id) {
                    //fallback suche über den namen... wemm der name in dem tree existiert, die id nachpflegen. erlaubt es, nachträglich in einem import auf die ids umzuschwenken
                    $mv_cat_id = $this->db_mv->fieldQuery("
                        SELECT
                            market_view_cat.mv_cat_id
                        FROM
                            market_view_cat
                        WHERE
                            market_view_cat.mvsrc_cat_name LIKE '" . $this->db_mv->escape($cat_name) . "' AND
                            market_view_cat.mvsrc_id = '" . $this->mvsrc_id . "' AND
                            market_view_cat.tree_id = '" . $tree_id . "' AND
                            market_view_cat.mvsrc_cat_id = ''
                    ");

                    if ($mv_cat_id) {
                        $this->db_mv->query("
                            UPDATE
                                market_view_cat
                            SET
                                market_view_cat.mvsrc_cat_id = '" . $this->db_mv->escape($mvsrc_cat_id) . "'
                            WHERE
                                market_view_cat.mv_cat_id = " . $mv_cat_id . "
                        ");
                    } else {
                        $this->db_mv->query("
                            INSERT INTO
                                market_view_cat
                            SET
                                market_view_cat.mvsrc_id = '" . $this->mvsrc_id . "',
                                market_view_cat.tree_id = '" . $tree_id . "',
                                market_view_cat.mvsrc_cat_name = '" . $this->db_mv->escape($cat_name) . "',
                                market_view_cat.mvsrc_cat_id = '" . $this->db_mv->escape($mvsrc_cat_id) . "'
                        ");

                        $mv_cat_id = $this->db_mv->insert_id();
                    }
                } elseif ($override_name) {
                    $this->db_mv->query("
                        UPDATE
                            market_view_cat
                        SET
                            market_view_cat.mvsrc_cat_name = '" . $this->db_mv->escape($cat_name) . "'
                        WHERE
                            market_view_cat.mv_cat_id = " . $mv_cat_id . "
                    ");
                }
            } else {
                $mv_cat_id = $this->db_mv->fieldQuery("
                    SELECT
                        market_view_cat.mv_cat_id
                    FROM
                        market_view_cat
                    WHERE
                        market_view_cat.mvsrc_cat_name LIKE '" . $this->db_mv->escape($cat_name) . "' AND
                        market_view_cat.mvsrc_id = '" . $this->mvsrc_id . "' AND
                        market_view_cat.tree_id = '" . $tree_id . "'
                ");

                if (!$mv_cat_id) {
                    $this->db_mv->query("
                        INSERT INTO
                            market_view_cat
                        SET
                            market_view_cat.mvsrc_id = '" . $this->mvsrc_id . "',
                            market_view_cat.tree_id = '" . $tree_id . "',
                            market_view_cat.mvsrc_cat_name = '" . $this->db_mv->escape($cat_name) . "'
                    ");

                    $mv_cat_id = $this->db_mv->insert_id();
                }
            }

            $this->cache_cats[$cache_key] = $mv_cat_id;
        }

        return $this->cache_cats[$cache_key];
    }


    private $cache_availabilities = null;

    /**
     * speichert die mv_availability und gibt die availability zurück
     *
     * @param string $availability
     * @return int $mv_availability
     */
    public function saveMvAvailability(string $availability): int
    {
        if ($this->cache_availabilities === null) {
            $this->cache_availabilities = $this->db_mv->query("
                SELECT
                    market_view_availability_mapping.mv_availability,
                    market_view_availability_mapping.availability
                FROM
                    market_view_availability_mapping
                WHERE
                    market_view_availability_mapping.mvsrc_id = '" . $this->mvsrc_id . "'
            ")->asSingleArray('mv_availability');
        }

        if (!isset($this->cache_availabilities[$availability])) {
            $this->db_mv->query("
                INSERT INTO
                    market_view_availability_mapping
                SET
                    market_view_availability_mapping.mvsrc_id = '" . $this->mvsrc_id . "',
                    market_view_availability_mapping.mv_availability = '" . $this->db_mv->escape($availability) . "'
            ");

            //aus der datenbank auslesen, damit der standardwert nicht doppelt gepflegt werden muss
            $this->cache_availabilities[$availability] = $this->db_mv->fieldQuery("
                SELECT
                    market_view_availability_mapping.availability
                FROM
                    market_view_availability_mapping
                WHERE
                    market_view_availability_mapping.mvsrc_id = '" . $this->mvsrc_id . "' AND
                    market_view_availability_mapping.mv_availability = '" . $this->db_mv->escape($availability) . "'
            ");
        }

        return $this->cache_availabilities[$availability];
    }

    /**
     * ermittelt für einen Bestand die $mv_availability
     *
     * @param int $inventory
     * @return int $mv_availability
     */
    public function saveInventory(int $inventory): int
    {
        if ($inventory >= 5) {
            return MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE;
        }
        if ($inventory > 0) {
            return MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE;
        }

        return MarketViewConst::AVAILABILITY_ID_KEIN_BETAND;
    }

    /**
     * @param DateObj $date
     * @return int $mv_availability
     */
    public function saveAvailabilityDate(DateObj $date): int
    {
        if (!$date->isValid()) {
            return MarketViewConst::AVAILABILITY_ID_KEIN_BETAND;
        }

        $days = $date->getDifToNow('workingdays5');
        $days *= -1;

        if ($days <= 4) {
            return MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE;
        }
        if ($days <= 6) {
            return MarketViewConst::AVAILABILITY_ID_5_DAYS;
        }
        if ($days <= 10) {
            return MarketViewConst::AVAILABILITY_ID_10_DAYS;
        }
        if ($days <= 15) {
            return MarketViewConst::AVAILABILITY_ID_15_DAYS;
        }

        return MarketViewConst::AVAILABILITY_ID_MORE_THEN_15_DAYS;
    }

    /**
     * @param DateObj $date
     * @return int $mv_availability
     */
    public function saveAvailabilityDatePessimistic(DateObj $date): int
    {
        $availability_id = $this->saveAvailabilityDate($date);

        if ($availability_id === 20) {
            $availability_id = 31;
        }

        return $availability_id;
    }

    public function productExists(string $mvsrc_product_id): bool
    {
        return $this->market_view_offer_states->getExistingOffer($mvsrc_product_id) !== null;
    }

    public function productExistsAndIsNotOffline(string $mvsrc_product_id): bool
    {
        $offer = $this->market_view_offer_states->getExistingOffer($mvsrc_product_id);
        if (!$offer) {
            return false;
        }

        return $offer->getAvailabilityId() !== MarketViewConst::AVAILABILITY_ID_OFFLINE;
    }

    /**
     * @param string $mvsrc_product_id
     * @param MarketViewProductRelation $relation
     */
    public function saveProductRelation(string $mvsrc_product_id, MarketViewProductRelation $relation): void
    {
        $this->db_mv->query("
            INSERT INTO
                market_view_product_relation_struct
            SET
                market_view_product_relation_struct.mvsrc_id = " . $this->db_mv->quote($this->mvsrc_id) . ",
                market_view_product_relation_struct.mvsrc_product_id = " . $this->db_mv->quote($mvsrc_product_id) . ",
                market_view_product_relation_struct.relations = " . $this->db_mv->quote($relation->serialize()) . "
            ON DUPLICATE KEY UPDATE
                market_view_product_relation_struct.relations = VALUES(market_view_product_relation_struct.relations)
        ");
    }

    public function getMvsrcId(): int
    {
        return $this->mvsrc_id;
    }
}
