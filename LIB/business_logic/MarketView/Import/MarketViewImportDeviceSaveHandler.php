<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;
use bqp\Exceptions\FatalException;
use wws\MarketView\Entities\MarketViewDevice;

class MarketViewImportDeviceSaveHandler
{
    private MarketViewImportSimple $market_view_import;
    private db_generic $db_mv;
    private ExtendedInsertQuery $statement;
    private ExtendedInsertQuery $statement_media;
    private int $mvsrc_id;
    private array $seen_devices = [];
    private array $devices_online_before = [];

    public function __construct(MarketViewImportSimple $market_view_import, db_generic $db_mv)
    {
        $this->market_view_import = $market_view_import;
        $this->db_mv = $db_mv;

        $this->mvsrc_id = $this->market_view_import->getMvsrcId();

        //das speichern ist jetzt erstmal relativ unsmart
        $this->statement = new ExtendedInsertQuery($this->db_mv, 'market_view_device', ['mvsrc_id', 'mvsrc_device_id', 'mv_hersteller_id', 'mv_cat_id', 'mv_cat_id_2', 'model_name', 'code1', 'code2', 'code3', 'code4', 'code5']);
        $this->statement->setAutoUpdate(true);
        $this->statement->setAutoexecute(1000);

        $this->statement_media = new ExtendedInsertQuery($this->db_mv, 'market_view_device_media', ['mvsrc_id', 'mvsrc_device_id', 'url', 'description']);
        $this->statement_media->setAutoUpdate(true);
        $this->statement_media->setAutoexecute(1000);
    }

    /**
     * bereitet ein vollupdate vor device_status online/offline
     * @return void
     */
    public function prepareFullUpdate(): void
    {
        $devices = $this->db_mv->query("
            SELECT
                market_view_device.mvsrc_device_id
            FROM
                market_view_device
            WHERE
                market_view_device.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_device.device_status = '" . MarketViewDevice::DEVICE_STATUS_ONLINE . "'
        ");

        $this->devices_online_before = [];

        foreach ($devices as $device) {
            $this->devices_online_before[$device['mvsrc_device_id']] = true;
        }
    }

    /**
     * beendet ein vollupdate und markiert die nicht gesehenen entität als offline.
     * ACHTUNG: prepareFullUpdate() muss vor dem import aufgerufen werden
     * @return void
     */
    public function endFullUpdate(): void
    {
        $this->end();

        if (!$this->devices_online_before) {
            return;
        }

        $mvsrc_device_ids = array_keys($this->devices_online_before);

        foreach (array_chunk($mvsrc_device_ids, 5000) as $mvsrc_device_ids_chunk) {
            $this->db_mv->query("
                UPDATE
                    market_view_device
                SET
                    market_view_device.device_status = '" . MarketViewDevice::DEVICE_STATUS_OFFLINE . "'
                WHERE
                    market_view_device.mvsrc_id = '" . $this->mvsrc_id . "' AND
                    market_view_device.mvsrc_device_id IN (" . $this->db_mv->in($mvsrc_device_ids_chunk) . ")
            ");
        }
    }

    public function preloadKnownDevices(): void
    {
        $devices = $this->db_mv->query("
            SELECT
                market_view_device.mvsrc_device_id
            FROM
                market_view_device
            WHERE
                market_view_device.mvsrc_id = '" . $this->mvsrc_id . "'
        ");

        foreach ($devices as $device) {
            $this->seen_devices[$device['mvsrc_device_id']] = true;
        }
    }


    public function saveDevice(MarketViewDevice $device): void
    {
        if (!$device->getMvsrcDeviceId()) {
            throw new FatalException('market_view_import_device without a $mvsrc_device_id');
        }

        unset($this->devices_online_before[$device->getMvsrcDeviceId()]);

        if (isset($this->seen_devices[$device->getMvsrcDeviceId()])) {
            return;
        }

        $daten = $this->prepareDevice($device);

        $this->statement->add($daten);

        $this->seen_devices[$device->getMvsrcDeviceId()] = true;

        foreach ($device->getMedia() as $media) {
            $raw = [
                'mvsrc_id' => $this->mvsrc_id,
                'mvsrc_device_id' => $device->getMvsrcDeviceId(),
                'url' => $media->getUrl(),
                'description' => $media->getDescription()
            ];

            $this->statement_media->add($raw);
        }
    }

    public function prepareDevice(MarketViewDevice $device): array
    {
        $daten = [
            'mvsrc_id' => $this->mvsrc_id,
            'mvsrc_device_id' => $device->getMvsrcDeviceId(),
            'device_status' => 'online',
            'mv_hersteller_id' => $device->getMvHerstellerId(),
            'mv_cat_id' => 0,
            'mv_cat_id_2' => 0,
            'model_name' => $device->getModelName(),
            'code1' => '',
            'code2' => '',
            'code3' => '',
            'code4' => '',
            'code5' => '',
            'url' => ''
        ];

        if ($device->getMvCatId()) {
            $daten['mv_cat_id'] = $device->getMvCatId();
        }

        if ($device->getMvCatId2()) {
            $daten['mv_cat_id_2'] = $device->getMvCatId2();
        }

        if ($device->getCode1()) {
            $daten['code1'] = $device->getCode1();
        }
        if ($device->getCode2()) {
            $daten['code2'] = $device->getCode2();
        }
        if ($device->getCode3()) {
            $daten['code3'] = $device->getCode3();
        }
        if ($device->getCode4()) {
            $daten['code4'] = $device->getCode4();
        }
        if ($device->getCode5()) {
            $daten['code5'] = $device->getCode5();
        }
        if ($device->getUrl()) {
            $daten['url'] = $device->getUrl();
        }

        return $daten;
    }


    public function end(): void
    {
        $this->statement->end();
        $this->statement_media->end();
    }
}
