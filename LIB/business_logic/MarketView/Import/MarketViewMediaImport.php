<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use bqp\storage\storage_mount_manager;
use ErrorException;
use InvalidArgumentException;
use service_loader;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\MarketViewMediaCollector;
use wws\MarketView\MarketViewMediaImageHtmlException;
use wws\MarketView\MarketViewMediaRepository;
use wws\MarketView\MarketViewMediaStorage;

class MarketViewMediaImport
{
    private int $mvsrc_id;

    private db_generic $db_mv;

    /**
     * @depracated @see media_storage
     * @var storage_mount_manager
     */
    private storage_mount_manager $mv_storage;

    private MarketViewMediaRepository $mv_media_repository;

    private $upload_cache = [];
    private MarketViewMediaStorage $media_storage;


    public function __construct(db_generic $db_mv, storage_mount_manager $mv_storage, MarketViewMediaRepository $mv_media_repository, MarketViewMediaStorage $media_storage)
    {
        $this->db_mv = $db_mv;
        $this->mv_storage = $mv_storage;
        $this->mv_media_repository = $mv_media_repository;

        $this->media_storage = $media_storage;
    }

    public function setMvsrcId(int $mvsrc_id): void
    {
        $this->mvsrc_id = $mvsrc_id;
    }

    public function existsByMd5(string $mvsrc_product_id, string $md5): bool
    {
        return $this->mv_media_repository->existsMediaByMd5($this->mvsrc_id, $mvsrc_product_id, $md5);
    }

    /**
     * Prüft anhand (mvsrc_id, mvsrc_product_id, media_key) ob das Asset bekannt ist.
     *
     * @param MarketViewMedia $mv_media
     * @return bool
     */
    public function isMediaKnown(MarketViewMedia $mv_media): bool
    {
        $mv_media_id = $this->db_mv->fieldQuery("
            SELECT
                market_view_media.mv_media_id
            FROM
                market_view_media
            WHERE
                market_view_media.mvsrc_id = " . $mv_media->getMvsrcId() . " AND
                market_view_media.mvsrc_product_id = '" . $this->db_mv->escape($mv_media->getMvsrcProductId()) . "' AND
                market_view_media.media_key = '" . $this->db_mv->escape($mv_media->getMediaKey()) . "'
        ");

        return (bool)$mv_media_id;
    }

    /**
     * @depracated @see \wws\MarketView\MarketViewMediaStorage::upload() / achtung anderes konzept!
     *
     * @param string $url
     * @param string $raw_content
     * @return void
     */
    private function uploadDirect(string $url, string $raw_content): void
    {
        if (isset($this->upload_cache[$url])) { //uploaden falls noch nicht passiert
            return;
        }

        $this->mv_storage->put($url, $raw_content);
        $this->upload_cache[$url] = true;
    }

    public function uploadContent(MarketViewMedia $market_view_media, string $blob): void
    {
        if (!$market_view_media->getMvsrcProductId()) {
            throw new InvalidArgumentException('mvsrc_product_id is missing');
        }

        if (!$market_view_media->isMd5Set()) {
            $market_view_media->setMd5(md5($blob));
        }

        if ($market_view_media->isLocalFilename()) {
            $url_storage = $this->media_storage->upload($blob, $market_view_media->getMvsrcId());
            $market_view_media->setUrlStorage($url_storage);
        } else {
            trigger_error("upload with marketview:// urls is deprecated. use local:// instead", E_USER_DEPRECATED);
            $this->uploadDirect($market_view_media->getUrl(), $blob);
        }

        $this->saveMarketViewMedia($market_view_media);
    }

    /**
     * ACHTUNG die Funktion prüft intern, ob es das Bild schon gibt, und macht dann ggf. nix mehr damit.
     * Das ist mir grade zu unsicher das anzufassen... als Alternative hab ich erstmal uploadPictureForce().
     * Zum Zeitpunkt wo das eingeführt wurde, gabs das storage_url konzept noch nicht und auch wahrscheinlich
     * media_key noch nicht fertig.
     * @param MarketViewMedia $market_view_media
     * @param string $image_blob
     * @return void
     * @throws ErrorException
     * @throws MarketViewMediaImageHtmlException
     */
    public function uploadPicture(MarketViewMedia $market_view_media, string $image_blob): void
    {
        $hash = md5($image_blob);
        $market_view_media->setMd5($hash);

        $status = $this->existsByMd5($market_view_media->getMvsrcProductId(), $market_view_media->getMd5());

        if ($status) {
            //@todo... wenn das bild schon existiert, müssen trotzdem die metadaten gespeichert werden!

            return;
        }

        $media_collector = service_loader::get(MarketViewMediaCollector::class);
        $media_collector->fillMediaMetaDataFromBlob($market_view_media, $image_blob);

        $this->uploadContent($market_view_media, $image_blob);
    }

    public function uploadPictureForce(MarketViewMedia $market_view_media, string $image_blob): void
    {
        $hash = md5($image_blob);
        $market_view_media->setMd5($hash);

        $media_collector = service_loader::get(MarketViewMediaCollector::class);
        $media_collector->fillMediaMetaDataFromBlob($market_view_media, $image_blob);

        $this->uploadContent($market_view_media, $image_blob);
    }

    /**
     * @param MarketViewMedia $market_view_media
     */
    public function saveMarketViewMedia(MarketViewMedia $market_view_media): void
    {
        if (!$market_view_media->getMvsrcProductId()) {
            throw new InvalidArgumentException('mvsrc_product_id is missing');
        }

        $market_view_media->setMvsrcId($this->mvsrc_id);

        $this->mv_media_repository->saveMedia($market_view_media);
    }
}
