<?php

namespace wws\MarketView\Import;

use bqp\files\GzCompress;
use Exception;
use input;
use InvalidArgumentException;

class MarketViewImportUtils
{
    /**
     * @param string $src_file
     * @param string $dst_file
     * @param bool $attach_gz_extension
     * @throws Exception
     */
    public static function moveFileCompressed(string $src_file, string $dst_file, bool $attach_gz_extension = true): void
    {
        if ($attach_gz_extension && !str_ends_with($dst_file, '.gz')) {
            $dst_file .= '.gz';
        }

        GzCompress::compressFile($src_file, $dst_file);
        unlink($src_file);
    }

    /**
     * bereinigt eine übergebene EAN, fall der Wert keine gültige EAN ist, wird ein leerer String zurückgegeben.
     *
     * @param string $ean
     * @return string
     */
    public static function normalizeEan(string $ean): string
    {
        $ean = trim($ean);
        $ean = str_replace(' ', '', $ean);

        if (strlen($ean) < 13) {
            //@todo ean mit 8 Ziffern.... neh. Prüfen warum das hier ist (quelle über market_view_product suchen) und dann raus damit.
            //ggf. 12 stellen wegen upcs... aber alles andere ist bullshit
            if (strlen($ean) >= 8) { //falls weniger als 8 zeichen kicken wir die ean, ansonsten mit 0 auffüllen
                $ean = str_pad($ean, 13, '0', STR_PAD_LEFT);
            } else {
                $ean = '';
            }
        }

        if ($ean === '0000000000000' || $ean === '8888888888888') {
            $ean = '';
        }

        //offensichtlich selbst vergebene eans rauswerfen
        //eans verwerfen die mit 999999 anfangen (knoll liefert bei unbekannten eans 999999 + artikelnummer, Bauknecht verwendet 9999999999994 bei nicht vorhandene eans)
        //00000 verwendet morele
        $ean_prefix = substr($ean, 0, 6);
        if ($ean_prefix === '999999' || $ean_prefix === '000000') {
            $ean = '';
        }

        if (!input::validateEAN($ean)) {
            $ean = '';
        }

        return $ean;
    }

    public static function normalizeMvsrcProductId(string $mvsrc_product_id): string
    {
        $mvsrc_product_id = trim($mvsrc_product_id);
        $mvsrc_product_id = substr($mvsrc_product_id, 0, 32); //ggf. wird damit eine utf8 sequenz getroffen -> egal...

        return $mvsrc_product_id;
    }

    /**
     * @param string $mvsrc_product_id
     * @return void
     * @throws InvalidArgumentException
     */
    public static function validateMvsrcProductId(string $mvsrc_product_id): void
    {
        if ($mvsrc_product_id === "") {
            throw new InvalidArgumentException('$mvsrc_product_id is empty');
        }

        if (strlen($mvsrc_product_id) > 32) {
            throw new InvalidArgumentException('$mvsrc_product_id (' . $mvsrc_product_id . ') is too long');
        }

        if ($mvsrc_product_id !== self::normalizeMvsrcProductId($mvsrc_product_id)) {
            throw new InvalidArgumentException('$mvsrc_product_id (' . $mvsrc_product_id . ') is not valid');
        }
    }
}
