<?php

namespace wws\MarketView\Import;

use bqp\db\db_generic;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\MarketView\Entities\MarketViewSonstiges;

/**
 * Kann mit in MarketViewImport gegeben werden. Es werden Änderungen an ausgewählten Feldern an den Stammdaten in den
 * Angebotsdaten getrackt und ggf. die callbacks onChange/onNew mit den alten und neuen Werten aufgerufen.
 * Es können auch Änderungen einzelner Werte in features_struct und sonstiges_struct überwacht werden.
 */
class MarketViewImportDataChangeTracking
{
    private array $observe_fields = [];
    private string $observe_sql = '';
    private int $mvsrc_id;
    private db_generic $db_mv;

    private array $last_offer_states = [];


    /**
     * @var callable
     */
    private $on_new;

    /**
     * @var callable
     */
    private $on_change;

    private $batch_preload = 10;

    private $observe_sonstiges_struct = false;
    private $observe_features_struct = false;

    public function __construct(db_generic $db_mv, int $mvsrc_id)
    {
        $this->db_mv = $db_mv;
        $this->mvsrc_id = $mvsrc_id;

        $this->on_new = function () {
        };
        $this->on_change = function () {
        };
    }

    public function onNew(callable $on_new): void
    {
        $this->on_new = $on_new;
    }

    public function onChange(callable $on_change): void
    {
        $this->on_change = $on_change;
    }

    /**
     * Legt fest welche Felder aus market_view_product überwacht werden sollen.
     * Zusätzlich können Änderungen om einzelnen Schlüsseln in features_struct und sonstiges_struct getrackt werden.
     *
     * Z.B.: ['product_name', 'mv_cat_id', 'mv_cat_id_2', 'mv_hersteller_id', 'beschreibung', 'features_struct', 'sonstiges_struct.ursprungsland', 'sonstiges_struct.klassenname']
     *
     * @param array $fields
     * @return void
     */
    public function observe(array $fields): void
    {
        $this->observe_fields = $fields;

        //
        $sql_columns = [];
        foreach ($fields as $field) {
            $column = $field;

            $pos = strpos($column, '.');
            if ($pos !== false) {
                $column = substr($column, 0, $pos);

                switch ($column) {
                    case 'features_struct':
                        $this->observe_features_struct = true;
                        break;
                    case 'sonstiges_struct':
                        $this->observe_sonstiges_struct = true;
                        break;
                }
            }

            $sql_columns[$column] = "market_view_product." . $column;
        }

        $this->observe_sql = implode(',', $sql_columns);
    }

    public function preloadState(string $mvsrc_product_id): void
    {
        //kleine optimierung -> es ist wahrscheinlich das daten schon nach $mvsrc_product_id sortiert reinkommen.
        //ein select >= kostet kaum mehr als ein = auf mvsrc_product_id. Deswegen ziehen wir hier je nach batch_preload
        //ein paar mehr Daten.

        if (isset($this->last_offer_states[$mvsrc_product_id])) {
            return;
        }

        $cmp = '=';
        if ($this->batch_preload > 1) {
            $cmp = '>=';
        }

        $this->last_offer_states = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id,
                " . $this->observe_sql . "
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_product.mvsrc_product_id " . $cmp . " '" . $this->db_mv->escape($mvsrc_product_id) . "'
            LIMIT
                " . $this->batch_preload . "
        ")->asArray('mvsrc_product_id');
    }

    private function getState(string $mvsrc_product_id): ?array
    {
        return $this->last_offer_states[$mvsrc_product_id] ?? null;
    }

    public function compareState(string $mvsrc_product_id, array $current_state): void
    {
        $old_state = $this->getState($mvsrc_product_id);

        if (!$old_state) {
            call_user_func($this->on_new, $this->mvsrc_id, $mvsrc_product_id, $current_state);
            return;
        }

        $differences = [];

        foreach ($this->observe_fields as $field) {
            if (!array_key_exists($field, $current_state)) {
                continue;
            }

            if (!$this->compare($old_state[$field], $current_state[$field])) {
                $differences[$field] = ['new' => $current_state[$field], 'old' => $old_state[$field]];
            }
        }

        //special
        if ($this->observe_features_struct && isset($old_state['features_struct']) && isset($current_state['features_struct'])) {
            $old_features = MarketViewFeatures::loadSerialized($old_state['features_struct']);
            $new_features = MarketViewFeatures::loadSerialized($current_state['features_struct']);

            foreach ($this->observe_fields as $field) {
                if (str_starts_with($field, 'features_struct.')) {
                    $key = substr($field, 16);
                    if ($old_features->getFeatureValue($key) != $new_features->getFeatureValue($key)) {
                        $differences['features_struct.' . $key] = ['new' => $new_features->getFeatureValue($key), 'old' => $old_features->getFeatureValue($key)];
                    }
                }
            }
        }

        //features
        if ($this->observe_sonstiges_struct && isset($old_state['sonstiges_struct']) && isset($current_state['sonstiges_struct'])) {
            $old_sonstiges = MarketViewSonstiges::loadSerialized($old_state['sonstiges_struct']);
            $new_sonstiges = MarketViewSonstiges::loadSerialized($current_state['sonstiges_struct']);

            foreach ($this->observe_fields as $field) {
                if (str_starts_with($field, 'sonstiges_struct.')) {
                    $key = substr($field, 17);
                    if ($old_sonstiges->getValue($key) != $new_sonstiges->getValue($key)) {
                        $differences['sonstiges_struct.' . $key] = ['new' => $new_sonstiges->getValue($key), 'old' => $old_sonstiges->getValue($key)];
                    }
                }
            }
        }
        //

        if ($differences) {
            call_user_func($this->on_change, $this->mvsrc_id, $mvsrc_product_id, $differences, $current_state);
        }
    }

    private function compare($old, $new): bool
    {
        if ($old === $new) {
            return true;
        }

        //0, '0', null, '' ->erstmal egal

        if ($old == $new) {
            return true;
        }

        return false;
    }
}
