<?php

namespace wws\MarketView\Import;

class MarketViewImportExistingOffer
{
    private int $availability_id;
    private float $vk_netto;

    public function __construct(int $availability_id, float $vk_netto)
    {
        $this->availability_id = $availability_id;
        $this->vk_netto = $vk_netto;
    }

    public function getAvailabilityId(): int
    {
        return $this->availability_id;
    }

    public function getVkNetto(): float
    {
        return $this->vk_netto;
    }
}
