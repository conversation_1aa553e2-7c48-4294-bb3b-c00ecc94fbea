<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use service_loader;
use wws\Product\ProductConst;
use wws\ProductDevice\ProductDeviceRepository;

/**
 * hilfsklasse für das matching von market_view_device mit product_device
 *
 * @package wws\market_view
 */
class MarketViewDeviceMatching
{
    protected db_generic $db_mv;
    private int $cat_tree_id;
    private ProductDeviceRepository $device_repository;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
        $this->cat_tree_id = ProductConst::CAT_TREE_ID_3;
        $this->device_repository = service_loader::getDiContainer()->get(ProductDeviceRepository::class);
    }

    public function addMatching(int $mvsrc_id, string $mvsrc_device_id, int $device_id): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_device
            SET
                market_view_device.device_id = " . $this->db_mv->quote($device_id) . "
            WHERE
                market_view_device.mvsrc_id = " . $this->db_mv->quote($mvsrc_id) . " AND
                market_view_device.mvsrc_device_id = " . $this->db_mv->quote($mvsrc_device_id) . "
        ");
    }

    /**
     * gibt alle $mv_hersteller_ids zurück die auf $hersteller_ids gematcht sind
     */
    public function getMvHerstellerIdsForBrandIds(array $brand_ids): array
    {
        return $this->db_mv->query("
            SELECT
                market_view_hersteller.mv_hersteller_id
            FROM
                market_view_hersteller
            WHERE
                market_view_hersteller.brand_id IN (" . $this->db_mv->makeIn($brand_ids) . ")
        ")->asSingleArray();
    }


    /**
     * gibt die brand_id für eine mv_hersteller_id zurück
     *
     * @param int $mv_hersteller_id
     * @return int|null
     */
    public function getBrandId(int $mv_hersteller_id): ?int
    {
        static $cache = [];

        if (!array_key_exists($mv_hersteller_id, $cache)) {
            $brand_id = $this->db_mv->fieldQuery("
                SELECT
                    market_view_hersteller.brand_id
                FROM
                    market_view_hersteller
                WHERE
                    market_view_hersteller.mv_hersteller_id = " . $mv_hersteller_id . "
            ");

            $cache[$mv_hersteller_id] = $brand_id;
        }

        return $cache[$mv_hersteller_id];
    }

    /**
     * Sucht anhand der Market View Kategorien eine WWS Kategorie. Gibt das in Form von detail_cat_id und main_cat_id zurück.
     * @param int $mv_cat_id
     * @param int $mv_cat_id_2
     * @return null|array{ detail_cat_id: int, main_cat_id: int }
     */
    public function getCatIds(int $mv_cat_id, int $mv_cat_id_2): ?array
    {
        static $cache = [];

        $mv_cat_ids = [];
        if ($mv_cat_id) {
            $mv_cat_ids[] = $mv_cat_id;
        }
        if ($mv_cat_id_2) {
            $mv_cat_ids[] = $mv_cat_id_2;
        }

        if (!$mv_cat_ids) {
            return null;
        }

        $cat_ids = [];

        foreach ($mv_cat_ids as $act_mv_cat_id) {
            if (!array_key_exists($act_mv_cat_id, $cache)) {
                $cache[$act_mv_cat_id] = $this->db_mv->fieldQuery("
                    SELECT
                        market_view_cat_mapping.cat_id
                    FROM
                        market_view_cat_mapping
                    WHERE
                        market_view_cat_mapping.mv_cat_id = " . $act_mv_cat_id . " AND
                        market_view_cat_mapping.cat_tree_id = " . $this->cat_tree_id . "
                ");
            }

            if ($cache[$act_mv_cat_id]) {
                $cat_ids[] = $cache[$act_mv_cat_id];
            }
        }

        if (!$cat_ids) {
            return null;
        }

        $cat_ids = array_unique($cat_ids);

        $possible_results = [];

        foreach ($cat_ids as $cat_id) {
            $possible_results[] = ['detail_cat_id' => $cat_id, 'main_cat_id' => $this->device_repository->getMainCatId($cat_id)];
        }

        foreach ($possible_results as $possible_result) {
            if ($possible_result['detail_cat_id'] != $possible_result['main_cat_id']) {
                return $possible_result;
            }
        }

        return current($possible_results);
    }
}
