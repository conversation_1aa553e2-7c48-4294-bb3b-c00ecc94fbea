<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Http\HttpUtils;
use bqp\Model\SmartDataEntityNotFoundException;
use debug;
use ErrorException;
use Exception;
use service_loader;
use Throwable;
use wws\MarketView\Entities\MarketViewMedia;

/**
 * Vervollständigt Metadaten von Assets/Media und lädt die Assets ggf. in ein lokalen Storage
 */
class MarketViewMediaCollector
{

    private MarketViewMediaRepository $media_repository;
    private MarketViewMediaCache $media_cache;
    private MarketViewMediaStorage $media_storage;
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv, MarketViewMediaCache $media_cache, MarketViewMediaStorage $media_storage, MarketViewMediaRepository $media_repository)
    {
        $this->media_cache = $media_cache;
        $this->media_storage = $media_storage;
        $this->media_repository = $media_repository;


        $this->db_mv = $db_mv;
    }


    public function run(): void
    {
        $this->fillQueue();
        $this->runQueue();
    }


    public function fillQueue(): void
    {
        $this->db_mv->query("
            INSERT INTO
                market_view_media_queue (mv_media_id, date_added)
            SELECT
                market_view_media.mv_media_id,
                NOW()
            FROM
                market_view_media INNER JOIN
                market_view_source ON (market_view_media.mvsrc_id = market_view_source.mvsrc_id) LEFT JOIN
                market_view_media_queue ON (market_view_media.mv_media_id = market_view_media_queue.mv_media_id)
            WHERE
                market_view_media_queue.mv_media_id IS NULL AND
                market_view_media.url_storage = '' AND
                market_view_media.media_type IN ('" . MarketViewMedia::MEDIA_TYPE_IMAGE_URL . "', '" . MarketViewMedia::MEDIA_TYPE_IMAGE . "') AND
                market_view_media.media_status != '" . MarketViewMedia::MEDIA_STATUS_MISSING . "'
        ");
    }

    public function runQueue(): void
    {
        $queue_list = $this->db_mv->query("
            SELECT
                market_view_media_queue.mv_media_id
            FROM
                market_view_media_queue
            WHERE
                market_view_media_queue.date_last IS NULL OR
                (
                    market_view_media_queue.date_last < NOW() - INTERVAL FLOOR(market_view_media_queue.error_count/3) DAY AND
                    RAND() > 0.5
                )
            ORDER BY
                market_view_media_queue.date_last IS NOT NULL,
                RAND()
            LIMIT
                20000
        ")->asArray();
        //Fehlgeschlagene Einträge zeitlich in die Länge ziehen.
        //Nach 16 Fehlern wird der Eintrag entfernt. Alle 3 Fehler verlängern wir den Zeitraum bevor es neuer Versuch unternommen wird um einen Tag.
        //Das wäre insgesamt ein Zeitraum von 62 Tagen. Durch das rand() machen wir aber nur einen neuen Versuch alle 2 Tage im Schnitt.
        //Sprich nach 2 bis 4 Monate ist jedes Asset durch.

        $this->processQueueList($queue_list);
    }

    public function runQueueNewItems(): void
    {
        $queue_list = $this->db_mv->query("
            SELECT
                market_view_media_queue.mv_media_id
            FROM
                market_view_media_queue
            WHERE
                market_view_media_queue.error_count = 0
            ORDER BY
                RAND()
            LIMIT
                20000
        ")->asArray();

        $this->processQueueList($queue_list);
    }

    private function processQueueList(array $queue_list): void
    {
        foreach ($queue_list as $row) {
            $mv_media = $this->media_repository->loadMedia($row['mv_media_id']);

            if ($mv_media->getUrlStorage()) {
                //zum beobachten...
                debug::dump('url storage ist gesetzt, obwohl in queue... kann eigentlich nur vorkommen, wenn concurrency issues auftreten.', $mv_media);
                continue;
            }

            //echo $mv_media->getMvMediaId().' '.$mv_media->getUrl()."\n";

            try {
                $blob = $this->fetchBlob($mv_media);
            } catch (Throwable $e) {
                $this->handleError($mv_media, $e);
                continue;
            }

            try {
                $this->fillMediaMetaDataFromBlob($mv_media, $blob);
            } catch (Throwable $e) {
                debug::dump($mv_media);
                debug::dumpException($e);
                continue;
            }

            $url_storage = $this->media_storage->upload($blob, $mv_media->getMvsrcId());
            $mv_media->setUrlStorage($url_storage);

            $this->media_repository->saveMedia($mv_media);

            $this->removeFromQueue($mv_media->getMvMediaId());
        }
    }

    private function handleError(MarketViewMedia $mv_media, Throwable $e): void
    {
        $act_queue_data = $this->db_mv->singleQuery("
            SELECT
                market_view_media_queue.mv_media_id,
                market_view_media_queue.error_count,
                market_view_media_queue.date_last,
                market_view_media_queue.error_count
            FROM
                market_view_media_queue
            WHERE
                market_view_media_queue.mv_media_id = " . $mv_media->getMvMediaId() . "
        ");

        if (!$act_queue_data) {
            throw new SmartDataEntityNotFoundException('market_view_media_queue entry not found (mv_media_id: ' . $mv_media->getMvMediaId() . ')');
        }

        $error_count = (int)$act_queue_data['error_count'];

        if ($error_count > 16) { //entität als missing markieren und aus queue entfernen
            $mv_media->setMediaStatus(MarketViewMedia::MEDIA_STATUS_MISSING);
            $this->media_repository->saveMedia($mv_media);
            $this->removeFromQueue($mv_media->getMvMediaId());
            return;
        }

        $error_count++;

        $error_message = get_class($e) . ': ' . $e->getMessage();

        $this->db_mv->query("
            UPDATE
                market_view_media_queue
            SET
                market_view_media_queue.date_last = NOW(),
                market_view_media_queue.error_count = " . $error_count . ",
                market_view_media_queue.error_last = '" . $this->db_mv->escape($error_message) . "'
            WHERE
                market_view_media_queue.mv_media_id = " . $mv_media->getMvMediaId() . "
        ");

        //@todo raus damit -> derzeit nur um ein Gefühl für die Probleme zu bekommen
        //        \debug::dump($mv_media);
        //        \debug::dumpException($e);
    }


    private function removeFromQueue(int $mv_media_id): void
    {
        $this->db_mv->query("
            DELETE FROM
                market_view_media_queue
            WHERE
                market_view_media_queue.mv_media_id = " . $mv_media_id
        );
    }

    private function fetchBlob(MarketViewMedia $mv_media): string
    {
        //@todo Achtung... das ist eine kompromiss lösung, die in der form nicht mehr funktioniert, wenn content neu geladen werden soll / abgleich ob sich inhalte geändert haben
        //da das collector modul da aber derzeit so oder so nicht drauf ausgelegt ist -> erstmal egal.
        if ($mv_media->getUrlStorage() && str_starts_with($mv_media->getUrlStorage(), 'marketview://')) {
            return $this->media_storage->get($mv_media->getUrlStorage());
        }

        if (str_starts_with($mv_media->getUrl(), 'marketview://')) {
            $url = $this->media_cache->buildUrl($mv_media->getMvMediaId(), $mv_media->getUrl());
        } else {
            $url = $mv_media->getUrl();
        }

        $blob = self::fetchExternalUrlAsBlob($url);

        if ($this->looksLikeHtml($blob)) { //Annahme: HTML wird hier nie erwartet -> nicht sauber signalisierte HTTP 4xx/5xx
            throw new MarketViewMediaImageHtmlException();
        }

        return $blob;
    }

    public static function fetchExternalUrlAsBlob(string $url): string
    {
        $opts = [
            "http" => [
                'timeout' => 5,
                'method' => 'GET',
                'header' => 'User-Agent: ' . HttpUtils::getRandomUserAgent() . "\r\n"
            ]
        ];

        //besonderheiten je nach zielhost
        //aus vagrant kann der hostname nicht aufgelöst werden und der port wäre auch falsch -> weiter aufs live system
        $url = str_replace('http://wws.ecom.local:8080/', 'https://wws.ecom-dresden.de/', $url);


        if (str_starts_with($url, 'https://assets.electronicpartner.io')) {
            $config = service_loader::getConfigRegistry()->get('ecom/supplier/ep');

            $opts['http']['header'] .= 'Authorization: Basic ' . base64_encode($config->getString('xml_user') . ':' . $config->getString('xml_password')) . "\r\n";
        }
        //

        $context = stream_context_create($opts);

        $blob = file_get_contents($url, false, $context);

        //file_get_contents ist dreck -> da kommt auch bei dns fehler "" zurück. Leere rückgaben werden jetzt auch erstmal als Fehler gewertet.
        //@todo das ganze auf laminas migrieren...
        if (!$blob) {
            throw new Exception('fehler bei fetchen von media');
        }

        return $blob;
    }

//
//    public function completeMissingFingerprints(): void
//    {
//        while (true) {
//            $result = $this->db_mv->query("
//                SELECT
//                    market_view_media.mv_media_id
//                FROM
//                    market_view_media
//                WHERE
//                    market_view_media.media_type = '".\wws\MarketView\Entities\MarketViewMedia::MEDIA_TYPE_IMAGE_URL."' AND
//                    market_view_media.fingerprint = ''
//                ORDER BY
//                    RAND()
//                LIMIT
//                    10
//            ");
//
//            if ($result->count() == 0) {
//                break;
//            }
//
//            $mv_medias = $this->media_repository->loadMediaByMvMediaIds($result->asSingleArray());
//
//            foreach ($mv_medias as $mv_media) {
//                $this->fillMediaMetaData($mv_media);
//
//                $this->media_repository->saveMedia($mv_media);
//            }
//        }
//    }

    public function fillMediaMetaData(MarketViewMedia $mv_media): void
    {
        $blob = $this->fetchBlob($mv_media);

        $this->fillMediaMetaDataFromBlob($mv_media, $blob);
    }

    public function fillMediaMetaDataFromBlob(MarketViewMedia $mv_media, string $blob): void
    {
        //nochmal sicherstelle das keine "Bilder" mit HTML im System landen... das ist auch nochmal im fetch drin, aber fetch muss nicht zwingend genutzt werden
        if (in_array($mv_media->getMediaType(), [$mv_media::MEDIA_TYPE_IMAGE, $mv_media::MEDIA_TYPE_IMAGE_URL])) {
            if ($this->looksLikeHtml($blob)) {
                throw new MarketViewMediaImageHtmlException();
            }
        }

        try {
            $meta_data = MarketViewMediaUtils::getMetaDataByRawImage($blob);

            $mv_media->setMd5($meta_data['md5']);
            $mv_media->setWidth($meta_data['width']);
            $mv_media->setHeight($meta_data['height']);
            $mv_media->setFingerprint($meta_data['fingerprint']);
        } catch (ErrorException $e) {
            if (strpos($e->getMessage(), 'Data is not in a recognized format')) {
                throw new Exception('Data is not in a recognized format ' . debug::hexDumpReturn(substr($blob, 0, 128)));
            }
            throw $e;
        }
    }

    private function looksLikeHtml(string $value): bool
    {
        $pos = strpos($value, '!DOCTYPE');
        $is_html = $pos !== false && $pos < 20;

        if ($is_html) {
            return true;
        }

        $pos = strpos($value, 'html');
        return $pos !== false && $pos < 20;
    }
}
