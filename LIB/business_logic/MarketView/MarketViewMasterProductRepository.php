<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;
use service_loader;
use wws\MarketView\Entities\MarketViewMasterProduct;
use wws\MarketView\Entities\MarketViewMedia;

class MarketViewMasterProductRepository
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }


    public function loadMasterProduct(int $mv_master_product_id): MarketViewMasterProduct
    {
        $row = $this->db_mv->singleQuery("
            SELECT
                market_view_master_product.mv_master_product_id,
                market_view_master_product.product_id,
                market_view_master_product.product_type,
                market_view_master_product.mv_master_cat_id,
                market_view_master_product.ean,
                market_view_master_product.mpn,
                market_view_master_product.product_name,
                market_view_master_product.product_name_src,
                market_view_master_product.mv_hersteller_id,
                market_view_master_product.sources_online,
                market_view_master_product.sources_available
            FROM
                market_view_master_product
            WHERE
                market_view_master_product.mv_master_product_id = $mv_master_product_id                
        ");

        if (!$row) {
            throw new SmartDataEntityNotFoundException();
        }

        $entity = new MarketViewMasterProduct();
        $entity->getSmartDataObj()->loadDaten($row);

        return $entity;
    }

    public function searchPrimaryMarketViewMedia(int $mv_master_product_id): ?MarketViewMedia
    {
        $mv_media_id = $this->db_mv->fieldQuery("
			SELECT
				market_view_media.mv_media_id
			FROM
				market_view_product INNER JOIN
				market_view_media ON (market_view_product.mvsrc_id = market_view_media.mvsrc_id AND market_view_product.mvsrc_product_id = market_view_media.mvsrc_product_id)
			WHERE
				market_view_product.mv_master_product_id = $mv_master_product_id AND
				market_view_media.media_type = '" . MarketViewMedia::MEDIA_TYPE_IMAGE_URL . "' AND
				market_view_media.topic != '" . MarketViewMedia::MEDIA_TOPIC_ICON . "'
			LIMIT
				1
		");

        if (!$mv_media_id) {
            return null;
        }

        return current(service_loader::get(MarketViewMediaRepository::class)->loadMediaByMvMediaIds([$mv_media_id]));
    }

    public function searchPrimaryMarketViewMediaThumbnailUrl(int $mv_master_product_id): ?string
    {
        $market_view_media = $this->searchPrimaryMarketViewMedia($mv_master_product_id);

        if (!$market_view_media) {
            return null;
        }

        $media_cache = service_loader::getDiContainer()->get(MarketViewMediaCache::class);

        return $media_cache->buildThumbnailUrl($market_view_media->getMvsrcId(), $market_view_media->getUrlStorage() ?: $market_view_media->getUrl());
    }
}