<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\Product\ProductFeature\ProductFeature;
use wws\Product\ProductFeature\ProductFeatureValues;

class MarketViewFeaturesMapper
{
    protected $mv_features = [];

    protected $features = [];
    protected $warnings = [];

    protected db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    public function addMvFeatures(int $mvsrc_id, MarketViewFeatures $mv_features): void
    {
        $this->mv_features[$mvsrc_id] = $mv_features;
    }

    public function setProductFeatureValues(ProductFeatureValues $feature_values): void
    {
        foreach ($feature_values->getFeatures() as $feature) {
            $this->features[] = $feature['ProductFeature'];
        }
    }

    protected function getMappingsForFeatures(): array
    {
        $feature_ids = $this->getFeatureIds();

        $mapping = $this->db_mv->query("
            SELECT
                market_view_features_mapping.feature_id,
                market_view_features_mapping.mvsrc_id,
                market_view_features_mapping.mvsrc_feature_id
            FROM
                market_view_features_mapping
            WHERE
                market_view_features_mapping.feature_id IN (" . $this->db_mv->makeIn($feature_ids) . ") AND
                market_view_features_mapping.mvsrc_id IN (" . $this->db_mv->makeIn($this->getMvsrcIds()) . ")
        ")->asMultiArray('feature_id');

        return $mapping;
    }

    protected function getMvsrcIds(): array
    {
        return array_keys($this->mv_features);
    }

    protected function getFeatureIds(): array
    {
        $feature_ids = [];

        foreach ($this->features as $feature) {
            $feature_ids[] = $feature->getFeatureId();
        }

        return $feature_ids;
    }

    public function test(): array
    {
        $mappings = $this->getMappingsForFeatures();

        $values = [];

        foreach ($mappings as $feature_id => $f_mapping) {
            $values[$feature_id] = [];
            foreach ($f_mapping as $mapping) {
                $value = $this->searchFeature($mapping['mvsrc_id'], $mapping['mvsrc_feature_id']);
                if ($value !== null) {
                    $values[$feature_id][] = $value;
                }
            }
        }

        $processed_values = [];

        foreach ($this->features as $feature) {
            $feature_id = $feature->getFeatureId();

            if (isset($values[$feature_id])) {
                $processed_values[$feature_id] = $this->processValues($feature, $values[$feature_id]);
            }
        }

        return $processed_values;
    }

    protected function searchFeature(int $mvsrc_id, string $mvsrc_feature_id): ?array
    {
        if (!isset($this->mv_features[$mvsrc_id])) {
            return null;
        }

        $feature = $this->mv_features[$mvsrc_id]->getFeature($mvsrc_feature_id);

        $feature['mvsrc_id'] = $mvsrc_id;

        return $feature;
    }

    protected function processValues(ProductFeature $feature, $values)
    {
        foreach ($values as $key => $daten) {
            $values[$key] = $this->convertValues($daten);
        }


        $raw_value_c = $values[0];
        $raw_value = $raw_value_c['value'];

        $result = null;

        switch ($feature->getType()) {
            case 'text':
            case 'text_long':
                $result = $raw_value;
                break;
            case 'bit':
                if ($this->isValueTrue($raw_value)) {
                    return 1;
                }
                if ($this->isValueFalse($raw_value)) {
                    return 0;
                }

                $matched_value = $feature->matchValueAgainstMatchValues($raw_value);

                if ($this->isValueTrue($matched_value)) {
                    return 1;
                }
                if ($this->isValueFalse($matched_value)) {
                    return 0;
                }

                $this->warnings[] = $feature->getFeatureId() . ' ' . $feature->getFeatureName() . ' BIT: unknown value "' . $raw_value . '"';

                break;
            case 'enum':
                foreach ($feature->getEnumValues() as $value) {
                    if ($value == $raw_value) {
                        return $value;
                    }
                }

                $matched_value = $feature->matchValueAgainstMatchValues($raw_value);

                foreach ($feature->getEnumValues() as $value) {
                    if ($value == $matched_value) {
                        return $value;
                    }
                }

                $this->warnings[] = $feature->getFeatureId() . ' ' . $feature->getFeatureName() . ' ENUM: unknown value "' . $raw_value . '"';

                break;
            case 'float':
            case 'int':
                //mit oder ohne einheit (suffix)
                if ($feature->getSuffix() && $this->isSuffixCalc($feature->getSuffix())) {
                    return $this->convertByUnit($feature->getSuffix(), $raw_value_c);
                }

                return $raw_value;
            default:
                throw new FatalException('unknown feature type');
        }

        return $result;
    }

    protected function isSuffixCalc($suffix)
    {
        return in_array(strtolower($suffix), ['mm', 'cm']);
    }

    protected function convertByUnit($unit, $value)
    {
        $result = $value['value'];

        if ($unit == 'cm') {
            $src_unit = $value['unit'] ?? false;

            if (!$src_unit) {
                if (strpos($value['name'], 'mm')) {
                    $src_unit = 'mm';
                }
            }

            switch ($src_unit) {
                case 'mm':
                    $result /= 10;
                    break;
            }
        }

        return $result;
    }

    protected function convertValues($daten)
    {
        if ($daten['mvsrc_id'] == 31) {
            if (is_numeric($daten['value'])) {
                $daten['value'] = (float)$daten['value'];
            }
        }

        return $daten;
    }


    public function isValueTrue($value)
    {
        return in_array(strtolower($value), ['ja', 'true', 'yes', 1]);
    }

    public function isValueFalse($value)
    {
        return in_array(strtolower($value), ['nein', 'false', 'no', '0']);
    }

    public function getWarnings()
    {
        return $this->warnings;
    }
}
