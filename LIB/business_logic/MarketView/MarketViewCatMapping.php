<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use InvalidArgumentException;

class MarketViewCatMapping
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }


    /**
     * Diese Funktion erzeugt/updatet ein Mapping. Der Einfachheit halber wird angenommen, dass das Tuple (cat_tree_id, mv_cat_id)
     * einzigartig ist. Die Datenstruktur sieht das nicht vor und ist bereits auf ein erweiterte Zuordnung mit Filtern und Prioritäten vorbereitet.
     *
     * @param int $cat_tree_id
     * @param int $cat_id
     * @param int $mv_cat_id
     */
    public function setLegacyMapping(int $cat_tree_id, int $cat_id, int $mv_cat_id): void
    {
        if (!$cat_tree_id) {
            throw new InvalidArgumentException('cat_tree_id is mandatory');
        }

        if (!$cat_id) {
            $this->db_mv->query("
                DELETE FROM
                    market_view_cat_mapping
                WHERE
                    market_view_cat_mapping.cat_tree_id = {$cat_tree_id} AND
                    market_view_cat_mapping.mv_cat_id = {$mv_cat_id}
            ");
            return;
        }

        $cat_mapping_id = $this->db_mv->fieldQuery("
            SELECT
                market_view_cat_mapping.cat_mapping_id
            FROM
                market_view_cat_mapping
            WHERE
                market_view_cat_mapping.cat_tree_id = {$cat_tree_id} AND
                market_view_cat_mapping.mv_cat_id = {$mv_cat_id}
        ");

        if ($cat_mapping_id) {
            $this->db_mv->query("
                UPDATE
                    market_view_cat_mapping
                SET
                    market_view_cat_mapping.cat_id = {$cat_id}
                WHERE
                    market_view_cat_mapping.cat_mapping_id = {$cat_mapping_id}
            ");
            return;
        }

        $this->db_mv->query("
            INSERT INTO
                market_view_cat_mapping
            SET
                market_view_cat_mapping.cat_tree_id = {$cat_tree_id},
                market_view_cat_mapping.mv_cat_id = {$mv_cat_id},
                market_view_cat_mapping.cat_id = {$cat_id}
        ");
    }

    public function removeMapping(int $cat_tree_id, int $cat_id, int $mv_cat_id): void
    {
        $this->db_mv->query("
            DELETE FROM
                market_view_cat_mapping
            WHERE
                market_view_cat_mapping.cat_tree_id = {$cat_tree_id} AND
                market_view_cat_mapping.mv_cat_id = {$mv_cat_id} AND
                market_view_cat_mapping.cat_id = {$cat_id}
        ");
    }
}
