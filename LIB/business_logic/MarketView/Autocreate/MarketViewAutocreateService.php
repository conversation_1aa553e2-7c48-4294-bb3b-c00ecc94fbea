<?php

namespace wws\MarketView\Autocreate;

use bqp\db\db_generic;
use service_loader;

class MarketViewAutocreateService
{

    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    public function autocreateLoader(int $mvsrc_id): MarketViewAutocreate
    {
        $autocreate_type = $this->db_mv->fieldQuery("
            SELECT
                market_view_source.autocreate_type
            From
                market_view_source
            WHERE
                market_view_source.mvsrc_id = " . $mvsrc_id . "
        ");

        if ($autocreate_type) {
            return service_loader::get($autocreate_type);
        }
        return new MarketViewAutocreateNone();
    }
}
