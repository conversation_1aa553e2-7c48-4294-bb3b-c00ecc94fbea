<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\ProductConst;

class MarketViewCatMappingProductType
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }


    public function writeMapping(int $mv_cat_id, string $product_type): void
    {
        if ($product_type === ProductConst::PRODUCT_TYPE_UNBEKANNT) {
            $product_type = '';
        }

        if (!$product_type) {
            $this->db_mv->query("
                DELETE FROM
                    market_view_cat_mapping_product_type
                WHERE
                    market_view_cat_mapping_product_type.mv_cat_id = $mv_cat_id
            ");
            return;
        }

        $this->db_mv->query("
            INSERT INTO
                market_view_cat_mapping_product_type
            SET
                market_view_cat_mapping_product_type.mv_cat_id = $mv_cat_id,
                market_view_cat_mapping_product_type.product_type = '" . $this->db_mv->escape($product_type) . "'
            ON DUPLICATE KEY UPDATE
                market_view_cat_mapping_product_type.product_type = VALUES(market_view_cat_mapping_product_type.product_type)
        ");
    }

    /**
     * ACHTUNG: Die Methode gibt derzeit nur den product_type für eine product_id zurück, falls diese Eindeutig ist.
     *
     * @param array $product_ids
     * @return array
     */
    public function getSuggestedProductType(array $product_ids): array
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_matching.product_id,
                GROUP_CONCAT(DISTINCT market_view_cat_mapping_product_type.product_type) as product_types
            FROM
                market_view_matching INNER JOIN
                market_view_product ON (market_view_matching.mvsrc_id = market_view_product.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_product.mvsrc_product_id) INNER JOIN
                market_view_cat_mapping_product_type ON (market_view_product.mv_cat_id = market_view_cat_mapping_product_type.mv_cat_id OR market_view_product.mv_cat_id_2 = market_view_cat_mapping_product_type.mv_cat_id)
            WHERE
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                market_view_matching.product_id IN (" . $this->db_mv->in($product_ids) . ")
            GROUP BY
                market_view_matching.product_id
        ")->asSingleArray('product_id');

        foreach ($result as $product_id => $product_type) {
            if (str_contains($product_type, ',')) {
                //$items = explode(',', $product_type);
                //var_dump($items);
                unset($result[$product_id]);
            }
        }

        return $result;
    }
}