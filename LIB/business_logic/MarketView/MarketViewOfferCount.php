<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Utils\ArrayUtils;

class MarketViewOfferCount
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    public function updateCountings(): void
    {
        $availability_ids = $this->db_mv->query("
            SELECT
                market_view_availability.availability_id
            FROM
                market_view_availability
            WHERE
                market_view_availability.available = 1
        ")->asSingleArray();

        //eventuell komplett auf market_view_master_product umstellen und die countings dann über ein LEFT JOIN auf master bestimmen...
        //oder ganz anders... die felder die relevant sind, laufen auch in einer queue ein. Ggf. könnte damit der delta bestimmt werden?

        $this->db_mv->query("
            UPDATE
                (
                    SELECT
                        market_view_master_product.mv_master_product_id,
                        SUM(IF(market_view_product.product_status = 'online', 1, 0)) AS other_sources_online,
                        SUM(IF(market_view_product.product_status = 'online' AND market_view_product.availability_id IN (" . $this->db_mv->in($availability_ids) . "), 1, 0)) AS other_sources_available
                    FROM
                        market_view_master_product INNER JOIN
                        market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id)
                    GROUP BY
                        market_view_master_product.mv_master_product_id
                ) AS offer_countings INNER JOIN
                market_view_product ON (market_view_product.mv_master_product_id = offer_countings.mv_master_product_id)
            SET
                market_view_product.other_sources_online = offer_countings.other_sources_online,
                market_view_product.other_sources_available = offer_countings.other_sources_available
        ");

//
//        $this->db_mv->query("
//            UPDATE
//                (
//                    SELECT
//                        market_view_master_product.mv_master_product_id,
//                        SUM(IF(market_view_product.product_status = 'online', 1, 0)) AS other_sources_online,
//                        SUM(IF(market_view_product.product_status = 'online' AND market_view_product.availability_id IN (".$this->db_mv->in($availability_ids)."), 1, 0)) AS other_sources_available
//                    FROM
//                        market_view_master_product INNER JOIN
//                        market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id)
//                    GROUP BY
//                        market_view_master_product.mv_master_product_id
//                ) AS offer_countings INNER JOIN
//                market_view_master_product ON (market_view_master_product.mv_master_product_id = offer_countings.mv_master_product_id)
//            SET
//                market_view_master_product.other_sources_online = offer_countings.other_sources_online,
//                market_view_master_product.other_sources_available = offer_countings.other_sources_available
//        ");
    }


    //die zwei funktionen sind hier nur zu dokumentations zwecken...
    //ich bin mir nicht ganz sicher ob der ansatz oben funktioniert (es könnte sein dass damit der slave gefickt wird...)
    //match_other_sources() wurde für ein partietielles update eines lieferanten eingesetzt (aber aus performance gründen dann rausgenommen)
    //match_other_sources_full() ist funktional updateCountings()
    /**
     * @depracated
     * @param int $mvsrc_id
     */
    public function match_other_sources(int $mvsrc_id): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_product
            SET
                market_view_product.other_sources_online = 0,
                market_view_product.other_sources_available = 0
            WHERE
                market_view_product.mvsrc_id = '$mvsrc_id'
        ");

        $result = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id,
                market_view_product.ean
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '$mvsrc_id' AND
                market_view_product.product_status = 'online' AND
                market_view_product.ean != ''
        ")->asSingleArray('mvsrc_product_id');
        $result = ArrayUtils::split($result, 500);

        $update_stmt = $this->db_mv->prepare("
            UPDATE
                market_view_product
            SET
                market_view_product.other_sources_available = :other_sources_available,
                market_view_product.other_sources_online = :other_sources_online
            WHERE
                market_view_product.mvsrc_id = '$mvsrc_id' AND
                market_view_product.mvsrc_product_id = :mvsrc_product_id
        ");

        foreach ($result as $eans) {
            $result = $this->db_mv->query("
                    SELECT
                        market_view_product.ean,
                        SUM(market_view_availability.available) AS other_sources_available,
                        COUNT(*) AS other_sources_online
                    FROM
                        market_view_product INNER JOIN
                        market_view_availability ON (market_view_product.availability_id = market_view_availability.availability_id)
                    WHERE
                        market_view_product.product_status = 'online' AND
                        market_view_product.ean != '' AND
                        market_view_product.ean IN (" . $this->db_mv->makeIn($eans) . ")
                    GROUP BY
                        market_view_product.ean
            ")->asArray();

            $this->db_mv->begin();

            foreach ($result as $daten) {
                $mvsrc_product_id = array_search($daten['ean'], $eans);
                if (!$mvsrc_product_id) {
                    continue;
                }

                $update_stmt->execute([
                    'other_sources_available' => $daten['other_sources_available'],
                    'other_sources_online' => $daten['other_sources_online'],
                    'mvsrc_product_id' => $mvsrc_product_id
                ]);
            }

            $this->db_mv->commit();
        }

        //auf master tabelle übertragen
        $this->db_mv->query("
            UPDATE
                market_view_master_product INNER JOIN
                market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id)
            SET
                market_view_master_product.sources_available = market_view_product.other_sources_available,
                market_view_master_product.sources_online = market_view_product.other_sources_online
            WHERE
                market_view_product.mvsrc_id = '$mvsrc_id'
        ");
    }


    /**
     * @depracated
     */
    public function match_other_sources_full(): void
    {
        $this->db_mv->begin();

        $this->db_mv->query("
            UPDATE
                market_view_product
            SET
                market_view_product.other_sources_online = 0,
                market_view_product.other_sources_available = 0
        ");

        $this->db_mv->query("
            UPDATE
                market_view_product INNER JOIN
                (
                    SELECT
                        market_view_product.ean,
                        SUM(market_view_availability.available) AS other_sources_available,
                        COUNT(*) AS other_sources_online
                    FROM
                        market_view_product INNER JOIN
                        market_view_availability ON (market_view_product.availability_id = market_view_availability.availability_id)
                    WHERE
                        market_view_product.product_status = 'online' AND
                        market_view_product.ean != ''
                    GROUP BY
                        market_view_product.ean
                ) AS temp ON (market_view_product.ean = temp.ean)
            SET
                market_view_product.other_sources_available = temp.other_sources_available,
                market_view_product.other_sources_online = temp.other_sources_online
        ");

        $this->db_mv->commit();
    }
}
