<?php

namespace wws\MarketView;

use bqp\Exceptions\FatalException;
use bqp\storage\storage_mount_manager;
use League\Flysystem\FileNotFoundException;
use service_loader;

class MarketViewMediaStorage
{
    private storage_mount_manager $storage;

    public function __construct()
    {
        $this->storage = service_loader::getStorageFactory()->get('marketview');
    }

    public function upload(string $blob, int $mvsrc_id): string
    {
        $url_storage = $this->buildUrlStorage(md5($blob), $mvsrc_id);

        //Wenn es die Datei schon gibt, nicht erneut uploaden. (Bei Symbolbildern, die mehreren Produkten zugeordnet sind,
        //uploaden wir ansonsten X Mal dieselbe Datei. Was wiederrum der Google Storage nicht mag... "rate limit for object mutation operations")
        //Durch den Hash ist sichergestellt, dass die Dateien identisch sind.
        if ($this->storage->has($url_storage)) {
            return $url_storage;
        }
        //

        if (!$this->storage->put($url_storage, $blob)) {
            throw new FatalException('upload failed');
        }

        return $url_storage;
    }

    public function get(string $url_storage): string
    {
        return $this->storage->read($url_storage);
    }

    public function urlStorageExists(string $url_storage): bool
    {
        return $this->storage->has($url_storage);
    }

    private function buildUrlStorage(string $hash, int $mvsrc_id): string
    {
        //marketview://mvsrc_id/2zeichen vom hash/hash

        $url = 'marketview://';
        $url .= str_pad($mvsrc_id, 3, '0', STR_PAD_LEFT) . '/';
        $url .= substr($hash, 0, 2) . '/' . $hash;

        return $url;
    }

    /**
     * Nicht direkt aufrufen! MarketViewMediaRepository::removeUrlStorageFromEntities(), oder MarketViewMediaRepository::deleteUrlStorageFromEntity() verwenden
     *
     * @param string $url_storage
     * @return void
     * @throws FileNotFoundException
     */
    public function _delete(string $url_storage): void
    {
        $this->storage->delete($url_storage);
    }
}
