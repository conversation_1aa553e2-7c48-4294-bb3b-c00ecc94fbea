<?php

namespace wws\MarketView;

use bqp\db\db_generic;

class MarketViewMasterCatTreeReader
{
    protected $cats_raw;

    protected $filter = [
        'max_ebene' => null
    ];

    protected db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    public function setFilterMaxEbene($ebene)
    {
        $this->filter['max_ebene'] = $ebene;
    }

    public function isFilteredCat($daten)
    {
        if ($this->filter['max_ebene']) {
            if ($daten['ebene'] > $this->filter['max_ebene']) {
                return true;
            }
        }

        return false;
    }

    protected function loadCatsRaw()
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_master_cat.mv_master_cat_id,
                market_view_master_cat.parent_mv_master_cat_id,
                market_view_master_cat.cat_name
            FROM
                market_view_master_cat
            ORDER BY
                market_view_master_cat.cat_name
        ")->asArray('mv_master_cat_id');

        $this->cats_raw = $result;
    }

    public function getTreeAsArray($mv_master_cat_id = 0)
    {
        $this->loadCatsRaw();

        return $this->getTreeAsArray_childs($mv_master_cat_id, 1);
    }

    protected function getTreeAsArray_childs($mv_master_cat_id, $ebene)
    {
        $childs = [];

        foreach ($this->cats_raw as $cat) {
            $cat['ebene'] = $ebene;

            if ($cat['parent_mv_master_cat_id'] != $mv_master_cat_id) {
                continue;
            }
            if ($this->isFilteredCat($cat)) {
                continue;
            }

            $child_nodes = $this->getTreeAsArray_childs($cat['mv_master_cat_id'], $ebene + 1);

            if ($child_nodes) {
                $cat['childs'] = $child_nodes;
            }

            $childs[] = $cat;
        }

        return $childs;
    }
}
