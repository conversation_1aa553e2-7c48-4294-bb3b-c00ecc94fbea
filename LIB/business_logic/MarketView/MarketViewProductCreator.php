<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use bqp\SimpleHtmlDom\SimpleHtmlDom;
use bqp\Utils\StringUtils;
use service_loader;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\Entities\MarketViewProduct;
use wws\MarketViewWwsConnector\Matching\Category\CategoryMatching;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatchingEan;
use wws\Product\actions\ProductShipmentTypeSetter;
use wws\Product\Product;
use wws\Product\ProductBrand\ProductBrandRepository;
use wws\Product\ProductConst;
use wws\Product\ProductMedia;
use wws\Product\ProductRepository;

class MarketViewProductCreator
{
    protected db_generic $db_mv;
    protected db_generic $db;
    protected ProductRepository $product_repository;
    protected MarketViewRepository $mv_repository;
    protected ProductShipmentTypeSetter $versand_setter;
    private MarketViewMediaCache $media_cache;
    private MarketViewMatching $mv_matching;

    protected $allowed_media_topics = [
        MarketViewMedia::MEDIA_TOPIC_UNKNOWN,
        MarketViewMedia::MEDIA_TOPIC_SCHEMA,
        MarketViewMedia::MEDIA_TOPIC_PRODUCT_VIDEO,
        MarketViewMedia::MEDIA_TOPIC_PRODUCT_IMAGE,
        MarketViewMedia::MEDIA_TOPIC_PRODUCT_DETAIL
    ];

    private int $default_brand_id;

    public function __construct(
        db_generic $db,
        db_generic $db_mv,
        MarketViewMediaCache $media_cache,
        MarketViewRepository $mv_repository,
        MarketViewMatching $mv_matching
    ) {
        $this->db_mv = $db_mv;
        $this->db = $db;

        $this->product_repository = service_loader::getProductRepository();
        $this->mv_repository = $mv_repository;

        $this->versand_setter = service_loader::get(ProductShipmentTypeSetter::class);

        $this->media_cache = $media_cache;
        $this->mv_matching = $mv_matching;

        $this->default_brand_id = service_loader::getConfigRegistry()->get('product_defaults')->getInt('brand_id');
    }

    public function getShipmentTypeSetter(): ProductShipmentTypeSetter
    {
        return $this->versand_setter;
    }

    public function getMvsrcProductAsProduct(MarketViewProduct $mv_product): Product
    {
        $mvsrc = $this->mv_repository->getMarketViewSource($mv_product->getMvsrcId());

        $product = $this->product_repository->newProduct();

        $product->setEan($mv_product->getEan());
        $product->setMpn($mv_product->getMpn());
        $product->setModelName($mv_product->getModelName());

        $product->setGewicht($mv_product->getGewicht());
        $product->setSizeH($mv_product->getHoehe());
        $product->setSizeB($mv_product->getBreite());
        $product->setSizeT($mv_product->getTiefe());
        $product->setColor($mv_product->getColor());
        $product->setUvp($mv_product->getUvp());
        if ($mv_product->isGenuinePartSet()) {
            $product->setGenuinePart($mv_product->isGenuinePart());
        }

        $product->setProductStatus(ProductConst::PRODUCT_STATUS_PREADD);

        $ek = $product->getOrCreateProductEkOffer($mv_product->getMvsrc()->getSupplierId());
        $ek->setEkListe($mv_product->getVkNetto());
        $ek->setEkActive(ProductConst::EK_GROUP_ID_FAV);
        $ek->setGrosProductId($mv_product->getMvsrcProductId());

        $brand_id = $mv_product->getHersteller()->getBrandId();

        if (!$brand_id) {
            $brand_id = $this->default_brand_id;
        }

        $product->setBrandId($brand_id);

        $product_type = ProductBrandRepository::getDefaultProductType($brand_id);
        if ($product_type) {
            $product->setProductType($product_type);
        }

        $product_name = $mv_product->getProductName();
        $product_name = preg_replace("~\s{1,}~", " ", $product_name);
        $product->setProductName($product_name);

        $mvsrc_id = (string)$mv_product->getMvsrcId();
        $mvsrc_product_id = (string)$mv_product->getMvsrcProductId();

        $matching = $this->mv_matching;

        $product->addSaveNewCallback(function (Product $product) use ($mvsrc_id, $mvsrc_product_id, $matching) {
            $matching->addMatching($mvsrc_id, $mvsrc_product_id, $product->getProductId());
            if ($product->getEan()) {
                $matching_ean = service_loader::getDiContainer()->get(ProductMatchingEan::class);
                $matching_ean->runForEan($product->getEan());
            }
            $matching->syncGrossistsForProduct($product->getProductId());
        });

        if ($mv_product->getVersandNetto()) {
            $this->getShipmentTypeSetter()->setByShipmentPriceNet($product, $mv_product->getVersandNetto());
        }

        if (!$product->isAsyncMedia()) {
            $data_source_id = $mvsrc->getImageDataSourceId();

            if ($data_source_id) {
                foreach ($this->mv_repository->loadMediaByMvProduct($mv_product) as $market_view_media) {
                    if (!$this->isRelevantMedia($market_view_media)) {
                        continue;
                    }

                    $product_media = $this->convertMarketViewMediaToProductMedia($market_view_media, $data_source_id);
                    $product->addAsyncMedia($this->media_cache->buildUrl($mv_product->getMvsrcId(), $market_view_media->getUrlStorage() ?: $market_view_media->getUrl()), $product_media);
                }
            }
        }

        if ($mvsrc->getTextDataSourceId() && !trim($product->getBeschreibung())) {
            $beschreibung = $mv_product->getBeschreibungForProductAsHtml();

            if ($beschreibung) {
                $product->setBeschreibung($beschreibung);
                $product->setBeschreibungQuelle($mvsrc->getTextDataSourceId());
            }
        }

        if ($mv_product->getGrundpreisFaktor()) {
            $product->setGrundpreisAktiv(true);
            $product->setGrundpreisEinheit(trim($mv_product->getGrundpreisMenge() . ' ' . $mv_product->getGrundpreisEinheit()));
            $product->setGrundpreisFaktor($mv_product->getGrundpreisFaktor());
        }

        $product = $mvsrc->getMvsrcProductAsProduct($mv_product, $product, $this->mv_repository);

        return $product;
    }

    /**
     * @param MarketViewMedia $market_view_media
     * @return bool
     */
    public function isRelevantMedia(MarketViewMedia $market_view_media): bool
    {
        if ($market_view_media->getTopic() === $market_view_media::MEDIA_TOPIC_ENERGIE_LABEL_2020) {
            return true;
        }

        if ($market_view_media->getTopic() === $market_view_media::MEDIA_TOPIC_EU_DATENBLATT_2020) {
            return true;
        }

        if ($market_view_media->getMediaType() !== MarketViewMedia::MEDIA_TYPE_IMAGE_URL) {
            return false;
        }

        if (!in_array($market_view_media->getTopic(), $this->allowed_media_topics)) {
            return false;
        }

        return true;
    }

    /**
     * wandelt ein MarketViewMedia zu ProductMedia um. Die kontrolle von $data_source_id obliegt den aufrufer. Werden Medien
     * aus einer Mvsrc erzeugt, für die kein image_data_source_id gesetzt ist, wieder wird hier auch nicht gesetzt, außer sie ist
     * explizit angegeben.
     *
     * @param MarketViewMedia $market_view_media
     * @param string|null $data_source_id
     * @return ProductMedia
     * @throws FatalException
     */
    public function convertMarketViewMediaToProductMedia(MarketViewMedia $market_view_media, ?string $data_source_id = null): ProductMedia
    {
        $product_media = new ProductMedia();
        $product_media->setMvMediaId($market_view_media->getMvMediaId());

        if ($data_source_id) {
            $product_media->setDataSourceId($data_source_id);
        } else {
            $data_source_id = $this->mv_repository->getImageDataSourceId($market_view_media->getMvsrcId());

            if ($data_source_id) {
                $product_media->setDataSourceId($data_source_id);
            }
        }

        switch ($market_view_media->getTopic()) {
            case MarketViewMedia::MEDIA_TOPIC_EU_DATENBLATT_2020:
                $product_media->setMediaTopicId(ProductConst::MEDIA_TOPIC_ENVKV_FICHE_2020_ORIGINAL);
                if ($market_view_media->getMvsrcId() == MarketViewConst::MVSRC_ID_EPREL) {
                    $product_media->setBeschreibung('english');
                }
                break;
            case MarketViewMedia::MEDIA_TOPIC_EU_DATENBLATT:
                $product_media->setMediaTopicId(ProductConst::MEDIA_TOPIC_ENVKV_FICHE_2010);
                break;
            case MarketViewMedia::MEDIA_TOPIC_ENERGIE_LABEL_2020:
                $product_media->setMediaTopicId(ProductConst::MEDIA_TOPIC_ENVKV_LABEL_2020);
                break;
            case MarketViewMedia::MEDIA_TOPIC_ENERGIE_LABEL:
                $product_media->setMediaTopicId(ProductConst::MEDIA_TOPIC_ENVKV_LABEL_2010);
                break;
        }

        $product_media->setMediaType($this->translateMediaType($market_view_media->getMediaType()));
        $product_media->setShow(true);

        return $product_media;
    }

    /**
     * bah... das ist auch alles käse. Die Methode kann nur auf Angebote verwendet werden die bereits angelegt sind.
     *
     * @param Product $product
     * @param MarketViewProduct $mv_product
     * @throws FatalException
     */
    public function envkv(Product $product, MarketViewProduct $mv_product): void
    {
        $mvsrc = $this->mv_repository->getMarketViewSource($mv_product->getMvsrcId());

        $mv_features = $mv_product->getMvFeatures();

        $eek = $this->getEnergyClass($mv_features);

        if ($eek) {
            $feature_value_object = $product->getFeatureContainer()->getFeatureValuesObject();

            if (!$feature_value_object->hasFeatures()) {
                //das heißt wir haben envkv produkte in einer Kategorie ohne envkv features... erstmal ignorieren
                return;
            }

            $eek_features = $this->db->query("
                SELECT
                    product_feature_groups.feature_group_id,
                    product_feature_groups.eek_2020_feature_id
                FROM
                    product_feature_groups
                WHERE
                    product_feature_groups.eek_2020_feature_id != 0
            ")->asSingleArray('feature_group_id');

            foreach ($eek_features as $feature_id) {
                if ($feature_value_object->featureExists($feature_id)) {
                    $feature_value_object->setValue($feature_id, $eek, false);
                }
            }
        }

        $data_source_id = $mvsrc->getImageDataSourceId();

        if ($data_source_id) {
            foreach ($this->mv_repository->loadMediaByMvProduct($mv_product) as $market_view_media) {
                switch ($market_view_media->getTopic()) {
                    case MarketViewMedia::MEDIA_TOPIC_EU_DATENBLATT_2020:
                    case MarketViewMedia::MEDIA_TOPIC_ENERGIE_LABEL_2020:
                        $product_media = $this->convertMarketViewMediaToProductMedia($market_view_media, $data_source_id);
                        $product_media->setProductId($product->getProductId());

                        $product_media->loadFile($this->media_cache->buildUrl($mv_product->getMvsrcId(), $market_view_media->getUrlStorage() ?: $market_view_media->getUrl()));

                        $product->addMedia($product_media);
                }
            }
        }

        $product->save();
    }

    /**
     * @param string $mv_media_type
     * @return string
     * @throws FatalException
     */
    private function translateMediaType(string $mv_media_type): string
    {
        switch ($mv_media_type) {
            case MarketViewMedia::MEDIA_TYPE_IMAGE_URL:
            case MarketViewMedia::MEDIA_TYPE_IMAGE:
                return ProductMedia::MEDIA_TYPE_PICTURE;
            case MarketViewMedia::MEDIA_TYPE_PDF:
                return ProductMedia::MEDIA_TYPE_PDF;
            default:
                throw new FatalException('unknown media type');
        }
    }

    private function getEnergyClass(MarketViewFeatures $market_view_features): ?string
    {
        if ($market_view_features->isFeature('energyClass')) {
            return $market_view_features->getFeatureValue('energyClass');
        }

        return null;
    }

    public function productWithCatId(MarketViewProduct $mv_product, int $cat_id): Product
    {
        $product = $this->getMvsrcProductAsProduct($mv_product);
        $product->setCatIdWithDefaults($cat_id);

        return $product;
    }


    /**
     * sucht anhand eines market_view_product ob dieser artikel schon im system
     * existiert
     *
     * @param MarketViewProduct $mv_product
     * @return int[] $product_ids
     */
    public function checkProductExists(MarketViewProduct $mv_product): array
    {
        return $this->product_repository->checkProductExistsEanMpn($mv_product->getEan(), $mv_product->getMpn());
    }

    /**
     * sucht anhand eines market_view_product mögliche system kategorien raus
     *
     * @param MarketViewProduct $mv_product
     */
    public function findPossibleCats(MarketViewProduct $mv_product)
    {
        $matching = service_loader::getDiContainer()->get(CategoryMatching::class);

        return $matching->getPossibleCats($mv_product->getMvsrcId(), $mv_product->getCat()->getMvCatId());
    }

    /**
     * sucht zu einer ean mögliche logistikmasse
     *
     * @param $ean
     * @return array
     * @todo gehört hier nicht hin (market_view_utilitis?)
     */
    public function getProductSize($ean)
    {
        $return = [
            'gewicht' => 0,
            'breite' => 0,
            'hoehe' => 0,
            'tiefe' => 0
        ];

        $result = $this->db_mv->query("
            SELECT 
                market_view_product.gewicht,
                market_view_product.breite,
                market_view_product.hoehe,
                market_view_product.tiefe
            FROM
                market_view_product
            WHERE
                market_view_product.ean = '$ean'
        ");

        foreach ($result as $daten) {
            $return['gewicht'] = max($return['gewicht'], $daten['gewicht']);

            if ($daten['breite'] && $daten['hoehe'] && $daten['tiefe']) {
                $return['breite'] = $daten['breite'];
                $return['hoehe'] = $daten['hoehe'];
                $return['tiefe'] = $daten['tiefe'];
            }
        }

        return $return;
    }

    public static function formatMarketViewFeatureIconsForBeschreibung($icons)
    {
        if (!$icons) {
            return '';
        }

        $result = '<ul class="mv_icons">';

        foreach ($icons as $icon) {
            $result .= '<li>';
            $result .= '<img src="/r/product_icons/' . str_replace('.jpg', '.png', $icon['image']) . '" title="' . StringUtils::htmlentities($icon['title']) . '">';
            if ($icon['description']) {
                $result .= '<div><b>' . $icon['title'] . '</b><br><br> ' . nl2br($icon['description']) . '</div>';
            }
            $result .= '</li>';
        }

        $result .= '</ul>';

        return $result;
    }

    public static function isMarketViewFeaturesInBeschreibung(string $beschreibung): bool
    {
        return str_contains($beschreibung, 'mv_icons_marker');
    }

    public static function addMarketViewFeatureIconsToBeschreibung($product_beschreibung, $features)
    {
        if (self::isMarketViewFeaturesInBeschreibung($product_beschreibung)) {
            $html = new SimpleHtmlDom();
            $html->load($product_beschreibung);

            $html->find('.mv_icons_marker', 0)->innertext = $features;

            $product_beschreibung = $html->outertext;
        } else {
            $product_beschreibung = '<div class="mv_icons_marker">' . $features . '</div> ' . $product_beschreibung;
        }

        return $product_beschreibung;
    }
}
