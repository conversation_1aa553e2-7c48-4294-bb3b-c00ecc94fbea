<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use wws\business_structure\Shop;
use wws\Product\ProductConst;

class MarketViewMasterProductCreator
{
    private MarketViewRepository $mv_repository;
    private MarketViewProductCreator $product_creator;

    protected db_generic $db;
    protected db_generic $db_mv;

    public function __construct(db_generic $db, db_generic $db_mv, MarketViewProductCreator $product_creator, MarketViewRepository $mv_repository)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;

        $this->product_creator = $product_creator;
        $this->mv_repository = $mv_repository;
    }

    public function fillEmptyProducts(): void
    {
        $this->db_mv->query("SET @num := 0, @type := 0;");
        $result = $this->db_mv->query("
            SELECT
                @num := IF(@type = daten.mv_master_product_id, @num + 1, 1) AS row_number_free,
                @type := daten.mv_master_product_id AS mv_master_product_id,
                daten.mvsrc_id,
                daten.product_name,
                daten.mv_hersteller_id,
                daten.mpn
            FROM
                (
                    SELECT
                        market_view_master_product.mv_master_product_id,
                        market_view_source.mvsrc_id,
                        market_view_product.product_name,
                        market_view_product.mv_hersteller_id,
                        market_view_product.mpn
                    FROM
                        market_view_master_product INNER JOIN
                        market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id) INNER JOIN
                        market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id)
                    WHERE
                        market_view_master_product.product_name_src IS NULL
                    ORDER BY
                        market_view_master_product.mv_master_product_id,
                        market_view_source.source_std_priority DESC
                ) AS daten
            HAVING
                row_number_free = 1
        ");

        foreach ($result as $daten) {
            $this->db_mv->query("
                UPDATE
                    market_view_master_product
                SET
                    market_view_master_product.product_name = '" . $this->db_mv->escape($daten['product_name']) . "',
                    market_view_master_product.product_name_src = '" . $daten['mvsrc_id'] . "',
                    market_view_master_product.mpn = '" . $this->db_mv->escape($daten['mpn']) . "',
                    market_view_master_product.mv_hersteller_id = '" . $daten['mv_hersteller_id'] . "'
                WHERE
                    market_view_master_product.mv_master_product_id = '" . $daten['mv_master_product_id'] . "'
            ");
        }
    }

    public function searchProductName(int $mv_master_product_id): array
    {
        $daten = $this->db_mv->singleQuery("
            SELECT
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id,

                market_view_source.product_creator
            FROM
                market_view_product INNER JOIN
                market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_product.mv_master_product_id = '" . $mv_master_product_id . "'
            ORDER BY
                market_view_source.source_std_priority DESC
            LIMIT
                1
        ");

        $mv_product = $this->mv_repository->getMarketViewProduct($daten['mvsrc_id'], $daten['mvsrc_product_id']);

        $product = $this->product_creator->getMvsrcProductAsProduct($mv_product);

        return [
            'product_name_src' => $daten['mvsrc_id'],
            'product_name' => $product->getProductName(),
            'mpn' => $product->getMpn()
        ];
    }


    public function getMvHerstellerId(int $mv_master_product_id): ?int
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_hersteller.mv_hersteller_id,
                market_view_hersteller.mapping_mv_hersteller_id
            FROM
                market_view_product INNER JOIN
                market_view_hersteller ON (market_view_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id)
            WHERE
                market_view_product.mv_master_product_id = '" . $mv_master_product_id . "'
            ORDER BY
                COUNT(*) DESC
        ");

        $mv_hersteller_id = null;

        foreach ($result as $daten) {
            if ($mv_hersteller_id === null) {
                $mv_hersteller_id = $daten['mv_hersteller_id'];
            }

            if ($daten['mapping_mv_hersteller_id']) {
                $mv_hersteller_id = $daten['mapping_mv_hersteller_id'];
            }
        }

        return $mv_hersteller_id;
    }


    public function buildOffers(): void
    {
        $this->buildMasterPrices();

        $this->calcOffersForPriceGroupes();
    }

    public function buildMasterPrices(): void
    {
        $this->db_mv->query("
            INSERT IGNORE INTO
                market_view_b2b_offers
                (mv_master_product_id)
            SELECT
                market_view_master_product.mv_master_product_id
            FROM
                market_view_master_product
        ");

        $this->buildMasterPricesByProduct();
        $this->buildMasterPricesByMarketView();
    }

    public function buildMasterPricesByProduct(): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_b2b_offers
            SET
                market_view_b2b_offers.update_flag = 0
        ");

        $product_ek = 'ROUND(IF(product_ek.supplier_id = 69, product_ek.ek_netto+15, IF(product_ek.supplier_id = 100, product_ek.ek_netto+30, product_ek.ek_netto)), 2) AS ek_netto_cor';

        $source_matching = $this->db_mv->query("
            SELECT
                market_view_b2b_source_matching.supplier_id,
                market_view_b2b_source_matching.b2b_availability_id
            FROM
                market_view_b2b_source_matching
            WHERE
                market_view_b2b_source_matching.b2b_availability_id IS NOT NULL AND
                market_view_b2b_source_matching.supplier_id > 0
        ")->asArray();

        $supplier_ids = array_column($source_matching, 'supplier_id');

        $sql_map = 'CASE product_ek.supplier_id';
        foreach ($source_matching as $matching) {
            $sql_map .= ' WHEN ' . $matching['supplier_id'] . ' THEN ' . $matching['b2b_availability_id'];
        }
        $sql_map .= ' END';

        $result = $this->db->query("
            SELECT
                best.*
            FROM
                (
                    SELECT
                        @num := if(@type = daten.product_id, @num + 1, 1) AS row_number_free,
                        @type := daten.product_id AS product_id,
                        daten.ek_netto_cor,
                        daten.offer_availability_id
                    FROM
                        (SELECT @num := 0, @type := 0) AS init,
                        (
                            SELECT
                                product_ek.product_id,
                                $product_ek,
                                IF(product_ek.mvsrc_availability_id IN(" . MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE . ", " . MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE . ") || product_ek.mvsrc_availability_id_virt IN (" . MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE . ", " . MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE . "), " . $sql_map . ", " . MarketViewConst::AVAILABILITY_ID_45 . ") AS offer_availability_id
                            FROM
                                product_ek
                            WHERE
                                product_ek.supplier_id IN (" . $this->db->in($supplier_ids) . ") AND
                                product_ek.mvsrc_availability_id NOT IN (" . MarketViewConst::AVAILABILITY_ID_65 . ", " . MarketViewConst::AVAILABILITY_ID_OFFLINE . ") AND
                                product_ek.bezug = 1 AND
                                product_ek.product_id != 0
                            ORDER BY
                                product_ek.product_id,
                                offer_availability_id,
                                ek_netto_cor
                        ) AS daten
                    HAVING
                        row_number_free = 1
                ) AS best
        ");

        $update_stmt = $this->db_mv->prepare("
            UPDATE
                market_view_master_product INNER JOIN
                market_view_b2b_offers ON (market_view_master_product.mv_master_product_id = market_view_b2b_offers.mv_master_product_id)
            SET
                market_view_b2b_offers.update_flag = 2,
                market_view_b2b_offers.availability_id = :offer_availability_id,
                market_view_b2b_offers.vk_netto = :ek_netto_cor,
                market_view_b2b_offers.vk_netto_best = 0
            WHERE
                market_view_master_product.product_id = :product_id
        ");

        foreach ($result as $daten) {
            $update_stmt->execute($daten);
        }

        $this->db_mv->query("
            UPDATE
                market_view_b2b_offers
            SET
                market_view_b2b_offers.availability_id = " . MarketViewConst::AVAILABILITY_ID_OFFLINE . "
            WHERE
                market_view_b2b_offers.update_flag = 0
        ");
    }


    public function buildMasterPricesByMarketView(): void
    {
        //ek preis nach lieferbarkeit, wenn lieferbar dann diesen, ansonsten den nächst günstigeren
        $this->db_mv->query("
                UPDATE
                    market_view_b2b_offers
                SET
                    market_view_b2b_offers.update_flag = 0
                WHERE
                    market_view_b2b_offers.update_flag != 2
            ");

        $result = $this->db_mv->query("
                UPDATE
                    market_view_b2b_offers INNER JOIN
                    (
                        SELECT
                            @num := if(@type = daten.mv_master_product_id, @num + 1, 1) AS row_number_free,
                            @type := daten.mv_master_product_id AS mv_master_product_id,
                            daten.vk_nnn,
                            daten.offer_availability_id
                        FROM
                            (SELECT @num := 0, @type := 0) AS init,
                            (
                                SELECT
                                    market_view_product.mv_master_product_id,
                                    market_view_product.vk_nnn,
                                    IF(market_view_product.availability_id IN(" . MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE . ", " . MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE . "), market_view_b2b_source_matching.b2b_availability_id, market_view_product.availability_id) AS offer_availability_id
                                FROM
                                    market_view_product INNER JOIN
                                    market_view_b2b_source_matching ON (market_view_product.mvsrc_id = market_view_b2b_source_matching.mvsrc_id)
                                WHERE
                                    market_view_b2b_source_matching.b2b_availability_id IS NOT NULL AND
                                    market_view_product.mv_master_product_id != '' AND
                                    market_view_product.availability_id NOT IN (" . MarketViewConst::AVAILABILITY_ID_65 . ", " . MarketViewConst::AVAILABILITY_ID_OFFLINE . ") AND
                                    market_view_product.product_id = 0 AND
                                    market_view_product.vk_nnn != 0
                                ORDER BY
                                    market_view_product.mv_master_product_id,
                                    offer_availability_id,
                                    market_view_product.vk_netto
                            ) AS daten
                        HAVING
                            row_number_free = 1
                    ) AS norm ON (market_view_b2b_offers.mv_master_product_id = norm.mv_master_product_id)
                SET
                    market_view_b2b_offers.update_flag = 1,
                    market_view_b2b_offers.availability_id = norm.offer_availability_id,
                    market_view_b2b_offers.vk_netto = norm.vk_nnn
                WHERE
                    market_view_b2b_offers.update_flag != 2
            ");

        $this->db_mv->query("
                UPDATE
                    market_view_b2b_offers
                SET
                    market_view_b2b_offers.availability_id = " . MarketViewConst::AVAILABILITY_ID_OFFLINE . "
                WHERE
                    market_view_b2b_offers.update_flag = 0
            ");

        //best netto
        $this->db_mv->query("
                UPDATE
                    market_view_b2b_offers
                SET
                    market_view_b2b_offers.update_flag = 0
                WHERE
                    market_view_b2b_offers.update_flag != 2
            ");

        $result = $this->db_mv->query("
                UPDATE
                    market_view_b2b_offers INNER JOIN
                    (
                        SELECT
                            @num := if(@type = daten.mv_master_product_id, @num + 1, 1) AS row_number_free,
                            @type := daten.mv_master_product_id AS mv_master_product_id,
                            daten.vk_nnn
                        FROM
                            (SELECT @num := 0, @type := 0) AS init,
                            (
                                SELECT
                                    market_view_product.mv_master_product_id,
                                    market_view_product.vk_nnn,
                                    IF(market_view_product.availability_id IN(10,20), market_view_b2b_source_matching.b2b_availability_id, market_view_product.availability_id) AS offer_availability_id
                                FROM
                                    market_view_product INNER JOIN
                                    market_view_b2b_source_matching ON (market_view_product.mvsrc_id = market_view_b2b_source_matching.mvsrc_id)
                                WHERE
                                    market_view_b2b_source_matching.b2b_availability_id IS NOT NULL AND
                                    market_view_product.mv_master_product_id != '' AND
                                    market_view_product.availability_id NOT IN (" . MarketViewConst::AVAILABILITY_ID_65 . ", " . MarketViewConst::AVAILABILITY_ID_OFFLINE . ") AND
                                    market_view_product.product_id = 0 AND
                                    market_view_product.vk_nnn != 0
                                ORDER BY
                                    market_view_product.mv_master_product_id,
                                    market_view_product.vk_nnn,
                                    offer_availability_id
                            ) AS daten
                        HAVING
                            row_number_free = 1
                    ) AS r ON market_view_b2b_offers.mv_master_product_id = r.mv_master_product_id
                SET
                    market_view_b2b_offers.update_flag = 1,
                    market_view_b2b_offers.vk_netto_best = r.vk_nnn
                WHERE
                    market_view_b2b_offers.update_flag != 2
            ");

        $this->db_mv->query("
                UPDATE
                    market_view_b2b_offers
                SET
                    market_view_b2b_offers.vk_netto_best = 0
                WHERE
                    market_view_b2b_offers.update_flag = 0
            ");
    }

    public function calcOffersForPriceGroupes(): void
    {
        $price_groupes = $this->db->query("
            SELECT
                b2b_price_groupes.b2b_price_group_id
            FROM
                b2b_price_groupes
        ")->asArray('b2b_price_group_id');

        $result = $this->db_mv->query("
            SELECT
                market_view_b2b_offers.mv_master_product_id,
                market_view_b2b_offers.vk_netto,
                market_view_b2b_offers.vk_netto_best,

                market_view_master_product.product_id
            FROM
                market_view_b2b_offers INNER JOIN
                market_view_master_product ON (market_view_b2b_offers.mv_master_product_id = market_view_master_product.mv_master_product_id)
            WHERE
                market_view_b2b_offers.availability_id != " . MarketViewConst::AVAILABILITY_ID_OFFLINE . "
        ");

        $update_stmt = $this->db_mv->prepare("
                INSERT INTO
                    market_view_b2b_offer_prices
                SET
                    market_view_b2b_offer_prices.mv_master_product_id = :mv_master_product_id,
                    market_view_b2b_offer_prices.b2b_price_group_id = :b2b_price_group_id,
                    market_view_b2b_offer_prices.vk_netto = :vk_netto,
                    market_view_b2b_offer_prices.vk_netto_best = :vk_netto_best
                ON DUPLICATE KEY UPDATE
                    market_view_b2b_offer_prices.vk_netto = VALUES(vk_netto),
                    market_view_b2b_offer_prices.vk_netto_best = VALUES(vk_netto_best)
        ");

        foreach ($result as $daten) {
            foreach ($price_groupes as $price_group) {
                $vk_netto = $this->calcPrice($daten['vk_netto'], $daten, $price_group['b2b_price_group_id']);
                $vk_netto_best = $this->calcPrice($daten['vk_netto_best'], $daten, $price_group['b2b_price_group_id']);

                $update_stmt->execute([
                    'mv_master_product_id' => $daten['mv_master_product_id'],
                    'b2b_price_group_id' => $price_group['b2b_price_group_id'],
                    'vk_netto' => $vk_netto,
                    'vk_netto_best' => $vk_netto_best
                ]);
            }
        }
    }

    /**
     * kalkuliert den Preis für die jeweilige MarketView Preisgruppe
     *
     * @param float $basis_vk_netto
     * @param array $daten
     * @param int $price_group_id
     * @return float
     */
    public function calcPrice(float $basis_vk_netto, array $daten, int $price_group_id): float
    {
        static $price_groupes = [];

        if (!$price_groupes) {
            $price_groupes = $this->db->query("
                SELECT
                    b2b_price_groupes.b2b_price_group_id,
                    b2b_price_groupes.price_10,
                    b2b_price_groupes.price_20,
                    b2b_price_groupes.price_50,
                    b2b_price_groupes.price_75,
                    b2b_price_groupes.price_100,
                    b2b_price_groupes.price_250,
                    b2b_price_groupes.price_500,
                    b2b_price_groupes.price_750,
                    b2b_price_groupes.price_1000,
                    b2b_price_groupes.price_2000,
                    b2b_price_groupes.price_gt_2000
                FROM
                    b2b_price_groupes
            ")->asArray('b2b_price_group_id');
        }

        //preis aus online shop
        static $shop_prices = [];

        if (!$shop_prices) {
            $shop_prices = $this->db->query("
                    SELECT
                        product.product_id,
                        product_shop.vk_netto
                    FROM
                        product INNER JOIN
                        product_shop ON (product.product_id = product_shop.product_id AND product_shop.shop_id = " . Shop::ALLEGO . ")
                    WHERE
                        product.product_status = 1 AND
                        product_shop.park = 0
                ")->asSingleArray('product_id');
        }

        $shop_price = $shop_prices[$daten['product_id']] ?? 0;

        $price_group = $price_groupes[$price_group_id];

        $prozent = 1000;

        if ($basis_vk_netto <= 10) {
            $prozent = $price_group['price_10'];
        } elseif ($basis_vk_netto <= 20) {
            $prozent = $price_group['price_20'];
        } elseif ($basis_vk_netto <= 50) {
            $prozent = $price_group['price_50'];
        } elseif ($basis_vk_netto <= 75) {
            $prozent = $price_group['price_75'];
        } elseif ($basis_vk_netto <= 100) {
            $prozent = $price_group['price_100'];
        } elseif ($basis_vk_netto <= 250) {
            $prozent = $price_group['price_250'];
        } elseif ($basis_vk_netto <= 500) {
            $prozent = $price_group['price_500'];
        } elseif ($basis_vk_netto <= 750) {
            $prozent = $price_group['price_750'];
        } elseif ($basis_vk_netto <= 1000) {
            $prozent = $price_group['price_1000'];
        } elseif ($basis_vk_netto <= 2000) {
            $prozent = $price_group['price_2000'];
        } else {
            $prozent = $price_group['price_gt_2000'];
        }

        $new_price = $basis_vk_netto * ($prozent / 100 + 1);

        if ($shop_price != 0 && $shop_price < $new_price) {
            $new_price = $shop_price;
        }

        return round($new_price, 2);
    }


    /**
     * tut die master_cat_id den master_product zuordnen
     * -matching basiert auf market_view_cat.mv_master_cat_id (händische pflege)
     *
     * @todo datenquellen priorisieren -> zurzeit ist immer die letzte Datenquelle priorisiert (market_view_product innodb -> mvsrc_id)
     */
    public function matchMasterCatIdsToProducts(): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_master_product INNER JOIN
                market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id) INNER JOIN
                market_view_cat ON (market_view_product.mv_cat_id = market_view_cat.mv_cat_id)
            SET
                market_view_master_product.mv_master_cat_id = market_view_cat.mv_master_cat_id
            WHERE
                market_view_master_product.mv_master_cat_id = 0 AND
                market_view_cat.mv_master_cat_id > 0
        ");
    }


    public function updateProductTypes(bool $full_update): void
    {
        $this->updateProductTypesByProductTypeMapping($full_update);
        $this->updateProductTypesByProductId();
    }


    public function updateProductTypesByProductTypeMapping(bool $full_update): void
    {
        $max_id = $this->db_mv->fieldQuery("
            SELECT
                MAX(market_view_master_product.mv_master_product_id)
            FROM
                market_view_master_product
        ");

        $batch_size = 250000;

        //batchen, sonst stirbt der slave... das mit der window function ist nicht sehr effektiv.
        for ($i = 0; $i <= $max_id; $i += $batch_size) {
            $sql_condition = 'market_view_master_product.mv_master_product_id BETWEEN ' . $i . ' AND ' . ($i + $batch_size);

            if (!$full_update) {
                $sql_condition .= " AND market_view_master_product.product_type = '" . ProductConst::PRODUCT_TYPE_UNBEKANNT . "' ";
            }

            $this->updateProductTypesByProductTypeMapping_batch($sql_condition);
        }
    }

    private function updateProductTypesByProductTypeMapping_batch(string $sql_condition): void
    {
        $change_select = "
            SELECT
                mapping.mv_master_product_id,
                mapping.product_type
            FROM
                (
                    SELECT
                        mapping_all.mv_master_product_id,
                        mapping_all.current_product_type,
                        mapping_all.product_type,
                        ROW_NUMBER() OVER (PARTITION BY mapping_all.mv_master_product_id ORDER BY mapping_all.mapping_count DESC) AS pos
                    FROM
                        (
                            SELECT
                                market_view_master_product.mv_master_product_id,
                                market_view_master_product.product_type AS current_product_type,
                                market_view_cat_mapping_product_type.product_type,
                                COUNT(*) AS mapping_count
                            FROM
                                market_view_master_product INNER JOIN
                                market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id) INNER JOIN
                                market_view_cat_mapping_product_type ON (market_view_product.mv_cat_id = market_view_cat_mapping_product_type.mv_cat_id || market_view_product.mv_cat_id_2 = market_view_cat_mapping_product_type.mv_cat_id)
                            WHERE
                                $sql_condition
                            GROUP BY
                                market_view_master_product.mv_master_product_id,
                                market_view_cat_mapping_product_type.product_type
                        ) AS mapping_all
                ) AS mapping
            WHERE
                mapping.pos = 1 AND
                mapping.current_product_type != mapping.product_type
        ";

        //$this->db_mv->query($change_select)->display();

        $this->db_mv->query("
            UPDATE
                market_view_master_product INNER JOIN
                ($change_select) AS product_type_mapping ON (market_view_master_product.mv_master_product_id = product_type_mapping.mv_master_product_id)
            SET
                market_view_master_product.product_type = product_type_mapping.product_type
        ");
    }

    public function updateProductTypesByProductId(): void
    {
        $product_ids = $this->db_mv->query("
            SELECT
                market_view_master_product.product_id
            FROM
                market_view_master_product
            WHERE
                market_view_master_product.product_id != 0 AND
                market_view_master_product.product_type = '" . ProductConst::PRODUCT_TYPE_UNBEKANNT . "'
        ")->asSingleArray();

        if (!$product_ids) {
            return;
        }

        $result = $this->db->query("
            SELECT
                product.product_type,
                product.product_id
            FROM
                product
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ") AND
                product.product_type != '" . ProductConst::PRODUCT_TYPE_UNBEKANNT . "'
        ")->asMultiArraySingle('product_type', 'product_id');

        foreach ($result as $product_type => $product_ids) {
            $this->db_mv->query("
                UPDATE
                    market_view_master_product
                SET
                    market_view_master_product.product_type = '" . $product_type . "'
                WHERE
                    market_view_master_product.product_id IN (" . $this->db->in($product_ids) . ")
            ");
        }
    }

//    public function updateProductTypesByProductTypeMapping(): void
//    {
//        $this->db_mv->query("
//            UPDATE
//                market_view_master_product INNER JOIN
//                market_view_product ON (market_view_master_product.ean = market_view_product.ean) INNER JOIN
//                market_view_cat_mapping_product_type ON (market_view_product.mv_cat_id = market_view_cat_mapping_product_type.mv_cat_id)
//            SET
//                market_view_master_product.product_type = market_view_cat_mapping_product_type.product_type
//            WHERE
//                market_view_master_product.product_type = '".ProductConst::PRODUCT_TYPE_UNBEKANNT."' AND
//                market_view_cat_mapping_product_type.product_type != '".ProductConst::PRODUCT_TYPE_UNBEKANNT."'
//        ");
//
//        $this->db_mv->query("
//            UPDATE
//                market_view_master_product INNER JOIN
//                market_view_product ON (market_view_master_product.ean = market_view_product.ean) INNER JOIN
//                market_view_cat_mapping_product_type ON (market_view_product.mv_cat_id_2 = market_view_cat_mapping_product_type.mv_cat_id)
//            SET
//                market_view_master_product.product_type = market_view_cat_mapping_product_type.product_type
//            WHERE
//                market_view_master_product.product_type = '".ProductConst::PRODUCT_TYPE_UNBEKANNT."' AND
//                market_view_cat_mapping_product_type.product_type != '".ProductConst::PRODUCT_TYPE_UNBEKANNT."'
//        ");
//    }

//    private function updateProductTypesByWwsCatMapping(): void
//    {
//        //ohne joins ist das schon ganz schön aufwendig...
//        $result = $this->db_mv->query("
//            SELECT
//                market_view_cat_mapping.cat_id,
//                market_view_master_product.mv_master_product_id
//            FROM
//                market_view_master_product INNER JOIN
//                market_view_product ON (market_view_master_product.ean = market_view_product.ean) INNER JOIN
//                market_view_cat_mapping ON (market_view_product.mv_cat_id = market_view_cat_mapping.mv_cat_id)
//            WHERE
//                market_view_master_product.product_type = '".ProductConst::PRODUCT_TYPE_UNBEKANNT."'
//        ")->asMultiArraySingle('cat_id', 'mv_master_product_id');
//
//        $this->helper_updateProductTypesByWwsCatMapping($result);
//
//        $result = $this->db_mv->query("
//            SELECT
//                market_view_cat_mapping.cat_id,
//                market_view_master_product.mv_master_product_id
//            FROM
//                market_view_master_product INNER JOIN
//                market_view_product ON (market_view_master_product.ean = market_view_product.ean) INNER JOIN
//                market_view_cat_mapping ON (market_view_product.mv_cat_id_2 = market_view_cat_mapping.mv_cat_id)
//            WHERE
//                market_view_master_product.product_type = '".ProductConst::PRODUCT_TYPE_UNBEKANNT."'
//        ")->asMultiArraySingle('cat_id', 'mv_master_product_id');
//
//        $this->helper_updateProductTypesByWwsCatMapping($result);
//    }
//
//    private function helper_updateProductTypesByWwsCatMapping(array $result): void
//    {
//        $mapping = $this->db->query("
//            SELECT
//                product_cat.cat_id,
//                product_cat.product_type
//            FROM
//                product_cat
//            WHERE
//                product_cat.cat_id IN (".$this->db->in(array_keys($result)).") AND
//                product_cat.product_type NOT IN ('', '".ProductConst::PRODUCT_TYPE_UNBEKANNT."')
//        ")->asSingleArray('cat_id');
//
//        foreach ($mapping as $cat_id => $product_type) {
//            $mv_master_product_ids = $result[$cat_id];
//
//            $this->db_mv->query("
//                UPDATE
//                    market_view_master_product
//                SET
//                    market_view_master_product.product_type = '".$product_type."'
//                WHERE
//                    market_view_master_product.mv_master_product_id IN (".$this->db_mv->in($mv_master_product_ids).")
//            ");
//        }
//    }

    public function syncMasterProductTypesToMvsrcProduct(): void
    {
        //market_view_product.product_type wird nicht direkt gepflegt, sondern immer über market_view_master_product
        $this->db_mv->query("
            UPDATE
                market_view_product INNER JOIN
                market_view_master_product ON (market_view_product.ean = market_view_master_product.ean)
            SET
                market_view_product.product_type = market_view_master_product.product_type
            WHERE
                market_view_product.product_type != market_view_master_product.product_type
        ");
    }
}
