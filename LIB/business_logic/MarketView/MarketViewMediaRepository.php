<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Model\SmartDataEntityNotFoundException;
use InvalidArgumentException;
use wws\MarketView\Entities\MarketViewMedia;

class MarketViewMediaRepository
{
    private db_generic $db_mv;
    private MarketViewMediaStorage $media_storage;

    public function __construct(db_generic $db_mv, MarketViewMediaStorage $media_storage)
    {
        $this->db_mv = $db_mv;
        $this->media_storage = $media_storage;
    }

    public function existsMedia(int $mvsrc_id, string $mvsrc_product_id): bool
    {
        return (bool)$this->db_mv->fieldQuery("
            SELECT
                market_view_media.mvsrc_product_id
            FROM
                market_view_media
            WHERE
                market_view_media.mvsrc_id = " . $mvsrc_id . " AND
                market_view_media.mvsrc_product_id = " . $this->db_mv->quote($mvsrc_product_id) . "
        ");
    }

    public function existsMediaByMd5(int $mvsrc_id, string $mvsrc_product_id, string $md5): bool
    {
        return (bool)$this->db_mv->fieldQuery("
            SELECT
                market_view_media.mvsrc_product_id
            FROM
                market_view_media
            WHERE
                market_view_media.mvsrc_id = $mvsrc_id AND
                market_view_media.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "' AND
                market_view_media.md5 = '" . $this->db_mv->escape($md5) . "'
            LIMIT
                1
        ");
    }

    public function saveMedia(Entities\MarketViewMedia $market_view_media): void
    {
        $mv_media_id = $market_view_media->getMvMediaId();

        if ($market_view_media->isLocalFilename() && !$market_view_media->getUrlStorage()) {
            throw new DevException('url_storage must be set with a local:// url!');
        }

        //Kompatibilität zu den alten surrogate keys
        if (!$market_view_media->isMvMediaIdResolved()) {
            $this->resolveMvMediaIdLegacy($market_view_media);
            $mv_media_id = $market_view_media->getMvMediaId();
        }

        $data = $market_view_media->_getRawData();
        if (!$data['mvsrc_product_id']) {
            throw new DevException('mvsrc_product_id is missing');
        }
        if (!$data['mvsrc_id']) {
            throw new DevException('mvsrc_id is missing');
        }

        if (!$data['media_type']) {
            throw new InvalidArgumentException('media_type is missing');
        }

        if (!$mv_media_id) {
            $data['created_at'] = date('Y-m-d H:i:s');

            $this->db_mv->simpleInsert('market_view_media', $data);
            $market_view_media->setMvMediaId($mv_media_id);
            $market_view_media->setMvMediaIdResolved(true);
        } else {
            $this->db_mv->simpleUpdate('market_view_media', $data, 'market_view_media.mv_media_id = ' . $mv_media_id);
        }
    }


    public static function getSqlFieldsForLoad(): string
    {
        return "
            market_view_media.mv_media_id,
            market_view_media.mvsrc_id,
            market_view_media.mvsrc_product_id,
            market_view_media.media_key,
            market_view_media.media_type,
            market_view_media.url,
            market_view_media.url_storage,
            market_view_media.topic,
            market_view_media.pos,
            market_view_media.description,
            market_view_media.md5,
            market_view_media.fingerprint,
            market_view_media.width,
            market_view_media.height
        ";
    }

    /**
     * @param int $mvsrc_id
     * @param string $mvsrc_product_id
     * @return MarketViewMedia[]
     */
    public function loadMediaForMvsrcProduct(int $mvsrc_id, string $mvsrc_product_id): array
    {
        $result = $this->db_mv->query("
            SELECT
                " . self::getSqlFieldsForLoad() . "
            FROM
                market_view_media
            WHERE
                market_view_media.mvsrc_id = '" . $mvsrc_id . "' AND
                market_view_media.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
            ORDER BY
                market_view_media.pos
        ");

        $entities = [];

        foreach ($result as $row) {
            $entity = new MarketViewMedia();
            $entity->fromArray($row);

            $entities[] = $entity;
        }

        return $entities;
    }

    public function loadMedia(int $mv_media_id): MarketViewMedia
    {
        $media = current($this->loadMediaByMvMediaIds([$mv_media_id]));

        if (!$media) {
            throw new SmartDataEntityNotFoundException('MarketViewMedia not found (' . $mv_media_id . ')');
        }

        return $media;
    }

    /**
     * @param array $mv_media_ids
     * @return MarketViewMedia[]
     */
    public function loadMediaByMvMediaIds(array $mv_media_ids): array
    {
        if (!$mv_media_ids) {
            return [];
        }

        $result = $this->db_mv->query("
            SELECT
                " . self::getSqlFieldsForLoad() . "
            FROM
                market_view_media
            WHERE
                market_view_media.mv_media_id IN (" . $this->db_mv->in($mv_media_ids) . ")
        ");

        $entities = [];

        foreach ($result as $row) {
            $entity = new MarketViewMedia();
            $entity->fromArray($row);

            $entities[] = $entity;
        }

        return $entities;
    }

    private function resolveMvMediaIdLegacy(MarketViewMedia $market_view_media): void
    {
        $mv_media_id = (int)$this->db_mv->fieldQuery("
            SELECT
                market_view_media.mv_media_id
            FROM
                market_view_media
            WHERE
                market_view_media.mvsrc_id = '" . $market_view_media->getMvsrcId() . "' AND
                market_view_media.mvsrc_product_id = '" . $this->db_mv->escape($market_view_media->getMvsrcProductId()) . "' AND
                market_view_media.media_key = '" . $this->db_mv->escape($market_view_media->getMediaKey()) . "'
        ");

        $market_view_media->setMvMediaId($mv_media_id);
        $market_view_media->setMvMediaIdResolved(true);
    }


    /**
     * entfernt $url_storage aus alle entsprechenden market_view_media entitäten, setzt die meta daten zurück und löscht das file aus dem storage.
     *
     * @param string $url_storage
     * @return void
     */
    public function deleteUrlStorageFromEntities(string $url_storage): void
    {
        if ($this->media_storage->urlStorageExists($url_storage)) {
            $this->media_storage->_delete($url_storage);
        }

        $mv_media_ids = $this->db_mv->query("
            SELECT
                market_view_media.mv_media_id
            FROM
                market_view_media
            WHERE
                market_view_media.url_storage = '" . $this->db_mv->escape($url_storage) . "'
        ")->asSingleArray();

        $market_view_medias = $this->loadMediaByMvMediaIds($mv_media_ids);

        foreach ($market_view_medias as $market_view_media) {
            $market_view_media->clearMetaData();
            $market_view_media->setUrlStorage('');
            $this->saveMedia($market_view_media);
        }
    }

    public function deleteUrlStorageFromEntity(MarketViewMedia $market_view_media): void
    {
        //throw new DevException('not implemented');

        if (!$market_view_media->getUrlStorage()) {
            throw new FatalException('entity has no url_storage location!');
        }

        //nur aus dem storage löschen, wenn es von keiner anderen entity referenziert wird
        $is_used_by_other_entities = $this->db_mv->fieldQuery("
            SELECT
                COUNT(*)
            FROM
                market_view_media
            WHERE
                market_view_media.url_storage = '" . $this->db_mv->escape($market_view_media->getUrlStorage()) . "' AND
                market_view_media.mv_media_id != " . $market_view_media->getMvMediaId() . "
        ");

        if (!$is_used_by_other_entities) {
            $this->media_storage->_delete($market_view_media->getUrlStorage());
        }
        //

        $market_view_media->setUrlStorage('');
        $market_view_media->clearMetaData();
        $this->saveMedia($market_view_media);
    }
}
