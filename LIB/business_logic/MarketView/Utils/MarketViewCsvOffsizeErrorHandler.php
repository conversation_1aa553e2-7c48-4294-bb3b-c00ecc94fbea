<?php

namespace wws\MarketView\Utils;

use bqp\Collection\CircularBuffer;
use debug;

class MarketViewCsvOffsizeErrorHandler
{
    private int $error = 0;
    private int $ignore_first_errors = 0;

    private CircularBuffer $error_cache;

    public function __construct(int $ignore_first_errors = 0)
    {
        $this->setIgnoreFirstErrors($ignore_first_errors);

        $this->error_cache = new CircularBuffer(100);
    }

    public function setIgnoreFirstErrors(int $ignore_first_errors): void
    {
        $this->ignore_first_errors = $ignore_first_errors;
    }

    public function __invoke(array $row, int $line): void
    {
        $this->error++;

        $this->error_cache->add(['line' => $line, 'data' => $row]);

        if ($this->error <= $this->ignore_first_errors) {
            return;
        }

        $message = 'Es sind mehr als ' . $this->ignore_first_errors . ' skip off size <PERSON><PERSON> aufgetreten.';
        debug::dump($message);
        debug::dump($this->error_cache->get());
        throw new MarketViewCsvOffsizeException($message);
    }
}
