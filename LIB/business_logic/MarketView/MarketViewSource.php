<?php

namespace wws\MarketView;

use bqp\Exceptions\FatalException;
use bqp\Exceptions\InputException;
use bqp\Model\SmartDataObj;
use db;

class MarketViewSource
{
    private SmartDataObj $data;

    public function __construct(?int $mvsrc_id = null)
    {
        $this->data = new SmartDataObj($this);

        if ($mvsrc_id === null) {
            $this->loadDefaults();
        } else {
            $this->load($mvsrc_id);
        }
    }

    public function loadDefaults(): void
    {
    }

    public function load(int $mvsrc_id): void
    {
        $db_mv = db::getInstance('market_view');

        $daten = $db_mv->singleQuery("
            SELECT
                market_view_source.mvsrc_id,
                market_view_source.mvsrc_name,
                market_view_source.typ,
                market_view_source.status,
                market_view_source.supplier_id,
                market_view_source.price_typ,
                market_view_source.inventory_typ,
                market_view_source.source_product_validity,
                market_view_source.source_availability_validity,
                market_view_source.source_price_validity,
                market_view_source.product_creator,
                market_view_source.source_std_priority,
                market_view_source.cache_media,
                market_view_source.ean_edit,
                market_view_source.image_data_source_id,
                market_view_source.remove_matching_on_ean_change,
                market_view_source.matching_mpn,
                market_view_source.matching_extra_class,
                market_view_source.use_devices,
                market_view_source.metric_product_availabilities_enabled,
                market_view_source.autocreate_type
            FROM
                market_view_source
            WHERE
                market_view_source.mvsrc_id = '" . $mvsrc_id . "'
        ");

        if (!$daten) {
            throw new FatalException("object with $mvsrc_id (" . $mvsrc_id . ") doesn't exists.");
        }

        $this->data->loadDaten($daten);
    }

    public function save(): int
    {
        $this->validate();

        $changes = $this->data->getChanges();

        if (!$changes) {
            return $this->getMvsrcId();
        }

        $db_mv = db::getInstance('market_view');

        $sql = [];

        foreach ($changes as $key => $value) {
            $sql[] = "market_view_source.$key = " . $db_mv->quote($value);
        }

        switch ($this->data->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db_mv->query("
                    INSERT INTO
                        market_view_source
                    SET
                        " . implode(',', $sql) . "
                ");

                $this->data->setterDirect('mvsrc_id', $db_mv->insert_id());
                break;
            case SmartDataObj::STATUS_UPDATE:
                $db_mv->query("
                    UPDATE
                        market_view_source
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        market_view_source.mvsrc_id = '" . $this->getMvsrcId() . "'
                    LIMIT
                        1
                ");
                break;
        }

        $this->data->setSaved();

        return $this->getMvsrcId();
    }

    public function validate(): void
    {
        $input = new InputException();

        //if(!$this->getFirma()) $input->add('firma', 'Bitte geben Sie einen Firmennamen.');

        $input->check();
    }

    public function setMvsrcId(int $mvsrc_id): bool
    {
        return $this->data->setter('mvsrc_id', $mvsrc_id);
    }

    public function setMvsrcName(string $mvsrc_name): bool
    {
        return $this->data->setter('mvsrc_name', $mvsrc_name);
    }

    public function setTyp(string $typ): bool
    {
        return $this->data->setter('typ', $typ);
    }

    public function setStatus(string $status): bool
    {
        return $this->data->setter('status', $status);
    }

    public function setSupplierId(int $supplier_id): bool
    {
        return $this->data->setter('supplier_id', $supplier_id);
    }

    public function setPriceTyp(string $price_typ): bool
    {
        return $this->data->setter('price_typ', $price_typ);
    }

    public function setInventoryTyp(string $inventory_typ): bool
    {
        return $this->data->setter('inventory_typ', $inventory_typ);
    }

    public function setSourceProductValidity(int $source_product_validity): bool
    {
        return $this->data->setter('source_product_validity', $source_product_validity);
    }

    public function setSourceAvailabilityValidity(int $source_availability_validity): bool
    {
        return $this->data->setter('source_availability_validity', $source_availability_validity);
    }

    public function setSourcePriceValidity(int $source_price_validity): bool
    {
        return $this->data->setter('source_price_validity', $source_price_validity);
    }

    public function setProductCreator(string $product_creator): bool
    {
        return $this->data->setter('product_creator', $product_creator);
    }

    public function setSourceStdPriority(int $source_std_priority): bool
    {
        return $this->data->setter('source_std_priority', $source_std_priority);
    }

    public function setCacheMedia(bool $cache_media): bool
    {
        return $this->data->setter('cache_media', $cache_media ? 1 : 0);
    }

    public function setEanEdit(bool $ean_edit): bool
    {
        return $this->data->setter('ean_edit', $ean_edit ? 1 : 0);
    }

    public function setImageDataSourceId(?string $image_data_source_id): bool
    {
        return $this->data->setter('image_data_source_id', $image_data_source_id);
    }

    public function setTextDataSourceId(?string $text_data_source_id): bool
    {
        return $this->data->setter('text_data_source_id', $text_data_source_id);
    }

    public function setRemoveMatchingOnEanChange(bool $remove_matching_on_ean_change): bool
    {
        return $this->data->setter('remove_matching_on_ean_change', $remove_matching_on_ean_change ? 1 : 0);
    }

    public function setMatchingMpn(bool $matching_mpn): bool
    {
        return $this->data->setter('matching_mpn', $matching_mpn ? 1 : 0);
    }

    public function setMatchingExtraClass(string $matching_extra_class): bool
    {
        return $this->data->setter('matching_extra_class', $matching_extra_class);
    }

    public function setUseDevices(bool $use_devices): bool
    {
        return $this->data->setter('use_devices', $use_devices ? 1 : 0);
    }

    public function setMetricProductAvailabilitiesEnabled(bool $metric_product_availabilities_enabled): bool
    {
        return $this->data->setter('metric_product_availabilities_enabled', $metric_product_availabilities_enabled ? 1 : 0);
    }

    public function setAutocreateType(string $autocreate_type): bool
    {
        return $this->data->setter('autocreate_type', $autocreate_type);
    }

    public function getMvsrcId(): int
    {
        return (int)$this->data->getter('mvsrc_id');
    }

    public function getMvsrcName(): string
    {
        return $this->data->getter('mvsrc_name');
    }

    public function getTyp(): string
    {
        return $this->data->getter('typ');
    }

    public function getStatus(): string
    {
        return $this->data->getter('status');
    }

    public function getSupplierId(): int
    {
        return (int)$this->data->getter('supplier_id');
    }

    public function getPriceTyp(): string
    {
        return $this->data->getter('price_typ');
    }

    public function getInventoryTyp(): string
    {
        return $this->data->getter('inventory_typ');
    }

    public function getSourceProductValidity(): int
    {
        return (int)$this->data->getter('source_product_validity');
    }

    public function getSourceAvailabilityValidity(): int
    {
        return (int)$this->data->getter('source_availability_validity');
    }

    public function getSourcePriceValidity(): int
    {
        return (int)$this->data->getter('source_price_validity');
    }

    public function getProductCreator(): string
    {
        return $this->data->getter('product_creator');
    }

    public function getSourceStdPriority(): int
    {
        return (int)$this->data->getter('source_std_priority');
    }

    public function getCacheMedia(): bool
    {
        return (bool)$this->data->getter('cache_media');
    }

    public function getEanEdit(): bool
    {
        return (bool)$this->data->getter('ean_edit');
    }

    public function getImageDataSourceId(): ?string
    {
        return $this->data->getter('image_data_source_id');
    }

    public function getTextDataSourceId(): ?string
    {
        return $this->data->getter('text_data_source_id');
    }

    public function getRemoveMatchingOnEanChange(): bool
    {
        return (bool)$this->data->getter('remove_matching_on_ean_change');
    }

    public function getMatchingMpn(): bool
    {
        return (bool)$this->data->getter('matching_mpn');
    }

    public function getMatchingExtraClass(): string
    {
        return $this->data->getter('matching_extra_class');
    }

    public function getUseDevices(): bool
    {
        return (bool)$this->data->getter('use_devices');
    }

    public function getMetricProductAvailabilitiesEnabled(): bool
    {
        return (bool)$this->data->getter('metric_product_availabilities_enabled');
    }

    public function getAutocreateType(): string
    {
        return $this->data->getter('autocreate_type');
    }
}