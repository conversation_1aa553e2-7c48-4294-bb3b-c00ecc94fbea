<?php

namespace wws\MarketView;

use bqp\db\db_generic;

class MarketViewCatCount
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    public function update(): void
    {
        $this->updateDeviceCounts();
        $this->updateProductCounts();
    }

    public function updateDeviceCounts(): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_cat
            SET
                market_view_cat.count_devices = 0
        ");

        $this->db_mv->query("
            UPDATE
                (
                    SELECT
                        market_view_device_cats.mv_cat_id,
                        SUM(market_view_device_cats.device_count) AS device_count
                    FROM
                        (
                            SELECT
                                market_view_device.mv_cat_id,
                                COUNT(*) AS device_count
                            FROM
                                market_view_device
                            GROUP BY
                                market_view_device.mv_cat_id
                        UNION
                            SELECT
                                market_view_device.mv_cat_id_2,
                                COUNT(*) AS device_count
                            FROM
                                market_view_device
                            GROUP BY
                                market_view_device.mv_cat_id_2
                        ) AS market_view_device_cats
                    GROUP BY
                        market_view_device_cats.mv_cat_id
                ) AS devices INNER JOIN
                market_view_cat ON (market_view_cat.mv_cat_id = devices.mv_cat_id)
            SET
                market_view_cat.count_devices = devices.device_count
        ");
    }

    public function updateProductCounts(): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_cat
            SET
                market_view_cat.count_products = 0
        ");

        $this->db_mv->query("
            UPDATE
                (
                    SELECT
                        market_view_product.mv_cat_id,
                        COUNT(*) AS product_count
                    FROM
                        market_view_product
                    GROUP BY
                        market_view_product.mv_cat_id
                ) AS market_view_cat_count INNER JOIN
                market_view_cat ON (market_view_cat.mv_cat_id = market_view_cat_count.mv_cat_id)
            SET
                market_view_cat.count_products = market_view_cat_count.product_count
        ");

        $this->db_mv->query("
            UPDATE
                (
                    SELECT
                        market_view_product.mv_cat_id_2 AS mv_cat_id,
                        COUNT(*) AS product_count
                    FROM
                        market_view_product
                    GROUP BY
                        market_view_product.mv_cat_id_2
                ) AS market_view_cat_count INNER JOIN
                market_view_cat ON (market_view_cat.mv_cat_id = market_view_cat_count.mv_cat_id)
            SET
                market_view_cat.count_products = market_view_cat.count_products+market_view_cat_count.product_count
        ");
    }
}
