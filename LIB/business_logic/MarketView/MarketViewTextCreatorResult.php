<?php

namespace wws\MarketView;

class MarketViewTextCreatorResult
{
    private string $product_name = '';
    private string $description = '';
    private ?string $description_data_source_id = null;

    public function isDescription(): bool
    {
        return (bool)trim($this->description);
    }

    public function getProductName(): string
    {
        return $this->product_name;
    }

    public function setProductName(string $product_name): void
    {
        $this->product_name = $product_name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getDescriptionDataSourceId(): ?string
    {
        return $this->description_data_source_id;
    }

    public function setDescriptionDataSourceId(?string $description_data_source_id): void
    {
        $this->description_data_source_id = $description_data_source_id;
    }
}
