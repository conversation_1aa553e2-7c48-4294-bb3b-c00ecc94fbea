<?php

namespace wws\MarketView\Metric;

use bqp\db\db_generic;
use bqp\Metric\MetricCollector;
use bqp\Metric\MetricService;
use bqp\Utils\ArrayUtils;
use service_loader;
use wws\Product\ProductRepositoryLegacy;

class MarketViewProductAvailabilitiesMetric
{


    private db_generic $db_mv;
    private MetricCollector $metric_collector;


    public string $date;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;

        $metric_service = service_loader::get(MetricService::class);
        $this->metric_collector = $metric_service->getMetricCollector('market_view_product_availabilities');

        $this->date = date('Y-m-d 00:00:00');
    }

    /**
     * @param string $date
     */
    public function setDate(string $date): void
    {
        $this->date = $date;
    }

    public function run(): void
    {
        $raw_data = $this->collectRawData();

        //das aufbereiten der aggregieren im speicher. ich hab grade kein rollup für product_type hinbekommen. das hätte ich extra abfragen müssen, will ich bei den datenmengen aber nicht.
        $data = ArrayUtils::groupBy($raw_data, 'mvsrc_id');
        foreach ($data as $mvsrc_id => $mvsrc_data) {
            $data[$mvsrc_id] = $this->groupMvsrcByProductType($mvsrc_data);
        }

        $this->metric_collector->setDate($this->date);
        foreach ($data as $mvsrc_id => $mvsrc_data) {
            foreach ($mvsrc_data as $product_type => $fields) {
                $this->metric_collector->add($fields, ['mvsrc_id' => $mvsrc_id, 'product_type' => $product_type]);
            }
        }
    }

    public function collectRawData()
    {
        $data = $this->db_mv->query("
            SELECT
                0 AS mvsrc_id,
                market_view_product.product_type,
                market_view_availability.metric_field,
                COUNT(*) AS qunatity
            FROM
                market_view_product INNER JOIN
                market_view_availability ON (market_view_product.availability_id = market_view_availability.availability_id)
            GROUP BY
                market_view_product.product_type,
                market_view_availability.metric_field
        ")->asArray();

        $data_detail = $this->db_mv->query("
            SELECT
                market_view_source.mvsrc_id,
                market_view_product.product_type,
                market_view_availability.metric_field,
                COUNT(*) AS qunatity
            FROM
                market_view_source INNER JOIN
                market_view_product ON (market_view_source.mvsrc_id = market_view_product.mvsrc_id) INNER JOIN
                market_view_availability ON (market_view_product.availability_id = market_view_availability.availability_id)
            WHERE
                market_view_source.metric_product_availabilities_enabled = 1
            GROUP BY
                market_view_source.mvsrc_id,
                market_view_product.product_type,
                market_view_availability.metric_field
        ")->asArray();

        $data = array_merge($data, $data_detail);

        return $data;
    }


    private function groupMvsrcByProductType(array $mvsrc_data): array
    {
        $result = [];

        $result_all = [];

        foreach (ArrayUtils::groupBy($mvsrc_data, 'product_type') as $product_type => $product_type_data) {
            $result[$product_type] = $this->flatMetrics($product_type_data);

            foreach ($result[$product_type] as $key => $value) {
                if (!isset($result_all[$key])) {
                    $result_all[$key] = 0;
                }

                $result_all[$key] += $value;
            }
        }

        $result['all'] = $result_all;

        return $result;
    }

    private function flatMetrics(array $product_type_data): array
    {
        $metrics = [
            'in_storage' => 0,
            'out_of_stock' => 0,
            'unknown' => 0,
            'offline' => 0,
            'total_online' => 0,
            'total' => 0
        ];

        foreach ($product_type_data as $value) {
            $metrics[$value['metric_field']] = (int)$value['qunatity'];
        }

        $metrics['total'] = array_sum($metrics);
        $metrics['total_online'] = $metrics['total'] - $metrics['offline'];

        unset($metrics['ignore']);

        return $metrics;
    }


    public function getTagMarketViewSourceNames(): array
    {
        $names = [
            0 => '- Alle -'
        ];

        $result = $this->db_mv->query("
            SELECT
                market_view_source.mvsrc_id,
                market_view_source.mvsrc_name
            FROM
                market_view_source
            WHERE
                market_view_source.metric_product_availabilities_enabled = 1
            ORDER BY
                market_view_source.mvsrc_name
        ")->asSingleArray('mvsrc_id');

        $names += $result;

        return $names;
    }

    public function getTagProductTypes(): array
    {
        $names = [
            'all' => '- Alle -'
        ];
        $names = array_merge($names, ProductRepositoryLegacy::getProductTypeNames());

        return $names;
    }

    public function getFieldNames(): array
    {
        return ArrayUtils::extract($this->metric_collector->getFieldDefinitions(), 'label', true);
    }
}
