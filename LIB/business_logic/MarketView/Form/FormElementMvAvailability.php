<?php

namespace wws\MarketView\Form;

use bqp\form\form_element_select;
use service_loader;
use wws\MarketView\MarketViewRepository;

class FormElementMvAvailability extends form_element_select
{

    public function __construct()
    {
        parent::__construct();

        $this->setLabel('Verfügbarkeit');

        $this->option_callback = function () {
            $repo = service_loader::get(MarketViewRepository::class);

            return array_column($repo->getAvailabilitiesAsArray(), 'availability_name', 'availability_id');
        };
    }
}
