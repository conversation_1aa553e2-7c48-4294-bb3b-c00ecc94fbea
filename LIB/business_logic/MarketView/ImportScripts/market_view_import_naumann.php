<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_naumann extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $this->getProductList();

        $this->parseCsv();

        parent::updateComplete();
    }

    private function parseCsv()
    {
        $csv = new CsvReader($this->config['temp_dir'] . '/naumann.csv');
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter(',');
        $csv->setSrcCharset('ISO-8859-1');
        $csv->setDstCharset($this->config['charset']);


        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string'],
            'product_name' => ['typ' => 'string'],
            'ean' => ['typ' => 'string'],
            'hersteller_name' => ['typ' => 'string'],
            'vk_netto' => ['typ' => 'float'],
            'mv_availability' => ['typ' => 'string'],
            'kw' => ['typ' => 'string']
        ]);

        $temp = [];

        $this->fullUpdatePrepare();

        foreach ($csv as $row) {
            $this->saveProduct($row);
        }

        $this->fullUpdateEnd();
    }

    protected function saveProduct(array $daten): void
    {
        $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);
        $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, 'unbekannt');

        $daten['availability_id'] = $this->saveMvAvailability($daten['mv_availability']);

        $daten['mv_availability'] = $daten['mv_availability'];
        if ($daten['kw']) {
            $daten['mv_availability'] .= ' ' . $daten['kw'];
        }

        parent::saveProduct($daten);
    }


    private function getProductList()
    {
        $this->clear();

        $content = file_get_contents($this->config['csv']);
        $content = str_replace('\\"', '"', $content); //nauman hat manchmal ein \ am ende eines Feldes, wird von fgetcsv als escape gewertet -> dreckt

        file_put_contents($this->config['temp_dir'] . '/naumann.csv', $content);
    }

    private function clear()
    {
        if (file_exists($this->config['temp_dir'] . '/naumann.csv')) {
            unlink($this->config['temp_dir'] . '/naumann.csv');
        }
    }
}
