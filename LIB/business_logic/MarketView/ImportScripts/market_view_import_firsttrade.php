<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\Vat\VatRate;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_firsttrade extends MarketViewImportSimple
{
    /**
     * @var VatRate
     */
    private $vat_rate;

    public function init(): void
    {
        $this->vat_rate = $this->getDefaultImportVatRate();
    }

    public function updateComplete(): void
    {
        $this->parseCsv();

        parent::updateComplete();
    }

    private function parseCsv()
    {
        $csv = new CsvReader($this->config['csv']);
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter('    ');
        $csv->setSrcCharset('ISO-8859-1');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->skipFirstLines(1);

        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string'],
            'product_name' => ['typ' => 'string'],
            'hersteller_name' => ['typ' => 'string'],
            'NONE',
            'cat_name' => ['typ' => 'string'],
            'beschreibung' => ['typ' => 'string'],
            'vk_brutto' => ['typ' => 'float'],
            'image' => ['typ' => 'string'],
            'url' => ['typ' => 'string'],
            'mv_availability' => ['typ' => 'string'],
            'shipping' => ['typ' => 'string'],
            'special' => ['typ' => 'string'],
            'expirationdate' => ['typ' => 'string'],
            'oldprice' => ['typ' => 'string'],
            'ean' => ['typ' => 'string']
        ]);

        $temp = [];

        $this->fullUpdatePrepare();

        while (($daten = $csv->getRow()) !== null) {
            $this->saveProduct($daten);
        }

        $this->fullUpdateEnd();
    }

    protected function saveProduct(array $daten): void
    {
        if (array_key_exists('hersteller_name', $daten)) {
            $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);
        }

        if (array_key_exists('cat_name', $daten)) {
            $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $daten['cat_name']);
        }

        $daten['availability_id'] = $this->saveMvAvailability($daten['mv_availability']);

        $daten['vk_netto'] = $daten['vk_brutto'] / $this->vat_rate->getAsFactor();


        $sonstiges = '';
        $sonstiges .= "url: $daten[url]\n";
        $sonstiges .= "shipping: $daten[shipping]\n";
        $sonstiges .= "special: $daten[special]\n";
        $sonstiges .= "oldprice: $daten[oldprice]\n";

        $daten['sonstiges'] = $sonstiges;


        parent::saveProduct($daten);

        $daten['image'] = str_replace('/xtm/images/', '/xtm/images/product_images/info_images/', $daten['image']);

        $this->saveMedia($daten);
    }
}
