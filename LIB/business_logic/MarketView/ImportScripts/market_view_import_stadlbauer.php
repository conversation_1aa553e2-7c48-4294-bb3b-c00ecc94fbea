<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\Exceptions\DevException;
use input;
use Laminas\Http\Client;
use wws\MarketView\Entities\MarketViewProduct;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\Product\Product;
use wws\Product\ProductBrand\ProductBrandRepository;
use wws\Product\ProductConst;

class market_view_import_stadl<PERSON>uer extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $this->parseCsv();

        //$this->parsePictures();
        parent::updateComplete();
    }

    private function parseCsv()
    {
        $csv = new CsvReader($this->config['csv']);
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter(';');
        $csv->setSrcCharset('UTF-8');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic(
            [
                'NONE', //ARKZWB;
                'NONE', //ARFNR;
                'NONE', //ARARTG;
                'mvsrc_product_id' => ['typ' => 'string'], //ARARTN;
                /*'hersteller_name' => array('typ' => 'string'),*/
                'product_name' => ['typ' => 'string'],
                'vk_netto' => ['typ' => 'float'],
                'ean' => ['typ' => 'string'],
                'zolltarifnummer' => ['typ' => 'string'],        //ARZTNR
                'NONE', //URL;
                'hoehe_mm' => ['typ' => 'float'],
                'breite_mm' => ['typ' => 'float'],
                'tiefe_mm' => ['typ' => 'float'],
                'gewicht' => ['typ' => 'float'], //ARGEWI;
                'NONE', //ARDTER;
                'NONE' //ARKZNA
            ]


        );

        $this->fullUpdatePrepare();

        while (($daten = $csv->getRow()) !== null) {
            $this->saveProduct($daten);
        }

        $this->fullUpdateEnd();
    }

    protected function saveProduct(array $daten): void
    {
        if (!$daten['mvsrc_product_id']) {
            return;
        }

        //$daten['mv_hersteller_id'] = $this->saveHersteller($daten['hersteller_name']);
        $daten['default_availability_id'] = 60;

        parent::saveProduct($daten);
    }

    private function parsePictures()
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = $this->mvsrc_id
        ");

        foreach ($result as $daten) {
            throw new DevException('not implemented');
        }
    }


    public function updateBestand()
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_product.product_id != 0
        ");

        foreach ($result as $daten) {
            $product_daten = $this->crawlProductInformation($daten['mvsrc_product_id']);

            if ($product_daten) {
                $product = [];
                $product['mvsrc_product_id'] = $daten['mvsrc_product_id'];

                $product['availability_id'] = $this->saveMvAvailability($product_daten['mv_availability']);
                $product['mv_availability'] = $product_daten['mv_availability'];

                if ($product_daten['liefertermin']) {
                    $product['mv_availability'] = $product['mv_availability'] . ' (' . $product_daten['liefertermin'] . ')';
                }

                if ($product_daten['preis']) {
                    $product['preis'] = $product_daten['preis'];
                }

                $this->updateProduct($product);

                // 'preis' => float 8.95
                // 'mv_availability' => string 'green' (length=5)
                // 'liefertermin' => null
                // 'ean' => string '9003150302018' (length=13)
            } else {
                //offline
                $this->setProductOffline($daten['mvsrc_product_id']);
            }
        }
    }

    protected $http_cleint = null;

    /**
     * @return Client
     */
    protected function getStadlbauerHttpCleint()
    {
        if (!$this->http_cleint) {
            $client = new Client();
            $client->setOptions(['maxredirects' => 5, 'timeout' => 30, 'useragent' => 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)', 'keepalive' => true]);

            $client->setUri('http://b2b.stadlbauer.at:8080/portal15/checklogin.jsp');
            $client->setParameterPost(['browser' => 'Netscape', 'version' => '5.0 (Windows de)', 'submit.x' => 12, 'submit.y' => 8, 'password' => $this->config['password'], 'user' => $this->config['username']]);
            $client->setMethod('POST');
            $client->send();

            $this->http_cleint = $client;
        }

        usleep(400000);

        return $this->http_cleint;
    }

    protected function crawlProductInformation($mvsrc_product_id)
    {
        $client = $this->getStadlbauerHttpCleint();

        $client->setUri('http://b2b.stadlbauer.at:8080/portal15/detail.jsp?product=' . $mvsrc_product_id);
        $response = $client->send();

        $body = $response->getBody();

        //$body = file_get_contents(config::getLegacy('system')->root_dir.'/temp.txt.txt');

        $body = mb_convert_encoding($body, 'UTF-8', 'ISO-8859-1');

        if (!preg_match_all('~<td class="warenkorb_shop">(.*?)</td>~s', $body, $result)) {
            return false;
        }

        $product = ['preis' => null, 'mv_availability' => null, 'liefertermin' => null, 'ean' => null];

        $stat = 'none';


        foreach ($result[1] as $daten) {
            $daten = trim($daten);

            while (1) {
                switch ($stat) {
                    case 'none':
                        switch ($daten) {
                            case '<b>EAN:</b>':
                                $stat = 'ean';
                                break;
                            case '<b>Verfügbar:</b>':
                                $stat = 'availability';
                                break;
                            case '<b>Preis:</b>':
                                $stat = 'price';
                                break;
                        }
                        break 2;
                    case 'ean':
                        $product['ean'] = $daten;
                        $stat = 'none';
                        break 2;
                    case 'availability':
                        preg_match('~<img src="images/([a-z]*).gif"~', $daten, $color);
                        $product['mv_availability'] = $color[1];
                        if ($product['mv_availability'] == 'yellow') {
                            $stat = 'availability_ext';
                            break 2;
                        } else {
                            $stat = 'none';
                            break 2;
                        }
                    // no break
                    case 'availability_ext':
                        if (!preg_match('~Voraussichtlich wieder verfügbar ab (.*)~u', $daten, $liefertermin)) {
                            $stat = 'none';
                            break;
                        }

                        $product['liefertermin'] = str_replace('&nbsp;', ' ', $liefertermin[1]);

                        break 2;
                    case 'price':
                        $preis = $daten;
                        $preis = str_replace('EUR', '', $preis);
                        $preis = str_replace('&nbsp;', '', $preis);
                        $preis = str_replace('.', '', $preis);

                        $preis = input::parseFloat($preis);

                        $product['preis'] = $preis;

                        $stat = 'none';
                        break 2;
                }
            }
        }

        return $product;
    }


    public static function getMvsrcProductAsProduct(MarketViewProduct $market_view_product, Product $product)
    {
        //produktname anppassen
        $product_name = trim($market_view_product->getProductName());

        if ($product->getBrandId()) {
            $brand_name = ProductBrandRepository::getBrandName($product->getBrandId());
            $product->setProductName($brand_name . ' ' . $product_name);
        }

        $product->setBeschreibung($market_view_product->getBeschreibung());
        $product->setBeschreibungQuelle(ProductConst::PRODUCT_DATA_SOURCE_HERSTELLER);

        return $product;
    }
}
