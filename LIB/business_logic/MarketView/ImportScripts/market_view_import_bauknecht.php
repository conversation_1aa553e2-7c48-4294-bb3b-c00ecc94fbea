<?php

namespace wws\MarketView\ImportScripts;

use bqp\Http\HttpUtils;
use debug;
use Exception;
use Laminas\Http\Client;
use wws\MarketView\Import\MarketViewImportSimple;

class market_view_import_bauknecht extends MarketViewImportSimple
{
    /**
     * @var Client
     */
    protected $http_client = null;

    /**
     * @return Client
     * @throws Exception
     */
    public function getHttpClient()
    {
        if (!$this->http_client) {
            $client = new Client();
            $client->setOptions([
                'maxredirects' => 5,
                'timeout' => 30,
                'useragent' => HttpUtils::getRandomUserAgent(),
                'keepalive' => true,
                'encodecookies' => false
            ]);

            $this->http_client = $client;

            //bosch tradeplace aktivieren
            $uid = round(microtime(true) * 1000);

            //viewstate auslesen
            $client->setUri('https://www.tradeplace.com/tradeplace/PublicPages/home.aspx?uid=' . $uid);
            $client->setMethod('GET');
            $response = $client->send();

            $viewstate = $this->extractViewstat($response->getBody());
            $event_validation = $this->extractEventvalidation($response->getBody());
            //allego
            $client->resetParameters();
            //login tradeplace

            $client->setParameterPost([
                '__EVENTTARGET' => '',
                '__EVENTARGUMENT' => '',
                '__EVENTVALIDATION' => $event_validation,
                '__VIEWSTATE' => $viewstate,

                'cookiesEnabled' => 'true',
                '_ctl0$LoginLine$UIButtonDefault' => 'Login',
                '_ctl0$LoginLine$UITextboxUsername' => $this->config['username'],
                '_ctl0$LoginLine$UITextboxPassword' => $this->config['password'],
            ]);


            $client->setUri('https://www.tradeplace.com/tradeplace/PublicPages/home.aspx?uid=' . $uid);
            $client->setMethod('POST');
            $response = $client->send();

            $content = $response->getBody();

            if (!strpos($content, 'Manuel  Monska')) {
                debug::dump($content);
                throw new Exception('Login konnte nicht geprüft werden!');
            }

            //login bauknecht
            $client->setUri('https://www.tradeplace.com/tradeplace/PortalManagement/MarketPlacePopup.aspx?providerID=28&Mode=1&uid=' . round(microtime(true) * 1000));
            $client->setMethod('GET');

            $response = $client->send();
            //$content = $response->getBody();
        }

        $this->http_client->resetParameters();

        return $this->http_client;
    }

    protected function extractViewstat($content)
    {
        //viewstat extrahieren
        if (!preg_match('~__VIEWSTATE" value="(.*?)"~', $content, $temp)) {
            throw new Exception('__VIEWSTATE konnte nicht extrahiert werden!');
        }
        return $temp[1];
    }

    protected function extractEventvalidation($content)
    {
        //viewstat extrahieren
        if (!preg_match('~__EVENTVALIDATION" value="(.*?)"~', $content, $temp)) {
            throw new Exception('__EVENTVALIDATION konnte nicht extrahiert werden!');
        }
        return $temp[1];
    }
}
