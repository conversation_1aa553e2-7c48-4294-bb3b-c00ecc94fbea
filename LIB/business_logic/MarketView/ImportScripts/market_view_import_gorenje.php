<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\Csv\TableReader;
use bqp\Csv\TableReaderSpreadsheetSource;
use debug;
use service_loader;
use wws\MarketView\Import\MarketViewImportSimple;

class market_view_import_gorenje extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $this->parseCsv($this->config['csv']);
        parent::updateComplete();
    }


    private function parseCsv($file): void
    {
//        $table = new CsvReader($file);
//        $table->setDelimiter(';');
//        $table->setSrcCharset('UTF-8');
//        $table->setDstCharset('UTF-8');
//        $table->setTrim(true);
////        $table->skipOffSizeLines(false);
//        $table->setInputFormatStatic([
//            'product_name' => array('typ' => 'string', 'checkHeader' => 'Typenbezeichnung'),
//            'NONE', //'art' => array('typ' => 'string', 'checkHeader' => 'Art'),
//            'beschreibung' => array('typ' => 'string', 'checkHeader' => ' Ausstattungsmerkmale / Allgemeine Info '),
//            'NONE', //'' => array('typ' => 'string', 'checkHeader' => ''),
//            'mvsrc_product_id' => array('typ' => 'string', 'checkHeader' => 'Artikel-Nr.Set-Nr.'),
//            'ean' => array('typ' => 'string', 'checkHeader' => 'EAN-Code'),
//            'uvp' => array('typ' => 'float', 'checkHeader' => 'UVPPreisinkl. MwSt.'),
//            'vk_netto' => array('typ' => 'float', 'checkHeader' => 'RnP Preis'),
//        ]);

        $src = new TableReaderSpreadsheetSource($this->config['csv']);
        $src->skipRows(6);
        $src->setColMaxNumeric(9);

        $table = new TableReader($src);
        $table->setInputFormatStatic([
            'product_name' => ['type' => 'string', 'checkHeader' => "Typenbezeichnung"],
            'NONE', //'art' => ['type' => 'string', 'checkHeader' => "Art"],
            'beschreibung' => ['type' => 'string', 'checkHeader' => "Ausstattungsmerkmale / Allgemeine Info"],
            'NONE', //'0' => ['type' => 'string', 'checkHeader' => null],
            'mvsrc_product_id' => ['type' => 'string', 'checkHeader' => "Artikel-Nr.\nSet-Nr."],
            'ean' => ['type' => 'string', 'checkHeader' => "EAN-Code"],
            'uvp' => ['type' => '?float', 'checkHeader' => "UVP\nPreis\ninkl. MwSt."],
            'vk_netto' => ['type' => '?float', 'checkHeader' => "RnP\n Preis  €/Stück"],
            'NONE'
        ]);

        $table->addDataCallback(function (array $row) {
            if (!$row['mvsrc_product_id'] || !$row['vk_netto']) {
                return null;
            }
            return $row;
        });

        //ACHTUNG: full update passt nicht immer -> lars bekommt aus verscheidenen quellen preislisten. da fehlen dann teilweise teile des sortiments
        $this->fullUpdatePrepare();

        foreach ($table as $row) {
            $this->saveProduct($row);
        }

        $this->fullUpdateEnd();
    }


    public function parseCsvAndReadAsSnp()
    {
        //$product_repository =

        $csv = new CsvReader($this->config['csv']);
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter(';');
        $csv->setSrcCharset('ISO-8859-1');
        $csv->setDstCharset('UTF-8');
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic([
            'product_name' => ['typ' => 'string'],
            'NONE', //Art;
            'beschreibung' => ['typ' => 'string'], //Ausstattungsmerkmale / Allgemeine Info ;
            'NONE', //neu
            'mvsrc_product_id' => ['typ' => 'string'],
            'ean' => ['typ' => 'string'],
            'uvp' => ['typ' => 'float'],
            'vk_netto' => ['typ' => 'float'], //"NLP Preis  €/Set";
        ]);

        $prices = [];

        foreach ($csv as $daten) {
            if (!$daten['mvsrc_product_id']) {
                continue;
            }
            if (!$daten['vk_netto']) {
                continue;
            }

            $daten['vk_netto'] = round($daten['vk_netto'] * 0.92 * 0.95 * 0.97 * 0.97, 2); //-8% -5% -3% -3%

            $prices[$daten['mvsrc_product_id']] = $daten['vk_netto'];
        }

        debug::dump($prices);

        $product_id_map = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id,
                market_view_product.product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_product.mvsrc_product_id IN (" . $this->db_mv->in(array_keys($prices)) . ")
        ")->asSingleArray('product_id');

        $products = service_loader::getProductRepository()->loadProducts(array_keys($product_id_map));

        foreach ($products as $product) {
            $mvsrc_product_id = $product_id_map[$product->getProductId()];

            $snp = $prices[$mvsrc_product_id];

            if (!$snp) {
                continue;
            }

            $product_ek_offer = $product->getProductEkOfferIfExists(38);

            if (!$product_ek_offer) {
                continue;
            }

            $product_ek_offer->setEkSnp($snp);

            $product->save();
        }
    }

    protected function saveProduct(array $daten): void
    {
//        $mvsrc_product_id = $this->getMvsrcProductIdByEan($daten['ean']);
//
//        if ($mvsrc_product_id) {
//            $product = [
//                'mvsrc_product_id' => $mvsrc_product_id,
//                'vk_netto' => $daten['vk_netto'],
//                'default_availability_id' => 60
//            ];
//
//            $this->updateProduct($product);
//        }
//
//        return;

        $daten['mv_hersteller_id'] = $this->saveMvHersteller('Gorenje');

        //$daten['mv_cat_id'] = $this->saveCategory(\wws\MarketView\MarketViewConst::TREE_ID_DEFAULT, [$daten['cat_name'], $daten['cat_name_2']]);

        $daten['product_name'] = str_replace('""', '"', 'Gorenje ' . $daten['product_name']);
        $daten['product_name'] = trim($daten['product_name'], ' *');
        $daten['default_availability_id'] = 60;

        //$daten['sonstiges'] = 'Serie: '.$daten['serie'];
        //if($daten['neu'] == 'Neu') $daten['sonstiges'] = "Neu";
        //if($daten['neu'] == 'Auslauf') $daten['eol'] = 1;

//        var_dump($daten);
//        return;

        parent::saveProduct($daten);
    }
}
