<?php

namespace wws\MarketView\ImportScripts;

use service_loader;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\Import\MarketViewImportProduct;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatchingKnownException;

/**
 * Dient als Backup für die automatische Bilderpflege. Das System ersetzt Bilder automatisch, falls diese in besserer Auflösung in anderen
 * Quellen bereitstehen. Die Bilder die ersetzt werden, können hiermit "gesichert" werden.
 * Z.B: wir bekommen Bilder von Sonepar. Die werden als Quelle Sonepar eingespielt. Angenommen dies ersetzt ein Hersteller Bild, und nachträglich entzieht Sonepar uns
 * die Bildrechte. Dann kann das System das Sonepar Bild wieder mit dem Herstellerbild ersetzen.
 */
class market_view_import_hersteller_bilder extends MarketViewImportSimple
{
    public function init(): void
    {
        //wenn für ein produkt mehrere bilder gesichert werden, dann wird MarketViewImportProduct mehrfach angelegt
        $this->market_view_import->setSilentDuplicateErrors(true);
    }

    public function addPicture(int $product_id, string $product_name, string $brand_name, string $ean, string $blob, string $storage_url): void
    {
        $mv_product = new MarketViewImportProduct();
        $mv_product->setProductName($product_name);
        $mv_product->setMvsrcProductId($product_id);
        $mv_product->setHerstellerName($brand_name);
        $mv_product->setEan($ean);

        $this->market_view_import->saveProduct($mv_product);

        //media speichern
        $media = new MarketViewMedia();
        $media->setMvsrcId($this->mvsrc_id);
        $media->setMvsrcProductId($product_id);
        $media->setMediaType(MarketViewMedia::MEDIA_TYPE_IMAGE);
        $media->setUrl($storage_url);

        $this->media_import->uploadPicture($media, $blob);

        //ggf. matching zwischen wws und marke view erzeugen
        $matching = service_loader::getDiContainer()->get(ProductMatching::class);
        try {
            $matching->addMatching($this->mvsrc_id, $product_id, $product_id);
        } catch (ProductMatchingKnownException) {
            //uninteressant -> passiert, wenn mehrere Bilder für ein Produkt gesichert werden
        }
    }
}
