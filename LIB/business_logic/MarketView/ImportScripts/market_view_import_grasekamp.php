<?php

namespace wws\MarketView\ImportScripts;

use bqp\Exceptions\FatalException;
use bqp\extern\grasekamp\grasekamp_scraper;
use wws\MarketView\Import\MarketViewImportSimple;

class market_view_import_grasekamp extends MarketViewImportSimple
{
    private $products_read = 0;

    public function updateBestand()
    {
        $this->products_read = 0;

        $client = new grasekamp_scraper();
        $client->addBaseUrl('https://dasversandhaus24.de/garten-und-balkon/pavillon/');

        $client->setProductCallback([$this, 'productHandler']);

        $client->run();

        parent::updateComplete();

        if ($this->products_read < 10) {
            throw new FatalException("weniger als 10 Grasekamp Produkte eingelesen. (" . $this->products_read . ")");
        }
    }

    public function productHandler($product)
    {
        if (strlen($product['mv_availability']) > 32) {
            $product['mv_availability'] = substr($product['mv_availability'], 0, 32);
        }

        $product['availability_id'] = $this->saveMvAvailability($product['mv_availability']);

        $this->updateProduct($product);

        $this->products_read++;
    }
}
