<?php

namespace wws\MarketView\ImportScripts;

use bqp\db\db_generic;
use service_loader;
use wws\business_structure\Shop;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\Import\MarketViewImportUtils;
use wws\MarketView\MarketViewConst;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\ProductConst;
use wws\ProductCat\ProductCatRepository;

class market_view_import_lager extends MarketViewImportSimple
{
    private db_generic $db;

    public function setProductDB(db_generic $db): void
    {
        $this->db = $db;
    }

    public function updateComplete(): void
    {
        $second_db = clone $this->db;

        $result = $second_db->query("
            SELECT
                product.product_nr AS mvsrc_product_id,
                product.product_name,
                product.ean,
                product.mpn,
                product.uvp,
                product.cat_id,

                product.gewicht,
                product.size_h AS hoehe,
                product.size_b AS breite,
                product.size_t AS tiefe,

                product_brand.brand_name AS hersteller_name,

                SUBSTRING(product_details.artikelinfo, 1, 2000) AS beschreibung,
                product_details.lieferumfang,
                product_details.color,

                product_lager_detail.lager_frei,
                product_lager_detail.lager_ek,
                product_ek.ek_netto
            FROM
                product_lager_detail INNER JOIN
                product ON (product.product_id = product_lager_detail.product_id) INNER JOIN
                product_brand ON (product.brand_id = product_brand.brand_id) INNER JOIN
                product_ek ON (product.product_id = product_ek.product_id AND product_ek.ek_fav = 1) INNER JOIN
                product_details ON (product.product_id = product_details.product_id AND product_details.lang = '" . ProductConst::PRODUCT_LANG_DEFAULT . "') INNER JOIN
                product_shop ON (product.product_id = product_shop.product_id AND product_shop.shop_id = " . Shop::ALLEGO . ")
            WHERE
                product_lager_detail.lager_id = '" . $this->config['lager_id'] . "' AND
                product.product_status != '" . ProductConst::PRODUCT_STATUS_DEL . "' AND
                product_shop.park = 0 AND
                product_lager_detail.lager_frei > 0
        ", 'auto', false);

        //ACHTUNG: konzeptionell kann das ein Problem sein. Ich hab vor 1 1/2 Jahren das Script angepasst, was dazu
        //geführt hat. Das keine Daten mehr ins MarketView übernommen wurden, wenn kein Bestand da ist. Das heißt Preise
        //und Stammdaten wurden auch nicht mehr ins MarketView übernommen. Das könnte nur in Bezug zu dem B2B Shop
        //eine Relevanz gehabt haben.
        //Ich passe das Verhalten nicht an, aber stellen den Code auf fullUpdate() um. Sollte sich das derzeitige Verhalten
        //als Problem rausstellen, drauf achten nicht sinnlos in MarketView rumzuschreiben. (siehe git history)

        $this->fullUpdatePrepare();

        foreach ($result as $row) {
            $row['mvsrc_product_id'] = MarketViewImportUtils::normalizeMvsrcProductId($row['mvsrc_product_id']);

            $row['vk_netto'] = $row['lager_ek'];
            if ($row['vk_netto'] == 0 || $row['vk_netto'] == 9999) {
                $row['vk_netto'] = $row['ek_netto'];
            }

            $this->saveProduct($row);
        }

        $this->fullUpdateEnd();

        $this->createNewMatchings();
    }

    protected function saveProduct(array $daten): void
    {
        if (!$daten['mvsrc_product_id']) {
            return;
        }

        $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);
        $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, ProductCatRepository::getCatPathAsString($daten['cat_id']));

        if (strlen($daten['beschreibung']) > 20000) {
            $daten['beschreibung'] = substr($daten['beschreibung'], 0, 20000);
        }

        $daten['availability_id'] = $this->saveInventory($daten['lager_frei']);
        $daten['mv_availability'] = $daten['lager_frei'];

        $daten['inventory'] = $daten['lager_frei'];

        parent::saveProduct($daten);
    }

    public function createNewMatchings(): void
    {
        $product_nrs = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_product.product_id = 0
        ")->asSingleArray();

        $result = $this->db->query("
            SELECT
                product.product_nr AS mvsrc_product_id,
                product.product_id
            FROM
                product
            WHERE
                product.product_nr IN (" . $this->db->in($product_nrs) . ")
        ");

        $matching = service_loader::getDiContainer()->get(ProductMatching::class);

        foreach ($result as $row) {
            $matching->addMatching($this->mvsrc_id, $row['mvsrc_product_id'], $row['product_id'], $matching::MATCHING_VERIFIED);
        }
    }
}
