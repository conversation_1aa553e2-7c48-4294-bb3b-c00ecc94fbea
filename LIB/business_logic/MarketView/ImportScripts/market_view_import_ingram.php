<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\files\ZipUtils;
use dbf_reader;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_ingram extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        ini_set('memory_limit', '512M'); //die dbf von ingram ist recht groß und entpacken und dbf reader laufen nur im speicher ab

        $this->getProductList();
        $this->parseCsv();
        $this->parseZubehoer();
        parent::updateComplete();
    }

    private function parseCsv()
    {
        $dbf = new dbf_reader($this->config['temp_dir'] . '/ingram.dbf');
        $num_rec = $dbf->dbf_num_rec;
        $field_num = $dbf->dbf_num_field;

        $this->fullUpdatePrepare();

        for ($i = 0; $i < $num_rec; $i++) {
            $daten = $dbf->getRowAssoc($i);

            $product = [];

            $product['cat_name'] = $daten['GRUPPE1'];
            $product['cat_name2'] = $daten['GRUPPE2'];
            $product['cat_name3'] = $daten['GRUPPE3'];
            $product['hersteller_name'] = $daten['HERSTELLER'];
            $product['mvsrc_product_id'] = $daten['ARTIKEL_NR'];
            $product['mpn'] = $daten['HSTNUMMER'];
            $product['product_name'] = $daten['ARTIKEL1'];
            $product['beschreibung'] = $daten['ARTIKEL2'];
            $product['evp'] = $daten['EVP'];
            $product['vk_netto'] = $daten['VK'];
            $product['gewicht'] = $daten['WEIGHT'];
            $product['ean'] = $daten['EANCODE'];

            $product['bild'] = $daten['PICTUREFIL'];
            $product['datenblatt'] = $daten['DATENBLATT'];

            $product['mv_availability'] = $daten['V'];

            $product['FVERSION'] = $daten['FVERSION'];
            $product['FSYSTEM'] = $daten['FSYSTEM'];
            $product['FSPRACHE'] = $daten['FSPRACHE'];
            $product['FFORMAT'] = $daten['FFORMAT'];
            $product['FBUS'] = $daten['FBUS'];
            $product['FTYPE'] = $daten['FTYPE'];
            $product['FGARANTIE'] = $daten['FGARANTIE'];
            $product['AKTIONPR'] = $daten['AKTIONPR'];
            $product['AKTBIS'] = $daten['AKTBIS'];
            $product['AKTION'] = $daten['AKTION'];
            $product['AKTIONBIS'] = $daten['AKTIONBIS'];
            $product['DATE_CREAT'] = $daten['DATE_CREAT'];
            $product['CATALOGUEI'] = $daten['CATALOGUEI'];
            $product['SUBSITUTE_'] = $daten['SUBSITUTE_'];
            $product['TOP'] = $daten['TOP'];
            $product['ETA'] = $daten['ETA'];
            $product['LIEFERANT'] = $daten['LIEFERANT'];

            $this->saveProduct($product);
        }

        $this->fullUpdateEnd();
    }

    protected function saveProduct(array $daten): void
    {
        $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);
        $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, [$daten['cat_name3'], $daten['cat_name2'], $daten['cat_name']]);

        $daten['availability_id'] = $this->saveMvAvailability($daten['mv_availability']);


        $sonstiges = '';
        $sonstiges .= 'Produktversion: ' . $daten['FVERSION'] . "\n";
        $sonstiges .= 'System: ' . $daten['FSYSTEM'] . "\n";
        $sonstiges .= 'Sprache: ' . $daten['FSPRACHE'] . "\n";
        $sonstiges .= 'Format: ' . $daten['FFORMAT'] . "\n";
        $sonstiges .= 'Bussystem: ' . $daten['FBUS'] . "\n";
        $sonstiges .= 'Formattyp: ' . $daten['FTYPE'] . "\n";
        $sonstiges .= 'Garantie: ' . $daten['FGARANTIE'] . "\n";
        $sonstiges .= 'Aktionspreis: ' . $daten['AKTIONPR'] . "\n";
        $sonstiges .= 'Aktionspreis gültig bis: ' . $daten['AKTBIS'] . "\n";
        $sonstiges .= 'Aktionsname: ' . $daten['AKTION'] . "\n";
        $sonstiges .= 'Aktionspreis gültig bis: ' . $daten['AKTIONBIS'] . "\n";
        $sonstiges .= 'Datum der Produkterfassung: ' . $daten['DATE_CREAT'] . "\n";
        $sonstiges .= 'Nachfolger Artikelnummer: ' . $daten['SUBSITUTE_'] . "\n";
        $sonstiges .= 'ETA Datum: ' . $daten['FGARANTIE'] . "\n";
        $sonstiges .= 'TOP 10 Klassifikation: ' . $daten['TOP'] . "\n";
        $sonstiges .= 'Lieferantenname: ' . $daten['LIEFERANT'] . "\n";

        $daten['sonstiges'] = $sonstiges;

        parent::saveProduct($daten);

        $this->saveMedia($daten);
    }

    protected function parseZubehoer()
    {
        $csv = new CsvReader($this->config['temp_dir'] . '/ingram_crosssell.csv');
        $csv->setDelimiter('    ');
        $csv->setSrcCharset('UTF-8');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic(['mvsrc_product_id' => ['typ' => 'string'], 'mvsrc_zub_product_id' => ['typ' => 'string']]);

        $cross_sell = [];

        while (($daten = $csv->getRow()) !== null) {
            $cross_sell[$daten['mvsrc_product_id']][] = $daten['mvsrc_zub_product_id'];
        }

        foreach ($cross_sell as $mvsrc_product_id => $mvsrc_zub_product_ids) {
            $this->saveCrossSelling($mvsrc_product_id, $mvsrc_zub_product_ids);
        }
    }

    private function getProductList()
    {
        $this->clear();

        //hauptdaten
        $url = 'http://' . $this->config['username'] . ':' . $this->config['password'] . '@www.ingrammicro.de/cgi-bin/scripts/get_PL.pl';

        file_put_contents($this->config['temp_dir'] . '/ingram.zip', file_get_contents($url));

        ZipUtils::extract($this->config['temp_dir'] . '/ingram.zip', $this->config['temp_dir']);

        rename($this->config['temp_dir'] . '/MAC_DBF.DBF', $this->config['temp_dir'] . '/ingram.dbf');

        //crossselling
        $url = 'http://' . $this->config['username'] . ':' . $this->config['password'] . '@www.ingrammicro.de/cgi-bin/scripts/get_file.pl?NAME=ZUBEHOER.zip';

        file_put_contents($this->config['temp_dir'] . '/ingram_crosselling.zip', file_get_contents($url));
        ZipUtils::extract($this->config['temp_dir'] . '/ingram_crosselling.zip', $this->config['temp_dir']);
        rename($this->config['temp_dir'] . '/ZUBEHOER.txt', $this->config['temp_dir'] . '/ingram_crosssell.csv');

        unlink($this->config['temp_dir'] . '/ingram_crosselling.zip');
        unlink($this->config['temp_dir'] . '/ingram.zip');
    }

    private function clear()
    {
        if (file_exists($this->config['temp_dir'] . '/ingram.dbf')) {
            unlink($this->config['temp_dir'] . '/ingram.dbf');
        }

        if (file_exists($this->config['temp_dir'] . '/mac_dbf.dbf')) {
            unlink($this->config['temp_dir'] . '/mac_dbf.dbf');
        }

        if (file_exists($this->config['temp_dir'] . '/ingram.zip')) {
            unlink($this->config['temp_dir'] . '/ingram.zip');
        }
    }
}
