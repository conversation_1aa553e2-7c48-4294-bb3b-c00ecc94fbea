<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_mcomp extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $this->parseCsv();
        parent::updateComplete();
    }

    private function parseCsv()
    {
        $csv = new CsvReader($this->config['csv']);
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter(";");
        $csv->setSrcCharset('UTF-8');
        $csv->setDstCharset('RAW');
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic(['product_name' => ['typ' => 'string'], 'eans' => ['typ' => 'string'], 'cat_name' => ['typ' => 'string'], 'hersteller_name' => ['typ' => 'string'], 'vk_netto' => ['typ' => 'float']]);

        $this->fullUpdatePrepare();

        while (($daten = $csv->getRow()) !== null) {
            $eans = explode(';', trim($daten['eans'], ';'));

            foreach ($eans as $ean) {
                if (!$ean) {
                    continue;
                }
                $daten['ean'] = $ean;
                $daten['mvsrc_product_id'] = $daten['ean'];

                $this->saveProduct($daten);
            }
        }

        $this->fullUpdateEnd();
    }

    protected function saveProduct(array $daten): void
    {
        $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);

        $daten['availability_id'] = 60;
        $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $daten['cat_name']);

        parent::saveProduct($daten);
    }
}
