<?php

use bqp\Csv\CsvReader;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_icecat extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        /*$this->getProductList();*/

        $this->parseCsv();


        parent::updateComplete();
    }

    public function parseCsv()
    {
        $csv = new CsvReader($this->config['temp_dir'] . '/icecat.txt');
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter('    ');
        $csv->setSrcCharset('UTF-8');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic(
            [
                'mvsrc_product_id' => ['typ' => 'string'],
                'NONE',
                'NONE',
                'hersteller_name' => ['typ' => 'string'],
                'NONE',
                'NONE',
                'daten_qualitaet' => ['typ' => 'string'],
                'NONE',
                'NONE',
                'cat_name' => ['typ' => 'string'],
                'NONE',
                'NONE',
                'product_name' => ['typ' => 'string'],
                'NONE',
                'NONE',
                'ean' => ['typ' => 'string'],
                'NONE',
                'NONE',
                'eol' => ['typ' => 'bool', 'bool' => ['N' => true, 'Y' => false]]
            ]
        );

        $temp = [];

        $this->fullUpdatePrepare();

        while (($daten = $csv->getRow()) !== null) {
            $this->saveProduct($daten);
        }

        $this->fullUpdateEnd();
    }

    public function saveProduct(array $daten): void
    {
        $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);

        if (!$this->isKnownHersteller($daten['mv_hersteller_id'])) {
            return;
        }

        $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $daten['cat_name']);
        $daten['mpn'] = $daten['mvsrc_product_id'];
        $daten['availability_id'] = 50;

        parent::saveProduct($daten);
    }
}
