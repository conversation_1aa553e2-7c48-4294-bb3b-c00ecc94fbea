<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\files\ZipUtils;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_actebis extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $this->getProductList();

        $this->parseCsv();

        parent::updateComplete();
    }

    private function parseCsv()
    {
        $csv = new CsvReader($this->config['temp_dir'] . '/atcebis.csv');
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter('    ');
        $csv->setSrcCharset('UTF-8');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string'],
            'mpn' => ['typ' => 'string'],
            'hersteller_name' => ['typ' => 'string'],
            'ean' => ['typ' => 'string'],
            'product_name' => ['typ' => 'string'],
            'inventory' => ['typ' => 'int'],
            'vk_netto' => ['typ' => 'float'],
            'listen_preis' => ['typ' => 'float'],
            'NONE', //kategorie
            'cat_name' => ['typ' => 'string'], //gruppe -> passenst gut als kategorie
            'NONE', //linie
            'eol' => ['typ' => 'string'],
            'gewicht' => ['typ' => 'string']
        ]);

        $temp = [];

        $this->fullUpdatePrepare();

        while (($daten = $csv->getRow()) !== null) {
            $this->saveProduct($daten);
        }

        $this->fullUpdateEnd();


        $this->parseZubehoer();
    }

    protected function parseZubehoer()
    {
        $csv = new CsvReader($this->config['temp_dir'] . '/atcebis_crosssell.csv');
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter('    ');
        $csv->setSrcCharset('UTF-8');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic(['mvsrc_product_id' => ['typ' => 'string'], 'mvsrc_zub_product_id' => ['typ' => 'string']]);

        $cross_sell = [];

        while (($daten = $csv->getRow()) !== null) {
            $cross_sell[$daten['mvsrc_product_id']][] = $daten['mvsrc_zub_product_id'];
        }

        foreach ($cross_sell as $mvsrc_product_id => $mvsrc_zub_product_ids) {
            $this->saveCrossSelling($mvsrc_product_id, $mvsrc_zub_product_ids);
        }
    }

    protected function saveProduct(array $daten): void
    {
        if (array_key_exists('hersteller_name', $daten)) {
            $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);
        }

        if (array_key_exists('cat_name', $daten)) {
            $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $daten['cat_name']);
        }

        $daten['product_line'] = '';

        $daten['inventory'] = $daten['inventory'];
        $daten['availability_id'] = $this->saveInventory($daten['inventory']);
        $daten['mv_availability'] = $daten['inventory'];

        $sonstiges = '';
        $sonstiges .= "EOL: $daten[eol]\n";
        $sonstiges .= "Listenpreis: $daten[listen_preis]\n";
        $daten['sonstiges'] = $sonstiges;

        parent::saveProduct($daten);
    }

    private function getProductList()
    {
        $this->clear();

        //daten von ftp holen
        //pricelist-1.txt.zip -> temp
        //csell.txt -> als temp/atcebis_crosssell.csv

        $conn_id = ftp_connect($this->config['ftp_host']);
        $login_result = ftp_login($conn_id, $this->config['ftp_user'], $this->config['ftp_pass']);

        ftp_get($conn_id, $this->config['temp_dir'] . '/atcebis.zip', 'pricelist-1.txt.zip', FTP_BINARY);
        ftp_get($conn_id, $this->config['temp_dir'] . '/atcebis_crosssell.csv', 'csell.txt', FTP_BINARY);

        ftp_close($conn_id);


        ZipUtils::extract($this->config['temp_dir'] . '/atcebis.zip', $this->config['temp_dir']);
        rename($this->config['temp_dir'] . '/0010636405.txt', $this->config['temp_dir'] . '/atcebis.csv');
    }

    private function clear()
    {
        //löschen

        if (file_exists($this->config['temp_dir'] . '/0010636405.txt')) {
            unlink($this->config['temp_dir'] . '/0010636405.txt');
        }

        if (file_exists($this->config['temp_dir'] . '/atcebis.zip')) {
            unlink($this->config['temp_dir'] . '/atcebis.zip');
        }

        if (file_exists($this->config['temp_dir'] . '/atcebis_crosssell.csv')) {
            unlink($this->config['temp_dir'] . '/atcebis_crosssell.csv');
        }

        if (file_exists($this->config['temp_dir'] . '/atcebis.csv')) {
            unlink($this->config['temp_dir'] . '/atcebis.csv');
        }
    }
}
