<?php

namespace wws\MarketView\ImportScripts;

use bqp\SimpleHtmlDom\SimpleHtmlDom;
use bqp\SimpleHtmlDom\SimpleHtmlDomNode;
use bqp\Utils\StringUtils;
use bqp\Vat\VatRate;
use Exception;
use input;
use scraper_ersatzteilecenter;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_ersatzteilecenter extends MarketViewImportSimple
{
    /**
     * @var VatRate
     */
    private $vat_rate;

    public function init(): void
    {
        $this->vat_rate = $this->getDefaultImportVatRate();
    }

    public function parseHtml($html_raw)
    {
        //var_dump($html_raw);

        $features = new MarketViewFeatures();

        $product = [
            'mvsrc_product_id' => null,
            'product_name' => null,
            'vk_netto' => null,
            'features_struct' => $features
        ];


        $html = new SimpleHtmlDom($html_raw);

        $scripts = $html->find('script');

        foreach ($scripts as $script) {
            if (preg_match('~dataLayer.push.*?"ecommerce": {(.*)~', $script->innertext(), $result)) {
                if (preg_match('~"brand":"(.*?)"~', $result[1], $brand)) {
                    $product['hersteller_name'] = StringUtils::convertJsonEscapedStringToUtf8($brand[1]);
                }

                if (preg_match('~"category":"(.*?)"~', $result[1], $category)) {
                    $product['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, StringUtils::convertJsonEscapedStringToUtf8($category[1]));
                }
            }
        }

        $product['product_name'] = $html->find('h1', 0)->innertext();
        $product['product_name'] = StringUtils::htmlentities_decode($product['product_name']);

        //var_dump($product['product_name']);


        $features->addFeature('product_name', $product['product_name']);

        if ($html->find('.product-shop .regular-price .price', 0)) {
            $product['vk_netto'] = input::parseFloat($html->find('.product-shop .regular-price .price', 0)->innertext());
        } elseif ($html->find('.product-shop .special-price .price', 0)) {
            $product['vk_netto'] = input::parseFloat($html->find('.product-shop .special-price .price', 0)->innertext());
        } else {
            throw new Exception('kein preis gefunden');
        }

        $product['vk_netto'] = round($product['vk_netto'] / $this->vat_rate->getAsFactor(), 4);

        $product['mvsrc_product_id'] = $html->find('.product-view', 0)->getAttribute('data-id');

        $tr = $html->find('#product-attribute-specs-table tr');

        foreach ($tr as $temp) {
            $name = trim($temp->find('th', 0)->innertext());
            $value = trim($temp->find('td', 0)->innertext());

            if ($name == 'EAN-Nummer') {
                $product['ean'] = $value;
                $features->addFeature('ean', $value, $name);
            } else {
                $features->addFeature($name, $value);
            }
        }

        $desc = $html->find('.short-description', 0);
        //lieferzeit
        $delivery = $desc->find('.delivery', 0)->getAttribute('class');
        $delivery = str_replace('delivery ', '', $delivery);

        $product['mv_availability'] = $delivery;
        $product['availability_id'] = $this->saveMvAvailability($product['mv_availability']);

        //beschreibung
        $beschreibung = '';
        foreach ($desc->find('.std') as $temp) {
            if ($beschreibung) {
                $beschreibung .= '<br>';
            }
            $beschreibung .= $temp->innertext();
        }


        $product['beschreibung'] = $beschreibung;


        //ratings
        $ratings_raw = $desc->find('.ratings', 0);
        if ($ratings_raw) {
            //var_dump($ratings_raw->innertext());

            if ($ratings_raw->find('.rating-box .rating')) { //es gibt teilweise bewertungen ohne wertung!?
                $rating_count = $ratings_raw->find('.rating-links span', 0)->innertext();
                $rating = $ratings_raw->find('.rating-box .rating', 0)->getAttribute('style');

                $rating_count = str_replace(['(', ')'], '', $rating_count);

                $features->addFeature('rating_count', $rating_count, 'Bewertungen - Anzahl');
                $rating = str_replace('width:', '', $rating);
                $features->addFeature('rating_value', $rating, 'Bewertungen - Rating');
            }
        }


        //zubehör
        $crosssel = $html->find('.related .products li');
        if ($crosssel) {
            $ids = [];

            foreach ($crosssel as $temp) {
                $ids[] = $this->handleCrosssel($temp);
            }

            if ($ids) {
                $this->saveCrossSelling($product['mvsrc_product_id'], $ids);
            }
        }

        $suitable = $html->find('#suitable-for', 0);

        if ($suitable) {
            //var_dump($suitable->innertext());

            $suitable_url = $suitable->find('#flexgrid_modellen', 0)->getAttribute('data-ajax-url');

            $features->addFeature('suitable_ajax_url', $suitable_url);


            /*$html = $suitable->innertext();
            $html = str_replace('www.ersatzteilecenter.de', 'localhost', $html);

            echo $html;*/

            $grid = $suitable->find('#flexgrid_modellen tr');

            $devices = [];

            foreach ($grid as $row) {
                $td = $row->find('td');

                if (count($td) !== 3) {
                    continue;
                }

                $device = [
                    'marke' => $td[0]->innertext(),
                    'typennummer' => $td[1]->find('a', 0)->innertext(),
                    'serie' => $td[2]->innertext()
                ];

                $devices[] = $device;
            }

            if ($devices) {
                $product['beschreibung_2'] = json_encode($devices);
            }
        }

        //url
        $canonical = $html->find('link[rel="canonical"]', 0);
        if ($canonical) {
            $product['url'] = $canonical->getAttribute('href');
        }

        //var_dump($product);

        //image-main
        if (isset($this->config['scraper'])) {
            $scraper = $this->config['scraper'];
            /* @var $scraper scraper_ersatzteilecenter */

            $url = $features->getFeatureValue('suitable_ajax_url');

            if ($url) {
                $scraper->addUrl($scraper->clearUrl($url), $scraper::STATUS_QUEUE, $scraper::TYPE_DEVICES, $product['mvsrc_product_id']);
            }

            $image = $html->find('#image-main', 0);
            if ($image) {
                $scraper->addUrl($scraper->clearUrl($image->src), $scraper::STATUS_QUEUE, $scraper::TYPE_IMAGE, $product['mvsrc_product_id']);
            }
        }

        $this->saveProduct($product);
    }


    protected function handleCrosssel(SimpleHtmlDomNode $html)
    {
        $product = [
            'mvsrc_product_id' => null,
            'product_name' => null,
            'ek_netto' => null
        ];

        $product['product_name'] = $html->find('h2 a', 0)->innertext();

        $checkbox = $html->find('input[type=checkbox]', 0);

        if ($checkbox) {
            $product['mvsrc_product_id'] = $html->find('input[type=checkbox]', 0)->getAttribute('value');
        } elseif ($url = $html->find('button', 0)->getAttribute('onclick')) {
            if (preg_match('~/product/([0-9]{5,8})/~', $url, $result)) {
                $product['mvsrc_product_id'] = $result[1];
            }
        }

        $product['vk_netto'] = input::parseFloat($html->find('.price', 0)->innertext());
        $product['vk_netto'] = round($product['vk_netto'] / $this->vat_rate->getAsFactor(), 4);

        //$this->saveCrosssel($product);

        return $product['mvsrc_product_id'];
    }

    public $crosssel_cache = [];

    protected function saveCrosssel($product)
    {
        if (isset($this->crosssel_cache[$product['mvsrc_product_id']])) {
            return;
        }

        $this->crosssel_cache[$product['mvsrc_product_id']] = true;

        $this->saveProduct($product);
    }


    public function parseCategoryHtml($html_raw)
    {
        $html = new SimpleHtmlDom($html_raw);

        //page
        $page = 1;
        $temp = $html->find('.pages li.current', 0);
        if ($temp) {
            $page = (int)$temp->innertext();
        }

        $offset = ($page - 1) * 50 + 1;

        //breadcrumbs
        $cats = [];
        $breadcrumbs = $html->find('.breadcrumbs li');

        foreach ($breadcrumbs as $breadcrumb) {
            $temp = strip_tags($breadcrumb->innertext());
            $temp = str_replace('>', '', $temp);
            $temp = trim($temp);

            if ($temp != 'Home') {
                $cats[] = $temp;
            }
        }

        //products
        $products = [];

        $raw_result = $html->find('#products-list > li');

        foreach ($raw_result as $i => $raw) {
            $product = [
                'mvsrc_product_id' => null,
                'pos' => $offset + $i,
                'beschreibung_2' => null
            ];

            //mvsrc_product_id
            $button = $raw->find('.button.btn-cart', 0);
            if ($button) {
                if (preg_match('~/product/([0-9]{4,7})/~', $button->outertext(), $t)) {
                    $product['mvsrc_product_id'] = $t[1];
                }
            }
            //beschreibung
            $description = $raw->find('.short-description', 0);
            if ($description) {
                $product['beschreibung_2'] = $description->innertext();
            }

            $products[] = $product;
        }

        //var_dump($products);
        //var_dump($page);
        //var_dump($cats);

        $mv_cat_id = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $cats);

        foreach ($products as $product) {
            $product['rank'] = $product['pos'];
            $product['mv_cat_id_2'] = $mv_cat_id;
            if (!$product['beschreibung_2']) {
                unset($product['beschreibung_2']);
            }
            $this->saveProduct($product);
            $this->addMarketViewProductCats($product['mvsrc_product_id'], [$mv_cat_id]);
        }
    }
}
