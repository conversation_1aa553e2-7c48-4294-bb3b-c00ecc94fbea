<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_sennheiser extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $this->parseCsv();
        parent::updateComplete();
    }

    private function parseCsv()
    {
        $csv = new CsvReader($this->config['csv']);
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter(';');
        $csv->setSrcCharset('UTF-8');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->skipFirstLines(1);

        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string'],
            'vpe' => ['typ' => 'string'],
            'product_name' => ['typ' => 'string'],
            'ean' => ['typ' => 'string'],
            'uvp' => ['typ' => 'float'],
            'vk_netto' => ['typ' => 'float'],
            'cat_name' => ['typ' => 'string'],
            'hersteller_name' => ['typ' => 'string'],
            'beschreibung' => ['typ' => 'string']
        ]);

        $temp = [];

        $this->fullUpdatePrepare();

        while (($daten = $csv->getRow()) !== null) {
            if (!$daten['mvsrc_product_id']) {
                continue;
            }
            $daten['mpn'] = $daten['mvsrc_product_id'];

            $daten['star'] = str_contains($daten['mvsrc_product_id'], '*');

            if ($daten['star']) {
                $daten['mvsrc_product_id'] = str_replace('*', '', $daten['mvsrc_product_id']);
            }

            $this->saveProduct($daten);
        }

        $this->fullUpdateEnd();
    }

    protected function saveProduct(array $daten): void
    {
        if (!$daten['product_name']) {
            return;
        }

        $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);

        $daten['ean'] = str_replace(' ', '', $daten['ean']);

        $daten['product_name'] = $daten['hersteller_name'] . ' ' . $daten['product_name'];

        $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $daten['cat_name']);
        $daten['sonstiges'] = 'vk_liste: ' . $daten['vk_liste'];
        if ($daten['star']) {
            $daten['sonstiges'] .= "\nProduktnummer mit Stern";
        }

        $daten['availability_id'] = 60;

        //var_dump($daten);

        parent::saveProduct($daten);
    }
}
