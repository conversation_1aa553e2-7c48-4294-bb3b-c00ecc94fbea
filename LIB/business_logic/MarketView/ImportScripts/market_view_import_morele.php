<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\Date\DateObj;
use bqp\Utils\FileUtils;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\Import\MarketViewImportUtils;
use wws\MarketView\MarketViewConst;
use wws\MarketView\Utils\MarketViewCsvOffsizeErrorHandler;

class market_view_import_morele extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $file = $this->loadFile();

        $this->processFile($file);
        $this->archiveFile($file);

        parent::updateComplete();
    }


    public function loadFile()
    {
        $dst = $this->config['dir'] . 'pricelist_' . date('YmdHis') . '.csv';

        $src = 'http://www.morele.net/download/feed/437/ZXhUdVFTeVlaODc0R2JCNjc0eWYvQT09';
        copy($src, $dst);

        return $dst;
    }

    public function processFile($file)
    {
        $csv = new CsvReader($file);
        $csv->setInputFormatStatic([
            'category' => ['typ' => 'string', 'checkHeader' => 'categoryName'],
            'hersteller_name' => ['typ' => 'string', 'checkHeader' => 'brandName'],
            'mpn' => ['typ' => 'string', 'checkHeader' => 'productCode'],
            'mvsrc_product_id' => ['typ' => 'string', 'checkHeader' => 'productId'],
            'product_name' => ['typ' => 'string', 'checkHeader' => 'productFullNameCSV'],
            'ean' => ['typ' => 'string', 'checkHeader' => 'productEan'],
            'vk_netto' => ['typ' => 'float', 'checkHeader' => 'productEuroPriceNetto'],
            'inventory' => ['typ' => 'int', 'checkHeader' => 'productFastestSupplierQuantity'],
            'deliveryestimateddays' => ['typ' => 'string', 'checkHeader' => 'deliveryEstimatedDays'],
            'supplierid' => ['typ' => 'string', 'checkHeader' => 'supplierId'],
        ]);

        $csv->setSkipOffSizeLineHandler(new MarketViewCsvOffsizeErrorHandler(25));

        $this->fullUpdatePrepare();

        foreach ($csv as $daten) {
            $product = [
                'mvsrc_product_id' => $daten['mvsrc_product_id'],
                'product_name' => $daten['product_name'],
                'hersteller_name' => trim($daten['hersteller_name']),
                'mpn' => trim($daten['mpn']),
                'ean' => trim($daten['ean']),
                'mv_cat_id' => $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, trim($daten['category'])),
                'vk_netto' => $daten['vk_netto'],
                'inventory' => (int)$daten['inventory'],
            ];

            if ($product['inventory']) {
                $product['availability_id'] = $this->saveInventory($product['inventory']);
            } elseif (trim($daten['deliveryestimateddays'])) {
                $days = (int)$daten['deliveryestimateddays'];

                if ($days > 0 && $days < 30) {
                    $date = new DateObj();
                    $date->addSimple('days', $days);

                    $product['availability_id'] = $this->saveAvailabilityDate($date);
                } else {
                    continue;
                }
            } else {
                //$product['availability_id'] = $this->saveInventory(0); //zuviele leichen
                continue;
            }

            $product['mv_availability'] = (int)$daten['inventory'] . ' LZ:' . (trim($daten['deliveryestimateddays']) ?: '-');

            $this->saveProduct($product);
        }

        $this->fullUpdateEnd();
    }

    public function archiveFile($file)
    {
        $dst = FileUtils::appendDir($file, 'LAST');

        MarketViewImportUtils::moveFileCompressed($file, $dst);
    }
}
