<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\Exceptions\FatalException;
use bqp\Http\HttpUtils;
use bqp\Json;
use debug;
use Laminas\Http\Client;
use wws\MarketView\Import\MarketViewImportProduct;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_amica extends MarketViewImportSimple
{
    private $client = null;

    public function getClient(): Client
    {
        if ($this->client === null) {
            $client = new Client();
            $client->setOptions([
                'maxredirects' => 5,
                'timeout' => 30,
                'useragent' => HttpUtils::getRandomUserAgent(),
                'keepalive' => true,
                'sslverifypeer' => false
            ]);

            //$client->setParameterPost(array('a' => $this->config['username'], 'b' => $this->config['password'], 'c' => '1'));
            $client->setUri('https://handel.amica-int.de/user_auth.get.php?a=' . $this->config['username'] . '&b=' . $this->config['password'] . '&c=1');
            $client->setMethod('GET'); //natürlich...
            $response = $client->send();

            $content = $response->getBody();

            $result = Json::decode($content);

            if (!$result['success']) {
                debug::dump($content);
                throw new FatalException('login failed');
            }

            $this->client = $client;
        }

        return $this->client;
    }

    public function loadBestandlist()
    {
        $client = $this->getClient();

        $client->setUri('https://handel.amica-int.de/data.get.php?section=produkte_bestand');
        $client->setMethod('GET');
        $response = $client->send();

        return $response->getBody();
    }

    public function updateByBestandlist()
    {
        $content = $this->loadBestandlist();

//        file_put_contents('test.json', $content);
//        exit;
//        $content = file_get_contents('test.json');
//        //\debug::dumpJson($content);

        $result = Json::decode($content);

        $columns = $result['columns'][0];

        $this->fullUpdatePrepare();

        foreach ($result['data'] as $row_raw) {
            $row = array_combine($columns, $row_raw);

            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($row['SAP_ID']);
            $product->setMvsrcProductIdAlt($row['SKU']);
            $product->setEan($row['EAN']);
            $product->setBeschreibung($row['DESCRIPTION_SHORT']);
            $product->setBeschreibung2(trim($row['p_info1']));
            $product->setHerstellerName('Amica');
            $product->setProductName($row['MANUFACTURER_TYPE_DESCR']);
            $product->setModelName($row['MANUFACTURER_TYPE_DESCR']);

            $product->setMvCatId($this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $row['p_cat_name']));
            $product->setMvCatId2($this->saveCategory(2, $row['PRODUCT_GROUP']));

            switch ($row['p_wbz_status']) {
                case 10:
                    $product->setMvAvailability('Ab Lager lieferbar');
                    break;
                case 5:
                    $product->setMvAvailability('Geringe Menge');
                    break;
                case 0:
                    $product->setMvAvailability('Nur mit Lieferzeit');
                    break;
                default:
                    $product->setMvAvailability('Unbekannt');
                    debug::dump('Unbekannter wbz status');
                    debug::dump($row);
                    break;
            }

            $product->setAvailabilityId($this->saveMvAvailability($product->getMvAvailability()));

            $this->market_view_import->saveProduct($product);
        }

        $this->fullUpdateEnd();

        parent::updateComplete();
    }

    public function readPricelist()
    {
        $csv = new CsvReader('amica EP Strecke-2020.csv');
        $csv->skipOffSizeLines(false);
        $csv->setDelimiter(';');
        $csv->setSrcCharset('ISO-8859-1');
        $csv->setDstCharset('UTF-8');
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic([
            'product_name' => ['typ' => 'string'],
            'vk_netto' => ['typ' => 'float'],
            'ean' => ['typ' => 'string']
        ]);

//        $csv->display();
//        exit;

        foreach ($csv as $row) {
            $mvsrc_product_id = $this->getMvsrcProductIdByEan($row['ean']);

            if (!$mvsrc_product_id) {
                continue;
            }

            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($mvsrc_product_id);
            $product->setVkNetto($row['vk_netto']);

            $this->market_view_import->updateProduct($product);
        }
    }
}
