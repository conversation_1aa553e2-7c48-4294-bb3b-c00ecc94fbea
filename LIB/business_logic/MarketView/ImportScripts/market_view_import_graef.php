<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\MarketViewConst;

class market_view_import_graef extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        $this->parseCsv();
        parent::updateComplete();
    }

    private function parseCsv()
    {
        $csv = new CsvReader($this->config['csv']);
        $csv->skipOffSizeLines(false);
        $csv->skipFirstLines(1);
        $csv->setDelimiter(';');
        $csv->setSrcCharset('ISO-8859-1');
        $csv->setDstCharset('UTF-8');
        $csv->setTrim(true);
        $csv->setInputFormatStatic(
            [
                'mvsrc_product_id' => ['typ' => 'string'],
                'product_name' => ['typ' => 'string'],
                'vk_netto' => ['typ' => 'float'],
                'vpe' => ['typ' => 'string'],
                'ean' => ['typ' => 'string'],
                'sonstiges_Ean der VPE' => ['typ' => 'string'],
                'beschreibung' => ['typ' => 'string'],
                'sonstiges_Bilddaten' => ['typ' => 'string'],
                'sonstiges_Produktdatenblatt' => ['typ' => 'string'],
                'cat_name' => ['typ' => 'string'],
                'product_line' => ['typ' => 'string'],
                'color' => ['typ' => 'string'],
                'NONE',
                'NONE',
                'NONE',
                'NONE',
                'NONE',
                'hoehe_mm' => ['typ' => 'string'],
                'breite_mm' => ['typ' => 'string'],
                'tiefe_mm' => ['typ' => 'string'],
                'gewicht_g' => ['typ' => 'string'],
                'NONE',
                'zubehoer' => ['typ' => 'string'],
            ]
        );

        //$this->fullUpdate_prepare();
        while (($daten = $csv->getRow()) !== null) {
            if (!$daten['vk_netto']) {
                continue;
            }


            $this->saveProduct($daten);
        }
        exit;
        //$this->fullUpdate_ended();
    }

    protected function saveProduct(array $daten): void
    {
        $daten['mpn'] = $daten['mvsrc_product_id'];
        $daten['hersteller_name'] = 'Graef';
        $daten['mv_hersteller_id'] = $this->saveMvHersteller('Graef');
        $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $daten['cat_name']);

        $daten['default_availability_id'] = 60;

        $daten['sonstiges'] = self::aggregateSonstiges($daten);

        $daten['zubehoer'] = array_map('trim', explode(',', $daten['zubehoer']));


        parent::saveProduct($daten);

        $this->saveCrossSelling($daten['mvsrc_product_id'], $daten['zubehoer']);
    }
}
