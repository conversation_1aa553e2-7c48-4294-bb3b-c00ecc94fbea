<?php

namespace wws\MarketView\ImportScripts;

use bqp\Csv\CsvReader;
use bqp\Exceptions\DevException;
use bqp\SimpleHtmlDom\SimpleHtmlDom;
use debug;
use Exception;
use input;
use Laminas\Http\Client;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\Product\Product;

class market_view_import_liebherr extends MarketViewImportSimple
{
    protected $http_client = null;
    protected $jsessionid = null;
    protected $token = null;

    public function updateComplete(): void
    {
        /*$result = $this->searchLitradeComplete('modell', 'T 1400-20');

        var_dump($result);
        exit;*/

        //$result = $this->searchLitradeComplete('volltext', 'EINBAUMODELL');

        $result = $this->searchLitradeComplete('modell', '%%%');

        foreach ($result as $daten) {
            $this->saveProduct($daten);
        }

        $this->searchEans();

        parent::updateComplete();
    }

    public function saveProduct(array $daten): void
    {
        $daten['product_name'] = preg_replace('~ {1,}~', ' ', $daten['product_name']);

        if (strlen($daten['product_name']) > 24) {
            return;
        }

        $daten['mvsrc_product_id'] = strtoupper(str_replace(' ', '', $daten['product_name']));
        $daten['mpn'] = $daten['mvsrc_product_id'];
        $daten['hersteller'] = 'Liebherr';
        $daten['mv_hersteller_id'] = $this->saveMvHersteller('Liebherr');
        $daten['availability_id'] = $this->saveMvAvailability($daten['availability']);
        $daten['mv_availability'] = $daten['availability'];
        $daten['vk_netto'] = $daten['listenpreis'];
        $daten['sonstiges'] = 'Preisliste: ' . $daten['preisliste'] . "\nBestellbar: " . $daten['orderable'];

        parent::saveProduct($daten);
    }


    public function searchEans()
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id,
                market_view_product.product_name,
                market_view_product.mpn
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_product.ean = ''
        ");

        foreach ($result as $daten) {
            if (!$daten['mpn']) {
                continue;
            }
            $mpn_alt = str_replace('-', '', $daten['mpn']);

            $ean = $this->db_mv->fieldQuery("
                SELECT
                    market_view_product.ean
                FROM
                    market_view_product
                WHERE
                    (
                        market_view_product.mpn LIKE '" . $this->db_mv->escape($daten['mpn']) . "' OR
                        market_view_product.mpn LIKE '" . $this->db_mv->escape($mpn_alt) . "'
                    ) AND
                    market_view_product.ean NOT LIKE '%999999%' AND
                    market_view_product.ean != ''
                GROUP BY
                    market_view_product.ean
            ");

            if (!$ean) {
                continue;
            }

            $this->db_mv->query("
                UPDATE
                    market_view_product
                SET
                    market_view_product.ean = '$ean'
                WHERE
                    market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                    market_view_product.mvsrc_product_id = '" . $this->db_mv->escape($daten['mvsrc_product_id']) . "'
            ");
        }
    }

    public function getHttpClient()
    {
        if (!$this->http_client) {
            $this->http_client = new Client();
            $this->http_client->setOptions([
                'maxredirects' => 5,
                'timeout' => 30,
                'sslverifypeer' => false,
                'useragent' => 'Mozilla/5.0 (Windows; U; Windows NT 6.1; de; rv:1.9.2.13) Gecko/20101203 Firefox/3.6.13',
                'keepalive' => true,
                'encodecookies' => false
            ]);


            //jsessionid auslesen
            //$this->http_client->setUri('https://litrade.liebherr.com/liebherr/Index.jsp');
            $this->http_client->setUri('https://litrade.liebherr.com/liebherr/login/requestEA.do?menu=startseite/log1&process=7&startContext=platform/mandant');
            $this->http_client->setMethod('GET');

            $content = $this->http_client->send()->getBody();

            if (!preg_match('~jsessionid=([[:xdigit:]]{32})~', $content, $temp)) {
                throw new Exception('jsseionid konnte nicht gefunden werden');
            }

            $this->jsessionid = $temp[1];

            //login senden
            $this->http_client->setUri('https://litrade.liebherr.com/liebherr/login/requestEA.do;jsessionid=' . $this->jsessionid);

            $this->http_client->setParameterPost(['process' => 2, 'showPasswordReq' => 'true', 'page' => '1', 'startContext' => 'platform/mandant', 'loginCompany' => $this->config['username'], 'loginName' => '', 'loginPassword' => $this->config['password']]);
            $this->http_client->setMethod('POST');
            $this->http_client->send();

            $this->http_client->resetParameters();

            //$this->http_client->setUri('https://litrade.liebherr.com/liebherr/login/requestEA.do;jsessionid='.$this->jsessionid.'?process=8&startContext=platform/mandant');
            //$this->http_client->setMethod('POST');
            //$this->http_client->send();

            $this->http_client->setUri('https://litrade.liebherr.com/liebherr/startcontent/requestEA.do;jsessionid=' . $this->jsessionid . '?process=20&startContext=null');
            $this->http_client->setMethod('GET');
            $content = $this->http_client->send()->getBody();

            if (!strpos($content, 'Allego')) {
                throw new Exception('login konnte nicht ausgeführt werden, keyword allego nicht gefunden');
            }


            $this->http_client->setUri('https://litrade.liebherr.com/liebherr/login/requestEA.do;jsessionid=' . $this->jsessionid . '?process=8&startContext=null');
            $this->http_client->setMethod('GET');
            $content = $this->http_client->send()->getBody();
        }

        sleep(1);

        return $this->http_client;
    }

    public function searchLitradeComplete($type, $search_phrase)
    {
        $products = [];

        $result = $this->searchLitrade($type, $search_phrase);

        $products = $result['products'];

        //$result['continue'] = false;

        while ($result['continue']) {
            try {
                $result = $this->searchLitrade_continue();

                foreach ($result['products'] as $product) {
                    $products[] = $product;
                }
            } catch (Exception $e) {
                debug::dump($e->getMessage());
                $result['continue'] = false;
            }
        }

        return $products;
    }

    public function searchLitrade($type, $search_phrase)
    {

        //return $this->parseSearchResultContent(file_get_contents('liebherr_test.txt'));


        $products = [];

        $client = $this->getHttpClient();

        //prozess starten und token raussuchen
        $dummyid = date('n') - 1 . date('j') . date('G') . date('i') . date('s') . rand(100, 999);

        $client->setUri('https://litrade.liebherr.com/liebherr/catalog/artsearch/requestEA.do;jsessionid=' . $this->jsessionid . '?menu=bestellung/art1&process=0&dummyId=' . $dummyid);
        $client->setMethod('GET');
        $content = $client->send()->getBody();

        $this->token = $token = $this->extractToken($content);

        //suchen
        $client->setUri('https://litrade.liebherr.com/liebherr/catalog/artsearch/requestEA.do;jsessionid=' . $this->jsessionid);

        $post = ['org.apache.struts.taglib.html.TOKEN' => $token, 'process' => '10', 'page' => '1', 'sortimentFlag' => '1', 'searchOption' => '1', 'modell' => '', 'fremdartikelnummer' => '', 'ean' => '', 'volltext' => ''];

        $client->setMethod('POST');

        switch ($type) {
            case 'modell':
                $post['searchOption'] = 1;
                $post[$type] = $search_phrase;
                break;
            case 'ean':
                $post['searchOption'] = 3;
                $post[$type] = $search_phrase;
                break;
            case 'volltext':
                $post['searchOption'] = 4;
                $post[$type] = $search_phrase;
                break;
            default:
                throw new DevException('unbekannter suchtyp');
        }

        $client->setParameterPost($post);

        $content = $client->send()->getBody();

        return $this->parseSearchResultContent($content);
    }

    public function searchLitrade_continue()
    {
        $client = $this->getHttpClient();

        $client->setUri('https://litrade.liebherr.com/liebherr/catalog/artlist/requestEA.do;jsessionid=' . $this->jsessionid);

        $client->setParameterPost([
            'org.apache.struts.taglib.html.TOKEN' => $this->token,
            'process' => '1',
            'selectedIndex' => '0',
            'listMode' => '1',
            'sortField' => 'artikelVO.bezeichnung',
            'sortDirection' => '1',
            'sortDataType' => '0',
            'filterValue.filterValue' => '',
            'filterValue.propertyName' => '',
            'filterValue.filterType' => '1',
            'pagingCommand' => '0',
            'focusId' => '0',
            'pageY' => '0'
        ]);
        $client->setMethod('POST');

        $content = $client->send()->getBody();

        return $this->parseSearchResultContent($content);
    }

    protected function parseSearchResultContent($content)
    {
        $return = ['products' => [], 'continue' => false, 'products_total' => 0, 'products_last_nr' => 0];

        $html = new SimpleHtmlDom();
        $html->load($content);


        //navigation suchen, um zu bestimmen ob weiter abfragen nötig sind
        $nav = $html->find('.paging .text', 0)->innertext;
        $nav = trim(str_replace('&nbsp;', ' ', $nav));

        if (!preg_match('~([0-9]{1,5}) von ([0-9]{1,5})$~', $nav, $temp)) {
            throw new Exception('pagination navigation nicht gefunden');
        }

        $return['products_total'] = $temp[2];
        $return['products_last_nr'] = $temp[1];

        $return['continue'] = $return['products_total'] != $return['products_last_nr'];

        //produkte auslesen

        $trs = $html->find('.list-blank tr');

        $state = 'none';
        $product = [];

        foreach ($trs as $tr) {
            if ($state == 'p2') {
                $state = 'none';
                $product = [];
            }

            if ($state == 'p1') {
                $state = 'p2';
            }

            if (strpos($tr->innertext, 'product-info')) {
                $state = 'p1';
            }


            switch ($state) {
                case 'p1':
                    $tds = $tr->find('td');

                    $product['orderable'] = (bool)strpos($tds[0]->innertext, 'In den Warenkorb');
                    $product['product_name'] = preg_replace('~ {2,}~', ' ', trim(strip_tags($tds[1]->innertext)));
                    $product['listenpreis'] = input::parseFloat(trim($tds[2]->innertext));
                    $product['preisliste'] = trim($tds[3]->innertext);

                    $product['uvp'] = input::parseFloat(trim($tds[5]->innertext));

                    //verfügbarkeit
                    $temp = $tds[4]->innertext;

                    preg_match('~title="(.*?)"~', $temp, $daten);

                    $product['availability'] = trim($daten[1]);

                    break;
                case 'p2':
                    $product['beschreibung'] = trim($tr->find('td', 0)->innertext);


                    $return['products'][] = $product;
                    break;
            }
        }


        /*foreach($result AS $obj) {

        }*/


        return $return;
    }


    protected function extractToken($content)
    {
        if (!preg_match('~TOKEN" value="([[:xdigit:]]{32})"~', $content, $temp)) {
            return false;
        }

        return $temp[1];
    }

    public function readCsv()
    {
        $csv = new CsvReader($this->config['csv']);

        $csv->skipOffSizeLines(false);
        $csv->setDelimiter(';');
        $csv->setSrcCharset('ISO-8859-1');
        $csv->setDstCharset('UTF-8');
        $csv->setTrim(true);
        $csv->skipFirstLines(1);
        $csv->setInputFormatStatic([

            'ean' => ['typ' => 'string'],
            'vk_netto' => ['typ' => 'float'],
            'sonstiges_LFH+ Nachlass' => ['typ' => 'string'],
            'uvp' => ['typ' => 'float'],
            'mvsrc_product_id' => ['typ' => 'string'],
            'product_name' => ['typ' => 'string']

            /*'cat_name' => array('typ' => 'string'),
            'product_line' => array('typ' => 'string'),
            'beschreibung' => array('typ' => 'string'),

            */
        ]);

        while (($daten = $csv->getRow()) !== null) {
            $this->saveProductCsv($daten);

            //$daten['mvsrc_product_id'] = $this->getMvsrcProductIdByEan($daten['ean']);


            //if($daten['mvsrc_product_id']) {
            //var_dump($daten);
            //    $this->savePricelist($daten);
            //}
        }
    }


    public function saveProductCsv($daten)
    {
        //$daten['product_name'] = preg_replace('~ {1,}~', ' ', $daten['product_name']);

        $daten['mvsrc_product_id'] = strtoupper(str_replace(' ', '', $daten['mvsrc_product_id']));

        $daten['mpn'] = $daten['mvsrc_product_id'];

        $daten['mv_hersteller_id'] = $this->saveMvHersteller('Liebherr');
        //$daten['mv_cat_id'] = $this->saveCategory(\wws\MarketView\MarketViewConst::TREE_ID_DEFAULT, $daten['cat_name']);

        $daten['sonstiges'] = self::aggregateSonstiges($daten, true);

        #parent::saveProduct($daten);

        if ($daten['sonstiges_LFH+ Nachlass']) {
            $product_id = $this->db_mv->fieldQuery("
                SELECT
                    market_view_product.product_id
                FROM
                    market_view_product
                WHERE
                    market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
                    market_view_product.mvsrc_product_id = '" . $this->db_mv->escape($daten['mvsrc_product_id']) . "'
            ");

            if ($product_id) {
                $product = new Product($product_id);

                $old = $product->getBemerkungEinkauf();
                if (trim($old)) {
                    $old = "\n\n" . $old;
                }

                $product->setBemerkungEinkauf(date('d.m.Y') . " LFH+ Nachlass: " . $daten['sonstiges_LFH+ Nachlass'] . $old);

                $product->save();
            }
        }
    }
}
