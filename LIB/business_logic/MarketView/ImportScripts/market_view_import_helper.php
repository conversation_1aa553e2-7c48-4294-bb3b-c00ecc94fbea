<?php

namespace wws\MarketView\ImportScripts;

use bqp\extern\Ep\EpShippingCostsClassificationUnknownException;
use bqp\extern\Ep\MarketViewImportEpLegacy;
use bqp\Utils\StringUtils;
use Exception;
use service_loader;
use wws\business_structure\Shop;
use wws\MarketView\Entities\MarketViewProduct;
use wws\MarketView\MarketViewRepository;
use wws\Product\actions\ProductShipmentTypeSetter;
use wws\Product\Product;
use wws\Product\ProductConst;

class market_view_import_helper
{
    public static function getMvsrcProductAsProductDgh(MarketViewProduct $market_view_product, Product $product)
    {
        return $product;
    }

    public static function getMvsrcProductAsProductSilit(MarketViewProduct $market_view_product, Product $product)
    {
        return $product;
    }

    public static function getMvsrcProductAsProductKnoll(MarketViewProduct $market_view_product, Product $product)
    {
        $hersteller_name = $market_view_product->getHersteller()->getHerstellerName();

        $product->setProductName($hersteller_name . ' ' . $product->getProductName());

        return $product;
    }

    public static function getMvsrcProductAsProductEp(MarketViewProduct $market_view_product, Product $product)
    {
        $hersteller_name = $market_view_product->getHersteller()->getHerstellerName();

        $product->setProductName($hersteller_name . ' ' . $product->getProductName());

        try {
            $versand_netto = MarketViewImportEpLegacy::versandClassificationToPrice($market_view_product->getVersandClassification() ?? '');

            $setter = service_loader::get(ProductShipmentTypeSetter::class);
            $setter->setByShipmentPriceNet($product, $versand_netto);
        } catch (EpShippingCostsClassificationUnknownException $e) {
        }

        return $product;
    }


    public static function getMvsrcProductAsProductNeumaerker(MarketViewProduct $market_view_product, Product $product, MarketViewRepository $mv_repository)
    {
        $product_name = trim($product->getProductName());

        if (!str_contains($product_name, 'Neumaerker')) {
            $product_name = 'Neumaerker ' . $product_name;
        }

        if (strlen($product_name) + strlen($market_view_product->getMpn()) + 1 <= 80) {
            $product_name .= ' ' . trim($market_view_product->getMpn());
        }

        $product->setProductName($product_name);

        $cat_id = $mv_repository->getCatIdForMvProduct($market_view_product);

        if ($cat_id) {
            $product->setCatIdWithDefaults($cat_id);
        }

        $product->setProductStatus(ProductConst::PRODUCT_STATUS_NORMAL);

        $shop = $product->getShop(Shop::ALLEGO);

        $shipment_type_setter = service_loader::get(ProductShipmentTypeSetter::class);

        if ($product->getGewicht() > 28) {
            $shipment_type_setter->setByShipmentTypeId($product, ProductConst::SHIPMENT_TYPE_ID_EINWEGPALETTE);
        } else {
            $shipment_type_setter->setByShipmentTypeId($product, ProductConst::SHIPMENT_TYPE_ID_PAKET);
        }

        $shop->setLieferbaranzeige(ProductConst::LIEFERBAR_ID_4_TO_8);


        return $product;
    }


    public static function getMvsrcProductAsProductSuntec(MarketViewProduct $market_view_product, Product $product, MarketViewRepository $mv_repository)
    {
        $product->setProductName('Suntec ' . $product->getProductName());

        $cat_id = $mv_repository->getCatIdForMvProduct($market_view_product);

        if (!$cat_id) {
            throw new Exception('Es wurde keine Kategorie zugeordnet');
        }

        $product->setCatIdWithDefaults($cat_id);
        $product->setProductStatus(ProductConst::PRODUCT_STATUS_NORMAL);

        $shop = $product->getShop(Shop::ALLEGO);
        $shop->setVersandId(ProductConst::SHIPMENT_TYPE_ID_PAKET);
        $product->setShipmentTypeId(ProductConst::SHIPMENT_TYPE_ID_PAKET);

        $shop->setLieferbaranzeige(ProductConst::LIEFERBAR_ID_4_TO_8);

        return $product;
    }

    public static function getMvsrcProductAsProductGardena(MarketViewProduct $market_view_product, Product $product)
    {
        if ($market_view_product->getBeschreibung() || $market_view_product->getFeatures()) {
            $product->setBeschreibung($market_view_product->getBeschreibung() . '<br><br><br>' . $market_view_product->getFeatures());
            $product->setBeschreibungQuelle('hersteller');
        }

        return $product;
    }

    public static function getMvsrcProductAsProductLeCreuset(MarketViewProduct $market_view_product, Product $product)
    {
        $product_name = self::normlizeLeCreusetProductName($product->getProductName());

        $product->setProductName('Le Creuset ' . $product_name);

        return $product;
    }

    public static function normlizeLeCreusetProductName($product_name)
    {
        $replace_words = [
            '(Le Creuset|^Lc) ' => '',
            'Für' => 'für',
            'Antih\.' => 'antihaft ',
            'Antihaft' => 'antihaft',
            '-st\.' => ' Stück',
            'St\.' => 'Stück',
            'Brennpas' => 'Brennpaste',
            'Rund' => 'rund',
            'Brat-u.servierpf.' => 'Brat- und Servierpfanne',
            'Grillpf\.' => 'Grillpfanne',
            'Steakgrillpf\.' => 'Steakgrillpfanne',
            'Glänzend|Gl\.$|Gl$' => 'glänzend',
            'Espressot\.' => 'Espressotassen',
            'Cappuccinot\.' => 'Cappuccinotassen',
            'Oval' => 'oval',
            'Aufbewahrungsgef\.' => 'Aufbewahrungsgefäß',
            'Aufbewahrungsgefäss' => 'Aufbewahrungsgefäß',
            'Groß' => 'groß',
            'Gross' => 'groß',
            'Quadr\.' => 'quadratisch ',
            'Recht\.|R\.eck\.' => 'rechteckig',
            'Viereckig' => 'viereckig',
            'Quadratisch' => 'quadratisch',
            '\-platt' => '-Platte',
            '\-löffel' => '-Löffel',
            ' Mm$' => 'mm',
            'öl' => 'Öl',
            'Und' => 'und',
            'Mit' => 'mit',
            'Zube$' => 'Zubehör',
            'Blau' => ', blau',
            'Rot' => ', rot',
            'Matt$' => 'matt',
            'Neu | Neu' => '',
            'Trad\.|Tradition' => 'Tradition',
            '(M\. Korkv|M\. Kork|M\. Kor|M\. Ko)' => 'mit Korkverschluss',
            'Gourmet-bräter' => 'Gourmet-Bräter',
            '(Sch\.|Schwar$|Schwarz|Schwa$|Schw$|Sch$|Schw\.$)' => ', schwarz',
            '(Kirschrot|Kirsc$|Kir$|Kirsch$|Kir\.|Kirs$|K$|Kirschr$)' => ', kirschrot',
            '(Ofenrot|Ofenr|Ofen\.|O\.ro$|O\.rot|Ofen$|O$|Ofe$)' => ', ofenrot',
            'ofenroto' => 'ofenrot',
            '(Rosmarin|Rosma$|Rosmar$)' => ', rosmarin',
            '(Marseille|Marsei$|Mars$|Mar$|Marseil$|Marse$)' => ', marseille',
            '(Citrus|Citr$|Citru$)' => ', citrus',
            '(Mandel|Mand$)' => ', mandel',
            '(Karibik|Karibi$|Karib$)' => ', karibik',
            'Kiwi' => ', kiwi',
            '(Creme|Cre$)' => ', creme',
            'Weiss' => ', weiss',
            '(Edelstahl|Edelst$)' => ', edelstahl',
            'schwarz-silb' => 'schwarz-silber',
            '(Mandarin|Mandar$|Manda$|Mandari$)' => ', mandarin',
            'Purple' => ', purple',
            'Burgund' => ', burgunder',
            'Orange' => ', orange',
            'Klar' => ', klar',
            'Black Metal' => ', black metal',
            'Black' => ', black',
            'Metal' => ', metal',
            'Cocos' => ', cocos',
            'Stone' => ', stone',
            '(Kirsch)' => ', kirschrot',
            'Tarte-form' => 'Tarte-Form',
            ' , ' => ', ',
            '([0-9,]{1,6}) Ml' => '\\1ml',
            '([0-9,]{1,6}) L' => '\\1l',
            'Klein' => 'klein',
            'M. Untert.' => 'mit Untertasse',
            '\-pfanne' => '-Pfanne',
            ',,' => ',',
        ];

        $product_name = $product_name;
        $product_name = preg_replace('~ {2,}~', ' ', $product_name);
        $product_name = StringUtils::ucFirstChars($product_name);

        $product_name = preg_replace('~([0-9]{1,3}) Cm~', '\\1cm', $product_name);


        foreach ($replace_words as $key => $word) {
            $product_name = preg_replace('~' . $key . '~', $word, $product_name);
        }

        return $product_name;
    }


    protected static $smeg_text = [
        'Designlinie Cortina' => '<b>Cortina</b><br>
Die Designlinie Cortina wurde in Zusammenarbeit mit dem Meisterschmied Giancarlo Candeago realisiert. Dieser wurde durch die Handwerkskunst der italienischen Gebirgsschmieden inspiriert. So entstand eine Hommage an die Kunst und Faszination schmiedeeiserner Werke, mit perfekter Verarbeitung und dezentem Farbeinsatz, im Bereich der Hausgeräte. Cortina – Backöfen, Kochmulden, Dunstabzugshauben und jetzt auch Kühlgeräte – perfekt inszeniert und das muss für jede Landhausküche!',
        'Designlinie Linea' => '<b>Linea</b><br>
Leichtigkeit, Balance und Ergonomie – kurzum Produkte im modernen und minimalistischem Stil. Die perfekte Symbiose  aus Edelstahl- und Glaselementen in Verbindung mit hochwertiger Technik und Funktionalität. Genau das ist Linea. Eine Bereicherung für jede moderne Küche. Linea – Backöfen, Gasmulden mit Hochleistungsbrennern, Mikrowellen, Dunstabzugshauben, Einbau-Vollautomaten und sogar Wärmeschubladen. Einfach einzigartig und perfekt aufeinander abgestimmt!',
        'Designlinie Nostalgie' => '<b>Nostalgie</b><br>
Die Designlinie Nostalgie steht für die Wärme und Gemütlichkeit der Küchen von einst. Speziell auserkoren für die Liebhaber desFeinen und Erlesenen, die sich stark auf traditionelle Elemente besinnen. Diese Designlinie mit ihren eleganten Hausgeräten schenkt der Küche eine unnachahmliche Atmosphäre.
Mit den Backöfen, Mikrowellen, Kochmulden, Kaffeemaschinen, Herden und Dunstabzugshauben gelangen Material, Farbe und Ästhetik in das Herz Ihres Hauses und verleihen eine erlesene Eleganz. Erlesene Produkte mit prägenden Komponenten als Zeugen einer Zeit, in der noch mit großer Leidenschaft Traditionen gepflegt wurden.',
        'Designlinie Victoria' => '<b>Victoria</b><br>
So sieht Tradition aus! Dabei bleibt die Liebe zum Detail aber nicht auf der Strecke. Die Victoria Kochzentren sind ein wahrer Blickfang. In 110 cm oder 90 cm, creme oder schwarz lassen Sie jedes Gericht zum Event werden. Wer dieses Ereignis einmal erlebt hat, möchte nie wieder darauf verzichten!',
        'Designlinie Stil Der 50° Jahre' => '<b>Stil der 50er Jahre</b><br>
Ein unverwechselbarer Retro-Stil zeichnet die Produktlinie im Stil der 50er Jahre aus. Hier verschmelzen akzentuierte Details, kurvenreiche Formen und lebhafte Farben mit der Technologie der neuesten Generation. Mit dieser Designlinie, die in den firmeneigenen Projektstudios entstanden ist, hat SMEG zweifelsohne die Wahrnehmung des Hausgeräts an sich revolutioniert.
Der industrielle Stil der normalen Standgeräte musste den wahren Ikonen weichen. Kultobjekten gleich sticht der farbige Kühlschrank FAB28 in der Küche heraus, der wie ein Hauptdarsteller seine Bühne regiert. In perfektem Einklang wurden auch die anderen Hausgeräte mit dem gleichen Retro-Thema geschaffen: Kühlschränke mit unterschiedlichen Fassungsvermögen und Abmessungen, Gefriergeräte, Waschmaschinen und Geschirrspüler.
Vielseitig in ihren verschiedenen Versionen sind sie Zeugen eines Made in Italy Designs, das weltweit berühmt und geschätzt wird und Kunst mit Funktionalität vereinigt.',
        'standard' => 'Gönnen Sie sich und Ihrer Küche doch etwas Lebensqualität und einen ganz eigen Stil! Wir freuen uns Ihnen nun auch Produkte der italienischen Designschmiede Smeg anbieten zu können. Design verbunden mit Funktionalität und dazu noch einer großen Portion Leidenschaft und Extravaganz, das haucht jeder Küche neues Leben ein.
    Smeg überzeugt mit dem Besonderen:  Verschiedenste Stile werden mit jahrelanger Erfahrung und dem Blick für das Detail verbunden. Das Ergebnis: pure Emotion!',
    ];


    public static function getMvsrcProductAsProductSmeg(MarketViewProduct $market_view_product, Product $product, MarketViewRepository $mv_repository)
    {
        $cat_id = $mv_repository->getCatIdForMvProduct($market_view_product);

        if ($cat_id) {
            $product->setCatIdWithDefaults($cat_id);
        }

        if ($market_view_product->getBeschreibung2()) {
            $beschreibung = '';
            //$beschreibung .= $market_view_product->getFeatures();
            //$beschreibung .= '<br><br>';
            $beschreibung .= $market_view_product->getBeschreibung2();

            $product->setBeschreibung($beschreibung);
            $product->setBeschreibungQuelle('hersteller');
        } elseif ($market_view_product->getBeschreibung()) {
            $product->setBeschreibung($market_view_product->getBeschreibung());
            $product->setBeschreibungQuelle('hersteller');
        }


        if (isset(self::$smeg_text[$market_view_product->getProductLine()])) {
            $text = self::$smeg_text[$market_view_product->getProductLine()];

            $product->setBeschreibung($text . '<br><br>' . $product->getBeschreibung());
        }

        $product->setBeschreibung(self::$smeg_text['standard'] . '<br><br>' . $product->getBeschreibung());

        return $product;
    }

    public static function getMvsrcProductAsProductFrg(MarketViewProduct $market_view_product, Product $product)
    {
        $features = $market_view_product->getMvFeatures();

        $features->removeFeature('vpe');

        $product->setBeschreibung($product->getBeschreibung() . '<br><br>' . $features->asHtmlSimple());

        return $product;
    }
}
