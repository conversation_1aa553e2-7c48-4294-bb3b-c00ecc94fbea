<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use Exception;
use service_loader;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatchingEan;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatchingMpn;
use wws\MarketViewWwsConnector\Sync\MarketViewProductEkOfferSync;

class MarketViewMatching
{
    private db_generic $db;
    private db_generic $db_mv;
    private MarketViewProductEkOfferSync $market_view_product_ek_offer_sync;
    private MarketViewRepository $mv_repository;
    private MarketViewFactory $mv_factory;

    public function __construct(
        db_generic $db_mv,
        db_generic $db,
        MarketViewProductEkOfferSync $market_view_product_ek_offer_sync,
        MarketViewRepository $mv_repository,
        MarketViewFactory $mv_factory
    ) {
        $this->db = $db;
        $this->db_mv = $db_mv;

        $this->market_view_product_ek_offer_sync = $market_view_product_ek_offer_sync;
        $this->mv_repository = $mv_repository;
        $this->mv_factory = $mv_factory;
    }

    /**
     * matcht die Produkte aus market_view_product in die Master Tabelle und zurück in market_view_product.mv_master_product_id
     * ACHTUNG: erzeugt nur leere Datensätze in der market_view_master_product
     *
     * quick matching -> matching nur über ean und ohne matching tabelle, kann ggf. angepasst werden
     */
    public function match_master_products(): void
    {
        //neue eans aus market_view_product in master kopieren
        $this->db_mv->query("
            INSERT INTO
                market_view_master_product (ean)
            SELECT
                DISTINCT market_view_product.ean
            FROM
                market_view_product LEFT JOIN
                market_view_master_product ON (market_view_product.ean = market_view_master_product.ean)
            WHERE
                market_view_master_product.mv_master_product_id IS NULL AND
                market_view_product.ean != ''
        ");

        //mv_master_product_id in market_view_product kopieren
        $this->db_mv->query("
            UPDATE
                market_view_product INNER JOIN
                market_view_master_product ON (market_view_product.ean = market_view_master_product.ean)
            SET
                market_view_product.mv_master_product_id = market_view_master_product.mv_master_product_id
            WHERE
                market_view_product.mv_master_product_id = 0
        ");

        //product_ids rüberkopieren
        $this->db_mv->query("
            UPDATE
                market_view_product INNER JOIN
                market_view_master_product ON (market_view_product.ean = market_view_master_product.ean)
            SET
                market_view_master_product.product_id = market_view_product.product_id
            WHERE
                market_view_master_product.product_id = 0 AND
                market_view_product.product_id != 0
        ");
    }

    /**
     * löscht das master matching, wenn ean vom angebot nicht mehr zum master produkt passt.
     * wenn das mater produkt danach ohne angebote ist, wird es gelöscht.
     */
    public function fix_master_matchings(): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id,
                market_view_product.mv_master_product_id
            FROM
                market_view_master_product INNER JOIN
                market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id)
            WHERE
                market_view_master_product.ean != market_view_product.ean
        ");

        $mv_master_product_ids = [];

        foreach ($result as $row) {
            $mv_master_product_ids[] = $row['mv_master_product_id'];

            $this->db_mv->query("
                UPDATE
                    market_view_product
                SET
                    market_view_product.mv_master_product_id = 0
                WHERE
                    market_view_product.mvsrc_id = '" . $row['mvsrc_id'] . "' AND
                    market_view_product.mvsrc_product_id = '" . $this->db_mv->escape($row['mvsrc_product_id']) . "'
            ");
        }

        if ($mv_master_product_ids) {
            $this->db_mv->query("
                DELETE
                    market_view_master_product
                FROM
                    market_view_master_product LEFT JOIN
                    market_view_product ON (market_view_master_product.mv_master_product_id = market_view_product.mv_master_product_id)
                WHERE
                    market_view_master_product.mv_master_product_id IN (" . $this->db_mv->in($mv_master_product_ids) . ") AND
                    market_view_product.mv_master_product_id IS NULL
            ");
        }
    }

    public function matchLocalProducts(int $mvsrc_id): void
    {
        $mvsrc = $this->mv_repository->getMarketViewSource($mvsrc_id);

        if ($mvsrc->isMatchingEan()) {
            $matching = service_loader::getDiContainer()->get(ProductMatchingEan::class);
            $matching->runForMvsrcId($mvsrc_id);
        }

        if ($mvsrc->isMatchingMpn()) {
            $matching_mpn = service_loader::getDiContainer()->get(ProductMatchingMpn::class);
            $matching_mpn->runForMvsrcId($mvsrc_id);
        }

        if ($mvsrc->getMatchingExtraClass()) {
            $matching_extra = $this->mv_factory->buildMatchingClass($mvsrc->getMatchingExtraClass(), $mvsrc);
            $matching_extra->runForMvsrcId($mvsrc_id);
        }
    }

    /**
     * @depracated - Das wird aktuell beim Anlegen von Produkten verwendet.
     */
    public function addMatching(int $mvsrc_id, string $mvsrc_product_id, int $product_id): void
    {
        $matching = service_loader::getDiContainer()->get(ProductMatching::class);
        $matching->addMatching($mvsrc_id, $mvsrc_product_id, $product_id);
    }

    /**
     * Führt eine synchronisation für das Produkt durch. (synchronen, fürs Frontend gedacht)
     *
     * @param int $product_id
     * @throws Exception wirft eine exception wenn beim speichern des Produktes etwas fehlschlägt
     */
    public function syncGrossistsForProduct(int $product_id): void
    {
        $this->market_view_product_ek_offer_sync->updateProduct($product_id);
    }
}
