<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use bqp\Json;
use bqp\Model\SmartDataEntityNotFoundException;
use cached_query;
use wws\MarketView\Entities\MarketViewGpsr;
use wws\MarketView\Entities\MarketViewHersteller;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\Entities\MarketViewProduct;
use wws\MarketView\Entities\MarketViewProductRelation;
use wws\MarketView\Entities\MarketViewSource;
use wws\Product\ProductConst;

class MarketViewRepository
{
    protected db_generic $db_mv;
    private MarketViewMediaRepository $mv_media_repository;

    public function __construct(db_generic $db_mv, MarketViewMediaRepository $mv_media_repository)
    {
        $this->db_mv = $db_mv;
        $this->mv_media_repository = $mv_media_repository;
    }

    /**
     * Datenbankhandler für MarketView
     * ->wird gebraucht für Datenquellen die andere Datenstrukturen verwenden wie Krempl oder BSH, ansonsten ist mit den bisherigen product_creator System kein Zugriff möglich
     *
     * @return db_generic
     * @todo product_creators umstellen auf eigenen Klassen und über diese über eine Factory erzeugen
     */
    public function getDbMv(): db_generic
    {
        return $this->db_mv;
    }

    public function getMarketViewSource(int $mvsrc_id): MarketViewSource
    {
        return $this->db_mv->getDoctrine()->find(MarketViewSource::class, $mvsrc_id);
    }

    public function getMarketViewHersteller(int $mv_hersteller_id): MarketViewHersteller
    {
        return $this->db_mv->getDoctrine()->find(MarketViewHersteller::class, $mv_hersteller_id);
    }

    public function getMarketViewGpsr(int $mv_gpsr_id): MarketViewGpsr
    {
        $row = $this->db_mv->singleQuery("
            SELECT
                market_view_gpsr.mv_gpsr_id,
                market_view_gpsr.mvsrc_id,
                market_view_gpsr.internal_key,
                market_view_gpsr.external_key,
                market_view_gpsr.name1,
                market_view_gpsr.name2,
                market_view_gpsr.street,
                market_view_gpsr.post_code,
                market_view_gpsr.city,
                market_view_gpsr.country,
                market_view_gpsr.email,
                market_view_gpsr.phone,
                market_view_gpsr.web,
                market_view_gpsr.misc,
                market_view_gpsr.date_added
            FROM
                market_view_gpsr
            WHERE
                market_view_gpsr.mv_gpsr_id = '" . $mv_gpsr_id . "'
        ");

        if (!$row) {
            throw new SmartDataEntityNotFoundException();
        }

        return new MarketViewGpsr($row);
    }

    /**
     * lädt ein market_view_product
     *
     * @param int $mvsrc_id
     * @param string $mvsrc_product_id
     * @return MarketViewProduct
     */
    public function getMarketViewProduct(int $mvsrc_id, string $mvsrc_product_id): MarketViewProduct
    {
//              $market_view_product = new market_view_product();
//
        //        $daten = $this->db_mv->singleQuery("
        //            SELECT
        //                market_view_product.market_view_product,
        //                market_view_product.mvsrc_id,
        //                market_view_product.mvsrc_product_id,
        //                market_view_product.mvsrc_product_id_alt,
        //                market_view_product.mv_master_product_id,
        //                market_view_product.date_added,
        //                market_view_product.date_updated,
        //                market_view_product.date_updated_vk_netto,
        //                market_view_product.date_updated_availability_id,
        //                market_view_product.content_flag,
        //                market_view_product.product_status,
        //                market_view_product.product_name,
        //                market_view_product.product_line,
        //                market_view_product.rank,
        //                market_view_product.mv_cat_id,
        //                market_view_product.mv_cat_id_2,
        //                market_view_product.mv_hersteller_id,
        //                market_view_product.vk_netto,
        //                market_view_product.vk_netto_info,
        //                market_view_product.vk_netto_max,
        //                market_view_product.vk_nnn,
        //                market_view_product.vk_list,
        //                market_view_product.versand_netto,
        //                market_view_product.mv_vat_rate,
        //                market_view_product.uvp,
        //                market_view_product.evp,
        //                market_view_product.grundpreis_einheit,
        //                market_view_product.grundpreis_menge,
        //                market_view_product.grundpreis_faktor,
        //                market_view_product.mv_availability,
        //                market_view_product.availability_id,
        //                market_view_product.inventory,
        //                market_view_product.eol,
        //                market_view_product.vpe,
        //                market_view_product.vpe_zwang,
        //                market_view_product.mpn,
        //                market_view_product.ean,
        //                market_view_product.color,
        //                market_view_product.beschreibung,
        //                market_view_product.beschreibung_2,
        //                market_view_product.lieferumfang,
        //                market_view_product.testergebnis,
        //                market_view_product.features,
        //                market_view_product.features_struct,
        //                market_view_product.sonstiges,
        //                market_view_product.keywords,
        //                market_view_product.breite,
        //                market_view_product.hoehe,
        //                market_view_product.tiefe,
        //                market_view_product.gewicht,
        //                market_view_product.url,
        //                market_view_product.zolltarifnummer,
        //                market_view_product.product_id,
        //                market_view_product.other_sources_online,
        //                market_view_product.other_sources_available,
        //                market_view_product.genuine_part
        //            FROM
        //                market_view_product
        //            WHERE
        //                market_view_product.mvsrc_id = '".(int)$mvsrc_id."' AND
        //                market_view_product.mvsrc_product_id = '".$this->db_mv->escape($mvsrc_product_id)."'
        //        ");


        $result = $this->db_mv->dqlQuery("
            SELECT
                pr
            FROM
                wws\MarketView\Entities\MarketViewProduct pr
            WHERE
                pr.mvsrc_id = '" . $mvsrc_id . "' AND
                pr.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
        ");

        return $result->getSingleResult();
    }

    /**
     * @return MarketViewMedia[]
     */
    public function loadMediaByMvProduct(Entities\MarketViewProduct $mv_product): array
    {
        return $this->loadMedia($mv_product->getMvsrcId(), $mv_product->getMvsrcProductId());
    }

    /**
     * @return MarketViewMedia[]
     */
    public function loadMedia(int $mvsrc_id, string $mvsrc_product_id): array
    {
        return $this->mv_media_repository->loadMediaForMvsrcProduct($mvsrc_id, $mvsrc_product_id);
    }


    /**
     * @param int|null $mv_master_cat_id
     * @return MarketViewMasterCat
     */
    public function getMarketViewMasterCat(?int $mv_master_cat_id = null): MarketViewMasterCat
    {
        $mv_master_cat = new MarketViewMasterCat();
        $mv_master_cat->load($mv_master_cat_id, $this->db_mv);
        return $mv_master_cat;
    }

    /**
     * @param MarketViewMasterCat $mv_master_cat
     * @return int $mv_master_cat_id
     */
    public function saveMarketViewMasterCat(MarketViewMasterCat $mv_master_cat): int
    {
        return $mv_master_cat->save($this->db_mv);
    }

    public function getNewZeitraueme(): array
    {
        return [
            24 => 'letzte 24h',
            48 => 'letzte 48h',
            336 => 'letzte 7 Tagen',
            720 => 'letzte 30 Tagen',
            1440 => 'letzte 60 Tagen',
            2160 => 'letzte 90 Tagen',
            4320 => 'letzte 180 Tagen',
            8640 => 'letzte 360 Tagen',
        ];
    }

    public function getDeliverableAvailabilityIds(): array
    {
        return [MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE, MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE];
    }


    public function getMvsrcNamesWithDevicesEnabled(): array
    {
        return $this->db_mv->query("
            SELECT
                market_view_source.mvsrc_id,
                market_view_source.mvsrc_name
            FROM
                market_view_source
            WHERE
                market_view_source.use_devices = 1
            ORDER BY
                market_view_source.mvsrc_name
        ")->asSingleArray('mvsrc_id');
    }

    public function getMvsrcNames(): array
    {
        static $names = null;

        if ($names === null) {
            $names = $this->db_mv->query("
                SELECT
                    market_view_source.mvsrc_id,
                    market_view_source.mvsrc_name
                FROM
                    market_view_source
                ORDER BY
                    market_view_source.mvsrc_name
            ")->asSingleArray('mvsrc_id');
        }

        return $names;
    }

    public function getAvailabilitiesAsArray(): array
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_availability.availability_id,
                market_view_availability.availability_name,
                market_view_availability.availability_color
            FROM
                market_view_availability
        ")->asArray('availability_id');

        return $result;
    }


    /**
     * gibt ein Array mit den Source Typen zurück
     *
     * @return array
     */
    public function getMvsrcTypes(): array
    {
        return [
            MarketViewConst::MVSRC_TYPE_SUPLLIER => 'Grossist',
            'shop' => 'Konkurrenz',
            'content' => 'Content',
            'psm' => 'PSM',
            'friend' => 'Freund',
            MarketViewConst::MVSRC_TYPE_SELF => 'Wir'
        ];
    }

    /**
     * gibt ein Array mit den Source Preis Typen zurück
     *
     * @return array
     */
    public function getMvsrcPriceTypes(): array
    {
        return [
            'none' => '',
            'ek_liste' => 'EK-Liste',
            'rnp' => 'RNP',
            'snp' => 'SNP'
        ];
    }

    /**
     * gibt ein Array mit den Source Inventar Typen zurück
     *
     * @return array
     */
    public function getMvsrcInventoryTypes(): array
    {
        return [
            'none' => 'keine Bestände/Verfügbarkeiten',
            'inventory' => 'Bestand',
            'availability' => 'Verfügbarkeit'
        ];
    }

    /**
     * gibt die css class für den source_type zurück
     *
     * @param string $source_type
     * @return string
     */
    public function getColorForSourceType(string $source_type): string
    {
        $type_colors = [
            MarketViewConst::MVSRC_TYPE_SUPLLIER => '',
            'shop' => 'red',
            'content' => 'yellow',
            'psm' => 'green',
            'friend' => 'blue',
            MarketViewConst::MVSRC_TYPE_SELF => 'yellow'
        ];

        return $type_colors[$source_type];
    }


    /**
     * löscht nicht mehr verwendete Kategorien
     */
    public function reorgMarketViewCats(): void
    {
        $this->db_mv->query("
            DELETE
                market_view_cat
            FROM
                market_view_cat LEFT JOIN
                market_view_product ON (market_view_product.mv_cat_id = market_view_cat.mv_cat_id)
            WHERE
                market_view_product.mvsrc_product_id IS NULL
        ");
    }

    /**
     * gibt die $cat_id die in market_view_cats für $mv_cat_id definiert ist
     * ->siehe Lieferanten Kategorie Matching
     *
     * @param int $mv_cat_id
     * @return int|null
     */
    public function getDefinedCatIdByMvCatId(int $mv_cat_id, int $cat_tree_id): ?int
    {
        static $cache = [];

        if (!array_key_exists($mv_cat_id, $cache)) {
            $cat_id = $this->db_mv->fieldQuery("
                SELECT
                    market_view_cat_mapping.cat_id
                FROM
                    market_view_cat_mapping
                WHERE
                    market_view_cat_mapping.mv_cat_id = '" . $mv_cat_id . "' AND
                    market_view_cat_mapping.cat_tree_id = '" . $cat_tree_id . "'
            ");

            $cache[$mv_cat_id] = $cat_id;
        }

        return $cache[$mv_cat_id];
    }

    /**
     * gibt die $cat_id die in market_view_cats für $mv_cat_id definiert ist
     * ->siehe Lieferanten Kategorie Matching
     *
     * @param MarketViewProduct $mv_product
     * @param int $cat_tree_id
     * @return int|null $cat_id
     */
    public function getCatIdForMvProduct(MarketViewProduct $mv_product, int $cat_tree_id = ProductConst::CAT_TREE_ID_DEFAULT): ?int
    {
        $cat_id = $this->getDefinedCatIdByMvCatId($mv_product->getMvCatId(), $cat_tree_id);

        if ($cat_id) {
            return $cat_id;
        }

        if ($mv_product->getMvCatId2()) {
            $cat_id = $this->getDefinedCatIdByMvCatId($mv_product->getMvCatId2(), $cat_tree_id);

            if ($cat_id) {
                return $cat_id;
            }
        }

        return null;
    }

    public function isMediaCacheEnabledForMvsrcId(int $mvsrc_id): bool
    {
        static $cache = null;

        if ($cache === null) {
            $cache = $this->db_mv->query("
                SELECT
                    market_view_source.mvsrc_id,
                    market_view_source.cache_media
                FROM
                    market_view_source
            ")->asSingleArray('mvsrc_id');
        }

        return $cache[$mvsrc_id];
    }

    public function getMarketViewProductRelation(int $mvsrc_id, string $mvsrc_product_id): MarketViewProductRelation
    {
        $relations = $this->db_mv->fieldQuery("
            SELECT
                market_view_product_relation_struct.relations
            FROM
                market_view_product_relation_struct
            WHERE
                market_view_product_relation_struct.mvsrc_id = " . $mvsrc_id . " AND
                market_view_product_relation_struct.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
        ");

        $struct = new Entities\MarketViewProductRelation();
        $struct->unserialize($relations);

        return $struct;
    }

    public function getMarketViewCatTreeNames(int $mvsrc_id): array
    {
        $trees = $this->db_mv->query("
            SELECT
                market_view_mvsrc_cat_tree.tree_id,
                market_view_mvsrc_cat_tree.tree_name
            FROM
                market_view_mvsrc_cat_tree
            WHERE
                market_view_mvsrc_cat_tree.mvsrc_id = " . $mvsrc_id . "
        ")->asSingleArray('tree_id');

        if (!$trees) {
            $trees = ['1' => 'Standard'];
        }

        return $trees;
    }

    public function getImageDataSourceId(int $mvsrc_id): ?string
    {
        static $map = null;

        if ($map === null) {
            $map = $this->db_mv->query("
                SELECT
                    market_view_source.mvsrc_id,
                    market_view_source.image_data_source_id
                FROM
                    market_view_source
                WHERE
                    market_view_source.image_data_source_id IS NOT NULL
           ")->asSingleArray('mvsrc_id');
        }

        return $map[$mvsrc_id] ?? null;
    }


    public function getDistinctSonstigesKeys(int $mvsrc_id): array
    {
        $sql = "
            SELECT 
                DISTINCT market_view_product.sonstiges_struct 
            FROM
                market_view_product 
            WHERE
                market_view_product.sonstiges_struct != '' AND
                market_view_product.mvsrc_id = $mvsrc_id
            ORDER BY
                RAND()
            LIMIT
                25000
        ";
        $query = new cached_query($sql);
        $query->setLifetime(60);

        $result = $this->db_mv->execute($query);
        $distinct_keys = [];

        foreach ($result as $row) {
            $keys = array_keys(Json::decode($row['sonstiges_struct']));
            $distinct_keys = array_unique(array_merge($distinct_keys, $keys));
        }

        return array_map(function ($key) {
            return ['Ergebnisse' => $key];
        }, $distinct_keys);
    }
}
