<?php

namespace wws\MarketView;

use bqp\Exceptions\DevException;
use bqp\Vat\VatRate;
use bqp\Vat\VatRateConst;
use wws\business_structure\Shop;
use wws\business_structure\ShopRepository;

class MarketViewVatRate
{
    public const MV_VAT_RATE_UNKNOWN = 'unknown';
    public const MV_VAT_RATE_FULL = 'full';
    public const MV_VAT_RATE_REDUCE = 'reduce';


    public function getMvVatRateForVatRateValue(string $vat_rate_value): string
    {
        switch ($vat_rate_value) {
            case VatRateConst::UST_19_INT:
            case VatRateConst::UST_16_INT:
                return self::MV_VAT_RATE_FULL;
            case VatRateConst::UST_7_INT:
            case VatRateConst::UST_5_INT:
                return self::MV_VAT_RATE_REDUCE;
        }

        throw new DevException('Unknown Vat Rate (' . $vat_rate_value . ')');
    }


    /**
     * Dient dazu um Brutto-Preise auf Netto umzurechnen. Vorzugsweise ist der aktuelle Steuersatz aus der Quelle zu nehmen! Sollte keiner Verfügbar sein,
     * liefert diese Funktion als Fallback den aktuell gültigen Steuersatz für Deutschland zurück. Das kann bei einer Mehrwertsteueränderungen zu
     * überschneidungen führen!
     *
     * @return VatRate
     */
    public function getDefaultImportVatRate(): VatRate
    {
        return ShopRepository::getDefaultVatRate(Shop::ALLEGO);
    }
}
