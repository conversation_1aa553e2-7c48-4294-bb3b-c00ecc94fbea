<?php

namespace wws\MarketView;

use bqp\db\db_generic;
use wws\Product\Media\ProductPictureFingerprint;

class MarketViewMediaUtils
{
    protected db_generic $db_mv;
    protected MarketViewMediaCache $media_cache;

    public function __construct(db_generic $db_mv, MarketViewMediaCache $media_cache)
    {
        $this->db_mv = $db_mv;
        $this->media_cache = $media_cache;
    }

    public static function getMetaDataByRawImage(string $image_blob): array
    {
        $image = imagecreatefromstring($image_blob);
        //$image = image_utils::loadFileAsGdImage($url);
        $daten = [];
        $daten['md5'] = md5($image_blob);
        $daten['width'] = imagesx($image);
        $daten['height'] = imagesy($image);
        $daten['fingerprint'] = ProductPictureFingerprint::createFingerprint($image);
        @imagedestroy($image);

        //@todo... memory leak -> scheint nicht direkt am code zu liegen, code ist sauber, imagedestroy ist sauber...
        //läuft ohne Problem mit dem selben $raw_content (speicher konstant), einer festen Menge $raw_content (speicher pendelt konstant)
        //knallt aber wenn mit unterscheidlichen Bildern aufgerufen wird. (aber deterministisch! :-/)
        //nachtrag: eventuell liegt es an file_get_contents()...
        gc_collect_cycles(); //behebt das Problem...

        return $daten;
    }
}
