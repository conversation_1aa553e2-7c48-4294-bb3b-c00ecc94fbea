<?php

namespace wws\MarketView;

use bqp\Currency\CurrencyUtils;
use service_loader;

class MarketViewOutputHelper
{
    protected MarketViewRepository $mv_repository;

    public function __construct(MarketViewRepository $mv_repository)
    {
        $this->mv_repository = $mv_repository;
    }


    /**
     * hilfs funktion für das table_object um die spalte market_view_product.availability zu rendern
     * @param array $daten
     * @param string $field
     * @return string
     */
    public function tableHelper_availability(array $daten, string $field): string
    {
        if ($field == 'availability') {
            $field = 'availability_id';
        }
        if (!isset($daten['mv_availability'])) {
            $daten['mv_availability'] = '';
        }

        return $this->getAvailabilityAsHtml($daten[$field], $daten['mv_availability']);
    }


    public function getAvailabilityAsHtml($availability_id, $beschreibung = '')
    {
        static $availabilities;

        if (!isset($availabilities)) {
            $availabilities = $this->mv_repository->getAvailabilitiesAsArray();
        }

        if (!isset($availabilities[$availability_id])) {
            return '';
        }

        $temp = $availabilities[$availability_id];

        $beschreibung = !empty($beschreibung) ? ' (' . $beschreibung . ')' : '';

        return '<div title="' . $temp['availability_name'] . $beschreibung . '" style="width: 10px; height: 10px; border: 1px solid black; background-color: #' . $temp['availability_color'] . '; margin: auto; display: inline-block;"></div>';
    }


    public function tableHelper_compare_ertrag($daten, $key)
    {
        if (!$daten['compare_dest_vk_netto']) {
            return null;
        }

        $ek_netto = $daten['compare_vk_netto'];
        if ($daten['korrektur']) {
            $ek_netto *= 1 + ($daten['korrektur'] / 100);
        }

        if ($ek_netto) {
            $spanne = ($daten['compare_dest_vk_netto'] - $ek_netto) / ($ek_netto / 100);
        } else {
            $spanne = 100;
        }

        if ($spanne <= 0) {
            $color = '#FF8080';
        } elseif ($spanne <= 7) {
            $color = '#FFAE5E';
        } else {
            $color = '#80FF80';
        }

        $value = CurrencyUtils::format($daten['compare_dest_vk_netto'] - $ek_netto);

        return ['style' => 'background-color: ' . $color . ' ! important; text-align: right;', 'value' => '<nobr>' . $value . '</nobr>'];
    }


    public function tableHelper_sourcetype_colorize($daten)
    {
        $color = service_loader::get(MarketViewRepository::class)->getColorForSourceType($daten['typ']);

        if (!$color) {
            return '';
        }

        return ['class' => $color];
    }

    public function tableHelper_sourcetype_colorize_compare($daten)
    {
        $color = service_loader::get(MarketViewRepository::class)->getColorForSourceType($daten['compare_typ']);

        if (!$color) {
            return '';
        }

        return ['class' => $color];
    }

    public function tableHelper_compare_spanne($daten, $key)
    {
        if (!$daten['compare_dest_vk_netto']) {
            return;
        }

        $ek_netto = $daten['compare_vk_netto'];
        if ($daten['korrektur']) {
            $ek_netto *= 1 + ($daten['korrektur'] / 100);
        }

        if ($ek_netto) {
            $spanne = ($daten['compare_dest_vk_netto'] - $ek_netto) / ($ek_netto / 100);
        } else {
            $spanne = 100;
        }

        if ($spanne <= 0) {
            $color = '#FF8080';
        } elseif ($spanne <= 7) {
            $color = '#FFAE5E';
        } else {
            $color = '#80FF80';
        }

        $value = number_format($spanne, 2, ',', '') . ' %';

        return ['style' => 'background-color: ' . $color . ' ! important; text-align: right;', 'value' => '<nobr>' . $value . '</nobr>'];
    }


    public function tableHelper_availability_compare($daten, $field)
    {
        static $availabilities;

        if ($field == 'availability') {
            $field = 'compare_availability_id';
        }

        if (!$availabilities) {
            $availabilities = $this->mv_repository->getAvailabilitiesAsArray();
        }

        $temp = $availabilities[$daten[$field]];

        return '<div title="' . $temp['availability_name'] . ' (' . $daten['compare_mv_availability'] . ')" style="width: 10px; height: 10px; border: 1px solid black; background-color: #' . $temp['availability_color'] . '; margin: auto;"></div>';
    }

    public function tableHelper_availability_compare_dest($daten, $field)
    {
        if (!$daten['compare_dest_vk_netto']) {
            return '';
        }

        static $availabilities;

        if ($field == 'availability') {
            $field = 'compare_dest_availability_id';
        }

        if (!$availabilities) {
            $availabilities = $this->mv_repository->getAvailabilitiesAsArray();
        }

        $temp = $availabilities[$daten[$field]];

        return '<div title="' . $temp['availability_name'] . ' (' . $daten['compare_dest_mv_availability'] . ')" style="width: 10px; height: 10px; border: 1px solid black; background-color: #' . $temp['availability_color'] . '; margin: auto;"></div>';
    }


    /**
     * hilfsfunktion für das table_object um die spalte market_view_source.typ zu rendern
     * @param array $daten
     * @param string $field
     * @return string
     */
    public function tableHelper_mvsrc_typ(array $daten, string $field): string
    {
        static $types;

        if (!$types) {
            $types = $this->mv_repository->getMvsrcTypes();
        }

        return $types[$daten[$field]] ?? '';
    }


    public function tableHelper_availability_b2b($daten, $field)
    {
        static $availabilities;

        if ($field == 'availability') {
            $field = 'availability_id';
        }

        $availability_id = $daten[$field];

        if (!isset($availabilities)) {
            $availabilities = $this->mv_repository->getAvailabilitiesAsArray();
        }

        $temp = $availabilities[$availability_id];

        $beschreibung = '';
        $add = '';
        switch ($availability_id) {
            case 10:
                $temp['availability_color'] = '06FF00';
                $temp['availability_name'] = 'lagernd Radebeul';
                $add = ' R';
                break;
            case 25:
                $temp['availability_color'] = 'CCFF00';
                $temp['availability_name'] = 'lagernd beim Lieferanten';
                break;
            case 40:
            case 45:
                $temp['availability_color'] = 'FFBB00';
                $temp['availability_name'] = 'unbekannt, bitte anfragen';
        }

        return '<div title="' . $temp['availability_name'] . $beschreibung . '" style="width: 13px; height: 13px; border: 1px solid black; background-color: #' . $temp['availability_color'] . '; margin: auto; display: inline-block;"></div>' . $add;
    }
}
