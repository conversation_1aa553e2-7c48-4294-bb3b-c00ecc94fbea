<?php

namespace wws\MarketView;

use Psr\Container\ContainerInterface;
use service_loader;
use wws\MarketView\Entities\MarketViewSource;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatchingModule;

class MarketViewFactory
{
    private ContainerInterface $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public static function getInstance(): MarketViewFactory
    {
        return service_loader::getDiContainer()->get(self::class);
    }

    public function getMarketViewRepository(): MarketViewRepository
    {
        return $this->container->get(MarketViewRepository::class);
    }

    public function getMarketViewOutputHelper(): MarketViewOutputHelper
    {
        return $this->container->get(MarketViewOutputHelper::class);
    }

    public function MarketViewOutputHelper(): MarketViewOutputHelper
    {
        return $this->getMarketViewOutputHelper();
    }

    public function getMarketViewDeviceMatching(): MarketViewDeviceMatching
    {
        return $this->container->get(MarketViewDeviceMatching::class);
    }

    public function getMarketViewMatching(): MarketViewMatching
    {
        return $this->container->get(MarketViewMatching::class);
    }

    public function getMarketViewAutocreator(int $mvsrc_id): MarketViewAutocreator
    {
        $autocreator = $this->container->get(MarketViewAutocreator::class);
        $autocreator->setMvsrcId($mvsrc_id);

        return $autocreator;
    }

    public function buildMatchingClass(string $class, MarketViewSource $mvsrc): ProductMatchingModule
    {
        $class = ltrim($class, '\\'); //php di + php namespace logik -> @see https://allego.myjetbrains.com/youtrack/issue/WWS-1702
        return $this->container->get($class);
    }
}
