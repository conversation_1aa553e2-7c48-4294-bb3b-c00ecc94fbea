<?php

namespace wws\MarketView;

use bqp\db\db_generic;

class MarketViewAutocreator
{
    protected db_generic $db;
    protected db_generic $db_mv;

    protected int $mvsrc_id;

    public function __construct(db_generic $db_mv, db_generic $db)
    {
        $this->db_mv = $db_mv;
        $this->db = $db;
    }

    public function setMvsrcId(int $mvsrc_id): void
    {
        $this->mvsrc_id = $mvsrc_id;
    }

    public function getSqlQuery(): string
    {
        $brand_ids = $this->db->query("
            SELECT
                product_brand.brand_id
            FROM
                product_brand
            WHERE
                product_brand.market_view_autocreate = 1
        ")->asSingleArray();

        return "SELECT
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id,
                market_view_product.product_name,
                market_view_product.versand_netto
            FROM
                market_view_product INNER JOIN
                market_view_hersteller ON (market_view_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id) LEFT JOIN
                market_view_autocreate_hersteller ON (market_view_autocreate_hersteller.mvsrc_id = market_view_product.mvsrc_id AND market_view_product.mv_hersteller_id = market_view_autocreate_hersteller.mv_hersteller_id)
            WHERE
                market_view_product.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_product.availability_id IN (10, 20) AND
                market_view_product.product_id = 0 AND
                market_view_hersteller.brand_id != 0 AND
                market_view_product.vk_netto BETWEEN 3 AND 2000 AND
                market_view_product.ean != '' AND
                (
                    market_view_autocreate_hersteller.status = 1 OR
                    market_view_hersteller.brand_id IN (" . $this->db_mv->in($brand_ids) . ")
                )
        ";
    }


    public function execute(): void
    {
        $sql = $this->getSqlQuery();

        $this->db_mv->query($sql)->display();
    }
}
