<?php

namespace wws\Mailsystem;

use bqp\db\db_generic;

class MailsystemArchiv
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function archivMails(array $mail_ids): void
    {
        if (!$mail_ids) {
            return;
        }

        //alle ungelesene mails als gelesen markieren
        $unreaden_mail_ids = $this->db->query("
            SELECT
                mailsystem_mails.mail_id
            FROM
                mailsystem_mails
            WHERE
                mailsystem_mails.mail_id IN (" . $this->db->in($mail_ids) . ") AND
                mailsystem_mails.status = 'unread'
        ")->asSingleArray();

        foreach ($unreaden_mail_ids as $mail_id) {
            $mail = new MailsystemMail($mail_id, false);
            $mail->setStatus(MailsystemMail::STATUS_READ);
            $mail->save();
        }

        //archivieren protokollieren
        foreach ($mail_ids as $mail_id) {
            MailsystemRepository::protokoll_mail($mail_id, 'archiv');
        }

        $this->db->query('
            INSERT INTO
                mailsystem_mails_archiv (
                    mail_id,
                    mailbox_id,
                    folder_id,
                    mail_note,
                    mailmodus,
                    betreff,
                    sender,
                    sender_email,
                    empfaenger,
                    empfaenger_email,
                    external_email,
                    date_mail,
                    date_receive,
                    status,
                    body,
                    anlagen,
                    header,
                    customer_id,
                    customer_id_status,
                    order_id,
                    archivierungs_datum
                )
            SELECT
                mailsystem_mails.mail_id,
                mailsystem_mails.mailbox_id,
                mailsystem_mails.folder_id,
                mailsystem_mails.mail_note,
                mailsystem_mails.mailmodus,
                mailsystem_mails.betreff,
                mailsystem_mails.sender,
                mailsystem_mails.sender_email,
                mailsystem_mails.empfaenger,
                mailsystem_mails.empfaenger_email,
                mailsystem_mails.external_email,
                mailsystem_mails.date_mail,
                mailsystem_mails.date_receive,
                mailsystem_mails.status,
                mailsystem_mails.body,
                mailsystem_mails.anlagen,
                mailsystem_mails.header,
                mailsystem_mails.customer_id,
                mailsystem_mails.customer_id_status,
                mailsystem_mails.order_id,
                UNIX_TIMESTAMP()
            FROM
                mailsystem_mails
            WHERE
                mailsystem_mails.mail_id IN (' . $this->db->makeIn($mail_ids) . ')
            ON DUPLICATE KEY UPDATE
                mailsystem_mails_archiv.mail_id = VALUES(mailsystem_mails_archiv.mail_id)
        ');

        $this->db->query('
            DELETE FROM
                mailsystem_mails
            WHERE
                mailsystem_mails.mail_id IN (' . $this->db->makeIn($mail_ids) . ')
        ');
    }
}
