<?php

namespace wws\Mailsystem;

use bqp\Config\ConfigContainer;
use bqp\Date\DateObj;
use bqp\Exceptions\FatalException;
use bqp\Mail\MimeMail;
use bqp\Mail\Pop3;
use bqp\Utils\MailUtils;
use config;
use db;
use Exception;
use service_loader;
use wws\Mails\MailArchivRepository;
use wws\Mails\MailConst;
use wws\Mails\MailContentAnalyse;
use wws\Mails\MailSearcher\MailSearcherWws;

class MailsystemReceiver
{
    /**
     * @var ConfigContainer|mixed|null
     */
    private $secret;

    public function __construct()
    {
        $this->secret = config::getLegacy('system', 'contact_form_secret'); //
    }

    /**
     * @param MailsystemMailbox $mailbox
     * @param string $mail
     * @param null|callable $back_ticker
     * @throws MailsystemConnectionException
     * @throws FatalException
     */
    public function receive(MailsystemMailbox $mailbox, string $mail, ?callable $back_ticker = null): void
    {
        $rule_engine = MailsystemRuleEngine::createByMailbox($mailbox, $mail);

        ini_set('memory_limit', '1024M');
        set_time_limit(0);

        $db = db::getInstance();
        $account_daten = $db->singleQuery("
            SELECT
                mailsystem_accounts.email,
                mailsystem_accounts.letzter_abruf,
                mailsystem_accounts.pop_server,
                mailsystem_accounts.username,
                mailsystem_accounts.passwort,
                
                mailsystem_mailboxen.mailbox_id,
                mailsystem_mailboxen.shop_id
            FROM
                mailsystem_accounts INNER JOIN
                mailsystem_mailboxen ON (mailsystem_accounts.mailbox_id = mailsystem_mailboxen.mailbox_id)
            WHERE
                mailsystem_accounts.email = '" . $db->escape($mail) . "'
        ");

        if (!$account_daten) {
            throw new FatalException('keine account daten hinterlegt');
        }

        $pop3 = new Pop3();

        $host = $account_daten['pop_server'];
        $port = 110;

        if (strpos($host, 'ssl:') === 0) {
            $port = 995;
        }

        if (strrpos($host, ':') + 1 >= strlen($host) - 5) {
            $port = substr($host, strrpos($host, ':') + 1);
            $host = substr($host, 0, strrpos($host, ':'));
        }


        if (!$pop3->connect($host, $port)) {
            throw new MailsystemConnectionException();
        }

        $mails = $pop3->login($account_daten['username'], $account_daten['passwort']);

        register_shutdown_function([&$pop3, 'quit']);

        switch ($mails) {
            case -1:
                $pop3->quit();
                throw new MailsystemConnectionException('Pop3 Error: ' . $pop3->ERROR);
            case 0:
                $pop3->quit();
                break;
            default:
                if ($back_ticker) {
                    call_user_func($back_ticker, $mails, 'anzahl');
                }

                for ($i = 1; $i <= $mails; $i++) {
                    $raw_mail = implode("", $pop3->get($i));

                    try {
                        $mime_mail = new MimeMail($raw_mail);
                    } catch (Exception $e) {
                        var_dump($e->getMessage());
                        continue;
                    }

                    $this->rewriteFromIfNecessary($mime_mail);

                    $mailsystem_mail = self::saveMail($mime_mail, $account_daten['mailbox_id'], $account_daten['shop_id']);

                    //debug
                    $mail_file = config::system('temp_dir') . '/raw_mails/mail_' . $mailsystem_mail->getMailId() . '.raw';
                    file_put_contents($mail_file, $raw_mail);

                    $rule_engine->execute($mailsystem_mail);

                    $pop3->delete($i);

                    if ($back_ticker) {
                        call_user_func($back_ticker, $i);
                    }
                }
                $pop3->quit();
        }
    }

    public static function saveMail(MimeMail $mail, $mailbox_id = 1, $shop_id = 1): MailsystemMail
    {
        $folder_id = -1;

        //anlagen speichern
        if (is_array($mail['anlagen']) && $mail['anlagen']) {
            $attachment_manager = service_loader::getMailAttachmentManager();

            $anlagen_sql = [];

            foreach ($mail['anlagen'] as $anlage) {
                $hash = $attachment_manager->saveAttachmentContent($anlage['body']);

                $daten = ['file' => $hash, 'org_filename' => $anlage['filename'], 'filesize' => strlen($anlage['body'])];

                if (isset($anlage['content_id'])) {
                    $daten['content_id'] = $anlage['content_id'];
                }

                $anlagen_sql[] = $daten;
            }

            $anlagen_sql = MailsystemAttachment::serialize($anlagen_sql);
        } else {
            $anlagen_sql = '';
        }

        $searcher = new MailSearcherWws();
        $searcher->setShopId($shop_id);
        $searcher->setByMimeMail($mail);

        $result = $searcher->search();

        $body = $mail['body'];

        //jetzt kommen hier schon data urls mit riesigen pngs rein... -> wir hacken den body ab, wenn der größer als x ist.
        //->der faktor ist beabsichtigt
        if (strlen($body) > 5 * 1024 * 1024) {
            $body = substr($body, 0, 1024 * 1024);
        }

        $mailsystem_mail = new MailsystemMail();
        $mailsystem_mail->setBetreff($mail->getBetreff());
        $mailsystem_mail->setMailmodus($mail['mailmodus']);
        $mailsystem_mail->setSender($mail['sender']);
        $mailsystem_mail->setSenderEmail($mail['sender_email']);
        $mailsystem_mail->setEmpfaenger($mail['empfaenger']);
        $mailsystem_mail->setEmpfaengerMail($mail->getEmpfaengerMail());
        $mailsystem_mail->setExternalEmail($mail->getSenderMail());
        $mailsystem_mail->setHeader($mail['header']);
        $mailsystem_mail->setDateReceive(new DateObj());
        $mailsystem_mail->setDate($mail->getDatum());
        $mailsystem_mail->setBody($body);
        $mailsystem_mail->setAnlagen($anlagen_sql);
        $mailsystem_mail->setMailboxId($mailbox_id);
        $mailsystem_mail->setFolderId($folder_id);
        $mailsystem_mail->setCustomerId($result->getCustomerId());
        $mailsystem_mail->setOrderId($result->getOrderId());

        $analyse = new MailContentAnalyse();

        if ($analyse->isStorno($mailsystem_mail->getBetreff(), $mailsystem_mail->getBody())) {
            $mailsystem_mail->addMailTag(MailConst::MAIL_TAG_STORNO);
        } elseif ($analyse->isWiderruf($mailsystem_mail->getBetreff(), $mailsystem_mail->getBody())) {
            $mailsystem_mail->addMailTag(MailConst::MAIL_TAG_WIDERRUF);
        }
        $mailsystem_mail->save();

        if ($result->getCustomerId()) {
            MailArchivRepository::addToCustomer($mailsystem_mail, $result->getCustomerId(), $result->getOrderId());
        }

        MailsystemRepository::protokoll_mail($mailsystem_mail->getMailId(), 'reciv');

        return $mailsystem_mail;
    }


    public function rewriteFromIfNecessary(MimeMail $mail): void
    {
        $headers = $mail->daten['header'];

        if (!preg_match('~X-Reply-To-From: (\X{32})~u', $headers, $match)) {
            return;
        }

        $sig = $match[1];

        $reply_to = MailUtils::extractReplayToMail($headers);

        if (!$reply_to) {
            return;
        }

        if (md5($reply_to . $this->secret) !== $sig) {
            return;
        }

        $mail->setSender($reply_to);
        $mail->setSenderMail($reply_to);
    }
}
