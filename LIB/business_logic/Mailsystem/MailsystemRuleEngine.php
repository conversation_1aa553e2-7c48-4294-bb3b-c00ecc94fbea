<?php

namespace wws\Mailsystem;

use DomainException;
use service_loader;

class MailsystemRuleEngine
{
    private MailsystemRepository $mailsystem_repository;
    private MailsystemArchiv $mailsystem_archiv;

    private array $rules = [];
    private string $account_email_address;


    public function __construct()
    {
        $this->mailsystem_repository = service_loader::getDiContainer()->get(MailsystemRepository::class);
        $this->mailsystem_archiv = service_loader::getDiContainer()->get(MailsystemArchiv::class);
    }

    public function setAccountEmailAddress(string $account_email_address): void
    {
        $this->account_email_address = $account_email_address;
    }

    public function addRule(array $rule): void
    {
        $this->rules[] = $rule;
    }

    public static function createByMailbox(MailsystemMailbox $mailbox, ?string $account_email_address = null): MailsystemRuleEngine
    {
        $rule_engine = new MailsystemRuleEngine();

        if ($account_email_address) {
            $rule_engine->setAccountEmailAddress($account_email_address);
        }

        foreach ($mailbox->getRules() as $rule) {
            $rule_engine->addRule($rule);
        }

        return $rule_engine;
    }

    public function execute(MailsystemMail $mailsystem_mail): void
    {
        $rules = $this->getMatchedRules($mailsystem_mail);

        if (!$rules) {
            return;
        }

        //aktuell nur die erste Regel abarbeiten
        //falls es nötig sein sollte, mehrere Regeln ab zu arbeiten, müssen die Regeln mit Prioritäten und ggf. Abbruch Optionen ergänzt werden
        $rule = $rules[0]['dann'];

        if (isset($rule['verschiebe'])) {
            $mailsystem_mail->setFolderId($rule['verschiebe']);
            $mailsystem_mail->save();
        }

        if (isset($rule['delete'])) {
            $this->mailsystem_repository->deleteMail($mailsystem_mail);
            return;
        }

        if (isset($rule['archivieren'])) {
            $this->mailsystem_archiv->archivMails([$mailsystem_mail->getMailId()]);
            return;
        }
    }

    public function getMatchedRules(MailsystemMail $mailsystem_mail): array
    {
        $matched_rules = [];

        foreach ($this->rules as $rule) {
            $regel_check = null;
            foreach ($rule['wenn'] as $regel_key => $value) {
                $regel_check_rule = false;

                switch ($regel_key) {
                    case 'von_konto':
                        if ($this->account_email_address && $value === $this->account_email_address) {
                            $regel_check_rule = true;
                        }
                        break;
                    case 'an_empfaenger_email':
                        if ($value === $mailsystem_mail->getEmpfaengerMail()) {
                            $regel_check_rule = true;
                        }
                        break;
                    case 'betreff_contains':
                        if (preg_match("~" . preg_quote($value, '~') . "~ui", $mailsystem_mail->getBetreff())) {
                            $regel_check_rule = true;
                        }
                        break;
                    case 'absender_contains':
                        if (preg_match("~" . preg_quote($value, '~') . "~ui", $mailsystem_mail->getSender())) {
                            $regel_check_rule = true;
                        }
                        break;
                }

                if ($regel_check === null) {
                    $regel_check = $regel_check_rule;
                } else {
                    $regel_check = $regel_check && $regel_check_rule;
                }
            }

            if ($regel_check) {
                $matched_rules[] = $rule;
            }
        }

        return $matched_rules;
    }


    public static function getRuleTemplates(): array
    {
        //ACHTUNG: controller_settings.php muss mit angepasst werden und MailsystemRuleExecuter
        $rule_templates = [];

        $rule_templates['wenn']['von_konto'] = 'Wenn E-Mail über Konto "<span style="color:blue;">{{konto}}</span>" eingeht';
        $rule_templates['wenn']['an_empfaenger_email'] = 'Wenn eine E-Mail für Empfänger "<span style="color:blue;">{{text}}</span>" eingeht';
        $rule_templates['wenn']['betreff_contains'] = 'Wenn E-Mail im Betreff "<span style="color:blue;">{{text}}</span>" enthält';
        $rule_templates['wenn']['absender_contains'] = 'Wenn Absender "<span style="color:blue;">{{text}}</span>" enthält';

        $rule_templates['dann']['verschiebe'] = 'verschiebe nach "<span style="color:blue;">{{ordner}}</span>"';
        $rule_templates['dann']['archivieren'] = 'archivieren';
        $rule_templates['dann']['delete'] = 'löschen';

        return $rule_templates;
    }

    public static function translateRule(array $rule, MailsystemMailbox $mailbox): string
    {
        $rule_text = '';

        $rule_templates = self::getRuleTemplates();

        $conditions_text = [];

        foreach ($rule['wenn'] as $wenn_key => $value) {
            $conditions_text[] = preg_replace("#({{.*?}})#", $value, $rule_templates['wenn'][$wenn_key]) . ', ';
        }
        if (count($conditions_text) === 1) {
            $rule_text .= $conditions_text[0];
        } else {
            $rule_text .= '"' . implode('" und "', array_map(function ($value) {
                    return trim($value, ' ,');
                }, $conditions_text)) . '", ';
        }

        foreach ($rule['dann'] as $dann_key => $value) {
            if ($dann_key === 'verschiebe') {
                try {
                    $value = $mailbox->getFolderName($value);
                } catch (DomainException $e) {
                    $value = '<b style="color: red;">unbekannter Ordner</b>';
                }
            }

            $rule_text .= preg_replace("#({{.*?}})#", $value, $rule_templates['dann'][$dann_key]) . '.';
        }

        return $rule_text;
    }
}
