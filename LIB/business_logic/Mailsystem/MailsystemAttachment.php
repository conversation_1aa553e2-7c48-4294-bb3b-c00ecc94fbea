<?php

namespace wws\Mailsystem;

use bqp\Json;

class MailsystemAttachment
{

    public static function unserialize(?string $attachments_raw): array
    {
        if (!$attachments_raw) {
            return [];
        }

        if (Json::looksLikeJson($attachments_raw)) {
            return Json::decode($attachments_raw);
        }

        return unserialize($attachments_raw);
    }

    public static function serialize(array $attachments): string
    {
        //file, org_filename, filesize und ggf. content_id

        if ($attachments) {
            return json_encode($attachments);
        }

        return '';
    }
}
