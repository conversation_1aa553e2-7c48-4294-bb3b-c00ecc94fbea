<?php

namespace wws\Mailsystem;

use bqp\db\db_generic;
use bqp\Event\EventDispatcher;
use bqp\Model\SmartDataEntityNotFoundException;
use db;
use env;
use service_loader;
use wws\Mails\Event\EventMailOrderProcessed;

class MailsystemRepository
{
    public const FOLDER_ID_INBOX = -1;
    public const FOLDER_ID_OUTBOX = -2;
    public const FOLDER_ID_DRAFTS = -3;

    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function getAutomaticDownloadMailboxes(): array
    {
        return $this->db->query("
            SELECT
                mailsystem_accounts.email,
                mailsystem_mailboxen.mailbox_id
            FROM
                mailsystem_mailboxen INNER JOIN
                mailsystem_accounts ON (mailsystem_accounts.mailbox_id = mailsystem_mailboxen.mailbox_id)
            WHERE
                mailsystem_mailboxen.automatic_download = 1
        ")->asArray();
    }

    /**
     * @param int $mail_id
     * @return MailsystemMail
     */
    public function loadMail($mail_id): MailsystemMail
    {
        return new MailsystemMail($mail_id, false);
    }

    public function saveMail(MailsystemMail $mail): int
    {
        return $mail->save();
    }

    /**
     * @param MailsystemMail $mail
     * @param string $reason
     */
    public function deleteMail(MailsystemMail $mail, string $reason = ''): void
    {
        $text = '';
        if ($reason) {
            $text .= $reason . ': ';
        }
        $text .= $mail->getSenderEmail() . ' - ' . $mail->getBetreff();

        if ($mail->getOrderId()) {
            $event_service = service_loader::getDiContainer()->get(EventDispatcher::class);
            $event_service->dispatch(new EventMailOrderProcessed($mail->getMailId(), $mail->getOrderId()));
        }

        self::protokoll_mail($mail->getMailId(), 'del', $text);

        $this->db->query("
            DELETE FROM
                mailsystem_mails
            WHERE
                mailsystem_mails.mail_id = '" . (int)$mail->getMailId() . "'
        ");
    }

    public function deleteMailByMailId(int $mail_id, string $reason = ''): void
    {
        $mail = $this->loadMail($mail_id);
        $this->deleteMail($mail, $reason);
    }

    /**
     * @param int $mailbox_id
     * @return MailsystemMailbox
     * @throws SmartDataEntityNotFoundException
     */
    public function loadMailbox(int $mailbox_id): MailsystemMailbox
    {
        $row = $this->db->singleQuery("
            SELECT
                mailsystem_mailboxen.mailbox_id,
                mailsystem_mailboxen.mailbox_name,
                mailsystem_mailboxen.shop_id,
                mailsystem_mailboxen.folders,
                mailsystem_mailboxen.rules,
                mailsystem_mailboxen.automatic_download
            FROM
                mailsystem_mailboxen
            WHERE
                mailsystem_mailboxen.mailbox_id = $mailbox_id
        ");

        if (!$row) {
            throw new SmartDataEntityNotFoundException('unknown mailbox');
        }

        $mailbox = new MailsystemMailbox();
        $mailbox->getSmartDataObj()->loadDaten($row);
        return $mailbox;
    }

    public function saveMailbox(MailsystemMailbox $mailbox): int
    {
        $smart_data = $mailbox->getSmartDataObj();

        $this->db->simpleInsertUpdate('mailsystem_mailboxen', $smart_data->getAsArray());

        if ($smart_data->isNew()) {
            $smart_data->setterDirect('mailbox_id', $this->db->insert_id());
        }

        $mailbox->getSmartDataObj()->setSaved();

        return $mailbox->getMailboxId();
    }

    public function getMailboxNames(): array
    {
        return $this->db->query("
            SELECT
                mailsystem_mailboxen.mailbox_id,
                mailsystem_mailboxen.mailbox_name
            FROM
                mailsystem_mailboxen
        ")->asSingleArray('mailbox_id');
    }

    public function bulkMove(array $mail_ids, int $folder_id, bool $unread = false, ?int $mailbox_id = null): void
    {
        foreach ($mail_ids as $mail_id) {
            self::protokoll_mail($mail_id, 'move', $folder_id);
            if ($unread) {
                self::protokoll_mail($mail_id, 'unread');
            }
        }

        $fields = [
            'mailsystem_mails.folder_id' => $folder_id
        ];

        if ($unread) {
            $fields['mailsystem_mails.status'] = 'unread';
        }

        if ($mailbox_id) {
            $fields['mailsystem_mails.mailbox_id'] = $mailbox_id;
        }

        $this->db->simpleUpdate('mailsystem_mails', $fields, "mailsystem_mails.mail_id IN (" . $this->db->in($mail_ids) . ")");
    }


    public function getRuleTemplates(): array
    {
        return MailsystemRuleEngine::getRuleTemplates();
    }

    public function translateRule(array $rule, MailsystemMailbox $mailbox): string
    {
        return MailsystemRuleEngine::translateRule($rule, $mailbox);
    }

    public static function protokoll_mail($mail_id, $action, $action_value = '')
    {
        $db = db::getInstance();

        $db->query("
            INSERT INTO
                protokoll_mails
            SET
                protokoll_mails.datum = NOW(),
                protokoll_mails.mail_id = '" . (int)$mail_id . "',
                protokoll_mails.user_id = '" . env::getUserId() . "',
                protokoll_mails.mailbox_id = '0',
                protokoll_mails.action = '" . $db->escape($action) . "',
                protokoll_mails.action_value = '" . $db->escape($action_value) . "'
        ");
    }
}
