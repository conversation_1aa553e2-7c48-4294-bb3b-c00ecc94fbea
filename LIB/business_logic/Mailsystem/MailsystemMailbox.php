<?php

namespace wws\Mailsystem;

use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use bqp\Tree\TreeReader;
use bqp\Tree\TreeReaderBuilderByParentKey;
use db;
use DomainException;

class MailsystemMailbox implements ProvideSmartDataObj
{
    private $data;

    private $folder_cache = [];

    public function __construct()
    {
        $this->data = new SmartDataObj($this);
    }

    public function getMailboxId(): int
    {
        return $this->data['mailbox_id'];
    }

    public function getShopId(): int
    {
        return $this->data['shop_id'];
    }

    public function isAutomaticDownload(): bool
    {
        return (bool)$this->data['automatic_download'];
    }

    public function getFolders(): array
    {
        return $this->folder_cache;
    }

    public function getFoldersAsTree(): TreeReader
    {
        $builder = new TreeReaderBuilderByParentKey(TreeReaderBuilderByParentKey::ARRAY_KEY, 'parent');
        $builder->setData($this->folder_cache);

        $tree_reader = $builder->createTreeReader();

        return $tree_reader;
    }

    /**
     * @param $key
     * @return string
     * @throws DomainException
     */
    public function getFolderName($key): string
    {
        if (!isset($this->folder_cache[$key])) {
            throw new DomainException('folder is unknown');
        }
        return $this->folder_cache[$key]['name'];
    }

    public function getRules(): array
    {
        if (!$this->data['rules']) {
            return [];
        }

        return unserialize($this->data['rules']);
    }

    public function getRule(?int $rule_id): array
    {
        $rules = $this->getRules();

        if (!isset($rules[$rule_id])) {
            throw new SmartDataEntityNotFoundException();
        }

        return $rules[$rule_id];
    }


    public function setRules(array $rules): bool
    {
        $raw_rules = serialize($rules);

        return $this->data->setter('rules', $raw_rules);
    }

    public function saveRule(array $rule, ?int $rule_id = null): int
    {
        $rules = $this->getRules();

        if ($rule_id !== null) {
            $rules[$rule_id] = $rule;
        } else {
            $rules[] = $rule;

            end($rules);
            $rule_id = key($rules);
        }

        $this->setRules($rules);
        return $rule_id;
    }

    public function removeRule(int $rule_id): void
    {
        $this->getRule($rule_id); //sicherstellen das es die regel gibt.

        $rules = $this->getRules();

        unset($rules[$rule_id]);

        $this->setRules($rules);
    }


    /**
     * @return SmartDataObj
     */
    public function getSmartDataObj(): SmartDataObj
    {
        return $this->data;
    }

    public function eventLoadData(): void
    {
        $this->recacheFolders();
    }

    private function recacheFolders(): void
    {
        $this->folder_cache = unserialize($this->data['folders']);

        foreach ($this->folder_cache as $key => $value) {
            $this->folder_cache[$key]['folder_id'] = $key;
        }
    }

    public function getAccountAddresses(): array
    {
        $db = db::getInstance();

        return $db->query("
            SELECT
                mailsystem_accounts.email
            FROM
                mailsystem_accounts
            WHERE
                mailsystem_accounts.mailbox_id = " . $this->getMailboxId() . "
        ")->asSingleArray();
    }
}
