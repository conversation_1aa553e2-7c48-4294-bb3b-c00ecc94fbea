<?php

namespace wws\Mailsystem;

use bqp\db\db_generic;
use wws\business_structure\Shop;
use wws\Customer\CustomerConst;
use wws\Mails\MailArchivRepository;
use wws\Mails\MailSearcher\MailSearcherWws;

class MailsystemDelayedCustomerSearch
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }


    public function processUnreadedMails(int $mailbox_id): void
    {
        $shop_id = Shop::ALLEGO;

        $mail_ids = $this->db->query("
            SELECT
                mailsystem_mails.mail_id
            FROM
                mailsystem_mails
            WHERE
                mailsystem_mails.mailbox_id = $mailbox_id AND
                mailsystem_mails.status = '" . MailsystemMail::STATUS_UNREAD . "' AND
                mailsystem_mails.customer_id = 0
        ")->asSingleArray();

        foreach ($mail_ids as $mail_id) {
            $mail = new MailsystemMail($mail_id);

            $searcher = new MailSearcherWws();
            $searcher->setShopId($shop_id);
            $searcher->setByMailsystemMail($mail);

            $result = $searcher->search();

            if (!$result->isSuccess()) {
                continue;
            }

            $this->processMail($mail, $result->getCustomerId(), $result->getOrderId());
        }
    }

    /**
     * Ordnet ältere Mails den Kunden und Aufträgen zu. Es wird aus Performance Gründen nicht MailSearcher
     * verwendet, sondern es wird nach den Mail-Adressen aller neuen Kunden in Zeitraum X im Mailsystem gesucht.
     * Weil: Wenn zum Eingang der Mail schon die Mail-Adresse in der DB wäre, wäre die Mail bereits zugeordnet.
     *
     * @param int $mailbox_id
     */
    public function processOldMails(int $mailbox_id): void
    {
        $threshold_days = 3;

        $result = $this->db->query("
                SELECT
                    customers.customer_id,
                    mailsystem_mails.mail_id,
                    0 AS archived
                FROM
                    customers LEFT JOIN
                    mailsystem_mails ON (customers.email = mailsystem_mails.external_email)
                WHERE
                    customers.added >= NOW() - INTERVAL " . $threshold_days . " DAY AND
                    customers.customer_status != '" . CustomerConst::CUSTOMER_STATUS_DELETE . "' AND
                    LENGTH(customers.email) > 5 AND
                    mailsystem_mails.customer_id = 0 AND
                    mailsystem_mails.date_receive >= customers.added - INTERVAL 90 DAY AND
                    mailsystem_mails.mailbox_id = $mailbox_id
            UNION
                SELECT
                    customers.customer_id,
                    mailsystem_mails_archiv.mail_id,
                    1 AS archived
                FROM
                    customers LEFT JOIN
                    mailsystem_mails_archiv ON (customers.email = mailsystem_mails_archiv.external_email)
                WHERE
                    customers.added >= NOW() - INTERVAL " . $threshold_days . " DAY AND
                    customers.customer_status != '" . CustomerConst::CUSTOMER_STATUS_DELETE . "' AND
                    LENGTH(customers.email) > 5 AND
                    mailsystem_mails_archiv.customer_id = 0 AND
                    mailsystem_mails_archiv.date_receive >= customers.added - INTERVAL 90 DAY AND
                    mailsystem_mails_archiv.mailbox_id = $mailbox_id
        ");

        if ($result->count() === 0) {
            return;
        }

        //bestellungen suchen -> wenn der Kunde genau eine Bestellung hat, dann nehmen wir an, dass diese Bestellung zu der Mail gehört
        $order_ids = $this->db->query("
            SELECT
                orders.customer_id,
                MIN(orders.order_id) AS order_id
            FROM
                orders
            WHERE
                orders.customer_id IN (" . $this->db->in($result->column('customer_id')) . ")
            GROUP BY
                orders.customer_id
            HAVING
                COUNT(*) = 1
        ")->asSingleArray('customer_id');

        foreach ($result as $row) {
            $customer_id = $row['customer_id'];
            $order_id = $order_ids[$row['customer_id']] ?? 0;

            $mail = new MailsystemMail($row['mail_id'], (bool)$row['archived']);

            $this->processMail($mail, $customer_id, $order_id);
        }
    }

    private function processMail(MailsystemMail $mail, int $customer_id, int $order_id): void
    {
        $mail->setCustomerId($customer_id);
        $mail->setOrderId($order_id);
        $mail->save();

        MailArchivRepository::addToCustomer($mail, $mail->getCustomerId(), $mail->getOrderId());
    }
}
