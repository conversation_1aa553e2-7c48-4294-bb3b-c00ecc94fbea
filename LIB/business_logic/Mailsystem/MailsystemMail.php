<?php

namespace wws\Mailsystem;

use bqp\Date\DateObj;
use bqp\Event\EventDispatcher;
use bqp\field_manager;
use bqp\Json;
use bqp\Model\ProvideSmartDataObj;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use bqp\Utils\MailUtils;
use db;
use Exception;
use service_loader;
use wws\Mails\Event\EventMailOrderProcessed;
use wws\Mails\Event\EventMailOrderUnprocessed;
use wws\Mails\MailAttachmentManager;
use wws\Mails\Templates\MailTemplateRepository;

class MailsystemMail implements ProvideSmartDataObj
{
    const STATUS_READ = 'read';
    const STATUS_UNREAD = 'unread';
    const STATUS_FORWARD = 'forward';
    const STATUS_ANSWERD = 'answerd';

    protected SmartDataObj $daten;

    protected bool $archived = false;

    public function __construct($mail_id = null, $archiv = false)
    {
        $this->daten = new SmartDataObj($this);

        if ($mail_id !== null) {
            $this->loadMail($mail_id, $archiv);
        } else {
            $this->loadDefaults();
        }
    }

    private function loadDefaults()
    {
        $defaults = field_manager::getInstance()->getDefaults('mailsystem_mails');

        $this->daten->setDefaults($defaults);
    }

    public function loadMail($mail_id, $archiv = false)
    {
        if (!$archiv) {
            $sql = "
                SELECT
                    mailsystem_mails.mail_id,
                    mailsystem_mails.mailbox_id,
                    mailsystem_mails.folder_id,
                    mailsystem_mails.prev_mail_id,
                    mailsystem_mails.mail_note,
                    mailsystem_mails.mailmodus,
                    mailsystem_mails.betreff,
                    mailsystem_mails.sender,
                    mailsystem_mails.sender_email,
                    mailsystem_mails.empfaenger,
                    mailsystem_mails.empfaenger_email,
                    mailsystem_mails.external_email,
                    mailsystem_mails.date_mail,
                    mailsystem_mails.date_receive,
                    mailsystem_mails.status,
                    mailsystem_mails.body,
                    mailsystem_mails.anlagen,
                    mailsystem_mails.header,
                    mailsystem_mails.mail_tags,    
                    mailsystem_mails.customer_id,
                    mailsystem_mails.order_id,
                    mailsystem_mails.user_id
                FROM
                    mailsystem_mails
                WHERE
                    mailsystem_mails.mail_id = '" . (int)$mail_id . "'
            ";
        } else {
            $sql = "
                SELECT
                    mailsystem_mails_archiv.mail_id,
                    mailsystem_mails_archiv.mailbox_id,
                    mailsystem_mails_archiv.folder_id,
                    mailsystem_mails_archiv.prev_mail_id,
                    mailsystem_mails_archiv.mail_note,
                    mailsystem_mails_archiv.mailmodus,
                    mailsystem_mails_archiv.betreff,
                    mailsystem_mails_archiv.sender,
                    mailsystem_mails_archiv.sender_email,
                    mailsystem_mails_archiv.empfaenger,
                    mailsystem_mails_archiv.empfaenger_email,
                    mailsystem_mails_archiv.external_email,
                    mailsystem_mails_archiv.date_mail,
                    mailsystem_mails_archiv.date_receive,
                    mailsystem_mails_archiv.status,
                    mailsystem_mails_archiv.body,
                    mailsystem_mails_archiv.anlagen,
                    mailsystem_mails_archiv.header,    
                    mailsystem_mails_archiv.customer_id,
                    mailsystem_mails_archiv.order_id
                FROM
                    mailsystem_mails_archiv
                WHERE
                    mailsystem_mails_archiv.mail_id = '" . (int)$mail_id . "'
            ";
        }

        $this->archived = $archiv;

        $daten = db::getInstance()->singleQuery($sql);

        if (!$daten) {
            throw new SmartDataEntityNotFoundException("Mail konnte nicht geladen werden.");
        }

        $this->daten->loadDaten($daten);
    }

    public function copyAttachmentsFromMailTemplate(string $mail_template_id): void
    {
        $db = db::getInstance();

        $attachments = $db->fieldQuery("
            SELECT
                einst_mail.attachments
            FROM
                einst_mail
            WHERE
                einst_mail.mail_id = '" . $db->escape($mail_template_id) . "'
        ");

        if (!$attachments) {
            return;
        }

        $attachments = Json::decode($attachments);

        foreach ($attachments as $attachment) {
            $mail_template_repository = new MailTemplateRepository($db);
            $storage = $mail_template_repository->getAttachmentStorage();
            $content = $storage->read($attachment['path']);

            $mail_attachment_manager = service_loader::get(MailAttachmentManager::class);
            $hash = $mail_attachment_manager->saveAttachmentContent($content);

            $anlagen = $this->getAnlagen();

            foreach ($anlagen as $anlage) {
                if ($anlage['file'] == $hash) {
                    return;
                }
            }

            $anlagen[] = [
                'file' => $hash,
                'org_filename' => $attachment['name'],
                'filesize' => strlen($content)
            ];
            $this->setAnlagen($anlagen);
        }
    }

    public function save(): int
    {
        if ($this->daten->getObjStatus() === SmartDataObj::STATUS_LOADED) {
            return $this->getMailId();
        }

        $db = db::getInstance();

        $changes = $this->daten->getChanges();

        $sql = [];

        $table = $this->archived ? 'mailsystem_mails_archiv' : 'mailsystem_mails';

        foreach ($changes as $key => $value) {
            $sql[$key] = $table . '.' . $key . " = '" . $db->escape($value) . "'";
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db->query("
                    INSERT INTO
                        $table
                    SET
                        " . implode(',', $sql) . "
                ");

                $this->daten->setterDirect('mail_id', $db->insert_id());

                break;
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                    UPDATE
                        $table
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        $table.mail_id = '" . $this->getMailId() . "'
                ");
                break;
        }

        $this->triggerEvents();

        if ($this->daten->isChange('status')) {
            MailsystemRepository::protokoll_mail($this->getMailId(), $this->getStatus());
        }

        $this->daten->setSaved();

        return $this->getMailId();
    }

    /**
     * gibt zurück ob die Mail im Archiv ist
     *
     * @return bool
     */
    public function isArchived(): bool
    {
        return (bool)$this->archived;
    }

    /**
     * @param $temp
     * @return boolean
     */
    public function setTemp($temp)
    {
        return $this->daten->setter('temp', $temp);
    }

    /**
     * @param $mailbox_id
     * @return boolean
     */
    public function setMailboxId($mailbox_id)
    {
        return $this->daten->setter('mailbox_id', $mailbox_id);
    }

    /**
     * @param $folder_id
     * @return boolean
     */
    public function setFolderId($folder_id)
    {
        return $this->daten->setter('folder_id', $folder_id);
    }

    /**
     * @param $mail_note
     * @return boolean
     */
    public function setMailNote($mail_note)
    {
        return $this->daten->setter('mail_note', $mail_note);
    }

    public function addMailNote(string $mail_note): bool
    {
        $temp = $this->getMailNote();
        if ($temp) {
            $temp .= "\n\n";
        }

        $temp .= $mail_note;

        return $this->daten->setter('mail_note', $temp);
    }

    /**
     * @param $mailmodus
     * @return boolean
     */
    public function setMailmodus($mailmodus)
    {
        return $this->daten->setter('mailmodus', $mailmodus);
    }

    /**
     * @param $betreff
     * @return boolean
     */
    public function setBetreff($betreff)
    {
        return $this->daten->setter('betreff', $betreff);
    }

    /**
     * @param $sender
     * @return boolean
     */
    public function setSender($sender)
    {
        return $this->daten->setter('sender', $sender);
    }

    /**
     * @param $sender_email
     * @return boolean
     */
    public function setSenderEmail($sender_email)
    {
        return $this->daten->setter('sender_email', $sender_email);
    }

    /**
     * @param $empfaenger
     * @return boolean
     */
    public function setEmpfaenger($empfaenger)
    {
        return $this->daten->setter('empfaenger', $empfaenger);
    }

    public function setEmpfaengerMail($empfaenger_email)
    {
        return $this->daten->setter('empfaenger_email', $empfaenger_email);
    }

    /**
     * die Mail Adresse des Kommunikationspartners.
     *
     * @param string $external_email
     * @return bool
     */
    public function setExternalEmail(string $external_email): bool
    {
        return $this->daten->setter('external_email', $external_email);
    }

    public function setDate(DateObj $date): bool
    {
        return $this->daten->setter('date_mail', $date->db());
    }

    public function setDateReceive(DateObj $date): bool
    {
        return $this->daten->setter('date_receive', $date->db());
    }

    /**
     * @param $status
     * @return bool
     */
    public function setStatus($status): bool
    {
        return $this->daten->setter('status', $status);
    }

    /**
     * @param $body
     * @return boolean
     */
    public function setBody($body)
    {
        return $this->daten->setter('body', $body);
    }

    public function setCustomerMemoTemplate(string $memo_template): bool
    {
        return $this->daten->setter('memo_template', $memo_template);
    }

    /**
     * @param $anlagen
     * @return boolean
     */
    public function setAnlagen($anlagen)
    {
        if (is_array($anlagen)) {
            $anlagen = MailsystemAttachment::serialize($anlagen);
        }

        return $this->daten->setter('anlagen', $anlagen);
    }

    /**
     * @param $header
     * @return boolean
     */
    public function setHeader($header)
    {
        return $this->daten->setter('header', $header);
    }

    /**
     * @param $customer_id
     * @return boolean
     */
    public function setCustomerId($customer_id)
    {
        return $this->daten->setter('customer_id', $customer_id);
    }

    /**
     * @param $customer_id_status
     * @return boolean
     */
    public function setCustomerIdStatus($customer_id_status)
    {
        return $this->daten->setter('customer_id_status', $customer_id_status);
    }

    /**
     * @param int $user_id
     * @return bool
     */
    public function setUserId(int $user_id): bool
    {
        return $this->daten->setter('user_id', $user_id);
    }

    /**
     * @return string mail_id
     */
    public function getMailId()
    {
        return $this->daten->getter('mail_id');
    }

    /**
     * @return string temp
     */
    public function getTemp()
    {
        return $this->daten->getter('temp');
    }

    /**
     * @return string mailbox_id
     */
    public function getMailboxId()
    {
        return $this->daten->getter('mailbox_id');
    }

    /**
     * @return string folder_id
     */
    public function getFolderId()
    {
        return $this->daten->getter('folder_id');
    }

    /**
     * @return string mail_note
     */
    public function getMailNote()
    {
        return $this->daten->getter('mail_note');
    }

    /**
     * @return string mailmodus
     */
    public function getMailmodus()
    {
        return $this->daten->getter('mailmodus');
    }

    /**
     * @return string betreff
     */
    public function getBetreff()
    {
        return $this->daten->getter('betreff');
    }

    /**
     * @return string sender
     */
    public function getSender()
    {
        return $this->daten->getter('sender');
    }

    /**
     * @return string sender_email
     */
    public function getSenderEmail()
    {
        return $this->daten->getter('sender_email');
    }

    public function getReplyToEmail()
    {
        return MailUtils::extractReplayToMail($this->daten->getter('header'));
    }

    /**
     * @return string empfaenger
     */
    public function getEmpfaenger()
    {
        return $this->daten->getter('empfaenger');
    }

    /**
     * @return string
     */
    public function getEmpfaengerMail()
    {
        return $this->daten->getter('empfaenger_email');
    }

    /**
     * @return DateObj
     * @throws Exception
     */
    public function getDatum()
    {
        return $this->getDate();
    }

    public function getDate(): DateObj
    {
        return new DateObj($this->daten->getter('date_mail'));
    }

    public function getDateReceive(): DateObj
    {
        return new DateObj($this->daten->getter('date_receive'));
    }

    /**
     * @return string status
     */
    public function getStatus()
    {
        return $this->daten->getter('status');
    }

    /**
     * @return string body
     */
    public function getBody()
    {
        return $this->daten->getter('body');
    }

    public function getCustomerMemoTemplate(): string
    {
        return $this->daten->getter('memo_template');
    }

    public function getAnlagen(): array
    {
        return MailsystemAttachment::unserialize($this->daten->getter('anlagen'));
    }

    public function getAnlagenRaw()
    {
        return $this->daten->getter('anlagen');
    }


    /**
     * @return string header
     */
    public function getHeader()
    {
        return $this->daten->getter('header');
    }

    public function getCustomerId(): int
    {
        return (int)$this->daten->getter('customer_id');
    }

    /**
     * @return string customer_id_status
     */
    public function getCustomerIdStatus()
    {
        return $this->daten->getter('customer_id_status');
    }

    /**
     * @return int editor_id
     */
    public function getUserId(): int
    {
        return (int)$this->daten->getter('user_id');
    }

    public function getOrderId(): int
    {
        return (int)$this->daten->getter('order_id');
    }

    public function setOrderId($order_id)
    {
        return $this->daten->setter('order_id', $order_id);
    }

    public function setPrevMailId(int $prev_mail_id): bool
    {
        return $this->daten->setter('prev_mail_id', $prev_mail_id);
    }

    public function getPrevMailId(): int
    {
        return $this->daten->getter('prev_mail_id');
    }

    /**
     * @param string $tag
     */
    public function addMailTag($tag)
    {
        $tags = $this->getMailTags();
        $tags[] = $tag;

        $tags = array_unique($tags);

        $this->daten->setter('mail_tags', implode(',', $tags));
    }

    /**
     * @return array
     */
    public function getMailTags()
    {
        $tags = $this->daten->getter('mail_tags');
        if (!$tags) {
            return [];
        }

        return explode(',', $tags);
    }

    /**
     * Aktuell nur für Statusänderungen bei E-Mails mit Orderid vorgesehen.
     *
     * @return bool
     */
    private function triggerEvents(): bool
    {
        if (!$this->getOrderId()) {
            return false;
        }

        if (!$this->daten->isChange('status')) {
            return false;
        }

        $event_service = service_loader::getDiContainer()->get(EventDispatcher::class);

        if ($this->daten->getOldValue('status') === self::STATUS_UNREAD) {
            $event_service->dispatch(new EventMailOrderProcessed($this->getMailId(), $this->getOrderId()));
        }

        if ($this->getStatus() === self::STATUS_UNREAD) {
            $event_service->dispatch(new EventMailOrderUnprocessed($this->getMailId(), $this->getOrderId()));
        }

        return true;
    }

    /**
     * @return SmartDataObj
     */
    public function getSmartDataObj(): SmartDataObj
    {
        return $this->daten;
    }

    public function getExternalEmail(): string
    {
        return $this->daten->getter('external_email');
    }
}
