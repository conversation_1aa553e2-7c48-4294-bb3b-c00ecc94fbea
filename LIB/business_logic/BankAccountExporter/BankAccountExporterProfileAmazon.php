<?php

namespace wws\BankAccountExporter;

use bqp\Exceptions\DevException;
use bqp\Utils\StringUtils;
use wws\BankAccount\BankTransaction;

class BankAccountExporterProfileAmazon extends BankAccountExporterProfileGeneric
{
    private $skip_fees = false;

    public function setSkipFees(bool $skip_fees): void
    {
        $this->skip_fees = $skip_fees;
    }

    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        $buchungstext = $this->translateTransactionType($row['transaction_type_extern']);

        if ($row['extern_transaction_id']) {
            $buchungstext .= ' ' . $row['extern_transaction_id'];
        }
        if ($row['auftnr']) {
            $buchungstext .= ' ' . $row['auftnr'];
        }

        $record->setReceipt('');
        $record->setAccount($this->src_account);
        $record->setBookingTextIfEmpty(StringUtils::limit($buchungstext, 60));

        switch ($row['transaction_type']) {
            case BankTransaction::TRANSACTION_TYPE_FEE:
                if ($this->skip_fees && $row['transaction_type']) {
                    $record->setSkipRecord(true);
                    return $record;
                }

                throw new DevException('gebühren export nicht implementiert');
            case BankTransaction::TRANSACTION_TYPE_TRANSIT:
                $record->setOffsetAccountIfEmpty($this->transit_account);
                break;
            case BankTransaction::TRANSACTION_TYPE_DEBITOR:
                $record->setOffsetAccount($row['debtor_account']);
                $record->setReceipt($row['auftnr']);
                break;
            case BankTransaction::TRANSACTION_TYPE_MISC:
                //nix zu tun... ist schon vorher alles relevante gesetzt
                break;
            default:
                throw new BankAccountExporterProfileException('unbekannte transaktionsart');
        }

        return $record;
    }


    private function translateTransactionType($transaction_type_extern): string
    {
        switch ($transaction_type_extern) {
            case 'Payout':
                return 'Auszahlung an Bankkonto';
            case 'Subscription-Fee':
                return 'Grundgebühr';
        }

        if (strpos($transaction_type_extern, 'Commission')) {
            return 'Gebühren (' . $transaction_type_extern . ')';
        }

        return $transaction_type_extern;
    }
}
