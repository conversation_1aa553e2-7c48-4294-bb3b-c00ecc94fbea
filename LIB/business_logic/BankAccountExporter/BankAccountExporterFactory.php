<?php

namespace wws\BankAccountExporter;

use bqp\Config\ConfigRegistry;
use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\extern\Check24\Check24SettlementExport;
use bqp\extern\Concardis\Settlements\ConcardisSettlementExport;
use bqp\extern\Ebay\BankAccountExporterProfileEbay;
use bqp\extern\Galaxus\BankAccountExporterProfileGalaxus;
use bqp\extern\idealo\BankAccountExporterProfileIdealo;
use bqp\extern\Kaufland\BankAccountExporterProfileKaufland;
use DomainException;
use Psr\Container\ContainerInterface;
use wws\BankAccount\BankAccountRepository;
use wws\buchhaltung\AccountingConsts;

class BankAccountExporterFactory
{
    private ContainerInterface $container;
    private db_generic $db;
    private BankAccountRepository $bank_account_repository;

    public function __construct(
        ContainerInterface $container,
        db_generic $db,
        BankAccountRepository $bank_account_repository
    ) {
        $this->container = $container;
        $this->db = $db;

        $this->bank_account_repository = $bank_account_repository;
    }

    /**
     * Gibt eine Instanz zurück die den kompletten Export-Prozess für ein Zeitbereich kapselt.
     *
     * @param int $account_id
     * @todo derzeit direkt in export_bank_accounts.php implementiert
     */
    public function getExporterPerMonth(int $account_id): void
    {
        throw new DevException('not implemented');
    }

    /**
     * Gibt eine Instanz zurück die den kompletten Export-Prozess pro Abrechnung für ein Account kapselt.
     *
     * @param int $account_id
     * @return BankAccountExporterPerSettlement
     */
    public function getExporterPerSettlement(int $account_id): BankAccountExporterPerSettlement
    {
        $bank_account = $this->bank_account_repository->load($account_id);

        if (!$bank_account->exportPerSettlement()) {
            throw new DomainException('A settlement export is not configurated for this account.');
        }

        switch ($account_id) {
            case AccountingConsts::BANK_ACCOUNT_CHECK24:
                $exporter = $this->container->get(Check24SettlementExport::class);
                break;
            case AccountingConsts::BANK_ACCOUNT_KREDITKARTE:
            case AccountingConsts::BANK_ACCOUNT_KREDITKARTE_K11:
                $exporter = $this->container->get(ConcardisSettlementExport::class);
                break;
            default:
                throw new DomainException('A settlement export is not configurated for this account. ($account_id)');
        }

        if (method_exists($exporter, 'setBankAccount')) {
            $exporter->setBankAccount($bank_account);
        }

        return $exporter;
    }

    /**
     * Gibt eine Instanz zurück die den
     *
     * @param int $account_id
     * @return BankAccountExporter
     */
    public function getExporterForAccountId(int $account_id): BankAccountExporter
    {
        $exporter = new BankAccountExporter($this->db);
        $exporter->setAccountId($account_id);

        $config = ConfigRegistry::getInstance()->get('ecom/accounting/datev');

        $writer = new BankAccountExporterWriterDatev();
        $writer->setDatevClientNumber($config->getString('client_number'));
        $writer->setDatevConsultantNumber($config->getString('consultant_number'));
        $exporter->setWriter($writer);

        switch ($account_id) {
            case AccountingConsts::BANK_ACCOUNT_CHECK24:
                $profile = new BankAccountExporterProfileCheck24();
                $profile->setSrcAccount(136920);
                $profile->setTransitAccount(136000);
                break;
            case AccountingConsts::BANK_ACCOUNT_PAYPAL:
                $profile = new BankAccountExporterProfilePaypal();
                $profile->setSrcAccount(121000);
                $profile->setTransitAccount(136000);
                break;
            case AccountingConsts::BANK_ACCOUNT_AMAZON:
                $profile = new BankAccountExporterProfileAmazon();
                $profile->setSrcAccount(136900);
                $profile->setTransitAccount(136000);
                $profile->setSkipFees(true);
                break;
            case AccountingConsts::BANK_ACCOUNT_KREDITKARTE:
                $profile = new BankAccountExporterProfileCreditcard();
                $profile->setSrcAccount(136710);
                $profile->setTransitAccount(136000);
                break;
            case AccountingConsts::BANK_ACCOUNT_KAUFLAND:
                $profile = new BankAccountExporterProfileKaufland();
                $profile->setSrcAccount(136930);
                $profile->setTransitAccount(136000);
                break;
            case AccountingConsts::BANK_ACCOUNT_EBAY:
                $profile = new BankAccountExporterProfileEbay();
                $profile->setSrcAccount(136910);
                $profile->setTransitAccount(136000);
                break;
            case AccountingConsts::BANK_ACCOUNT_COMMERZ:
                $profile = new BankAccountExporterProfileBank();
                $profile->setSrcAccount(122000);
                $profile->setTransitAccount(136000);
                break;
            case AccountingConsts::BANK_ACCOUNT_IDEALO:
                $profile = new BankAccountExporterProfileIdealo();
                $profile->setSrcAccount(136940);
                $profile->setTransitAccount(136000);
                break;
            case AccountingConsts::BANK_ACCOUNT_UNZER:
                $profile = new BankAccountExporterProfileUnzer();
                $profile->setSrcAccount(136730);
                break;
            case AccountingConsts::BANK_ACCOUNT_GALAXUS:
                $profile = new BankAccountExporterProfileGalaxus();
                $profile->setSrcAccount(136950);
                break;
            default:
                throw new DevException('kein profile für export');
        }

        $exporter->setProfile($profile);

        return $exporter;
    }
}
