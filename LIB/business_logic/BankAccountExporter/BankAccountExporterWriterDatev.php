<?php

namespace wws\BankAccountExporter;

use bqp\Date\DateObj;
use bqp\Datev\DatevEntry;
use bqp\Datev\DatevEntryBatchWriter;
use bqp\Exceptions\DevException;
use Throwable;

class BankAccountExporterWriterDatev implements BankAccountExporterWriter, BankAccountExporterWriterDateAware
{
    /**
     * @var BankAccountExportRecord[]
     */
    private $records = [];

    /**
     * @var string
     */
    private $datev_client_number;

    /**
     * @var string
     */
    private $datev_consultant_number;

    /**
     * @var DateObj
     */
    private $date_begin;

    /**
     * @var DateObj
     */
    private $date_end;

    public function addRecord(BankAccountExportRecord $record): void
    {
        $this->records[] = $record;
    }


    public function setDateRange(DateObj $date_begin, DateObj $date_end): void
    {
        $this->date_begin = $date_begin;
        $this->date_end = $date_end;
    }

    /**
     * @param string $datev_client_number
     */
    public function setDatevClientNumber(string $datev_client_number): void
    {
        $this->datev_client_number = $datev_client_number;
    }

    /**
     * @param string $datev_consultant_number
     */
    public function setDatevConsultantNumber(string $datev_consultant_number): void
    {
        $this->datev_consultant_number = $datev_consultant_number;
    }


    public function asString(): string
    {
        $writer = new DatevEntryBatchWriter();

        $writer->setClient($this->datev_client_number);
        $writer->setConsultant($this->datev_consultant_number);
        $writer->setGeneralLedgerAccountNumberLength(6);
        $writer->setDateFrom($this->date_begin);
        $writer->setDateUntil($this->date_end);
        $writer->setFiscalYear($this->date_begin->format('Y'));

        foreach ($this->records as $record) {
            $entry = new DatevEntry();

            try {
                $entry->setAccountNumber($record->getAccount());

                if ($record->getOffsetAccount() == '-1') {
                    $entry->setContraAccountEmpty(true);
                } else {
                    $entry->setContraAccountNumber($record->getOffsetAccount());
                    $entry->setPostingKey($record->getPostingKey());
                }

                $amount = $record->getAmount();

                if ($amount > 0) {
                    $entry->setTransactionValue($amount);
                    $entry->setDebitCreditIndicator($entry::CREDIT);
                } else {
                    $entry->setTransactionValue(-$amount);
                    $entry->setDebitCreditIndicator($entry::DEBIT);
                }

                $entry->setTransactionDate($record->getTransactionDate());
                $entry->setInvoiceField1($record->getReceipt());
                $entry->setEntryText($record->getBookingText());

                if ($record->getCurrencyCode() !== 'EUR') {
                    //Wechselkurs muss im Datevexport angegeben werden... gibts in der Form aktuell nicht.
                    //Wenn das nötig wird, muss da nochmal geschaut werden. -> das war bisher nur bei Paypal der Fall und da gibts eine Währungswechselbuchung
                    throw new DevException('fremdwährungen sind nicht implementiert');
                }

                $writer->addEntry($entry);
            } catch (Throwable $exception) {
                //Eigentlich ist das hier dumm, weil der Fehler schon in den reingegeben Daten liegt. Eine CSV würde mit diesem Fehler exportiert werden.
                //->aber um das im Frontend einfacher zu handhaben zu machen, hier gezielt "behandeln"
                $envelope_exception = new BankAccountExporterWriterDatevException('Es ist ein Fehler beim Exportieren der Transaktion (' . ($record->getInternalTransactionId() ?? '-') . ') aufgetreten. ' . $exception->getMessage(), 0, $exception);
                $envelope_exception->setTransactionId($record->getInternalTransactionId());

                throw $envelope_exception;
            }
        }

        return $writer->getAsString();
    }
}