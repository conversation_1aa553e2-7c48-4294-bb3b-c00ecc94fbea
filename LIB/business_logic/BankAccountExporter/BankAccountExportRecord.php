<?php

namespace wws\BankAccountExporter;

use bqp\Date\DateObj;

/**
 * Class BankAccountBookingRecord
 * @package wws\BankAccountExporter
 */
class BankAccountExportRecord
{
    /**
     * @var DateObj
     */
    private $transaction_date;
    private $amount;
    private $currency_code;

    private $account;
    private $offset_account;
    private $posting_key;

    private $receipt;
    private $booking_text;

    private $skip_record = false;

    /**
     * unsere buchhaltung_bank_transactions.transaction_id wird mitgeschleift um ggf. <PERSON><PERSON> in späteren steps transparenter machen zu können.
     * @var int|null
     */
    private ?int $internal_transaction_id = null;

    public function getTransactionDate(): DateObj
    {
        return $this->transaction_date;
    }

    public function setTransactionDate(DateObj $transaction_date): void
    {
        $this->transaction_date = $transaction_date;
    }

    /**
     * @return float
     */
    public function getAmount(): float
    {
        return $this->amount;
    }

    /**
     * @param float $amount
     */
    public function setAmount(float $amount): void
    {
        $this->amount = $amount;
    }

    /**
     * @return string
     */
    public function getCurrencyCode(): string
    {
        return $this->currency_code;
    }

    /**
     * @param string $currency_code
     */
    public function setCurrencyCode($currency_code): void
    {
        $this->currency_code = $currency_code;
    }

    /**
     * @return mixed
     */
    public function getAccount()
    {
        return $this->account;
    }

    /**
     * @param mixed $account
     */
    public function setAccount($account): void
    {
        $this->account = $account;
    }

    /**
     * @return mixed
     */
    public function getOffsetAccount()
    {
        return $this->offset_account;
    }

    /**
     * @param mixed $offset_account
     */
    public function setOffsetAccount($offset_account): void
    {
        $this->offset_account = $offset_account;
    }

    public function setOffsetAccountIfEmpty($offset_account): void
    {
        if ($this->offset_account === null) {
            $this->offset_account = $offset_account;
        }
    }

    /**
     * @return mixed
     */
    public function getReceipt()
    {
        return $this->receipt;
    }

    /**
     * @param mixed $receipt
     */
    public function setReceipt($receipt): void
    {
        $this->receipt = $receipt;
    }

    /**
     * @return string
     */
    public function getBookingText(): string
    {
        return $this->booking_text;
    }

    /**
     * @param string $booking_text
     */
    public function setBookingText($booking_text): void
    {
        $this->booking_text = $booking_text;
    }

    public function setBookingTextIfEmpty(string $booking_text): void
    {
        if (!$this->booking_text) {
            $this->booking_text = $booking_text;
        }
    }

    public function isSkipRecord(): bool
    {
        return $this->skip_record;
    }

    public function setSkipRecord(bool $skip_record): void
    {
        $this->skip_record = $skip_record;
    }

    public function getPostingKey()
    {
        return $this->posting_key;
    }

    public function setPostingKey($posting_key): void
    {
        $this->posting_key = $posting_key;
    }


    /**
     * @return array
     */
    public function getAsArrayLegacy(): array
    {
        return [
            'datum' => $this->transaction_date,
            'betrag' => $this->amount,
            'waehrung' => $this->currency_code,
            'konto' => $this->account,
            'gegenkonto' => $this->offset_account,
            'beleg' => $this->receipt,
            'buchungstext' => $this->booking_text
        ];
    }

    public function getInternalTransactionId(): ?int
    {
        return $this->internal_transaction_id;
    }

    public function setInternalTransactionId(?int $internal_transaction_id): void
    {
        $this->internal_transaction_id = $internal_transaction_id;
    }
}
