<?php

namespace wws\BankAccountExporter;

use wws\BankAccount\BankTransaction;

class BankAccountExporterProfileCheck24 extends BankAccountExporterProfileGeneric
{


    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        if ($record->getAmount() === 0.0) {
            $record->setSkipRecord(true);
            return $record;
        }

        $record->setReceipt('');
        $record->setAccount($this->src_account);

        switch ($row['transaction_type']) {
            case BankTransaction::TRANSACTION_TYPE_FEE:
                $record->setSkipRecord(true);
                return $record;
            case BankTransaction::TRANSACTION_TYPE_TRANSIT:
                $settlement_name = str_replace('csv_partner_', '', $row['settlement_name']);

                $record->setBookingTextIfEmpty('Geldtransfer Check24 (' . $settlement_name . ')');
                $record->setOffsetAccountIfEmpty($this->getTransitAccount());
                return $record;
            case BankTransaction::TRANSACTION_TYPE_DEBITOR:
                $buchungstext = '';
                if ($row['auftnr']) {
                    $buchungstext .= ' ' . $row['auftnr'];
                }
                if ($row['extern_transaction_id']) {
                    $buchungstext .= ' ' . $row['extern_transaction_id'];
                }

                $buchungstext .= ' ' . $this->translateTransactionType($row['transaction_type_extern']);

                $buchungstext = trim($buchungstext);

                $record->setBookingTextIfEmpty($buchungstext);
                $record->setOffsetAccountIfEmpty($row['debtor_account']);
                $record->setReceipt($row['auftnr']);

                return $record;
            default:
                throw new BankAccountExporterProfileException('unbekannter transaction_type');
        }
    }


    public function translateTransactionType($transaction_type_extern): string
    {
        switch ($transaction_type_extern) {
            case 'payed':
                return '';
            case 'correction':
                return 'Korrektur';
            case 'fee':
                return 'Gebühr';
            case 'fee-payment':
                return 'ZGebühr';
            case 'transit':
                return 'Auszahlung';
            default:
                var_dump($transaction_type_extern);
                return '';
        }
    }
}
