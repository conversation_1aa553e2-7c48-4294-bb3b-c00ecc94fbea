<?php

namespace wws\BankAccountExporter;

use wws\BankAccount\BankTransaction;

class BankAccountExporterProfilePaypal extends BankAccountExporterProfileGeneric
{

    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        $record->setReceipt('');
        $record->setAccount($this->src_account);

        switch ($row['transaction_type']) {
            case BankTransaction::TRANSACTION_TYPE_FEE:
                $record->setSkipRecord(true);
                return $record;
            case BankTransaction::TRANSACTION_TYPE_TRANSIT:
                $record->setBookingTextIfEmpty('Geldtransfer');
                $record->setOffsetAccountIfEmpty($this->getTransitAccount());
                return $record;
            case BankTransaction::TRANSACTION_TYPE_DEBITOR:
                $buchungstext = $this->translateTransactionType($row['transaction_type_extern']);

                if ($row['extern_transaction_id']) {
                    $buchungstext .= ' ' . $row['extern_transaction_id'];
                }
                if ($row['auftnr']) {
                    $buchungstext .= ' ' . $row['auftnr'];
                }
                $record->setBookingTextIfEmpty($buchungstext);
                $record->setOffsetAccountIfEmpty($row['debtor_account']);
                $record->setReceipt($row['auftnr'] ?? '');
                return $record;
            case BankTransaction::TRANSACTION_TYPE_MISC:
                $buchungstext = $this->translateTransactionType($row['transaction_type_extern']);

                if ($row['extern_transaction_id']) {
                    $buchungstext .= ' ' . $row['extern_transaction_id'];
                }
                if ($row['auftnr']) {
                    $buchungstext .= ' ' . $row['auftnr'];
                }
                $record->setBookingTextIfEmpty($buchungstext);
                $record->setOffsetAccountIfEmpty($row['debtor_account']);
                return $record;
            default:
                throw new BankAccountExporterProfileException('unbekannter transaction_type');
        }
    }


    public function translateTransactionType($transaction_type_extern): string
    {
        $type = '';
        if (strpos($transaction_type_extern, '-Fee')) {
            $type = 'Gebühren';
        }

        if (!$type) {
            switch ($transaction_type_extern) {
                case 'T0000':
                    $type = 'Zahlung';
                    break;
                case 'T0004':
                    $type = 'Zahlung';
                    break;
                case 'T0006':
                    $type = 'Zahlung';
                    break;
                case 'T0007':
                    $type = 'Zahlung';
                    break;
                case 'T0200':
                    $type = 'Währungswechsel';
                    break;
                case 'T1106':
                    $type = 'Paypal Zwangs-Rückbuchung';
                    break;
                case 'T1201':
                    $type = 'Paypal Zwangs-Rückbuchung';
                    break;
                case 'T1107':
                    $type = 'Rückzahlung';
                    break;
                case 'T1110':
                    $type = 'Paypal blockiert';
                    break;
                case 'T1111':
                    $type = 'Paypal freigegeben';
                    break;
                case 'T0400':
                    $type = 'Auszahlung an Bankkonto';
                    break;
                case 'T1500':
                case 'T1501':
                    $type = 'Paypal einbehalten (allgemein)';
                    break;
                case 'T1105':
                    $type = 'Paypal freigegeben (allgemein)';
                    break;
            }
        }

        if (!$type) {
            switch (substr($transaction_type_extern, 0, 3)) {
                case 'T01':
                    $type = 'Gebühr';
                    break;
                case 'T03':
                    $type = 'Bank abbuchung';
                    break;
                case 'T06':
                    $type = 'Rückbuchung (Kreditkarte)';
                    break;
                case 'T08':
                    $type = 'Bonus';
                    break;
                case 'T09':
                    $type = 'Incentive';
                    break;
            }
        }

        if (!$type) {
            var_dump($transaction_type_extern);
        }

        return $type . ' (' . $transaction_type_extern . ')';
    }
}
