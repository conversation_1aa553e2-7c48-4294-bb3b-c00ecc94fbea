<?php

namespace wws\BankAccountExporter;

use bqp\Date\DateObj;
use bqp\Date\DateRange;
use bqp\db\db_generic;
use wws\BankAccount\bank_repository;
use wws\BankAccount\BankTransaction;

/**
 * Class BankAccountExporter
 *
 * Exportmodul für "Bank"-Transaktionen. Kapselt das Sammenln, Filtern, Kontieren und erzeugen einer Export-Datei.
 *
 * Diese Modul übernimmt keine tiefergehenden prüfungen aus, noch den Transport der Daten!
 *
 * @package wws\BankAccountExporter
 */
class BankAccountExporter
{
    /**
     * @var int
     */
    private $account_id;

    /**
     * @var DateObj
     */
    private $date_begin;
    /**
     * @var DateObj
     */
    private $date_end;

    /**
     * @var int
     */
    private $settlement_id;

    /**
     * @var null|array
     */
    private $transaction_ids = null;

    private db_generic $db;

    /**
     * @var BankAccountExporterProfile
     */
    private $profile;

    /**
     * @var BankAccountExporterWriter
     */
    private $writer;

    /**
     * @var float
     */
    private $total_amount = 0;

    public function __construct(db_generic $db)
    {
        $this->db = $db;

        $this->writer = new BankAccountExporterWriterCsv();
    }

    public function setWriter(BankAccountExporterWriter $writer): void
    {
        $this->writer = $writer;
    }

    public function setAccountId(int $account_id): void
    {
        $this->account_id = $account_id;
    }


    public function setProfile(BankAccountExporterProfile $profile): void
    {
        $this->profile = $profile;
    }


    public function setDateRange(DateObj $date_begin, DateObj $date_end): void
    {
        $this->date_begin = $date_begin;
        $this->date_end = $date_end;
    }

    public function setSettlementId(int $settlement_id): void
    {
        $this->settlement_id = $settlement_id;
    }

    public function setTransactionIds(array $transaction_ids): void
    {
        $this->transaction_ids = $transaction_ids;
    }

    /**
     * @return BankAccountExportRecord[]
     * @throws BankAccountExporterProfileException
     */
    public function collectBankAccountExportRecords(): array
    {
        $records = [];

        if ($this->settlement_id) {
            $where = " buchhaltung_bank_transactions.settlement_id = '" . (int)$this->settlement_id . "'";
        } elseif ($this->transaction_ids) {
            $where = " buchhaltung_bank_transactions.transaction_id IN (" . $this->db->in($this->transaction_ids) . ") ";
        } else {
            $where = " buchhaltung_bank_transactions.transaction_date BETWEEN '" . $this->date_begin->db('begin') . "' AND '" . $this->date_end->db('end') . "'";
        }

        $result = $this->db->query("
            SELECT
                buchhaltung_bank_transactions.transaction_id,
                buchhaltung_bank_transactions.transaction_date,
                buchhaltung_bank_transactions.extern_transaction_id,
                buchhaltung_bank_transactions.transaction_type,
                buchhaltung_bank_transactions.transaction_type_extern,
                buchhaltung_bank_transactions.amount,
                buchhaltung_bank_transactions.currency_code,    
                buchhaltung_bank_transactions.sender_name,
                buchhaltung_bank_transactions.text_reference_1,
                buchhaltung_bank_transactions.text_reference_2,
                buchhaltung_bank_transactions.booking_text,
                buchhaltung_bank_transactions.person_konto_nr,
                buchhaltung_bank_transactions.posting_key,
                buchhaltung_bank_transactions.customer_id AS customer_id_transaction,
                customers_transaction.debtor_account AS debtor_account_transaction,
                orders.customer_id,
                orders.auftnr,
                customers.debtor_account AS customer_debtor_account,
                buchhaltung_bank_settlements.settlement_id,
                buchhaltung_bank_settlements.settlement_name,
                buchhaltung_bank_settlements.settlement_reference,
                buchhaltung_bank_settlements.settlement_date
            FROM
                buchhaltung_bank_transactions LEFT JOIN
                buchhaltung_bank_settlements ON (buchhaltung_bank_transactions.settlement_id = buchhaltung_bank_settlements.settlement_id) LEFT JOIN
                orders ON (buchhaltung_bank_transactions.order_id = orders.order_id) LEFT JOIN
                customers ON (orders.customer_id = customers.customer_id) LEFT JOIN
                customers AS customers_transaction ON (buchhaltung_bank_transactions.customer_id = customers_transaction.customer_id)
            WHERE
                buchhaltung_bank_transactions.account_id = '" . (int)$this->account_id . "' AND
                buchhaltung_bank_transactions.transaction_status != '" . BankTransaction::TRANSACTION_STATUS_FILTERED . "' AND
                $where
            ORDER BY
                buchhaltung_bank_transactions.transaction_date
        ");

        foreach ($result as $daten) {
            $daten['debtor_account'] = $daten['debtor_account_transaction'] ?: $daten['customer_debtor_account'];

            $record = new BankAccountExportRecord();
            $record->setInternalTransactionId($daten['transaction_id']);

            $record->setTransactionDate(new DateObj($daten['transaction_date']));
            $record->setAmount($daten['amount']);
            $record->setCurrencyCode($daten['currency_code']);
            $record->setBookingText($daten['booking_text']); //ACHTUNG: i.d.R. ist der leer und muss vergeben werden
            $record->setPostingKey($daten['posting_key']);
            if ($daten['person_konto_nr']) {
                $record->setOffsetAccount($daten['person_konto_nr']);
            }

            $record = $this->profile->fillRecord($record, $daten);

            if ($record->isSkipRecord()) {
                continue;
            }

            $records[] = $record;
        }

        return $records;
    }

    /**
     * @return string
     * @throws BankAccountExporterProfileException
     */
    public function getAsString(): string
    {
        $records = $this->collectBankAccountExportRecords();

        if ($this->writer instanceof BankAccountExporterWriterDateAware) {
            $this->writer->setDateRange($this->date_begin, $this->date_end);
        }

        foreach ($records as $record) {
            $this->writer->addRecord($record);
        }

        return $this->writer->asString();
    }

    public function getPossibleFilename(): string
    {
        $filename = 'EXTF_';
        $filename .= bank_repository::getAccountFilenameSlug($this->account_id);

        if ($this->settlement_id) {
            $filename .= '_' . $this->settlement_id;
        } else {
            $range = new DateRange($this->date_begin, $this->date_end);

            if ($range->isFullMonth()) {
                $filename .= '_' . $this->date_begin->format('Y.m');
            } else {
                $filename .= '_' . $this->date_begin->format('Y.m.d') . '_bis_' . $this->date_end->format('Y.m.d');
            }
        }

        $filename .= '.csv';

        return $filename;
    }

    public function execute(): BankAccountExportResult
    {
        return new BankAccountExportResult($this->getPossibleFilename(), $this->getAsString());
    }
}
