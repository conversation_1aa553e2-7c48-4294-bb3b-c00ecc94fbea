<?php

namespace wws\BankAccountExporter;

use bqp\Csv\Column\CsvColumnDate;
use bqp\Csv\Column\CsvColumnDecimal;
use bqp\Csv\Column\CsvColumnEnum;
use bqp\Csv\Column\CsvColumnText;
use bqp\Csv\CsvWriter;

class BankAccountExporterWriterCsvSettlement implements BankAccountExporterWriter
{
    /**
     * @var BankAccountExportRecord[]
     */
    private $records = [];

    private $show_date = true;

    public function addRecord(BankAccountExportRecord $record): void
    {
        $this->records[] = $record;
    }

    public function setShowDate(bool $show_date): void
    {
        $this->show_date = $show_date;
    }

    public function asString(): string
    {
        $csv = new CsvWriter();
        $csv->setDelimiter(';');
        $csv->setQuotes($csv::QUOTES_AUTO);
        $csv->setEncoding('CP1252');
//        $csv->setColumnsSimple(array(
//            'datum',
//            'betrag',
//            'waehrung',
//            'konto',
//            'gegenkonto',
//            'beleg',
//            'buchtungstext'
//        ));

        if ($this->show_date) {
            $column = new CsvColumnDate('datum');
            $column->setDateFormat('Y-m-d H:i:s');
            $csv->addColumn($column);
        }

        $column = new CsvColumnDecimal('betrag');
        $column->setCsvNumberFormat(2, '.', '');
        $csv->addColumn($column);

        $column = new CsvColumnEnum('waehrung');
        $column->setEnums(['EUR', 'USD']);
        $csv->addColumn($column);

        $column = new CsvColumnText('konto');
        $column->setMinLength(2);
        $column->setMaxLength(10);
        $csv->addColumn($column);

        $column = new CsvColumnText('gegenkonto');
        $column->setMinLength(2);
        $column->setMaxLength(10);
        $csv->addColumn($column);

        $column = new CsvColumnText('beleg');
        $column->setMaxLength(20);
        $csv->addColumn($column);

        $column = new CsvColumnText('buchungstext');
        $column->setMaxLength(200);
        $csv->addColumn($column);

        foreach ($this->records as $record) {
            $row = $record->getAsArrayLegacy();

            $csv->addDaten($row);
        }

        return $csv->getAsString();
    }
}
