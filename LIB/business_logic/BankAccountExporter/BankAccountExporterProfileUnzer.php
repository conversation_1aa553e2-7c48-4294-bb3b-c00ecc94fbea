<?php

namespace wws\BankAccountExporter;

use wws\BankAccount\BankTransaction;

class BankAccountExporterProfileUnzer extends BankAccountExporterProfileGeneric
{

    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        $record->setReceipt('');
        $record->setAccount($this->src_account);

        switch ($row['transaction_type']) {
            case BankTransaction::TRANSACTION_TYPE_FEE:
            case BankTransaction::TRANSACTION_TYPE_TRANSIT:
                $record->setSkipRecord(true);
                return $record;
            case BankTransaction::TRANSACTION_TYPE_DEBITOR:
                $buchungstext = '';
                if ($row['auftnr']) {
                    $buchungstext .= ' ' . $row['auftnr'];
                }
                $buchungstext .= ' ' . $row['extern_transaction_id'];
                $buchungstext .= ' ' . $row['text_reference_2'];

                $record->setBookingTextIfEmpty(trim($buchungstext));
                $record->setOffsetAccountIfEmpty($row['debtor_account']);
                $record->setReceipt($row['auftnr']);
                return $record;
            case BankTransaction::TRANSACTION_TYPE_MISC:
                $buchungstext = 'MISC -';

                if ($row['extern_transaction_id']) {
                    $buchungstext .= ' ' . $row['extern_transaction_id'];
                }
                if ($row['auftnr']) {
                    $buchungstext .= ' ' . $row['auftnr'];
                }
                $record->setBookingTextIfEmpty($buchungstext);
                $record->setOffsetAccountIfEmpty($row['debtor_account']);
                return $record;
            default:
                throw new BankAccountExporterProfileException('unbekannter transaction_type');
        }
    }
}
