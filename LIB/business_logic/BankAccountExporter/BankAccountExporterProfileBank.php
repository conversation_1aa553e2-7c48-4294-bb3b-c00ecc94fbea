<?php

namespace wws\BankAccountExporter;

use bqp\Utils\StringUtils;
use wws\BankAccount\BankTransaction;

class BankAccountExporterProfileBank extends BankAccountExporterProfileGeneric
{

    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        $record->setAccount($this->getSrcAccount());
        $record->setReceipt('');

        switch ($row['transaction_type']) {

            case BankTransaction::TRANSACTION_TYPE_FEE:
            case BankTransaction::TRANSACTION_TYPE_MISC:
                $record->setBookingTextIfEmpty($this->getReferenceMisc($row));
                return $record;
            case BankTransaction::TRANSACTION_TYPE_TRANSIT:
                $record->setBookingTextIfEmpty($this->getReferenceMisc($row));
                $record->setOffsetAccountIfEmpty($this->getTransitAccount());
                return $record;
            case BankTransaction::TRANSACTION_TYPE_DEBITOR:
                $record->setBookingTextIfEmpty($this->getReferenceDebtor($row));
                $record->setOffsetAccountIfEmpty($row['debtor_account']);
                $record->setReceipt($row['auftnr'] ?? '');
                return $record;
            default:
                throw new BankAccountExporterProfileException('unbekannter transaction_type (' . $row['transaction_type'] . ')');
        }
    }


    private function getReferenceMisc(array $row): string
    {
        $reference = StringUtils::limit($row['sender_name'], 12) . ' ';

        if ($row['text_reference_1']) {
            $reference .= $row['text_reference_1'];
        } else {
            $reference .= $row['text_reference_2'];
        }
        return StringUtils::limit($reference, 60);
    }

    private function getReferenceDebtor(array $row): string
    {
        if ($row['text_reference_1']) {
            $reference = $row['text_reference_1'];
        } else {
            $reference = $row['text_reference_2'];
        }
        $reference .= ' ' . StringUtils::limit($row['sender_name'], 20);

        return StringUtils::limit($reference, 60);
    }
}
