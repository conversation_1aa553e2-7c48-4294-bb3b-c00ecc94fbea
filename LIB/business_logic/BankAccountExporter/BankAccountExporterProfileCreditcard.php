<?php

namespace wws\BankAccountExporter;

use bqp\Date\DateObj;

class BankAccountExporterProfileCreditcard extends BankAccountExporterProfileGeneric
{

    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        if ($record->getAmount() === 0.0) {
            $record->setSkipRecord(true);
            return $record;
        }

        if ($record->getOffsetAccount()) {
            throw new BankAccountExporterProfileException('Buchungssatz ohne Konto');
        }

        $record->setAccount($this->src_account);

        $record->setOffsetAccountIfEmpty($row['debtor_account']);
        $record->setReceipt($row['auftnr']);

        $date = new DateObj($row['transaction_date']);
        $booking_text = 'KK ';
        $booking_text .= $date->format('d.m.Y');
        if ($row['text_reference_2']) {
            $booking_text .= ' ';
            $booking_text .= $row['text_reference_2'];
        }
        $booking_text .= ' (' . $row['settlement_id'] . ')';

        $record->setBookingTextIfEmpty($booking_text);

        return $record;
    }
}
