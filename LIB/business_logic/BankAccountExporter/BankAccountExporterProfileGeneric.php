<?php

namespace wws\BankAccountExporter;

abstract class BankAccountExporterProfileGeneric implements BankAccountExporterProfile
{
    /**
     * @var int
     */
    protected $src_account;

    /**
     * @var int
     */
    protected $transit_account;

    public function setSrcAccount(int $src_account): void
    {
        $this->src_account = $src_account;
    }

    public function getSrcAccount(): int
    {
        return $this->src_account;
    }

    public function setTransitAccount(int $transit_account): void
    {
        $this->transit_account = $transit_account;
    }

    public function getTransitAccount(): int
    {
        return $this->transit_account;
    }
}
