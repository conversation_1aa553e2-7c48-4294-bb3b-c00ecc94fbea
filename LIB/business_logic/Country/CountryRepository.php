<?php

namespace wws\Country;

use db;

class CountryRepository
{
    /**
     * @thorws CountryUnknownException
     */
    public static function getCountry(int $country_id): Country
    {
        static $countries = null;

        if ($countries === null) {
            $countries = [];
            $countries[CountryConst::COUNTRY_ID_DE] = new Country([
                'country_id' => CountryConst::COUNTRY_ID_DE,
                'name' => 'Deutschland',
                'sign_iso_2' => 'DE',
                'sign_iso_3' => 'DEU',
                'sign_kfz' => 'D',
                'eu' => 1
            ]);
            $countries[CountryConst::COUNTRY_ID_AT] = new Country([
                'country_id' => CountryConst::COUNTRY_ID_AT,
                'name' => 'Österreich',
                'sign_iso_2' => 'AT',
                'sign_iso_3' => 'AUT',
                'sign_kfz' => 'A',
                'eu' => 1
            ]);
        }

        if (!isset($countries[$country_id])) {
            $countries[$country_id] = self::loadCountry($country_id);
        }

        return $countries[$country_id];
    }

    /**
     * @param int $country_id
     * @return Country
     * @thorws CountryUnknownException
     */
    private static function loadCountry(int $country_id): Country
    {
        $data = db::getInstance()->singleQuery("
            SELECT
                einst_countries.country_id,
                einst_countries.name,
                einst_countries.sign_iso_2,
                einst_countries.sign_iso_3,
                einst_countries.sign_kfz,
                einst_countries.eu
            FROM
                einst_countries
            WHERE
                einst_countries.country_id = $country_id
        ");

        if (!$data) {
            throw new CountryNotFoundException('country is unknown (' . $country_id . ')');
        }

        return new Country($data);
    }


    public static function isEu($country_id): bool
    {
        if ($country_id == CountryConst::AT) {
            return true;
        }

        $eu = db::getInstance()->fieldQuery("
                SELECT
                    einst_countries.eu
                FROM
                    einst_countries
                WHERE
                    einst_countries.country_id = '" . (int)$country_id . "'
        ");

        return $eu == 1;
    }

    public static function getCountryNameById($country_id)
    {
        if ($country_id == CountryConst::DE) {
            return 'Deutschland';
        }

        static $countries = null;

        if ($countries === null) {
            $countries = self::getCountries();
        }

        return $countries[$country_id];
    }

    public static function getCountries()
    {
        $db = db::getInstance();

        return $db->query("
            SELECT
                einst_countries.country_id,
                einst_countries.name
            FROM
                einst_countries
            ORDER BY
                einst_countries.name
        ")->asSingleArray('country_id');
    }

    public static function getCountriesShop()
    {
        $db = db::getInstance();

        return $db->query("
            SELECT
                einst_countries.country_id,
                einst_countries.name
            FROM
                einst_countries
            WHERE
                einst_countries.show_in_shop = 1
            ORDER BY
                einst_countries.name
        ")->asSingleArray('country_id');
    }

    public static function getCountrySignById($country_id): string
    {
        if ($country_id == CountryConst::DE) {
            return 'DE';
        }

        return db::getInstance()->fieldQuery("
                SELECT
                    einst_countries.sign_iso_2
                FROM
                    einst_countries
                WHERE
                    einst_countries.country_id = '" . (int)$country_id . "'
        ");
    }

    public static function isInland($country_id): bool
    {
        return $country_id == CountryConst::DE;
    }

    public static function getCountryByIsoSign2(string $country_sign): Country
    {
        static $map = null;

        $country_sign = strtoupper($country_sign);

        if ($country_sign === 'DE') {
            return self::getCountry(CountryConst::COUNTRY_ID_DE);
        }

        // UK ist kein ISO 2 Code, aber wurde uns trotzdem als GPSR-Daten geliefert
        if ($country_sign == 'UK') {
            $country_sign = 'GB';
        }

        if ($map === null) {
            $map = db::getInstance()->query("
                SELECT
                    einst_countries.country_id,
                    einst_countries.sign_iso_2
                FROM
                    einst_countries
                WHERE
                    einst_countries.sign_iso_2 != ''
            ")->asSingleArray('sign_iso_2');
        }

        if (!isset($map[$country_sign])) {
            throw new CountryNotFoundException('country sign is unknown (' . $country_sign . ')');
        }

        return self::getCountry($map[$country_sign]);
    }

    /**
     * Versucht das Land anhand des übergebenen $text zu bestimmen.
     *
     * @param string $text
     * @return Country
     * @throws CountryNotFoundException
     * @todo ggf. 3 stellige codes, englische namen, etc
     */
    public static function getCountryByText(string $text): Country
    {
        if (strlen($text) === 2) {
            return self::getCountryByIsoSign2($text);
        }

        $country_id = self::getCountryIdByGermanCountryName($text);

        if ($country_id) {
            return self::getCountry($country_id);
        }

        throw new CountryNotFoundException('country cannot be determined by text (' . $text . ')');
    }

    public static function getCountryIdByGermanCountryName(string $country_name): ?int
    {
        $country_name = strtolower($country_name);

        if ($country_name === 'deutschland' or $country_name == '') {
            return CountryConst::DE;
        }

        $db = db::getInstance();

        $country_id = (int)$db->fieldQuery("
                SELECT
                    einst_countries.country_id
                FROM
                    einst_countries
                WHERE
                    einst_countries.name LIKE '" . $db->escape($country_name) . "'
        ");

        return $country_id ?: null;
    }
}
