<?php

namespace wws\Country;

class Country
{
    /**
     * @var int
     */
    private $country_id;

    /**
     * @var string
     */
    private $country_name;

    /**
     * @var string
     */
    private $sign_iso_2;

    /**
     * @var string
     */
    private $sign_iso_3;

    /**
     * @var string
     */
    private $plate_country_code;

    /**
     * @var bool
     */
    private $is_eu;


    public function __construct(array $data)
    {
        $this->country_id = (int)$data['country_id'];
        $this->country_name = $data['name'];
        $this->sign_iso_2 = $data['sign_iso_2'];
        $this->sign_iso_3 = $data['sign_iso_3'];
        $this->plate_country_code = $data['sign_kfz'];
        $this->is_eu = (bool)$data['eu'];
    }

    public function getCountryId(): int
    {
        return $this->country_id;
    }

    public function getCountryName(): string
    {
        return $this->country_name;
    }

    /**
     * ISO 3166-2 Alpha 2
     *
     * @return string
     * @throws CountryValueNotDefinedException
     */
    public function getCountrySign2(): string
    {
        $sign = $this->sign_iso_2;

        if (!$sign) {
            throw new CountryValueNotDefinedException('ISO 3166-1 Alpha 2 is not defined for this country (' . $this->getCountryId() . ')');
        }

        return $sign;
    }

    /**
     * ISO 3166-2 Alpha 3
     *
     * @return string
     * @throws CountryValueNotDefinedException
     */
    public function getCountrySign3(): string
    {
        $sign = $this->sign_iso_3;

        if (!$sign) {
            throw new CountryValueNotDefinedException('ISO 3166-1 Alpha 3 is not defined for this country (' . $this->getCountryId() . ')');
        }

        return $sign;
    }

    /**
     * KFZ Ländercode
     *
     * @return string
     * @throws CountryValueNotDefinedException
     */
    public function getPlateCountryCode(): string
    {
        $value = $this->plate_country_code;

        if (!$value) {
            throw new CountryValueNotDefinedException('plate country code is not defined for this country (' . $this->getCountryId() . ')');
        }

        return $value;
    }

    public function isEu(): bool
    {
        return $this->is_eu;
    }
}
