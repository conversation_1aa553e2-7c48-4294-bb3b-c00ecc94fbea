<?php

namespace wws\Nummernkreis;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use config;
use db;

/**
 * erzeugt sequentiel nummern
 */
class Nummernkreis
{
    private db_generic $db;
    private bool $is_internal_transaction = false;

    protected $type;
    protected $type_id = 0;
    protected $used_type_id = 0;
    protected $params;
    protected $config = [];

    protected $loaded = false;
    protected $current_number = 0;

    protected $step = 1;
    protected $prefix = '';
    protected $suffix = '';
    protected $padding = false;
    protected $value_start = 0;

    public static function getInstance($type, $params = [])
    {
        return new Nummernkreis($type, $params);
    }

    public function __construct($type, $params = [])
    {
        $this->db = db::getInstance();

        $this->type = $type;
        $this->params = $params;
        $this->type_id = isset($params['type_id']) ? $params['type_id'] : null;
        $this->used_type_id = $this->type_id;

        if ($this->type_id) {
            $file = config::system('root_dir') . 'configs/nummernkreise/' . $type . '_' . $this->type_id . '.conf.php';
        } else {
            $file = config::system('root_dir') . 'configs/nummernkreise/' . $type . '.conf.php';
        }

        if (!file_exists($file)) {
            throw new DevException('nummernkreis nicht konfiguriert');
        }

        $_cfg = include($file);

        if (isset($_cfg['step']) && $_cfg['step']) {
            $this->step = $_cfg['step'];
        }
        if (isset($_cfg['prefix']) && $_cfg['prefix']) {
            $this->prefix = $_cfg['prefix'];
        }
        if (isset($_cfg['suffix']) && $_cfg['suffix']) {
            $this->suffix = $_cfg['suffix'];
        }
        if (isset($_cfg['padding']) && $_cfg['padding']) {
            $this->padding = $_cfg['padding'];
        }
        if (isset($_cfg['value_start']) && $_cfg['value_start']) {
            $this->value_start = $_cfg['value_start'];
        }
        if (isset($_cfg['value_source']) && $_cfg['value_source']) {
            $this->type = $_cfg['value_source'];
        }
        if (isset($_cfg['used_type_id'])) {
            $this->used_type_id = $_cfg['used_type_id'];
        }
    }

    protected function getCurrentNumber()
    {
        if (!$this->loaded) {
            $this->loaded = true;

            if ($this->db->isTransactionActive()) {
                $this->is_internal_transaction = false;
            } else {
                $this->db->begin();
                $this->is_internal_transaction = true;
            }

            $this->current_number = $this->db->fieldQuery("
                SELECT
                    einst_sonstiges.wert
                FROM
                    einst_sonstiges
                WHERE
                    einst_sonstiges.name = '" . $this->type . "' AND
                    einst_sonstiges.shop_id = '" . $this->used_type_id . "'
                FOR UPDATE
            ");

            if (!$this->current_number) {
                $this->db->query("
                    INSERT INTO
                        einst_sonstiges
                    SET
                        einst_sonstiges.wert = '" . $this->value_start . "',
                        einst_sonstiges.name = '" . $this->type . "',
                        einst_sonstiges.shop_id = '" . $this->used_type_id . "'
                ");

                $this->current_number = $this->value_start;
            }

            if ($this->current_number < $this->value_start) {
                $this->current_number = $this->value_start;
            }
        }
        $this->current_number += $this->step;

        return $this->current_number;
    }

    protected function getNextNumber()
    {
        $nr = $this->getCurrentNumber();

        if ($this->padding) {
            $nr = str_pad($nr, $this->padding, '0', STR_PAD_LEFT);
        }

        if ($this->prefix) {
            $nr = $this->prefix . $nr;
        }

        if ($this->suffix) {
            $nr .= $this->suffix;
        }

        return $nr;
    }

    protected function save()
    {
        $this->db->query("
            UPDATE
                einst_sonstiges
            SET
                einst_sonstiges.wert = $this->current_number
            WHERE
                einst_sonstiges.name = '" . $this->type . "' AND
                einst_sonstiges.shop_id = '" . $this->used_type_id . "'
        ");

        if ($this->is_internal_transaction) {
            $this->db->commit();
        }

        $this->loaded = false;
    }

    public function getNumber()
    {
        $nr = $this->getNextNumber();

        $this->save();

        return $nr;
    }

    public function getNumbers($count = 1)
    {
        $nrs = [];

        for ($i = 1; $i <= $count; $i++) {
            $nrs[] = $this->getNextNumber();
        }

        $this->save();

        return $nrs;
    }
}
