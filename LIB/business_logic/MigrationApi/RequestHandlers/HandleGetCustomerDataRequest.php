<?php

namespace wws\MigrationApi\RequestHandlers;

use wws\MigrationApi\Entities\Response;
use wws\MigrationApi\Interfaces\HandlesRequest;
use wws\shop\Account\AccountReadService;
use wws\shop\Account\Exceptions\CustomerNotFound;

class HandleGetCustomerDataRequest implements HandlesRequest
{
    /** @var AccountReadService */
    private $account_read_service;

    /** @var Response */
    private $response;

    public function handleRequest(array $request_data): void
    {
        $this->response = new Response();
        $this->response->setStatusCode(404);

        if (isset($request_data['email'])) {
            try {
                $customer_id = $this->account_read_service->getCustomerIdByEmail(
                    $request_data['email']
                );
                if (!$customer_id) {
                    throw new CustomerNotFound('by email ' . $request_data['email']);
                }

                $this->response->setResponseData([
                    'customer' => $this->account_read_service->getCustomerData(
                        $customer_id
                    )
                ]);
                $this->response->setStatusCode(200);
            } catch (CustomerNotFound $customer_not_found) {
                $this->response->setResponseData([
                    'errorMessage' => 'Customer not found ' . $customer_not_found->getMessage(),
                    'errorCode' => 1
                ]);
            }
        } else {
            $this->response->setResponseData([
                'errorMessage' => 'Customer not found',
                'errorCode' => 1
            ]);
        }
    }

    public function getResponseObject(): ?Response
    {
        return $this->response;
    }

    public function setAccountReadService(AccountReadService $account_read_service): void
    {
        $this->account_read_service = $account_read_service;
    }
}
