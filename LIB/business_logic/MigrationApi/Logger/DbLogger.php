<?php

namespace wws\MigrationApi\Logger;

use Monolog\Logger;
use mysqli;
use wws\core\logger\MonologMysqliHandler;

class DbLogger extends Logger
{
    public function __construct(mysqli $db)
    {
        $handler = new MonologMysqliHandler(
            $db,
            'wws_migration_api_log',
            ['ip', 'path', 'response_status_code'],
            Logger::DEBUG
        );
        parent::__construct('wws_migration_api_log', [$handler]);
    }
}
