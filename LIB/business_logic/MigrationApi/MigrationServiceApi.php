<?php

namespace wws\MigrationApi;

use GuzzleHttp\Psr7\Response;
use wws\MigrationApi\Exceptions\UnknownType;
use wws\MigrationApi\Interfaces\HandlesRequest;
use wws\MigrationApi\Logger\DbLogger;
use wws\MigrationApi\RequestHandlers\HandleGetCustomerDataRequest;
use wws\shop\Account\AccountReadService;

class MigrationServiceApi
{
    public const REQUEST_TYPE_GET_CUSTOMER_DATA = 'request_type_get_customer_data';

    /** @var DbLogger */
    private $logger;

    /** @var AccountReadService */
    private $account_read_service;

    /** @var string[] */
    private $response_headers;

    public function __construct(DbLogger $logger, AccountReadService $account_read_service)
    {
        $this->logger = $logger;
        $this->account_read_service = $account_read_service;
        $this->response_headers = [
            'Content-type: application/json',
            'Pragma: no-cache',
            'Expires: 0',
        ];
    }

    /**
     * @param string $type
     * @param array $request_data
     * @throws UnknownType
     */
    public function handleRequest(string $type, array $request_data): void
    {
        $request_handler = $this->getRequestHandlerByType($type);

        if ($request_handler === null) {
            throw new UnknownType($type);
        }

        $request_handler->handleRequest($request_data);

        $response = $this->buildResponse($request_handler->getResponseObject());

        $this->log(
            $_SERVER['REMOTE_ADDR'],
            "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]",
            $response->getStatusCode()
        );

        $this->outputResponse($response);
    }

    private function getRequestHandlerByType(string $type): HandlesRequest
    {
        $request_handler = null;

        switch ($type) {
            case self::REQUEST_TYPE_GET_CUSTOMER_DATA:
                $request_handler = new HandleGetCustomerDataRequest();
                $request_handler->setAccountReadService($this->account_read_service);
                break;
        }

        return $request_handler;
    }

    private function buildResponse(Entities\Response $response_object): Response
    {
        return new Response(
            $response_object->getStatusCode(),
            $this->response_headers,
            json_encode($response_object->getResponseData())
        );
    }

    private function outputResponse(Response $response): void
    {
        foreach ($response->getHeaders() as $header) {
            header($header[0]);
        }

        http_response_code($response->getStatusCode());
        echo $response->getBody();
    }

    private function log($ip, string $path, int $status_code): void
    {
        $this->logger->info(
            '',
            [
                'ip' => $ip,
                'path' => $path,
                'response_status_code' => $status_code,
            ]
        );
    }
}
