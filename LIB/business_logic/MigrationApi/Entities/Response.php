<?php

namespace wws\MigrationApi\Entities;

class Response
{
    /** @var int */
    private $status_code;

    /** @var array */
    private $response_data = [];

    public function setStatusCode(int $status_code): void
    {
        $this->status_code = $status_code;
    }

    public function setResponseData(array $response_data): void
    {
        $this->response_data = $response_data;
    }

    public function getStatusCode(): int
    {
        return $this->status_code;
    }

    public function getResponseData(): array
    {
        return $this->response_data;
    }
}
