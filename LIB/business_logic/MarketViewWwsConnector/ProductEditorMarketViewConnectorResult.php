<?php

namespace wws\MarketViewWwsConnector;

class ProductEditorMarketViewConnectorResult
{
    private array $market_view_offers = [];

    public function setOffers(array $offers): void
    {
        $this->market_view_offers = $offers;
    }

    public function addOffer(array $offers): void
    {
        $this->market_view_offers[] = $offers;
    }

    public function getOfferForSupplierId(int $supplier_id, string $preferred_gros_product_id): ?array
    {
        $matches = [];
        foreach ($this->market_view_offers as $offer) {
            if ($offer['supplier_id'] == $supplier_id) {
                $matches[] = $offer;
            }
        }

        if (count($matches) > 0) {
            foreach ($matches as $offer) {
                if ($offer['mvsrc_product_id'] == $preferred_gros_product_id) {
                    return $offer;
                }
            }

            return $matches[0];
        }

        return null;
    }

    public function hasGrosMultipleAutoMatchings(int $supplier_id): bool
    {
        $count = 0;
        $count_verified = 0;
        foreach ($this->market_view_offers as $offer) {
            if ($offer['supplier_id'] == $supplier_id) {
                $count++;
                if ($offer['matching_status'] === 'verified') {
                    $count_verified++;
                }
            }
        }

        //mehrere matchings und mindestens ein davon auto
        return $count > 1 && $count - $count_verified > 0;
    }
}
