<?php

namespace wws\MarketViewWwsConnector;

use wws\MarketView\MarketViewConst;
use wws\MarketView\MarketViewOutputHelper;
use wws\MarketView\MarketViewRepository;

/**
 * Class MarketViewAvailabilityServiceBridge
 *
 * Bildet die Trennung zwischen Market View und WWS ab und bündelt das Wissen/Bedeutung der $mvsrc_availability_ids.
 * Statt die Mvsrc Availabilities als eigene Datenstruktur im WWS abzubilden, wird direkt auf MarketView zugegriffen.
 * Solange die Datenbank auf demselben System läuft, ist das kein Problem.
 *
 * @package wws\MarketViewWwsBridge
 */
class MarketViewAvailabilityServiceConnector
{
    private MarketViewRepository $market_view_repository;
    private MarketViewOutputHelper $mv_output_helper;

    public function __construct(MarketViewOutputHelper $mv_output_helper, MarketViewRepository $market_view_repository)
    {
        $this->mv_output_helper = $mv_output_helper;
        $this->market_view_repository = $market_view_repository;
    }

    /**
     * Achtung: "$mvsrc_availability_id" === 0 wird auch mit als Unbekannt gewertet!
     *
     * @param int $mvsrc_availability_id
     * @return bool
     */
    public function isUnknown(int $mvsrc_availability_id): bool
    {
        return $mvsrc_availability_id === MarketViewConst::AVAILABILITY_ID_UNKNOWN || $mvsrc_availability_id === 0;
    }

    public function isDeliverable(int $mvsrc_availability_id): bool
    {
        return in_array($mvsrc_availability_id, $this->getDeliverableAvailabilityIds());
    }

    public function isDeliveryTime(int $mvsrc_availability_id): bool
    {
        return in_array($mvsrc_availability_id, [MarketViewConst::AVAILABILITY_ID_5_DAYS, MarketViewConst::AVAILABILITY_ID_10_DAYS, MarketViewConst::AVAILABILITY_ID_15_DAYS, MarketViewConst::AVAILABILITY_ID_MORE_THEN_15_DAYS]);
    }

    public function isComing(int $mvsrc_availability_id): bool
    {
        return $mvsrc_availability_id === MarketViewConst::AVAILABILITY_ID_ZULAUF;
    }

    public function isComingOrDeliveryTime(int $mvsrc_availability_id): bool
    {
        return $this->isComing($mvsrc_availability_id) || $this->isDeliveryTime($mvsrc_availability_id);
    }

    public function isNoStock(int $mvsrc_availability_id): bool
    {
        return $mvsrc_availability_id === MarketViewConst::AVAILABILITY_ID_KEIN_BETAND;
    }

    /**
     * @return int[]
     */
    public function getDeliverableAvailabilityIds(): array
    {
        return [MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE, MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE];
    }

    /**
     * @return int[]
     */
    public function getAvailabilityIdsOffline(): array
    {
        return [MarketViewConst::AVAILABILITY_ID_OFFLINE];
    }

    /**
     * @return int[]
     */
    public function getAvailabilityIdsNotAvailable(): array
    {
        return [MarketViewConst::AVAILABILITY_ID_KEIN_BETAND, MarketViewConst::AVAILABILITY_ID_AUF_BESTELLUNG, MarketViewConst::AVAILABILITY_ID_NICHT_RELEVANT];
    }

    public function getAvailabilityAsHtml($availability_id, $beschreibung = ''): string
    {
        return $this->mv_output_helper->getAvailabilityAsHtml($availability_id, $beschreibung);
    }

    /**
     * Berechnet den aktuellen mvsrc_availability_id anhand des Bestands. Die Methode wird für product_lager gebraucht
     * um die Bestände als Verfügbarkeit auf Lieferanten zu spiegeln.
     * ACHTUNG: die Logik ist anders als im Market View Teil -> (0 = kein Bestand, hier = offline)
     *
     * @param int $inventory
     * @return int
     * @todo dreck... gibt aktuell keine einfache Lösung um das sinnvoll abzubilden.
     */
    public function inventoryToMvsrcAvailabilityId(int $inventory): int
    {
        if ($inventory > 5) {
            return MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE;
        }
        if ($inventory > 0) {
            return MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE;
        }

        return MarketViewConst::AVAILABILITY_ID_OFFLINE;
    }

    public function getMarketViewAvailabilities(): array
    {
        return $this->market_view_repository->getAvailabilitiesAsArray();
    }

    public function getMarketViewAvailabilityNames(): array
    {
        return array_column($this->getMarketViewAvailabilities(), 'availability_name', 'availability_id');
    }
}
