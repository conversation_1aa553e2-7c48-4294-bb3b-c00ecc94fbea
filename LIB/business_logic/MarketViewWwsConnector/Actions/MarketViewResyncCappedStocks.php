<?php

namespace wws\MarketViewWwsConnector\Actions;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use wws\Lager\WarenausgangLieferschein;
use wws\MarketView\MarketViewConst;
use wws\MarketViewWwsConnector\Sync\MarketViewProductEkOfferSync;
use wws\ProductStock\ProductStockConst;
use wws\Supplier\SuppliersConst;

/**
 * Teilweise bekommen wir gedeckelt Bestände von Lieferanten. Das führt durch die synchronisation, die nur bei Änderungen ausgelöst wird, und
 * unserer Bestandsführungen zu dem Effekt, dass bei uns der Bestand 0 ist, beim Lieferanten aber 100.
 *
 * Das Script reduziert diesen Effekt, indem geschaut wird, was für Warenbewegungen es gab und stößt eine synchronisation an.
 * Warenbewegungen: Beendete Lieferscheine für den jeweiligen Lieferanten in Zeitraum x/Tag x
 *
 * Ein Buffer von 3 Tagen scheint hier sinnvoll, um überschneidungen aus dem Weg zu gehen. Bei Schnelldrehern hilft dieser Buffer nicht. Der Sync
 * beinhaltet dann auch alle erledigten Lieferscheine in diesem Buffer und eventuellen damit auch Überschneidungen mit den Beständen. Wird sich zeigen...
 */
class MarketViewResyncCappedStocks
{
    private db_generic $db;
    private db_generic $db_mv;
    private MarketViewProductEkOfferSync $market_view_product_ek_offer_sync;

    public function __construct(db_generic $db_mv, db_generic $db, MarketViewProductEkOfferSync $market_view_product_ek_offer_sync)
    {
        $this->db_mv = $db_mv;
        $this->db = $db;
        $this->market_view_product_ek_offer_sync = $market_view_product_ek_offer_sync;
    }

    public function run(): void
    {
        $date = new DateObj();
        $date->subSimple('days', 3); //3 Tage Buffer

        $this->runByDate($date, $date);
    }

    public function runByDate(DateObj $date_begin, DateObj $date_end): void
    {
        $map = $this->getMvsrcMap();

        $lager_ids = array_column($map, 'lager_id');

        $result = $this->db->query("
            SELECT
                DISTINCT
                warenausgang_lieferschein.lager_id,
                warenausgang_lieferschein_items.product_id
            FROM
                warenausgang_lieferschein INNER JOIN
                warenausgang_lieferschein_items ON (warenausgang_lieferschein.lieferschein_id = warenausgang_lieferschein_items.lieferschein_id)
            WHERE
                warenausgang_lieferschein.lager_id IN (" . $this->db->in($lager_ids) . ") AND
                warenausgang_lieferschein.status = '" . WarenausgangLieferschein::STATUS_ERLEDIGT . "' AND
                warenausgang_lieferschein.datum_erledigt BETWEEN '" . $date_begin->db('begin') . "' AND '" . $date_end->db('end') . "'
        ")->asMultiArraySingle('lager_id', 'product_id');

        foreach ($result as $lager_id => $product_ids) {
            $mvsrc_id = $this->getMvsrcIdForLagerId($map, $lager_id);

            $this->forceSync($mvsrc_id, $product_ids, 72);
        }
    }

    private function getMvsrcIdForLagerId(array $map, int $lager_id): int
    {
        foreach ($map as $row) {
            if ($row['lager_id'] == $lager_id) {
                return $row['mvsrc_id'];
            }
        }

        throw new DevException('not possible');
    }

    /**
     * @return array [<mvsrc_id,mvsrc_product_id,lager_id>]
     */
    private function getMvsrcMap(): array
    {
        $supplier_id_map = $this->db->query("
            SELECT
                supplier.supplier_id,
                supplier.lager_id
            FROM
                supplier INNER JOIN
                einst_lager ON (supplier.lager_id = einst_lager.lager_id)
            WHERE
                einst_lager.lager_typ = '" . ProductStockConst::LAGER_TYPE_SUPPLIER . "'       
        ")->asSingleArray('supplier_id');

        unset($supplier_id_map[SuppliersConst::SUPPLIER_ID_KREMPEL]); //krempl interesiert hier nicht, dafür gibts ein anderen fix, der sehr optimistisch ist

        $map = $this->db_mv->query("
            SELECT
                market_view_source.supplier_id,
                market_view_source.mvsrc_id,
                market_view_source.mvsrc_name
            FROM
                market_view_source
            WHERE
                market_view_source.supplier_id IN (" . $this->db_mv->in(array_keys($supplier_id_map)) . ") AND
                market_view_source.typ = '" . MarketViewConst::MVSRC_TYPE_SUPLLIER . "'
        ")->addMappingArray('supplier_id', $supplier_id_map, 'lager_id');

        return $map->asArray();
    }

    public function forceSync(int $mvsrc_id, array $product_ids, int $max_availability_age_in_hours = 24): void
    {
        $result = $this->db_mv->query("
            SELECT
                DISTINCT
                market_view_matching.mvsrc_id,
                market_view_matching.mvsrc_product_id
            FROM
                market_view_matching INNER JOIN
                market_view_product ON (market_view_matching.mvsrc_id = market_view_product.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_product.mvsrc_product_id) INNER JOIN
                market_view_availability ON (market_view_product.availability_id = market_view_availability.availability_id)
            WHERE
                market_view_matching.mvsrc_id = $mvsrc_id AND
                market_view_matching.product_id IN (" . $this->db_mv->in($product_ids) . ") AND
                market_view_product.date_updated_availability_id >= DATE_SUB(NOW(), INTERVAL $max_availability_age_in_hours HOUR) AND
                market_view_availability.available = 1
        ");

        foreach ($result as $row) {
            $this->market_view_product_ek_offer_sync->queueByMarketViewOffer($row['mvsrc_id'], $row['mvsrc_product_id']);
        }
    }
}
