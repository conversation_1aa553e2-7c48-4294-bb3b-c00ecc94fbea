<?php

namespace wws\MarketViewWwsConnector\Matching;

use bqp\db\db_generic;
use InvalidArgumentException;
use service_loader;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\MarketViewWwsConnector\ProductDeviceMarketViewConnector;
use wws\Product\ProductConst;
use wws\ProductDevice\ProductDeviceMapping;

class MarketViewProductDeviceMappingSync
{
    private db_generic $db;
    private db_generic $db_mv;
    private ProductDeviceMapping $product_device_mapping;
    private ProductDeviceMarketViewConnector $product_device_market_view_connector;

    private ?array $mvsrc_ids = null;

    public function __construct(db_generic $db_mv, db_generic $db, ProductDeviceMapping $product_device_mapping)
    {
        $this->db_mv = $db_mv;
        $this->db = $db;

        $this->product_device_mapping = $product_device_mapping;

        $this->product_device_market_view_connector = service_loader::get(ProductDeviceMarketViewConnector::class);
    }

    public function setMvsrcIds(array $mvsrc_ids): void
    {
        $this->mvsrc_ids = $mvsrc_ids;
    }

    public function syncAllProducts(): void
    {
        $product_ids = $this->db->query("
            SELECT
                product.product_id
            FROM
                product
            WHERE
                product.product_type = '" . ProductConst::PRODUCT_TYPE_XET . "' AND
                product.product_status = " . ProductConst::PRODUCT_STATUS_NORMAL . "
        ")->asSingleArray();

        $this->syncByProductIds($product_ids);
    }

    public function syncByProductIds(array $product_ids): void
    {
        $mapping = $this->product_device_mapping->getBulkHandler();

        foreach ($product_ids as $product_id) {
            $new_device_ids = $this->getNewMarketViewDeviceIdsForProductId($product_id);

            if ($new_device_ids) {
                $mapping->addMappings($product_id, $new_device_ids);
            }
        }

        $mapping->flush();
    }

    public function syncByDeviceIds(array $device_ids): void
    {
        $where = '';
        if ($this->mvsrc_ids) {
            $where .= " AND market_view_product_device.mvsrc_id IN (" . $this->db_mv->in($this->mvsrc_ids) . ")";
        }

        $product_ids = $this->db_mv->query("
            SELECT
                DISTINCT
                market_view_matching.product_id
            FROM
                market_view_device INNER JOIN
                market_view_product_device ON (market_view_device.mvsrc_id = market_view_product_device.mvsrc_id AND market_view_device.mvsrc_device_id = market_view_product_device.mvsrc_device_id) INNER JOIN
                market_view_matching ON (market_view_product_device.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product_device.mvsrc_product_id = market_view_matching.mvsrc_product_id)
            WHERE
                market_view_device.device_id IN (" . $this->db_mv->in($device_ids) . ") AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "'
                $where
        ")->asSingleArray();

        $this->syncByProductIds($product_ids);
    }

    public function getNewMarketViewDeviceIdsForProductId(int $product_id): array
    {
        $device_ids = $this->db->query("
            SELECT
                product_device_mapping.device_id
            FROM
                product_device_mapping
            WHERE
                product_device_mapping.product_id = $product_id
        ")->asSingleArray();

        $market_view_device_ids = $this->product_device_market_view_connector->getDeviceIds($product_id, $this->mvsrc_ids);

        return array_diff($market_view_device_ids, $device_ids);
    }

    /**
     * ändert das MarketView zu WWS Geräte Mapping für $merge_device_id auf $device_id
     *
     * @param int $device_id
     * @param int $merge_device_id
     * @todo deplaziert, ist matching und nicht sync... (aber auch schon die klasse selbst: sync in matching)
     *
     */
    public function mergeDeviceId(int $device_id, int $merge_device_id): void
    {
        if (!$merge_device_id) {
            throw new InvalidArgumentException('$merge_device_id musst be setted');
        }

        $this->db_mv->query("
            UPDATE
                market_view_device
            SET
                market_view_device.device_id = $device_id
            WHERE
                market_view_device.device_id = $merge_device_id
        ");
    }
}
