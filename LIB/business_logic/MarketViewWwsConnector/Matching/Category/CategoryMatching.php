<?php

namespace wws\MarketViewWwsConnector\Matching\Category;

use bqp\db\db_generic;

class CategoryMatching
{
    private db_generic $db;
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv, db_generic $db)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;
    }

    /**
     * gibt eine Auswahl möglich passender Systemkategorien zurück
     * @param int $mvsrc_id
     * @param int $mv_cat_id
     * @return array cat_id, cat_name, cat_path_text, anzahl
     */
    public function getPossibleCats(int $mvsrc_id, int $mv_cat_id, ?int $cat_tree_id = null): array
    {
        $product_ids = $this->db_mv->query("
            SELECT
                market_view_product.product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = " . $mvsrc_id . " AND
                (
                    market_view_product.mv_cat_id = " . $mv_cat_id . " OR
                    market_view_product.mv_cat_id_2 = " . $mv_cat_id . "
                ) AND
                market_view_product.product_id
            LIMIT
                10000
        ")->asSingleArray();

        $where = '';
        if ($cat_tree_id) {
            $where .= ' AND product_cat.cat_tree_id = ' . $cat_tree_id;
        }

        return $this->db->query("
            SELECT
                product_cat.cat_id,
                product_cat.cat_name,
                product_cat.cat_path_text,
                COUNT(*) AS anzahl
            FROM
                product INNER JOIN
                product_cat ON (product.cat_id = product_cat.cat_id)
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ") AND
                product_cat.unsorted = 0
                $where
            GROUP BY
                product_cat.cat_id,
                product_cat.cat_name
            ORDER BY
                anzahl DESC
        ")->asArray();
    }
}
