<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

class MarketViewRemoveMatching
{
    private int $product_id;
    private ?int $mvsrc_id;
    private bool $disable_bezug = false;
    private string $reason = '';

    /**
     * @var int[]
     */
    private array $matching_ids = [];

    public function __construct(int $product_id, ?int $mvsrc_id = null)
    {
        $this->product_id = $product_id;
        $this->mvsrc_id = $mvsrc_id;
    }

    public function isDisableBezug(): bool
    {
        return $this->disable_bezug;
    }

    public function setDisableBezug(bool $disable_bezug): void
    {
        $this->disable_bezug = $disable_bezug;
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function setReason(string $reason): void
    {
        $this->reason = $reason;
    }

    public function getProductId(): int
    {
        return $this->product_id;
    }

    public function setProductId(int $product_id): void
    {
        $this->product_id = $product_id;
    }

    public function getMvsrcId(): ?int
    {
        return $this->mvsrc_id;
    }

    public function setMvsrcId(int $mvsrc_id): void
    {
        $this->mvsrc_id = $mvsrc_id;
    }

    public function hasMatchingIds(): bool
    {
        return (bool)$this->matching_ids;
    }

    public function getMatchingIds(): array
    {
        return $this->matching_ids;
    }

    public function setMatchingIds(array $matching_ids): void
    {
        $this->matching_ids = $matching_ids;
    }

    public function setMatchingId(int $matching_id): void
    {
        $this->matching_ids = [$matching_id];
    }
}
