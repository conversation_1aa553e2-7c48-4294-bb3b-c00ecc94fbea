<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

use bqp\db\db_generic;

class ProductMatchingEanChange
{
    private db_generic $db_mv;
    private ProductMatching $product_matching;

    public function __construct(db_generic $db_mv, ProductMatching $product_matching)
    {
        $this->product_matching = $product_matching;
        $this->db_mv = $db_mv;
    }

    public function removeMatchingForConfiguredSources(): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_source.mvsrc_id
            FROM
                market_view_source
            WHERE
                market_view_source.remove_matching_on_ean_change = 1
        ");

        foreach ($result as $data) {
            $this->removeMatchingByMvsrcId($data['mvsrc_id']);
        }
    }

    public function removeMatchingForSourceIfEnabled(int $mvsrc_id): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_source.mvsrc_id
            FROM
                market_view_source
            WHERE
                market_view_source.mvsrc_id = $mvsrc_id AND
                market_view_source.remove_matching_on_ean_change = 1
        ");

        foreach ($result as $data) {
            $this->removeMatchingByMvsrcId($data['mvsrc_id']);
        }
    }

    public function removeMatchingByMvsrcId(int $mvsrc_id): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_local_product_cache.product_id,
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id,
                
                market_view_matching.matching_id
            FROM
                market_view_product INNER JOIN
                market_view_matching ON (
                        market_view_product.mvsrc_id = market_view_matching.mvsrc_id AND
                        market_view_product.mvsrc_product_id = market_view_matching.mvsrc_product_id
                ) INNER JOIN
                market_view_local_product_cache ON (market_view_matching.product_id = market_view_local_product_cache.product_id)
            WHERE
                market_view_product.mvsrc_id = " . $mvsrc_id . " AND
                market_view_matching.matching_status = 'auto' AND
                market_view_product.ean != market_view_local_product_cache.ean
            ORDER BY
                market_view_local_product_cache.product_id
        ");

        foreach ($result as $row) {
            $reason = new MarketViewRemoveMatching($row['product_id'], $row['mvsrc_id']);
            $reason->setMatchingId($row['matching_id']);
            $reason->setDisableBezug(true);
            $reason->setReason("MarketView EAN geändert - Bezug für Lieferanten deaktiviert");

            $this->product_matching->removeProductMatching($reason);
        }
    }
}
