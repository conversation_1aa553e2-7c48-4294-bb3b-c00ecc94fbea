<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

use bqp\db\db_generic;
use debug;
use InvalidArgumentException;
use wws\MarketView\MarketViewConst;
use wws\Product\ProductConst;

class ProductMatchingConflicts
{
    private db_generic $db;
    private db_generic $db_mv;
    private ProductMatching $product_matching;

    public function __construct(db_generic $db_mv, db_generic $db, ProductMatching $product_matching)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;
        $this->product_matching = $product_matching;
    }


    /**
     * sucht nach Matchings die mehrere Produkte treffen, wenn davon nur eins aktiv ist (nicht gelöscht)
     * werden die anderen Matchings aufgelöst.
     */
    public function resolveDoubleMatchingsWithDeletedProducts()
    {
        /*
        $result = $this->db_mv->query("
            SELECT
                market_view_matching.mvsrc_id,
                market_view_matching.mvsrc_product_id,
                COUNT(*) AS matching_count,
                GROUP_CONCAT(market_view_matching.product_id ORDER BY market_view_matching.product_id SEPARATOR ';')
            FROM
                market_view_matching
            GROUP BY
                market_view_matching.mvsrc_product_id,
                market_view_matching.mvsrc_id
            HAVING
                matching_count > 1
        ")->display();
        */

        $result = $this->db->query("
            SELECT
                product.ean,
                GROUP_CONCAT(CONCAT(product.product_id,'|',product.product_status) SEPARATOR ';') AS products,
                COUNT(*) AS ean_count,
                SUM(IF(product.product_status = " . ProductConst::PRODUCT_STATUS_DEL . ", 1, 0)) AS delete_count
            FROM
                product
            WHERE
                product.ean != ''
            GROUP BY
                product.ean
            HAVING
                ean_count > 1 AND
                ean_count-1 = delete_count
        ");

        foreach ($result as $daten) {
            $temp = explode(';', $daten['products']);

            foreach ($temp as $product) {
                list($product_id, $product_status) = explode('|', $product);

                if ($product_status != ProductConst::PRODUCT_STATUS_DEL) {
                    continue;
                }

                $matching_ids = $this->db_mv->query("
                    SELECT
                        market_view_matching.matching_id
                    FROM
                        market_view_matching
                    WHERE
                        market_view_matching.product_id = '$product_id' AND
                        market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "'
                ")->asSingleArray();

                foreach ($matching_ids as $matching_id) {
                    $this->product_matching->updateMatchingStatus($matching_id, ProductMatching::MATCHING_WRONG);
                }
            }
        }
    }


    /**
     * Sollten Produkte doppelt angelegt sein, können mit dieser Methode die Matchings zwischen diesen Produkten
     * angeglichen werden. Das ist besonders bei Ersatzteilen relevant.
     */
    public function mergeMatchingsForDuplicateProductIds(array $product_ids): void
    {
        if (count($product_ids) < 2) {
            debug::dump($product_ids);
            throw new InvalidArgumentException('must called with more the one product_id');
        }

        //alle mappings (?) auf alle produt_ids kopieren
        $matchings = $this->db_mv->query("
            SELECT
                market_view_matching.mvsrc_id,
                market_view_matching.mvsrc_product_id,
                COUNT(*) AS act_matchings
            fROM
                market_view_matching INNER JOIN
                market_view_source ON (market_view_matching.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_matching.product_id IN (" . $this->db_mv->in($product_ids) . ") AND
                market_view_source.typ != '" . MarketViewConst::MVSRC_TYPE_SELF . "'
            GROUP BY
                market_view_matching.mvsrc_id,
                market_view_matching.mvsrc_product_id
        ")->asArray();

        foreach ($product_ids as $product_id) {
            foreach ($matchings as $matching) {
                if ($matching['act_matchings'] == count($product_ids)) { //matching bereits bei allen produkten hinterlegt
                    continue;
                }

                $this->product_matching->addMatching($matching['mvsrc_id'], $matching['mvsrc_product_id'], $product_id);
            }
        }
    }
}
