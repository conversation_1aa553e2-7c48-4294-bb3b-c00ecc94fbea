<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

class ProductMatchingCollection implements ProductMatchingModule
{
    /**
     * @var ProductMatchingModule[]
     */
    private array $matching_modules = [];

    public function addMatchingModule(ProductMatchingModule $module): void
    {
        $this->matching_modules[] = $module;
    }


    public function runForMvsrcId(int $mvsrc_id): void
    {
        foreach ($this->matching_modules as $matching_module) {
            $matching_module->runForMvsrcId($mvsrc_id);
        }
    }
}
