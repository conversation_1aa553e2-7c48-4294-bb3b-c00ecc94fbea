<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use wws\MarketViewWwsConnector\Sync\MarketViewProductEkOfferSync;

class ProductMatching
{
    public const MATCHING_WRONG = 'wrong';
    public const MATCHING_AUTO = 'auto';
    public const MATCHING_VERIFIED = 'verified';

    private db_generic $db;
    private db_generic $db_mv;
    private MarketViewProductEkOfferSync $market_view_product_ek_offer_sync;

    public function __construct(db_generic $db_mv, db_generic $db, MarketViewProductEkOfferSync $market_view_product_ek_offer_sync)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;

        $this->market_view_product_ek_offer_sync = $market_view_product_ek_offer_sync;
    }

    public function getMatchingStatusNames(): array
    {
        return [
            self::MATCHING_AUTO => 'automatisch',
            self::MATCHING_WRONG => 'falsch',
            self::MATCHING_VERIFIED => 'geprüft'
        ];
    }

    /**
     * @param int $mvsrc_id
     * @param string $mvsrc_product_id
     * @param int $product_id
     * @return void
     * @thorws ProductMatchingException
     */
    public function addMatching(int $mvsrc_id, string $mvsrc_product_id, int $product_id, string $matching_status = self::MATCHING_AUTO): void
    {
        $matching_status_current = $this->db_mv->fieldQuery("
            SELECT
                market_view_matching.matching_status
            FROM
                market_view_matching
            WHERE
                market_view_matching.product_id = '" . $product_id . "' AND
                market_view_matching.mvsrc_id = '" . $mvsrc_id . "' AND
                market_view_matching.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
        ");

        if ($matching_status_current === self::MATCHING_AUTO || $matching_status_current === self::MATCHING_VERIFIED) {
            throw new ProductMatchingKnownException('Matching bereits bekannt. (' . $mvsrc_id . ', ' . $mvsrc_product_id . ', ' . $product_id . ')');
        }

        if ($matching_status_current === self::MATCHING_WRONG) {
            throw new ProductMatchingKnownWrongException('Matching bereits bekannt und als falsch markiert. (' . $mvsrc_id . ', ' . $mvsrc_product_id . ', ' . $product_id . ')');
        }

        if ($matching_status_current === null) {
            $this->db_mv->query("
                INSERT INTO
                    market_view_matching
                SET
                    market_view_matching.product_id = '" . $product_id . "',
                    market_view_matching.mvsrc_id = '" . $mvsrc_id . "',
                    market_view_matching.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "',
                    market_view_matching.matching_status = '" . $this->db_mv->escape($matching_status) . "'
            ");

            $this->refreshMarketViewProductProductId($mvsrc_id, $mvsrc_product_id);
        } else {
            throw new DevException('unbekannter matching status');
        }

        $this->market_view_product_ek_offer_sync->queueByMarketViewOffer($mvsrc_id, $mvsrc_product_id);
    }


    /**
     * @param int $matching_id
     * @param string $matching_status
     */
    public function updateMatchingStatus(int $matching_id, string $matching_status): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_matching
            SET
                market_view_matching.matching_status = '" . $this->db_mv->escape($matching_status) . "'
            WHERE
                market_view_matching.matching_id = '" . $matching_id . "'
        ");

        if ($this->db_mv->affected_rows() === 0) {
            return;
        }

        $mvsrc_product_id_tuples = $this->getMvsrcProductIdTuples([$matching_id]);

        if ($matching_status === self::MATCHING_WRONG) {
            //event -> market_view.matching.wrong {matching_id: $matching_id}#
            $this->market_view_product_ek_offer_sync->clearLocalProductMatchingByMatchingId($matching_id, new MarketViewRemoveMatching(0));
        }

        foreach ($mvsrc_product_id_tuples as $mvsrc_product_id_tuple) {
            $this->refreshMarketViewProductProductId($mvsrc_product_id_tuple['mvsrc_id'], $mvsrc_product_id_tuple['mvsrc_product_id']);
        }
    }


    public function removeProductMatching(MarketViewRemoveMatching $remove_action): void
    {
        $where = "";
        if ($remove_action->getMvsrcId()) {
            $where = " AND market_view_matching.mvsrc_id = " . $remove_action->getMvsrcId();
        }

        if ($remove_action->hasMatchingIds()) {
            $matching_ids = $remove_action->getMatchingIds();
        } else {
            $matching_ids = $this->db_mv->query("
                SELECT
                    market_view_matching.matching_id
                FROM
                    market_view_matching
                WHERE
                    market_view_matching.product_id = " . $remove_action->getProductId() . "
                    $where
            ")->asSingleArray();
        }

        foreach ($matching_ids as $matching_id) {
            $this->market_view_product_ek_offer_sync->clearLocalProductMatchingByMatchingId($matching_id, $remove_action);
        }

        $mvsrc_product_id_tuples = $this->getMvsrcProductIdTuples($matching_ids);

        //matchings löschen
        $this->db_mv->query("
            DELETE FROM
                market_view_matching
            WHERE
                market_view_matching.matching_id IN (" . $this->db_mv->in($matching_ids) . ")
        ");

        foreach ($mvsrc_product_id_tuples as $mvsrc_product_id_tuple) {
            $this->refreshMarketViewProductProductId($mvsrc_product_id_tuple['mvsrc_id'], $mvsrc_product_id_tuple['mvsrc_product_id']);
        }
    }

    /**
     * hält market_view_product.product_id in sync mit market_view_matching
     * @param int $mvsrc_id
     * @param string $mvsrc_product_id
     * @return void
     */
    private function refreshMarketViewProductProductId(int $mvsrc_id, string $mvsrc_product_id): void
    {
        $current_product_id = (int)$this->db_mv->fieldQuery("
            SELECT
                market_view_product.product_id
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = '" . $mvsrc_id . "' AND
                market_view_product.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
        ");

        $product_ids = $this->db_mv->query("
            SELECT
                market_view_matching.product_id
            FROM
                market_view_matching INNER JOIN
                market_view_product ON (market_view_product.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product.mvsrc_product_id = market_view_matching.mvsrc_product_id)
            WHERE
                market_view_matching.mvsrc_id = " . $mvsrc_id . " AND
                market_view_matching.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "' AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "'
        ")->asSingleArray();

        if (in_array($current_product_id, $product_ids)) {
            return;
        }

        $new_product_id = 0;
        if ($product_ids) {
            $new_product_id = (int)$product_ids[0];
        }

        $this->db_mv->query("
            UPDATE
                market_view_product
            SET
                market_view_product.product_id = '" . $new_product_id . "'
            WHERE
                market_view_product.mvsrc_id = '" . $mvsrc_id . "' AND
                market_view_product.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
        ");
    }

    private function getMvsrcProductIdTuples(array $matching_ids): array
    {
        return $this->db_mv->query("
            SELECT
                market_view_matching.mvsrc_id,
                market_view_matching.mvsrc_product_id
            FROM
                market_view_matching
            WHERE
                market_view_matching.matching_id IN (" . $this->db_mv->in($matching_ids) . ")
        ")->asArray();
    }
}
