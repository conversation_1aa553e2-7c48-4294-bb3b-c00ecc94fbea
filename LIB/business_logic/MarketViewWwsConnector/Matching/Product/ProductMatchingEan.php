<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

use bqp\db\db_generic;

class ProductMatchingEan implements ProductMatchingModule
{
    private ProductMatching $product_matching;
    private db_generic $db_mv;
    private db_generic $db;

    public function __construct(ProductMatching $product_matching, db_generic $db_mv, db_generic $db)
    {
        $this->product_matching = $product_matching;
        $this->db_mv = $db_mv;
        $this->db = $db;
    }

    /**
     * @param int|null $mvsrc_id
     * @return array
     */
    public function collectNewMatchings(?int $mvsrc_id = null): array
    {
        $mvsrc_sql = '';

        if ($mvsrc_id) {
            $mvsrc_sql = " AND market_view_product.mvsrc_id = '$mvsrc_id' ";
        }

        $result = $this->db_mv->query("
            SELECT
                market_view_local_product_cache.product_id,

                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id
            FROM
                market_view_product INNER JOIN
                market_view_local_product_cache ON (market_view_product.ean = market_view_local_product_cache.ean) LEFT JOIN
                market_view_matching ON (
                    market_view_product.mvsrc_id = market_view_matching.mvsrc_id AND
                    market_view_product.mvsrc_product_id = market_view_matching.mvsrc_product_id AND
                    market_view_matching.product_id = market_view_local_product_cache.product_id
                )
            WHERE
                market_view_product.ean != '' AND
                market_view_local_product_cache.ean != '' AND /* support for optimizer */
                market_view_matching.mvsrc_id IS NULL
                $mvsrc_sql
        ");

        $matchings = [];

        foreach ($result as $row) {
            //@todo... trennung sinnvoll oder nicht sinnvoll?! Hier können schon ordentliche Datenmengen zustande kommen. lassen, funktional, generator oder trennung raus und direkt addMatching()?!
            //generator wäre hier das sinnvollste...
            $matchings[] = ['mvsrc_id' => $row['mvsrc_id'], 'mvsrc_product_id' => $row['mvsrc_product_id'], 'product_id' => $row['product_id']];
        }

        return $matchings;
    }

    private function saveMatchings(array $matchings): void
    {
        foreach ($matchings as $matching) {
            $this->product_matching->addMatching($matching['mvsrc_id'], $matching['mvsrc_product_id'], $matching['product_id']);
        }
    }

    public function runForMvsrcId(int $mvsrc_id): void
    {
        $matchings = $this->collectNewMatchings($mvsrc_id);

        $this->saveMatchings($matchings);
    }

    public function run(): void
    {
        $matchings = $this->collectNewMatchings();

        $this->saveMatchings($matchings);
    }


    public function collectNewMatchingsForEan(string $ean): array
    {
        $ean = trim($ean);
        if (!$ean) {
            return [];
        }

        $product_ids = $this->db->query("
            SELECT
                product.product_id
            FROM
                product
            WHERE
                product.ean LIKE '" . $this->db->escape($ean) . "'    
        ")->asSingleArray();

        $mvsrc_products = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id
            FROM
                market_view_product
            WHERE
                market_view_product.ean = '" . $this->db->escape($ean) . "'
        ")->asArray();

        //matching tuples erstellen
        $matching_tuples = [];

        foreach ($product_ids as $product_id) {
            foreach ($mvsrc_products as $mvsrc_product) {
                $matching_tuples[] = [
                    'product_id' => $product_id,
                    'mvsrc_id' => $mvsrc_product['mvsrc_id'],
                    'mvsrc_product_id' => $mvsrc_product['mvsrc_product_id']
                ];
            }
        }

        //bekannte matching tupels auslesen
        $known_matching_tuples = $this->db_mv->query("
            SELECT
                market_view_matching.product_id,
                market_view_matching.mvsrc_id,
                market_view_matching.mvsrc_product_id
            FROM
                market_view_matching
            WHERE
                (market_view_matching.product_id, market_view_matching.mvsrc_id, market_view_matching.mvsrc_product_id) IN (" . $this->db_mv->inTuple($matching_tuples) . ")
        ");

        //bekannte matching tuples entfernen
        foreach ($known_matching_tuples as $known_match_tuple) {
            foreach ($matching_tuples as $key => $matching_tuple) {
                if (
                    $matching_tuple['product_id'] == $known_match_tuple['product_id'] &&
                    $matching_tuple['mvsrc_id'] == $known_match_tuple['mvsrc_id'] &&
                    $matching_tuple['mvsrc_product_id'] == $known_match_tuple['mvsrc_product_id']
                ) {
                    unset($matching_tuples[$key]);
                    break;
                }
            }
        }

        return $matching_tuples;
    }


    public function runForEan(string $ean): void
    {
        $matchings = $this->collectNewMatchingsForEan($ean);

        $this->saveMatchings($matchings);
    }
}
