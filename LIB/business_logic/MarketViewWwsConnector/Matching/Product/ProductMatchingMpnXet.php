<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

use bqp\db\db_generic;
use wws\Product\ProductBrand\ProductBrandRepository;

/**
 * Matching-Modul für Ersatzteile über die MPN
 * Es wird innerhalb der "Brand-Gruppen" gematcht und die MPNs werden für das matching "normalisiert".
 * Z.B.: DA6201378B == DA62-01378B
 */
class ProductMatchingMpnXet implements ProductMatchingModule
{
    private ProductMatching $product_matching;
    private db_generic $db_mv;
    private db_generic $db;

    public function __construct(ProductMatching $product_matching, db_generic $db_mv, db_generic $db)
    {
        $this->product_matching = $product_matching;
        $this->db_mv = $db_mv;
        $this->db = $db;
    }

    /**
     * @param int $mvsrc_id
     * @return array<int, array{mv_hersteller_ids: int[], quantity:int, brand_ids: int[]}>
     */
    public function getUnmatchedOffersGroupedByBrandGroup(int $mvsrc_id): array
    {
        $brand_mapping = ProductBrandRepository::getGroupBrandsWithSubBrandIds();

        $result = $this->db_mv->query("
            SELECT
                market_view_product.mv_hersteller_id,
                market_view_hersteller.brand_id,
                COUNT(*) AS quantity
            FROM
                market_view_product INNER JOIN
                market_view_hersteller ON (market_view_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id)
            WHERE
                market_view_product.mvsrc_id = $mvsrc_id AND
                market_view_hersteller.brand_id != 0 AND
                market_view_product.product_id = 0 AND
                market_view_product.mpn != ''
            GROUP BY
                market_view_product.mv_hersteller_id
        ");

        $result->addCallback(function (array $row) use ($brand_mapping) {
            $row['main_brand_id'] = $row['brand_id'];
            $row['brand_ids'] = [$row['brand_id']];

            foreach ($brand_mapping as $main_brand_id => $brand_ids) {
                if (in_array($row['brand_id'], $brand_ids)) {
                    $row['main_brand_id'] = $main_brand_id;
                    $row['brand_ids'] = $brand_ids;
                    break;
                }
            }

            return $row;
        });

        $grouped_result = [];

        foreach ($result as $row) {
            if (!isset($grouped_result[$row['main_brand_id']])) {
                $grouped_result[$row['main_brand_id']] = [
                    'mv_hersteller_ids' => [],
                    'quantity' => 0,
                    'brand_ids' => $row['brand_ids']
                ];
            }

            $grouped_result[$row['main_brand_id']]['mv_hersteller_ids'][] = $row['mv_hersteller_id'];
            $grouped_result[$row['main_brand_id']]['quantity'] += $row['quantity'];
        }

        return $grouped_result;
    }


    public function collectNewMatchings(int $mvsrc_id): array
    {
        $matchings = [];

        $brand_groups = $this->getUnmatchedOffersGroupedByBrandGroup($mvsrc_id);

        foreach ($brand_groups as $brand_group) {
            $mv_offers = $this->db_mv->query("
                SELECT
                    market_view_product.mvsrc_product_id,
                    market_view_product.mpn
                FROM
                    market_view_product
                WHERE
                    market_view_product.mvsrc_id = $mvsrc_id AND
                    market_view_product.mv_hersteller_id IN (" . $this->db_mv->in($brand_group['mv_hersteller_ids']) . ") AND
                    market_view_product.mpn != '' AND
                    market_view_product.product_id = 0
            ")->addCallback(function (array $row) {
                $row['mpn'] = $this->normalizeMpn($row['mpn']);
                return $row;
            })->asSingleArray('mvsrc_product_id');

            //eventuell muss der block gebatched werden... beim bsh sortiment sind halt 200.000 angebote, die wir hier laden.
            $mpn_product_id_map = $this->db->query("
                SELECT
                    product.mpn,
                    product.product_id
                FROM
                    product
                WHERE
                    product.brand_id IN (" . $this->db->in($brand_group['brand_ids']) . ") AND
                    product.mpn != ''
            ")->addCallback(function (array $row) {
                $row['mpn'] = $this->normalizeMpn($row['mpn']);
                return $row;
            })->asSingleArray('mpn');
            //

            foreach ($mv_offers as $mvsrc_product_id => $mpn) {
                if (!$mpn) {
                    continue;
                }

                $product_id = null;

                if (isset($mpn_product_id_map[$mpn])) {
                    $product_id = $mpn_product_id_map[$mpn];

                    $matchings[] = ['mvsrc_id' => $mvsrc_id, 'mvsrc_product_id' => $mvsrc_product_id, 'product_id' => $product_id];
                }
            }
        }

        return $matchings;
    }

    public function runForMvsrcId(int $mvsrc_id): void
    {
        $matchings = $this->collectNewMatchings($mvsrc_id);

        foreach ($matchings as $matching) {
            try {
                $this->product_matching->addMatching($matching['mvsrc_id'], $matching['mvsrc_product_id'], $matching['product_id']);
            } catch (ProductMatchingKnownWrongException) {
                //kann ignoriert werden...
            }
        }
    }

    private function normalizeMpn(string $mpn): string
    {
        $mpn = strtolower($mpn);
        $mpn = preg_replace('/[^a-z0-9]/', '', $mpn);

        return $mpn;
    }
}
