<?php

namespace wws\MarketViewWwsConnector\Matching\Product;

use bqp\db\db_generic;
use wws\Product\ProductConst;

class ProductMatchingMpn implements ProductMatchingModule
{
    private ProductMatching $product_matching;
    private db_generic $db_mv;
    private db_generic $db;

    public function __construct(ProductMatching $product_matching, db_generic $db_mv, db_generic $db)
    {
        $this->product_matching = $product_matching;
        $this->db_mv = $db_mv;
        $this->db = $db;
    }


    public function collectNewMatchings(int $mvsrc_id): array
    {
        $matchings = [];

        $brands_map = $this->db_mv->query("
            SELECT
                market_view_product.mv_hersteller_id,
                market_view_hersteller.hersteller_name,
                market_view_hersteller.brand_id,
                COUNT(*) AS quantity
            FROM
                market_view_product INNER JOIN
                market_view_hersteller ON (market_view_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id)
            WHERE
                market_view_product.mvsrc_id = $mvsrc_id AND
                market_view_hersteller.brand_id != 0 AND
                market_view_product.product_id = 0 AND
                market_view_product.mpn != ''
            GROUP BY
                market_view_product.mv_hersteller_id
        ");

        foreach ($brands_map as $brand_data) {
            $mv_data = $this->db_mv->query("
                SELECT
                    market_view_product.mvsrc_product_id,
                    market_view_product.product_name,
                    market_view_product.mpn
                FROM
                    market_view_product
                WHERE
                    market_view_product.mvsrc_id = $mvsrc_id AND
                    market_view_product.mv_hersteller_id = '" . $brand_data['mv_hersteller_id'] . "' AND
                    market_view_product.mpn != '' AND
                    market_view_product.product_id = 0
            ")->asArray();

            if (!$mv_data) {
                continue;
            }

            $map = $this->db->query("
                SELECT
                    product.mpn,
                    product.product_id
                FROM
                    product
                WHERE
                    product.brand_id = '" . $brand_data['brand_id'] . "' AND
                    (
                        product.mpn IN (" . $this->db->in(array_column($mv_data, 'mpn')) . ")
                    ) AND
                    product.mpn != '' AND
                    product.product_type != '" . ProductConst::PRODUCT_TYPE_XET . "'
            ")->asSingleArray('mpn');

            foreach ($mv_data as $row) {
                $product_id = null;

                if (isset($map[$row['product_name']])) {
                    $product_id = $map[$row['product_name']];
                } elseif ($row['mpn'] && isset($map[$row['mpn']])) {
                    $product_id = $map[$row['mpn']];
                }

                if ($product_id) {
                    $matchings[] = ['mvsrc_id' => $mvsrc_id, 'mvsrc_product_id' => $row['mvsrc_product_id'], 'product_id' => $product_id];
                }
            }
        }

        return $matchings;
    }

    public function runForMvsrcId(int $mvsrc_id): void
    {
        $matchings = $this->collectNewMatchings($mvsrc_id);

        foreach ($matchings as $matching) {
            $this->product_matching->addMatching($matching['mvsrc_id'], $matching['mvsrc_product_id'], $matching['product_id']);
        }
    }
}
