<?php

namespace wws\MarketViewWwsConnector\Matching\Brand;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\ProductConst;

class BrandMatching
{
    private db_generic $db_mv;
    private db_generic $db;

    public function __construct(db_generic $db_mv, db_generic $db)
    {
        $this->db_mv = $db_mv;
        $this->db = $db;
    }

    public function run(): void
    {
        $this->matchByBrandName();
        //$this->matchBrandsByMatching();
    }

    public function matchByBrandName(): void
    {
        $brand_names = $this->db->query("
            SELECT
                product_brand.brand_name,
                product_brand.brand_id
            FROM
                product_brand
            WHERE
                product_brand.brand_id NOT IN (" . ProductConst::BRAND_ID_UNBEKANNT . ")
        ")->addCallback(function (array $row) {
             $row['brand_name'] = $this->normalize($row['brand_name']);
             return $row;
        })->asSingleArray('brand_name');

        $mv_hersteller = $this->db_mv->query("
            SELECT
                market_view_hersteller.mv_hersteller_id,
                market_view_hersteller.hersteller_name
            FROM
                market_view_hersteller
            WHERE
                market_view_hersteller.brand_id = 0 AND
                market_view_hersteller.mv_hersteller_id != 0
        ")->addCallback(function (array $row) {
             $row['hersteller_name'] = $this->normalize($row['hersteller_name']);
             return $row;
        })->asSingleArray('mv_hersteller_id');

        foreach ($mv_hersteller as $mv_hersteller_id => $hersteller_name) {
            if (!isset($brand_names[$hersteller_name])) {
                continue;
            }

            $this->setBrandIdForMvHersteller($brand_names[$hersteller_name], $mv_hersteller_id);
        }
    }

    /**
     * @param int $mv_hersteller_id
     * @return array<int, array{brand_id: int, brand_name: string, product_count: int}>
     */
    public function getBrandSuggestions(int $mv_hersteller_id): array
    {
        $product_ids = $this->db_mv->query("
            SELECT
                market_view_matching.product_id
            FROM
                market_view_product INNER JOIN
                market_view_matching ON (market_view_product.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product.mvsrc_product_id = market_view_matching.mvsrc_product_id)
            WHERE
                market_view_product.mv_hersteller_id = '$mv_hersteller_id' AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "'
            LIMIT
                1000
        ")->asSingleArray();

        if (!$product_ids) {
            return [];
        }

        return $this->db->query("
            SELECT
                product_brand.brand_id,
                product_brand.brand_name,
                COUNT(*) AS product_count
            FROM
                product INNER JOIN
                product_brand ON (product.brand_id = product_brand.brand_id)
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ")
            GROUP BY
                product_brand.brand_id
            ORDER BY
                product_count DESC
        ")->asArray();
    }

    /**
     * Achtung: gefährlich! Die Datenqualität und Mischung zwischen Marke, Lieferant und Hersteller erzeugt hier mehr Probleme als es löst.
     * @return void
     */
    public function matchBrandsByMatching(): void
    {
        $mv_herstellers = $this->db_mv->query("
            SELECT
                market_view_hersteller.mv_hersteller_id,
                market_view_hersteller.hersteller_name
            FROM
                market_view_hersteller INNER JOIN
                (
                    SELECT
                        DISTINCT market_view_product.mv_hersteller_id
                    FROM
                        market_view_product
                ) AS t ON (market_view_hersteller.mv_hersteller_id = t.mv_hersteller_id)
            WHERE
                market_view_hersteller.brand_id = 0 AND
                market_view_hersteller.mv_hersteller_id != 0
        ");

        var_dump(count($mv_herstellers));

        foreach ($mv_herstellers as $mv_hersteller) {
            $mv_hersteller_id = $mv_hersteller['mv_hersteller_id'];

            $suggestions = $this->getBrandSuggestions($mv_hersteller_id);

            //
            $brand_id = null;

            if ($brand_id) {
                $this->setBrandIdForMvHersteller($brand_id, $mv_hersteller_id);
            }
        }
    }

    public function matchBrandsByEan(): void
    {
        throw new DevException();

        $mv_hersteller_ids = $this->db_mv->query("
            SELECT
                market_view_hersteller.mv_hersteller_id
            FROM
                market_view_hersteller
            WHERE
                market_view_hersteller.brand_id = 0 AND
                market_view_hersteller.mv_hersteller_id != 0
        ")->asSingleArray();

        foreach ($mv_hersteller_ids as $mv_hersteller_id) {
            $eans = $this->db_mv->query("
                SELECT
                    market_view_product.ean
                FROM
                    market_view_product
                WHERE
                    market_view_product.mv_hersteller_id = '$mv_hersteller_id' AND
                    market_view_product.ean != ''
                ORDER BY
                    RAND()
                LIMIT
                    500
            ")->asSingleArray();

            if (count($eans) < 10) {
                continue;
            }

            $brand_id = $this->db->fieldQuery("
                SELECT
                    product.brand_id,
                    COUNT(*) AS anzahl
                FROM
                    product INNER JOIN
                    product_brand ON (product.brand_id = product_brand.brand_id)
                WHERE
                    product.ean IN (" . $this->db->makeIn($eans) . ")
                GROUP BY
                    product.brand_id
                HAVING
                    anzahl > 5
                ORDER BY
                    COUNT(*) DESC
                LIMIT
                    1
            ");

            if ($brand_id) {
                $this->setBrandIdForMvHersteller($brand_id, $mv_hersteller_id);
            }
        }
    }

    public function changeBrandId(int $brand_id, int $new_brand_id): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_hersteller
            SET
                market_view_hersteller.brand_id = '" . $new_brand_id . "'
            WHERE
                market_view_hersteller.brand_id = '" . $brand_id . "'
        ");
    }

    /**
     * @param int $brand_id
     * @param int $mv_hersteller_id
     */
    public function setBrandIdForMvHersteller(int $brand_id, int $mv_hersteller_id): void
    {
        $this->db_mv->query("
            UPDATE
                market_view_hersteller
            SET
                market_view_hersteller.brand_id = " . $brand_id . "
            WHERE
                market_view_hersteller.mv_hersteller_id = " . $mv_hersteller_id . "
        ");
    }

    private function normalize(mixed $value): string
    {
        //akzents?

        $value = preg_replace('~[^a-z0-9]~iu', '', $value); //äöüß ist bewusst!
        return strtolower($value);
    }
}
