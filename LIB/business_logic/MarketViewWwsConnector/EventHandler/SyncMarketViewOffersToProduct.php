<?php

namespace wws\MarketViewWwsConnector\EventHandler;

use wws\MarketViewWwsConnector\Sync\MarketViewProductEkOfferSync;
use wws\Product\Event\EventProductChange;
use wws\Product\ProductEk;

class SyncMarketViewOffersToProduct
{
    private MarketViewProductEkOfferSync $market_view_product_ek_offer_sync;

    public function __construct(MarketViewProductEkOfferSync $market_view_product_ek_offer_sync)
    {
        $this->market_view_product_ek_offer_sync = $market_view_product_ek_offer_sync;
    }

    public function handleEvent(EventProductChange $event): void
    {
        $changes = $event->getEntityChanges();

        foreach ($changes->getChanges('ek_fulfill_versand_source') as $change) {
            if ($change['new_value'] === ProductEk::EK_FULFILL_SOURCE_MARKET_VIEW && $change['old_value'] !== null) {
                $this->queue($event->getProductId());
                return;
            }
        }
    }

    public function queue(int $product_id): void
    {
        $this->market_view_product_ek_offer_sync->queueByProductId($product_id);
    }
}
