<?php

namespace wws\MarketViewWwsConnector\EventHandler;

use wws\MarketViewWwsConnector\Matching\Product\MarketViewRemoveMatching;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\Event\EventProductChange;

class RemoveProductMatchingOnEanChange
{
    private ProductMatching $product_matching;

    public function __construct(ProductMatching $product_matching)
    {
        $this->product_matching = $product_matching;
    }

    public function handleEvent(EventProductChange $event): void
    {
        $changes = $event->getEntityChanges();

        if (!$changes->isChange('ean')) {
            return;
        }

        $change = $changes->getChange('ean');
        if (!$change['old_value']) {
            return;
        }

        $this->product_matching->removeProductMatching(new MarketViewRemoveMatching($event->getProductId()));
    }
}
