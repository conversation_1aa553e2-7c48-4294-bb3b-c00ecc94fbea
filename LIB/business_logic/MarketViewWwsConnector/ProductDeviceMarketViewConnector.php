<?php

namespace wws\MarketViewWwsConnector;

use bqp\db\db_generic;
use url_gateway;
use wws\MarketView\MarketViewMediaCache;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;

class ProductDeviceMarketViewConnector
{
    private db_generic $db_mv;
    private MarketViewMediaCache $market_view_media_cache;

    public function __construct(db_generic $db_mv, MarketViewMediaCache $market_view_media_cache)
    {
        $this->db_mv = $db_mv;
        $this->market_view_media_cache = $market_view_media_cache;
    }

    /**
     * @param int $device_id
     * @return array
     */
    public function searchMedia(int $device_id): array
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_device.mvsrc_device_id,
                market_view_device.model_name,
                market_view_device.code1,
                market_view_device_media.url,
                market_view_device_media.description,
                market_view_source.mvsrc_id,
                market_view_source.mvsrc_name
            FROM
                market_view_device INNER JOIN
                market_view_device_media ON (market_view_device.mvsrc_id = market_view_device_media.mvsrc_id AND market_view_device.mvsrc_device_id = market_view_device_media.mvsrc_device_id) INNER JOIN
                market_view_source ON (market_view_device.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_device.device_id = $device_id 
        ")->addCallback([$this, 'buildImageUrls'])->asArray();

        return $result;
    }

    public function buildImageUrls(array $row): array
    {
        if ($this->market_view_media_cache->isStorageUrl($row['url'])) {
            //$row['preview_image'] = '/res/images/no_pic100x100.jpg';

            $row['preview_image'] = $this->market_view_media_cache->buildThumbnailUrl($row['mvsrc_id'], $row['url']);
            $row['user_url'] = $this->market_view_media_cache->buildUrl($row['mvsrc_id'], $row['url']);
        } else {
            $row['preview_image'] = '/res/images/no_pic100x100.jpg';
            $row['user_url'] = url_gateway::create($row['url']);
        }

        return $row;
    }

    public function getDeviceIds(int $product_id, ?array $mvsrc_ids = null): array
    {
        $where = '';
        if ($mvsrc_ids) {
            $where .= "AND market_view_matching.mvsrc_id IN (" . $this->db_mv->in($mvsrc_ids) . ")";
        }

        return $this->db_mv->query("
            SELECT
                DISTINCT
                market_view_device.device_id
            FROM
                market_view_matching INNER JOIN
                market_view_product_device ON (market_view_product_device.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product_device.mvsrc_product_id = market_view_matching.mvsrc_product_id) INNER JOIN
                market_view_device ON (market_view_device.mvsrc_id = market_view_product_device.mvsrc_id AND market_view_device.mvsrc_device_id = market_view_product_device.mvsrc_device_id)
            WHERE
                market_view_matching.product_id = $product_id AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                market_view_device.device_id > 0 AND
                market_view_product_device.is_online = 1
                $where
        ")->asSingleArray();
    }

    public function getRelationSources(int $device_id, int $product_id): array
    {
        return $this->db_mv->query("
            SELECT               
                market_view_source.mvsrc_name,

                market_view_device.mvsrc_device_id,
                market_view_device.model_name,
                market_view_device.code1,
                
                market_view_matching.product_id,                
                market_view_product.product_name,
                
                market_view_product_device.is_online,
                market_view_matching.matching_status,
                market_view_device.device_status
            FROM
                market_view_matching INNER JOIN
                market_view_product_device ON (market_view_product_device.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product_device.mvsrc_product_id = market_view_matching.mvsrc_product_id) INNER JOIN
                market_view_device ON (market_view_device.mvsrc_id = market_view_product_device.mvsrc_id AND market_view_device.mvsrc_device_id = market_view_product_device.mvsrc_device_id) INNER JOIN
                market_view_product ON (market_view_product.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product.mvsrc_product_id = market_view_matching.mvsrc_product_id) INNER JOIN
                market_view_source ON (market_view_source.mvsrc_id = market_view_matching.mvsrc_id)
            WHERE
                market_view_device.device_id = $device_id AND
                market_view_matching.product_id = $product_id
        ")->asArray();
    }

    public function getExclusiveDeviceIds(int $product_id, array $blacklisted_mvsrc_ids = []): array
    {
        $providing_mvsrc_ids = $this->db_mv->query("
            SELECT
                market_view_device.device_id,
                market_view_device.mvsrc_id
            FROM
                market_view_matching INNER JOIN
                market_view_product_device ON (market_view_product_device.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product_device.mvsrc_product_id = market_view_matching.mvsrc_product_id) INNER JOIN
                market_view_device ON (market_view_device.mvsrc_id = market_view_product_device.mvsrc_id AND market_view_device.mvsrc_device_id = market_view_product_device.mvsrc_device_id)
            WHERE
                market_view_matching.product_id = $product_id AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                market_view_device.device_id > 0 AND
                market_view_product_device.is_online = 1
        ")->asMultiArray('device_id');

        $providing_mvsrc_ids = array_map(function ($rows) {
            return array_column($rows, 'mvsrc_id');
        }, $providing_mvsrc_ids);

        $device_ids = array_keys($providing_mvsrc_ids);

        return array_filter($device_ids, function ($device_id) use ($providing_mvsrc_ids, $blacklisted_mvsrc_ids) {
            return !array_diff($providing_mvsrc_ids[$device_id], $blacklisted_mvsrc_ids);
        });
    }
}
