<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Utils\StringUtils;
use db;
use service_loader;
use wws\MarketView\MarketViewCatMapping;
use wws\MarketView\MarketViewConst;
use wws\ProductCat\ProductCat;

/*
 * erlaubt es ein MarketView <PERSON> in einen Kategoriebaum zu syncen. (oder <PERSON><PERSON> davon)
 * Wichtig: die Quelle muss mit mvsrc_cat_id gepflegt werden. (bzw. sollte -> jede Ä<PERSON>ung in den Namen erzeugt neue Kategorien!)
 */
class MarketViewProductCatSync
{
    private const MV_CAT_CONCAT = ' :: ';

    private db_generic $db_mv;
    private MarketViewCatMapping $market_view_cat_mapping;

    private int $mvsrc_id;
    private int $mv_tree_id = MarketViewConst::TREE_ID_DEFAULT;

    private string $root_mvsrc_cat_name = '';
    private int $cat_tree_id = 1;
    private db_generic $db;
    private int $root_cat_id = 0;
    private bool $override_cat_names = true;
    private bool $default_cat_visibility = true;

    private string $extra_relevant_filter_where_sql = '';
    private string $extra_relevant_filter_join_sql = '';
    private ?string $default_product_type = null;

    public function __construct()
    {
        $this->db_mv  = db::getInstance('market_view');
        $this->db = db::getInstance();
        $this->market_view_cat_mapping = service_loader::getDiContainer()->get(MarketViewCatMapping::class);

        //@todo rausziehen
        $this->extra_relevant_filter_where_sql = " AND market_view_cat.mvsrc_cat_name LIKE '%" . self::MV_CAT_CONCAT . "%'"; //das ist primäre bei k11 für ASWO
    }


    public function setCatTreeId(int $cat_tree_id): void
    {
        $this->cat_tree_id = $cat_tree_id;
    }

    public function setRootCatId(int $root_cat_id): void
    {
        $this->root_cat_id = $root_cat_id;
    }

    public function setMvsrcId(int $mvsrc_id): void
    {
        $this->mvsrc_id = $mvsrc_id;
    }

    public function setMvTreeId(int $mv_tree_id): void
    {
        $this->mv_tree_id = $mv_tree_id;
    }

    public function setMvRootCatByMvsrcCatName(string $root_mvsrc_cat_name): void
    {
        $this->root_mvsrc_cat_name = $root_mvsrc_cat_name;
    }


    public function setDefaultProductType(string $default_product_type): void
    {
        $this->default_product_type = $default_product_type;
    }

    public function filterForActiveProducts(): void
    {
        $this->extra_relevant_filter_join_sql = "
            INNER JOIN (
                SELECT
                    DISTINCT market_view_product_cat.mv_cat_id
                FROM
                    market_view_product INNER JOIN
                    market_view_product_cat ON (market_view_product_cat.mv_cat_id = market_view_product.mv_cat_id)
                WHERE
                    market_view_product.mvsrc_id = " . $this->mvsrc_id . " AND
                    market_view_product.product_status = 'online'
            ) AS t ON (market_view_cat.mv_cat_id = t.mv_cat_id)
        ";
    }


    public function run(): void
    {
        $market_view_cats = $this->getMarketViewCats();

        $this->doSyncCats($market_view_cats);
    }



    private function doSyncCats(array $market_view_cats): void
    {
        $cat_id_map = [];

        foreach ($market_view_cats as $market_view_cat) {
            $parent_cat_id = $this->root_cat_id;
            if ($market_view_cat['cat_path_prefix']) {
                $parent_cat_id = $cat_id_map[$market_view_cat['cat_path_prefix']];
            }

            $cat_id = (int)$market_view_cat['cat_id'];
            $create_mapping = false;

            if ($cat_id) {
                $cat = new ProductCat($cat_id);
            } else {
                $cat = new ProductCat();
                $cat->setCatName(StringUtils::limit($market_view_cat['cat_name'], 50));

                //ggf. eine ProductCat entity als Vorlage nehmen, statt die klasse hier x fach zu erweitern
                $cat->setVisible($this->default_cat_visibility);
                if ($this->default_product_type) {
                    $cat->setProductType($this->default_product_type);
                }

                $create_mapping = (bool)$market_view_cat['mv_cat_id'];
            }

            if ($cat->getCatTreeId() && $cat->getCatTreeId() !== $this->cat_tree_id) {
                throw new FatalException('cat_tree_id mismatch!!!');
            }

            if ($this->override_cat_names) {
                $cat->setCatName(StringUtils::limit($market_view_cat['cat_name'], 50));
            }

            $cat->setParentCatId($parent_cat_id);
            $cat->setCatTreeId($this->cat_tree_id);

            $cat->save();

            if ($create_mapping) {
                $this->market_view_cat_mapping->setLegacyMapping($this->cat_tree_id, $cat->getCatId(), $market_view_cat['mv_cat_id']);
            }

            $cat_id_map[$market_view_cat['cat_path']] = $cat->getCatId();
        }
    }

    public function getMarketViewCats(): array
    {
        $path_filter = $this->root_mvsrc_cat_name;
        $path_filter_len = 0;

        if ($path_filter) {
            $path_filter .= self::MV_CAT_CONCAT;
            $path_filter_len = strlen($path_filter);
        }

        //der query selektiert die relevanten Kategorien
        $relevant_category_names = $this->db_mv->query("
            SELECT
                market_view_cat.mvsrc_cat_name
            FROM
                market_view_cat
                " . $this->extra_relevant_filter_join_sql . "
            WHERE
                market_view_cat.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_cat.tree_id = " . $this->mv_tree_id . "  AND
                market_view_cat.mvsrc_cat_name LIKE '" . $this->db_mv->escape($path_filter) . "%'
                " . $this->extra_relevant_filter_where_sql . "
        ")->asSingleArray();

        //ggf. auch hier nochmal ein umweg machen und über die mv_cat_id gehen, dass das cleaner von außen ist. (funktional geht es aber nur über den namen)

        //alle kategorien, mit erweiterten daten
        $extended_data = $this->db_mv->query("
            SELECT
                market_view_cat.mvsrc_cat_name,
                market_view_cat.mv_cat_id,
                market_view_cat.mvsrc_cat_id,
                market_view_cat_mapping.cat_id
            FROM
                market_view_cat LEFT JOIN
                market_view_cat_mapping ON (
                    market_view_cat.mv_cat_id = market_view_cat_mapping.mv_cat_id AND
                    market_view_cat_mapping.cat_tree_id = " . $this->cat_tree_id . "
                )
            WHERE
                market_view_cat.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_cat.tree_id = " . $this->mv_tree_id . " 
        ")->asArray('mvsrc_cat_name');

        //
        $all_node_names = $this->completeMissingNodes($relevant_category_names);

        if ($path_filter) {
            foreach ($all_node_names as $key => $mvsrc_cat_name) {
                if (!str_starts_with($mvsrc_cat_name, $path_filter)) {
                    unset($all_node_names[$key]);
                }
            }
        }

        $categories = [];

        foreach ($all_node_names as $mvsrc_cat_name) {
            $filtered_mvsrc_cat_name = substr($mvsrc_cat_name, $path_filter_len);

            $cat_path_elements = explode(self::MV_CAT_CONCAT, $filtered_mvsrc_cat_name);

            $category = [
                'cat_path' => $filtered_mvsrc_cat_name,
                'cat_name' => $cat_path_elements[count($cat_path_elements) - 1],
                'cat_path_prefix' => implode(self::MV_CAT_CONCAT, array_slice($cat_path_elements, 0, -1)),
                'mvsrc_cat_name' => $mvsrc_cat_name,
                'level' => count($cat_path_elements),
                'mv_cat_id' => null,
                'mvsrc_cat_id' => null,
                'cat_id' => null,
                'is_explicit_relevant' => in_array($mvsrc_cat_name, $relevant_category_names),
            ];

            if (isset($extended_data[$mvsrc_cat_name])) {
                $category['mv_cat_id'] = (int)$extended_data[$mvsrc_cat_name]['mv_cat_id'];
                $category['mvsrc_cat_id'] = $extended_data[$mvsrc_cat_name]['mvsrc_cat_id'];
                $category['cat_id'] = $extended_data[$mvsrc_cat_name]['cat_id'];
            }

            $categories[] = $category;
        }

        $categories = $this->fillMissingCatIds($categories);

        return $categories;
    }

    private function completeMissingNodes(array $mvsrc_cat_names): array
    {
        $cat_paths = [];

        foreach ($mvsrc_cat_names as $cat_path) {
            $cats = explode(self::MV_CAT_CONCAT, $cat_path);

            $sub_path = '';

            foreach ($cats as $cat) {
                $sub_path = $sub_path ? $sub_path . self::MV_CAT_CONCAT . $cat : $cat;

                if (!isset($cat_paths['$sub_path'])) {
                    $cat_paths[$sub_path] = $sub_path;
                }
            }
        }

        return array_values($cat_paths);
    }


    /**
     * da es in dem Baum einige Knoten nicht gibt und damit auch kein cat_id mapping angelegt werden kann,
     * bestimmen wir über die Namen und danach über Kinder die fehlenden cat_ids.
     * (1. Idee ware nur über die Kinder -> folgendes Problem:
     * Solange ASWO die Kategorien nicht verschiebt, passt es... ;-)
     * Problem ist, wenn eine Kategorie mit cat_id in einen Knoten verschoben wird, der keine cat_id  hat und diese Kategorie
     * an erste Position landet (abc sortierung). Dann nimmt dieser Knoten die cat_id des bisherigen Knotens an.
     * Wir haben dann zwei Knoten mit der gleichen cat_id. Die Kinder merged es dann in diese eine cat_id und die Kategorie
     * nimmt den Namen des letzten Knotens an.
     * Mitigation wäre, erst über die Namen versuchen die fehlende cat_ids zu bestimmen und danach erst über die Kinder.
     * Das sollte relativ robust sein, aber auch nicht 100%. (->mit implementiert, weil relativ easy)
     * )
     *
     * @param array $market_view_cats
     * @return array
     */
    public function fillMissingCatIds(array $market_view_cats): array
    {
        usort($market_view_cats, fn ($a, $b) => strlen($a['cat_path']) <=> strlen($b['cat_path']));

        //
        $cat_path_prefix = $this->cat_tree_id . ' | ';
        if ($this->root_cat_id) {
            $cat_path_prefix = $this->db->fieldQuery("
                SELECT
                    product_cat.cat_path_text
                FROM
                    product_cat
                WHERE
                    product_cat.cat_id = " . $this->root_cat_id . "
            ");

            $cat_path_prefix .= ' | ';
        }
        //

        $cat_path_prefix_len = strlen($cat_path_prefix);

        $wws_cats = $this->db->query("
            SELECT
                product_cat.cat_id,
                product_cat.parent_cat_id,
                product_cat.cat_path_text
            FROM
                product_cat
            WHERE
                product_cat.cat_tree_id = " . $this->cat_tree_id . " AND
                product_cat.cat_path_text LIKE '" . $this->db->escape($cat_path_prefix) . "%'
        ")->asArray('cat_id');

        foreach ($wws_cats as $cat_id => $wws_cat) {
            $wws_cats[$cat_id]['cat_path_text'] = substr($wws_cat['cat_path_text'], $cat_path_prefix_len);
        }

        //fehlende cat_ids über den namen aufläsen
        $path_text_map = [];
        foreach ($wws_cats as $cat_id => $data) {
            $path = explode(' | ', $data['cat_path_text']);

            $path_text_map[implode(self::MV_CAT_CONCAT, $path)] = $cat_id;
        }

        foreach ($market_view_cats as $key => $market_view_cat) {
            if ($market_view_cat['cat_id']) {
                continue;
            }

            $cat_id = $path_text_map[$market_view_cat['cat_path']] ?? null;
            $market_view_cats[$key]['cat_id'] = $cat_id;
        }

        //cat_id pfade fürs wws
        foreach ($wws_cats as $wws_cat) {
            $wws_cat['cat_id_path'] = [];
            $act_cat = $wws_cat;

            do {
                $wws_cat['cat_id_path'][] = $act_cat['cat_id'];
                $act_cat = $wws_cats[$act_cat['parent_cat_id']] ?? null;
            } while ($act_cat);

            $wws_cat['cat_id_path'] = array_reverse($wws_cat['cat_id_path']);
            $wws_cats[$wws_cat['cat_id']] = $wws_cat;
        }
        //

        //baum rückwärts durchgehen, wir haben immer die kinder für ein knoten vor dem jeweiligen knoten
        $market_view_cats = array_reverse($market_view_cats);

        $last_mv_cat_prefix = null;
        $last_mv_cat_id_path = null;

        foreach ($market_view_cats as $key => $market_view_cat) {
            if (!$market_view_cat['cat_id']) {
                if ($market_view_cat['cat_path'] === $last_mv_cat_prefix) {
                    $market_view_cat['cat_id'] = $last_mv_cat_id_path[count($last_mv_cat_id_path) - 1];
                }
            }

            if ($market_view_cat['cat_id']) {
                $pos = strrpos($market_view_cat['cat_path'], self::MV_CAT_CONCAT);
                $last_mv_cat_prefix = $pos ? substr($market_view_cat['cat_path'], 0, $pos) : '';
                $last_mv_cat_id_path = $wws_cats[$market_view_cat['cat_id']]['cat_id_path'];
                array_pop($last_mv_cat_id_path);
            }

            $market_view_cats[$key] = $market_view_cat;
        }

        $market_view_cats = array_reverse($market_view_cats);

        return $market_view_cats;
    }
}
