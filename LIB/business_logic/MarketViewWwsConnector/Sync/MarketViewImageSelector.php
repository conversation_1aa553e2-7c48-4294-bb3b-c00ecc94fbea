<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Image\PHasher;
use config;
use db;
use service_loader;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\ImportScripts\market_view_import_hersteller_bilder;
use wws\MarketView\MarketViewConst;
use wws\MarketView\MarketViewMediaCache;
use wws\MarketView\MarketViewMediaRepository;
use wws\MarketView\MarketViewProductCreator;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\Product;
use wws\Product\ProductBrand\ProductBrandRepository;
use wws\Product\ProductConst;
use wws\Product\ProductMedia;
use wws\Product\ProductRepositoryLegacy;

class MarketViewImageSelector
{
    public const TYPE_PRODUCT = 'product';
    public const TYPE_MARKET_VIEW = 'mv';

    protected int $threshold = 80;
    protected array $pictures = [];
    protected array $picture_groupes = [];

    protected array $ignore_fingerprints = [];

    private array $ignore_fingerprints_default = [
        '74fcf9b9b8f8c0c0' => true, //leider kein bild vorhanden
        '80e8e9c9a5253434' => true, //leider kein bild vorhanden
        '0000000000000000' => true, //weiß oder annähernd weiße bilder,
        '0686096919390584' => true, //ek servicegroup Logo + "Platzhalter"
        '06061569197915c4' => true, //ek servicegroup Logo + "Platzhalter"
    ];

    public bool $ignore_duplicates = true;

    private db_generic $db;
    private db_generic $db_mv;

    private ?int $data_source_profile_id = null;

    private market_view_import_hersteller_bilder $market_view_import;

    public function __construct(db_generic $db, db_generic $db_mv)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;
    }

    /**
     * @param int|null $data_source_profile_id
     */
    public function setDataSourceProfileId(?int $data_source_profile_id): void
    {
        $this->data_source_profile_id = $data_source_profile_id;
    }


    public function reset(): void
    {
        $this->pictures = [];
        $this->picture_groupes = [];
        $this->ignore_fingerprints = $this->ignore_fingerprints_default;
    }

    public function loadProduct(int $product_id): void
    {
        $this->reset();

        $this->loadByProductId($product_id);

        $this->loadIgnoreFingerprintsFromProduct($product_id);
        $this->addMarketViewMediaByProductId($product_id);

        $this->compare();
    }

    public function addPicture($type, $fingerprint, $id, $prio, string $md5, $meta = [])
    {
        if (isset($this->ignore_fingerprints[$fingerprint])) {
            return false;
        }

        $daten = [
            'type' => $type,
            'fingerprint' => $fingerprint,
            'id' => $id,
            'prio' => $prio,
            'md5' => $md5
        ];

        $daten = array_merge($daten, $meta);

        $daten['picture_group'] = null;

        $this->pictures[$id] = $daten;

        return true;
    }


    protected function loadIgnoreFingerprintsFromProduct(int $product_id): void
    {
        $ignore_fingerprints = $this->db->fieldQuery("
            SELECT
                product.deleted_picture_fingerprints
            FROM
                product
            WHERE
                product.product_id = '" . $product_id . "'
        ");

        $ignore_fingerprints = explode("\n", $ignore_fingerprints);

        foreach ($ignore_fingerprints as $fingerprint) {
            $this->addIgnoreFingerprint($fingerprint);
        }
    }

    /**
     * ACHTUNG: wird reseted bei jedem durchlauf!
     * @param string $fingerprint
     * @return void
     * @see addIgnoreFingerprintDefault()
     */
    public function addIgnoreFingerprint(string $fingerprint): void
    {
        $this->ignore_fingerprints[$fingerprint] = true;
    }

    public function addIgnoreFingerprintDefault(string $fingerprint): void
    {
        $this->ignore_fingerprints_default[$fingerprint] = true;
    }

    protected function loadByProductId(int $product_id): void
    {
        $select = '';
        $from = '';
        if ($this->data_source_profile_id) {
            $from = " LEFT JOIN
                product_media_data_source_usage ON (product_media.product_media_id = product_media_data_source_usage.product_media_id AND product_media_data_source_usage.data_source_profile_id = " . $this->data_source_profile_id . ")";
            $select = ", product_media_data_source_usage.show_picture ";
        }

        $result = $this->db->query("
            SELECT
                product_media.pos,
                product_media.product_media_id,
                product_media.fingerprint,
                product_media.md5_hash,
                product_media.width,
                product_media.height,
                product_media.data_source_id
                $select
            FROM
                product_media
                $from
            WHERE
                product_media.product_id = " . $this->db->quote($product_id) . " AND
                product_media.media_type = '" . ProductMedia::MEDIA_TYPE_PICTURE . "'
            ORDER BY
                product_media.pos
        ");

        foreach ($result as $daten) {
            $daten['img_url'] = config::system('https_url') . 'getimage.php?product_media_id=' . $daten['product_media_id'] . '&size=org';

            $prio = 100;
            $prio += round(($daten['width'] * $daten['height']) / 2000);

            $this->addPicture(self::TYPE_PRODUCT, $daten['fingerprint'], $daten['product_media_id'], $prio, $daten['md5_hash'], $daten);
        }
    }

    public function countProductPictures(): int
    {
        $count = 0;
        foreach ($this->pictures as $picture) {
            if ($picture['type'] == self::TYPE_PRODUCT) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Wenn die $data_spource_profile_id gesetzt wurde, liefert das die Anazhl der Bilder die dafür freigegeben sind
     *
     * @return int
     * @throws FatalException
     */
    public function countProductPicturesWithRights(array $excluded_data_source_ids = []): int
    {
        if (!$this->data_source_profile_id) {
            throw new FatalException('data_source_profile_id is not set');
        }

        $count = 0;
        foreach ($this->pictures as $picture) {
            if ($picture['type'] !== self::TYPE_PRODUCT) {
                continue;
            }

            if (in_array($picture['data_source_id'], $excluded_data_source_ids)) {
                continue;
            }

            if (!isset($picture['show_picture'])) {
                throw new DevException('show_picture is missing');
            }

            if ($picture['show_picture'] == 1) {
                $count++;
            }
        }

        return $count;
    }

    protected function addMarketViewMediaByProductId(int $product_id): void
    {
        $result = $this->db_mv->query("
            SELECT
                " . MarketViewMediaRepository::getSqlFieldsForLoad() . ",

                market_view_source.image_data_source_id AS data_source_id
            FROM
                market_view_matching INNER JOIN
                market_view_media ON (market_view_matching.mvsrc_id = market_view_media.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_media.mvsrc_product_id) INNER JOIN
                market_view_source ON (market_view_media.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_matching.product_id = " . $this->db_mv->quote($product_id) . " AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                market_view_media.media_status IN ('" . MarketViewMedia::MEDIA_STATUS_ONLINE . "', '" . MarketViewMedia::MEDIA_STATUS_OFFLINE . "') AND
                market_view_media.topic NOT IN ('" . MarketViewMedia::MEDIA_TOPIC_ICON . "', '" . MarketViewMedia::MEDIA_TOPIC_ENERGIE_LABEL . "') AND
                market_view_media.media_type IN ('" . MarketViewMedia::MEDIA_TYPE_IMAGE . "', '" . MarketViewMedia::MEDIA_TYPE_IMAGE_URL . "') AND
                market_view_media.fingerprint NOT IN ('', 'error') AND
                market_view_source.image_data_source_id IS NOT NULL
        ");

        $media_cache = service_loader::getDiContainer()->get(MarketViewMediaCache::class);

        $mv_product_creator = service_loader::get(MarketViewProductCreator::class);

        foreach ($result as $daten) {
            $daten['img_url'] = $media_cache->buildUrl($daten['mvsrc_id'], $daten['url_storage'] ?: $daten['url']);

            $prio = round(($daten['width'] * $daten['height']) / 2000);
            if ($daten['mvsrc_id'] == MarketViewConst::MVSRC_ID_DEG_GARBSEN) { //deg wegen watermark
                $prio -= 100;
            }

            if ($daten['mvsrc_id'] == MarketViewConst::MVSRC_ID_FRANZ_KERSTIN) { //wegen teils grauen hintergrund und rechte schlechter als hersteller bild
                $prio -= 100;
            }

            $market_view_media = new MarketViewMedia();
            $market_view_media->fromArray($daten);

            $product_media = $mv_product_creator->convertMarketViewMediaToProductMedia($market_view_media);

            $daten['product_media_json'] = $product_media->serializeData();

            $this->addPicture(self::TYPE_MARKET_VIEW, $daten['fingerprint'], $daten['mv_media_id'], $prio, $daten['md5'], $daten);
        }
    }


    public function compare(): void
    {
        if (!$this->pictures) {
            return;
        }

        $picture_group = 1;

        $hasher = PHasher::Instance();

        $compares = $this->pictures;

        foreach ($this->pictures as $daten) {
            if ($daten['fingerprint'] === '') {
                throw new FatalException('fingerprint is missing');
            }
        }

        foreach ($this->pictures as $id => $daten) {
            $this->pictures[$id]['similar'] = [];

            $fingerprint = $daten['fingerprint'];

            /*echo $id.' - ';
            echo $fingerprint.'<br>';
            echo $hasher->HashAsTable($fingerprint);
            echo '<br>';

            echo 'Similarity:<br>';*/
            foreach ($compares as $c_id => $c_daten) {
                $c_fingerprint = $c_daten['fingerprint'];
                if ($id === $c_id) {
                    continue;
                }

                //"fallback"...
                //zwischen gdlib 3.2 und 3.3 ergeben sich in 30% der fälle abweichungen in den fingerprints. :o (vermutlich wurde was an der interpolation beim resize geändert...)
                if ($c_daten['md5'] === $daten['md5']) {
                    $this->pictures[$id]['similar'][] = $c_id;
                    continue;
                }
                //


                $pre = $hasher->CompareHashs($fingerprint, $c_fingerprint);

                if ($pre > $this->threshold) {
                    $this->pictures[$id]['similar'][] = $c_id;
                }


                /*if($pre > $this->threshold) echo '<b>';
                echo $c_id.' - ';
                echo $c_fingerprint.': ';
                echo $pre;
                if($pre > $this->threshold) echo '</b>';
                echo '<br>';*/
            }
            //echo '<hr>';

            //picture groups füllen
            if ($daten['picture_group']) {
                continue;
            }
            if (!$this->pictures[$id]['similar']) {
                $this->pictures[$id]['picture_group'] = $picture_group++;
            } else {
                foreach ($this->pictures[$id]['similar'] as $c_id) {
                    if ($this->pictures[$c_id]['picture_group']) {
                        $this->pictures[$id]['picture_group'] = $this->pictures[$c_id]['picture_group'];
                        break;
                    }
                }

                if (!$this->pictures[$id]['picture_group']) {
                    $this->pictures[$id]['picture_group'] = $picture_group++;

                    foreach ($this->pictures[$id]['similar'] as $c_id) {
                        $this->pictures[$c_id]['picture_group'] = $picture_group;
                    }
                }
            }
        }

        //bilder gruppieren.
        $this->picture_groupes = [];

        foreach ($this->pictures as $id => $daten) {
            if (!isset($this->picture_groupes[$daten['picture_group']])) {
                $this->picture_groupes[$daten['picture_group']] = [];
            }

            $this->picture_groupes[$daten['picture_group']][$id] = $daten;
        }
    }

    public function showPictureGroups()
    {
        foreach ($this->picture_groupes as $picture_group => $pictures) {
            echo '<div style="border: 5px solid black; margin: 10px; padding: 10px;">';
            echo '<h1>' . $picture_group . '</h1>';

            foreach ($pictures as $picture) {
                echo '<img src="' . $picture['img_url'] . '" style="max-height: 200px; max-width: 200px;">';
                echo '<br>';
                if ($picture['type'] == self::TYPE_PRODUCT) {
                    echo '<b>Produkt</b>';
                } else {
                    echo $picture['type'];
                }
                echo '<br>';
                echo $picture['id'];
                echo '<br>';
                echo $picture['width'] . 'x' . $picture['height'];
                echo '<br>';
                echo $picture['data_source_id'];
                echo '<br>';
                echo 'Prio: ' . $picture['prio'];
                echo '<br>';
                echo 'Fingerprint: ' . $picture['fingerprint'];

                if ($picture['type'] == 'mv') {
                    echo '<br>';
                    echo 'mv_media_id: ' . $picture['mv_media_id'];
                    echo '<br>';
                    echo 'url: ' . $picture['url'];
                    echo '<br>';
                    echo 'url_storage: ' . $picture['url_storage'];
                }
                echo '<hr>';
            }

            echo '</div>';
        }
    }

    public function getCommands(): array
    {
        //arbeitsanweisung erstellen
        $commands = [];

        foreach ($this->picture_groupes as $picture_group => $pictures) {
            //sortieren nach prio
            uasort($pictures, function ($p1, $p2) {
                //$pictures
                if ($p1['prio'] == $p2['prio']) {
                    return 0;
                }

                return $p1['prio'] > $p2['prio'] ? -1 : 1;
            });

            //dubletten entfernen

            $count = 0;
            foreach ($pictures as $id => $picture) {
                if ($picture['type'] == self::TYPE_PRODUCT) {
                    $count++;

                    if ($count > 1) {
                        if (!$this->ignore_duplicates) {
                            $commands[] = ['command' => 'remove', 'reason_text' => 'duplicate', 'picture' => $picture];
                        }
                        unset($pictures[$id]);
                    }
                }
            }

            //bild ersetzen
            if (count($pictures) > 1) {
                $i = 0;
                $replace = null;
                foreach ($pictures as $id => $picture) {
                    if ($i++ === 0) {
                        if ($picture['type'] == self::TYPE_PRODUCT) {
                            break;
                        }

                        $replace = $picture;
                    } elseif ($picture['type'] == self::TYPE_PRODUCT) {
                        $commands[] = ['command' => 'replace', 'reason_text' => 'better market_view image', 'src_picture' => $picture, 'dst_picture' => $replace];
                        break;
                    }
                }
            }

            //neues bild hinzufügen
            if ($count == 0) {
                foreach ($pictures as $id => $picture) {
                    $commands[] = ['command' => 'add', 'reason_text' => 'no image', 'picture' => $picture];
                    break;
                }
            }
        }

        return $commands;
    }

    public function countCommands(array $commands): array
    {
        $result = [
            'add' => 0,
            'replace' => 0,
            'remove' => 0
        ];

        foreach ($commands as $command) {
            $result[$command['command']]++;
        }

        return $result;
    }


    public function isType(int $id, string $type): bool
    {
        return $this->pictures[$id]['type'] == $type;
    }

    public function executeCommandsForProductId(int $product_id, array $commands = null): void
    {
        if ($commands === null) {
            $commands = $this->getCommands();
        }

        if (!$commands) {
            return;
        }

        $product = ProductRepositoryLegacy::loadProduct($product_id);

        foreach ($commands as $command) {
            $product->setEditReason('media_collector');

            switch ($command['command']) {
                case 'add':
                    $product_media = new ProductMedia();
                    $product_media->setDataSourceId($command['picture']['data_source_id']);
                    if (isset($command['picture']['product_media_json'])) {
                        $product_media->serializeDataLoad($command['picture']['product_media_json']);
                    }

                    if (!empty($command['picture']['mv_media_id'])) {
                        $product_media->setMvMediaId($command['picture']['mv_media_id']);
                    }

                    $product->addAsyncMedia($command['picture']['img_url'], $product_media);
                    break;
                case 'replace':
                    $src_media = $product->getMedia($command['src_picture']['id']);
                    $src_media->del();

                    //bild in marketview
                    $this->backupPictureToMarketView($product, $src_media);

                    $product_media = new ProductMedia();
                    if (isset($command['picture']['product_media_json'])) {
                        $product_media->serializeDataLoad($command['picture']['product_media_json']);
                    }

                    if (!empty($command['picture']['mv_media_id'])) {
                        $product_media->setMvMediaId($command['picture']['mv_media_id']);
                    }

                    $product_media->setBeschreibung($src_media->getBeschreibung());
                    $product_media->setPos($src_media->getPos());

                    $product->addAsyncMedia($command['dst_picture']['img_url'], $product_media);

                    break;
                case 'remove':
                    $product_media = $product->getMedia($command['picture']['id']);
                    $product_media->del();

                    break;
            }
        }

        $product->save();
    }

    public function backupPictureToMarketView(Product $product, ProductMedia $product_media): void
    {
        if ($product_media->getDataSourceId() !== ProductConst::PRODUCT_DATA_SOURCE_HERSTELLER) {
            return;
        }

        $storage = service_loader::getStorageFactory()->get('media');

        $raw_image = $storage->read('media://artimages/' . $product_media->getFilename());

        $storage_url = 'local://extern/hersteller_bilder/' . $product_media->getFilename();

        $market_view_import = $this->getMediaImportForBackup();
        $market_view_import->addPicture($product->getProductId(), $product->getProductName(), ProductBrandRepository::getBrandName($product->getBrandId()), $product->getEan(), $raw_image, $storage_url);
    }

    private function getMediaImportForBackup(): market_view_import_hersteller_bilder
    {
        if (!isset($this->market_view_import)) {
            $this->market_view_import = new market_view_import_hersteller_bilder([
                'mvsrc_id' => MarketViewConst::MVSRC_ID_HERSTELLER_BILDER,
                'db_mv' => db::getInstance('market_view')
            ]);
        }

        return $this->market_view_import;
    }
}
