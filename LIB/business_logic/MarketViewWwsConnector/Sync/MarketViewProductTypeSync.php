<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\Collection\BatchedIterator;
use bqp\db\db_generic;
use wws\MarketView\MarketViewCatMappingProductType;
use wws\Product\Product;
use wws\Product\ProductConst;
use wws\Product\ProductRepository;

class MarketViewProductTypeSync
{
    private ProductRepository $product_repository;
    private MarketViewCatMappingProductType $mapping_product_type;
    private db_generic $db;

    public function __construct(ProductRepository $product_repository, db_generic $db, MarketViewCatMappingProductType $mapping_product_type)
    {
        $this->product_repository = $product_repository;
        $this->db = $db;
        $this->mapping_product_type = $mapping_product_type;
    }

    public function fillMissingProductTypes(): void
    {
        $product_ids_all = $this->db->query("
            SELECT
                product.product_id
            FROM
                product
            WHERE
                product.product_type = '" . ProductConst::PRODUCT_TYPE_UNBEKANNT . "'
        ")->asSingleArray();

        $iterator = BatchedIterator::fromArray($product_ids_all, 10000);

        $iterator->each(function (array $product_ids) {
            $mapping = $this->mapping_product_type->getSuggestedProductType($product_ids);
            if (!$mapping) {
                return;
            }

            $products = $this->product_repository->loadProductsBatched(array_keys($mapping), 250);
            $products->edit(function (Product $product) use ($mapping) {
                $product->setProductType($mapping[$product->getProductId()]);
            });
        });
    }
}
