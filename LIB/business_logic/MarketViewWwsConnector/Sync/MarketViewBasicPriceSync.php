<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use input;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\Product;
use wws\Product\ProductRepository;

/**
 * übernimmt eventuelle Grundpreisangaben aus MarketView in die Produkte
 */
class MarketViewBasicPriceSync
{
    private db_generic $db_mv;
    private ProductRepository $product_repository;

    private bool $override = true;

    public function __construct(ProductRepository $product_repository, db_generic $db_mv)
    {
        $this->product_repository = $product_repository;
        $this->db_mv = $db_mv;
    }

    public function copyBasicPriceToProduct(): void
    {
        $all_basic_prices = $this->db_mv->query("
            SELECT
                DISTINCT
                market_view_matching.product_id,
                market_view_product.grundpreis_einheit,
                market_view_product.grundpreis_menge,
                market_view_product.grundpreis_faktor
            FROM
                market_view_product INNER JOIN
                market_view_matching ON (market_view_product.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product.mvsrc_product_id = market_view_matching.mvsrc_product_id)
            WHERE
                market_view_product.grundpreis_faktor IS NOT NULL AND
                market_view_product.grundpreis_faktor > 0 AND
                market_view_product.grundpreis_einheit != '' AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "'
            ORDER BY
                market_view_matching.product_id
        ")->asMultiArray('product_id');

        $product_ids = array_keys($all_basic_prices);

        $load = $this->product_repository->getDefaultLoadConfig();
        $load->setProductDetailsDefault(false);

        $products = $this->product_repository->loadProductsBatched($product_ids, 250, $load);

        $products->edit(function (Product $product) use ($all_basic_prices) {
            if (!$this->override && $product->isGrundpreisAktiv()) {
                return false;
            }

            $basic_prices = $all_basic_prices[$product->getProductId()];

            $basic_price = $this->normalizeBasicPrices($basic_prices);

            $product->setGrundpreisAktiv(1);
            $product->setGrundpreisEinheit($basic_price['grundpreis_einheit_wws']);
            $product->setGrundpreisFaktor($basic_price['grundpreis_faktor']);

            return true;
        });
    }

    /**
     * @param array $basic_prices
     * @return array{
     *         grundpreis_einheit: string,
     *         grundpreis_menge: float,
     *         grundpreis_faktor: float,
     *         grundpreis_einheit_wws: string
     *     }
     */
    private function normalizeBasicPrices(array $basic_prices): array
    {
        foreach ($basic_prices as $key => $basic_price) {
            $basic_prices[$key] = $this->normalizeBasicPrice($basic_price);

            //wir nehmen das erste und gut ist
            return $basic_prices[$key];
        }
    }

    private function normalizeBasicPrice(array $basic_price): array
    {
        $basic_price['grundpreis_einheit'] = $this->normalizeUnit($basic_price['grundpreis_einheit']);
        $basic_price['grundpreis_menge'] = input::parseFloat($basic_price['grundpreis_menge']);
        $basic_price['grundpreis_faktor'] = input::parseFloat($basic_price['grundpreis_faktor']);

        switch ($basic_price['grundpreis_einheit']) {
            case 'Stück':
            case 'Anwendung':
                $basic_price['grundpreis_einheit_wws'] = trim($basic_price['grundpreis_menge'] . ' ' . $basic_price['grundpreis_einheit']);
                break;
            default:
                $basic_price['grundpreis_einheit_wws'] = trim($basic_price['grundpreis_menge'] . $basic_price['grundpreis_einheit']);
        }

        return $basic_price;
    }

    private function normalizeUnit(string $unit): string
    {
        switch ($unit) {
            case 'ml':
                return 'ml';
            case 'l':
            case 'LT':
                return 'l';
            case 'g':
            case 'GR':
                return 'g';
            case 'kg':
                return 'kg';
            case 'm':
            case 'Meter':
            case 'MTR':
                return 'm';
            case 'Stück':
            case 'Stck':
            case 'Stk':
                return 'Stück';
            case 'Anw.':
                return 'Anwendung';
            default:
                return $unit;
        }
    }
}
