<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\Collection\BatchedIterator;
use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\Product;
use wws\Product\ProductConst;
use wws\Product\ProductRepository;

class MarketViewLogisticDataSync
{
    private db_generic $db;
    private db_generic $db_mv;
    private ProductRepository $product_repository;
    private int $mvsrc_id;

    public function __construct(ProductRepository $product_repository, db_generic $db, db_generic $db_mv)
    {
        $this->product_repository = $product_repository;
        $this->db = $db;
        $this->db_mv = $db_mv;
    }

    public function setMvsrcId(int $mvsrc_id): void
    {
        //die implementierung läuft derzeit nur auf Basis einer Quelle, mehrere Quellen müssen ggf. nacheinander ausgeführt werden
        $this->mvsrc_id = $mvsrc_id;
    }

    private function assertMvsrcId(): void
    {
        if (!$this->mvsrc_id) {
            throw new DevException('mvsrc_id must be set');
        }
    }

    public function fillMissingDimensions(): void
    {
        $this->assertMvsrcId();

        $product_ids_all = $this->db->query("
            SELECT
                product.product_id
            FROM
                product
            WHERE
                product.size_h = 0 AND
                product.product_type != '" . ProductConst::PRODUCT_TYPE_XET . "'
        ")->asSingleArray();

        $iterator = BatchedIterator::fromArray($product_ids_all, 1000);

        $iterator->each(function (array $product_ids) {
            $market_view_result = $this->db_mv->query("
                SELECT
                    market_view_matching.product_id,
                    market_view_product.breite,
                    market_view_product.hoehe,
                    market_view_product.tiefe
                FROM
                    market_view_matching INNER JOIN
                    market_view_product ON (market_view_matching.mvsrc_id = market_view_product.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_product.mvsrc_product_id)
                WHERE
                    market_view_matching.mvsrc_id = " . (int)$this->mvsrc_id . " AND
                    market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                    market_view_matching.product_id IN (" . $this->db_mv->in($product_ids) . ") AND
                    market_view_product.hoehe > 0 
            ")->asArray();

            $products = $this->product_repository->searchByArray($market_view_result, 250);

            $products->edit(function (Product $product, array $data) {
                $product->setSizeB($data['breite']);
                $product->setSizeH($data['hoehe']);
                $product->setSizeT($data['tiefe']);
            });
        });
    }

    public function fillMissingWeight(): void
    {
        $this->assertMvsrcId();

        $product_ids_all = $this->db->query("
            SELECT
                product.product_id
            FROM
                product
            WHERE
                product.gewicht = 0 AND
                product.product_type != '" . ProductConst::PRODUCT_TYPE_XET . "'
        ")->asSingleArray();

        $iterator = BatchedIterator::fromArray($product_ids_all, 1000);

        $iterator->each(function (array $product_ids) {
            $market_view_result = $this->db_mv->query("
                SELECT
                    market_view_matching.product_id,
                    market_view_product.gewicht
                FROM
                    market_view_matching INNER JOIN
                    market_view_product ON (market_view_matching.mvsrc_id = market_view_product.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_product.mvsrc_product_id)
                WHERE
                    market_view_matching.mvsrc_id = " . (int)$this->mvsrc_id . " AND
                    market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                    market_view_matching.product_id IN (" . $this->db_mv->in($product_ids) . ") AND
                    market_view_product.gewicht > 0 
            ")->asArray();

            $products = $this->product_repository->searchByArray($market_view_result, 250);

            $products->edit(function (Product $product, array $data) {
                $product->setSizeB($data['gewicht']);
            });
        });
    }
}
