<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\Collection\BatchedIterator;
use bqp\db\db_generic;
use env;
use Exception;
use service_loader;
use wws\MarketView\MarketViewConst;
use wws\MarketViewWwsConnector\Matching\Product\MarketViewRemoveMatching;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\Product;
use wws\Product\ProductConst;
use wws\Product\ProductRepository;
use wws\ProductStock\ProductStockBookingService;
use wws\Supplier\SupplierRepository;
use wws\Supplier\SuppliersConst;

class MarketViewProductEkOfferSync
{
    private db_generic $db_mv;
    private ProductRepository $product_repository;
    private bool $validate_matching_cache = false;

    public function __construct(db_generic $db_mv, ProductRepository $product_repository)
    {
        $this->db_mv = $db_mv;
        $this->product_repository = $product_repository;
    }

    public function clearLocalProductMatchingByMatchingId(int $matching_id, MarketViewRemoveMatching $remove_matching): void
    {
        $row = $this->db_mv->singleQuery("
            SELECT
                market_view_matching.product_id,
                market_view_matching.mvsrc_product_id,
                market_view_source.supplier_id
            FROM
                market_view_matching INNER JOIN
                market_view_source ON (market_view_matching.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_matching.matching_id = $matching_id
        ");

        $this->clearLocalProductMatching($row['product_id'], $row['supplier_id'], $remove_matching->getReason(), $remove_matching->isDisableBezug());
    }

    public function clearLocalProductMatching(int $product_id, int $supplier_id, ?string $reason = null, bool $disable_bezug = false): void
    {
        $product = $this->product_repository->loadProduct($product_id);

        $product_ek_offer = $product->getProductEkOfferIfExists($supplier_id);
        if (!$product_ek_offer) {
            return;
        }

        $product_ek_offer->setMvsrcAvailabilityId(0);
        $product_ek_offer->setMvsrcAvailabilityIdVirt(0);
        $product_ek_offer->setAvailabilityInfo('');
        $product_ek_offer->setGrosProductId('');

        if ($disable_bezug) {
            $product_ek_offer->setBezug(false);
        }

        if ($reason) {
            $product->setEditReason($reason);
        }

        $this->product_repository->save($product);

        //lager buchen
        //->in der Form aber auch unschön... -> als Event auslagern
        $parameter = [
            'product_id' => $product_id,
            'supplier_id' => $supplier_id,
            'inventory_typ' => 'inventory',
            'inventory' => 0,
            'matching_status' => ProductMatching::MATCHING_WRONG
        ];

        $this->updateProductSuppliersBestand($parameter);
    }

    public function updateAllProducts(): void
    {
        $result = $this->db_mv->query("
            SELECT
                DISTINCT market_view_matching.product_id               
            FROM
                market_view_matching INNER JOIN
                market_view_source ON (market_view_matching.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                market_view_source.supplier_id != 1 AND
                market_view_source.typ = '" . MarketViewConst::MVSRC_TYPE_SUPLLIER . "'
            ORDER BY
                market_view_matching.product_id
        ")->asSingleArray();

        foreach (new BatchedIterator($result, 1000) as $product_ids) {
            $this->updateProductIds($product_ids);
        }
    }

    public function updateProductsByQueue(): void
    {
        $this->validate_matching_cache = true;

        //@todo eventuell komplett auf events umstellen... da ist das ganze Batch Processing schon sauber abstrahiert
        //Problem: der update cache wird per db Trigger gepflegt

        //datensätze die nicht gematcht werden löschen
        $this->db_mv->query("
            DELETE
                market_view_update_cache
            FROM
                market_view_update_cache INNER JOIN
                market_view_source ON (market_view_update_cache.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_source.supplier_id = 1
        ");

        //Achtung: für den Sync eines Produktes müssen alle Angebot berücksichtigt werden, auch wenn wir exakt wissen, welche Angebote sich geändert haben... (@see MarketViewProductEkOfferSync::resolveMultipleOffers())
        $result = $this->db_mv->query("
            SELECT
                market_view_update_cache.mvsrc_id,
                market_view_update_cache.mvsrc_product_id,
                market_view_matching.product_id
            FROM            
                market_view_update_cache LEFT JOIN
                market_view_matching ON (market_view_matching.mvsrc_id = market_view_update_cache.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_update_cache.mvsrc_product_id)
        ");

        foreach (new BatchedIterator($result, 1000) as $batch) {
            $batch_delete = [];
            $product_ids = [];

            foreach ($batch as $row) {
                $batch_delete[] = [$row['mvsrc_id'], $row['mvsrc_product_id']];

                if ($row['product_id']) {
                    $product_ids[$row['product_id']] = $row['product_id'];
                }
            }

            if ($product_ids) {
                $this->updateProductIds($product_ids);
            }

            $this->updateProductsByQueueClear($batch_delete);
        }
    }

    public function queueByMarketViewOffer(int $mvsrc_id, string $mvsrc_product_id): void
    {
        $this->db_mv->query("
            INSERT IGNORE INTO
                market_view_update_cache
            SET
                market_view_update_cache.mvsrc_id = '" . $mvsrc_id . "',
                market_view_update_cache.mvsrc_product_id = '" . $this->db_mv->escape($mvsrc_product_id) . "'
        ");
    }

    public function queueByProductId(int $product_id): void
    {
        $this->db_mv->query("
            INSERT IGNORE INTO
                market_view_update_cache
            SELECT
                market_view_matching.mvsrc_id,
                market_view_matching.mvsrc_product_id
            FROM
                market_view_matching
            WHERE
                market_view_matching.product_id = $product_id
        ");
    }

    public function updateProduct(int $product_id): void
    {
        $this->updateProductIds([$product_id]);
    }

    public function updateProductIds(array $product_ids): void
    {
        $products = $this->product_repository->loadProducts($product_ids);

        $result = $this->db_mv->query("
            SELECT
                market_view_matching.matching_id,
                market_view_matching.product_id,
                market_view_matching.matching_status,
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id,
                market_view_product.vk_netto,
                market_view_product.vk_netto_per_quantity,
                market_view_product.vk_netto_info,
                market_view_product.metal_surcharge_state,
                market_view_product.versand_netto,
                market_view_product.inventory,
                market_view_product.availability_id,
                market_view_product.mv_availability,
                market_view_product.versand_classification,
                market_view_product.dropshipping,
                market_view_product.vpe,
                market_view_product.vpe_zwang,
                
                market_view_source.supplier_id,
                market_view_source.price_typ,
                market_view_source.inventory_typ
            FROM
                market_view_matching INNER JOIN
                market_view_source ON (market_view_matching.mvsrc_id = market_view_source.mvsrc_id) INNER JOIN
                market_view_product ON (market_view_matching.mvsrc_id = market_view_product.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_product.mvsrc_product_id)
            WHERE
                market_view_matching.product_id IN (" . $this->db_mv->in($product_ids) . ") AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                market_view_source.supplier_id != 1 AND
                market_view_source.typ = '" . MarketViewConst::MVSRC_TYPE_SUPLLIER . "'
            ORDER BY
                market_view_matching.product_id
        ")->asMultiArrayStream('product_id');

        foreach ($result as $product_id => $market_view_offers) {
            $this->syncProductEkOffers($products[$product_id], $market_view_offers);
        }
    }

    private function syncProductEkOffers(Product $product, array $market_view_offers): void
    {
        if ($this->validate_matching_cache) {
            $market_view_offers = $this->clearInvalidCachedMatchingStatus($market_view_offers);
        }

        $unfiltered_product_ek_offers = $market_view_offers;
        $market_view_offers = $this->resolveMultipleOffers($product, $market_view_offers);

        $is_with_metal_surcharge = false;

        foreach ($market_view_offers as $market_view_offer) {
            if (env::isK11()) {
                //*nerv* das lässt sich jetzt nicht pauschal entfernen. aktuell sind im produktstamm noch daten vom onboarder drin.
                //die daten im market view für bsh und electrolux sind der endkundenpreis, die daten vom onboarder sind schon konditioniert.
                //die zwei quellen werden jetzt einfach nix gesynct und gut ist. (hatte überlegt die supplier_id aus market_view_source zu kicken, hätte aber gern noch die verknüpfung)
                //also erstmal so und zukünftig nochmal schauen, wie wir damit umgehen.
                if (in_array($market_view_offer['supplier_id'], [SuppliersConst::SUPPLIER_ID_KREMPEL_BSH, SuppliersConst::SUPPLIER_ID_KREMPEL_ELECTROLUX])) {
                    continue;
                }
            }

            //fehlender ek (0€ ist mir hier zu heiß) oder "datenmüll" reinbekommen (ab 10_000_000 wird es mit den formeln problematisch... was aber beabsichtigt ist!).
            if (!$market_view_offer['vk_netto'] || $market_view_offer['vk_netto'] > 1_000_000) {
                $market_view_offer['vk_netto'] = 9999;
            }

            switch ($market_view_offer['price_typ']) {
                case MarketViewConst::PRICE_TYP_NONE:
                    $product_ek_offer = $product->getProductEkOfferIfExists($market_view_offer['supplier_id']);
                    break;
                case MarketViewConst::PRICE_TYP_MANUAL:
                    $offer_is_new = !$product->productEkOfferExist($market_view_offer['supplier_id']);
                    $product_ek_offer = $product->getOrCreateProductEkOffer($market_view_offer['supplier_id']);
                    if ($offer_is_new) {
                        $product_ek_offer->setBezug(false);
                        $product_ek_offer->setEkListe(0);
                    }
                    break;
                default:
                    $product_ek_offer = $product->getOrCreateProductEkOffer($market_view_offer['supplier_id']);
                    break;
            }

            if (!$product_ek_offer) {
                continue;
            }

            $ek_info = [];

            $product_ek_offer->setMvsrcAvailabilityId($market_view_offer['availability_id']);
            $product_ek_offer->setAvailabilityInfo($market_view_offer['mv_availability']);
            if ($market_view_offer['availability_id'] == MarketViewConst::AVAILABILITY_ID_OFFLINE) {
                $product_ek_offer->setMvsrcAvailabilityIdVirt(MarketViewConst::AVAILABILITY_ID_0);
            }

            $product_ek_offer->setGrosProductId($market_view_offer['mvsrc_product_id']);
            $ek_info[] = $market_view_offer['vk_netto_info'];

            if (
                //wenn explizit von uns gesperrt, dann nicht überschreiben
                $product_ek_offer->getDropshipping() !== ProductConst::DROPSHIPPING_BLOCKED_BY_RULE &&
                $product_ek_offer->getDropshipping() !== ProductConst::DROPSHIPPING_BLOCKED_MANUAL
            ) {
                $product_ek_offer->setDropshipping($market_view_offer['dropshipping']);
            }

            if ($market_view_offer['versand_netto'] === null) {
                if ($product_ek_offer->getEkFulfillVersandSource() === $product_ek_offer::EK_FULFILL_SOURCE_MARKET_VIEW) {
                    $product_ek_offer->setEkFulfillVersand(null);
                }
            } else {
                if (!$product_ek_offer->isEkFulfillVersandManual()) {
                    $product_ek_offer->setEkFulfillVersandSource($product_ek_offer::EK_FULFILL_SOURCE_MARKET_VIEW);
                    $product_ek_offer->setEkFulfillVersand($market_view_offer['versand_netto']);

                    if ($product_ek_offer->getDropshipping() === ProductConst::DROPSHIPPING_BLOCKED_BY_SUPPLIER) {
                        $product_ek_offer->setEkFulfillVersand(null);
                    }
                }
            }

            if ($market_view_offer['availability_id'] != MarketViewConst::AVAILABILITY_ID_OFFLINE || $product_ek_offer->getEkListe() == 0) {
                $vk_netto = $market_view_offer['vk_netto'];

                if ($market_view_offer['vk_netto_per_quantity'] > 1) {
                    if (($vk_netto / $market_view_offer['vk_netto_per_quantity']) > 0.5) {
                        $vk_netto = $vk_netto / $market_view_offer['vk_netto_per_quantity'];
                    } else {
                        $ek_info[] = 'Preis pro ' . $market_view_offer['vk_netto_per_quantity'] . ' Stk.';
                    }
                }

                switch ($market_view_offer['price_typ']) {
                    case MarketViewConst::PRICE_TYP_EK_LISTE:
                        $product_ek_offer->setEkListe(round($vk_netto, 2));
                        break;
                    case MarketViewConst::PRICE_TYP_SNP:
                        $product_ek_offer->setEkSnp(round($vk_netto, 2));
                        break;
                }
            }

            if ($this->hasVpeZwang($market_view_offer)) {
                $product_ek_offer->setVpeZwang(true);
                $product_ek_offer->setVpeInfo($market_view_offer['vpe']);
            } else {
                $product_ek_offer->setVpeZwang(false);
                $product_ek_offer->setVpeInfo('');
            }

            //metal_surcharge_state
            if ($market_view_offer['metal_surcharge_state'] === MarketViewConst::METAL_SURCHARGE_STATE_INCLUDED) {
                $is_with_metal_surcharge = true;
                $ek_info[] = 'inkl. Mezu';
            }
            if ($market_view_offer['metal_surcharge_state'] === MarketViewConst::METAL_SURCHARGE_STATE_EXCLUDED_UNKNOWN) {
                $is_with_metal_surcharge = true;
                $ek_info[] = 'exkl. Mezu !!!';
            }

            $product_ek_offer->setEkInfo(implode(' | ', array_filter($ek_info)));

            //krempl sperrgut (@todo zum testen, kann wieder raus, wenn das geklärt ist)
            if ($product_ek_offer->getSupplierId() === SuppliersConst::SUPPLIER_ID_KREMPEL) {
                $product->toggleProductTag(ProductConst::TAG_KREMPL_SPERRGUT, $market_view_offer['versand_classification'] === 'Sperrgut');
            }

            //lagerbestand updaten
            $this->updateProductSuppliersBestand($market_view_offer);
        }

        //abakus product tag / falls das mehrfach vorkommen sollte -> auf events umstellen
//        $is_abakus = false;
//        foreach ($product->getProductEkOffers() as $ek_offer) {
//            //auf wunsch von Lars. Kein Abakus wenn das Angebot offline ist.
//            if ($ek_offer->getMvsrcAvailabilityId() === MarketViewConst::AVAILABILITY_ID_OFFLINE) {
//                continue;
//            }
//
//            if (strpos($ek_offer->getEkInfo(), 'Abakus') !== false) {
//                $is_abakus = true;
//            }
//        }
//        $product->toggleProductTag(ProductConst::TAG_ABAKUS, $is_abakus);

        //metal_surcharge   //mehrfach genug?!
        $product->toggleProductTag(ProductConst::TAG_METZU, $is_with_metal_surcharge);

        $product->toggleProductTag(ProductConst::TAG_EK_OFFER_CONFLICT, $this->hasEkConflict($product, $unfiltered_product_ek_offers));

        //
        try {
            $product->save();
        } catch (Exception $e) {
            echo $product->getProductId() . '<br>';
            throw $e;
        }
    }

    /**
     * Das System unterstützt derzeit nur ein Angebot für ein Produkt pro Lieferant. Product unterstützt mehrere Angebote.
     * Der Rest des Systems aber nicht, weil dort weiterhin die Annahme gilt: ein Angebot.
     *
     * Diese Methode entfernt mehrfach Angebote. Bereits bekannte Angebote werden dabei bevorzugt.
     *
     * @param Product $product
     * @param array $market_view_offers
     * @return array
     */
    private function resolveMultipleOffers(Product $product, array $market_view_offers): array
    {
        $result = [];

        $known_supplier_ids = [];

        //alle bekannten offers aus $market_view_offers in $result scheiben
        foreach ($product->getProductEkOffers() as $ek_offer) {
            foreach ($market_view_offers as $key => $market_view_offer) {
                if ($market_view_offer['supplier_id'] == $ek_offer->getSupplierId() && $market_view_offer['mvsrc_product_id'] == $ek_offer->getGrosProductId()) {
                    $result[] = $market_view_offer;
                    unset($market_view_offers[$key]);

                    $known_supplier_ids[] = $ek_offer->getSupplierId();
                }
            }
        }

        //wen in $market_view_offers noch offers enthalten sind (das sind bisher unbekannte angebote), dann auch in result übernhemen, solang in result noch kein Angebot des Lieferanten vorliegt.
        if ($market_view_offers) {
            foreach ($market_view_offers as $key => $market_view_offer) {
                if (!in_array($market_view_offer['supplier_id'], $known_supplier_ids)) {
                    $result[] = $market_view_offer;
                    $known_supplier_ids[] = $market_view_offer['supplier_id'];
                }
            }
        }

        return $result;
    }

    private function hasEkConflict(Product $product, array $market_view_offers): bool
    {
        $offers_by_supplier = [];

        foreach ($market_view_offers as $market_view_offer) {
            if (!isset($offers_by_supplier[$market_view_offer['supplier_id']])) {
                $offers_by_supplier[$market_view_offer['supplier_id']] = 0;
            }

            $offers_by_supplier[$market_view_offer['supplier_id']]++;
        }

        unset($offers_by_supplier[SuppliersConst::SUPPLIER_ID_AB_LEERFELD]);

        foreach ($offers_by_supplier as $supplier_id => $count) {
            if ($count > 1) {
                //return true;

                //Das ist u.U. etwas inkonsistent, weil das hier nicht getriggert wird, wenn sich der Bezug ändert, das
                //ist aber für den use case ok. Wir nehmen etwas "noise" aus dem Frontend.
                $product_ek_offer = $product->getProductEkOfferIfExists($supplier_id);

                if ($product_ek_offer && $product_ek_offer->getBezug()) {
                    return true;
                }
            }
        }

        return false;
    }

    private function updateProductsByQueueClear(array $batch_delete): void
    {
        if (!$batch_delete) {
            return;
        }

        $this->db_mv->query("
            DELETE FROM
                market_view_update_cache
            WHERE
                (market_view_update_cache.mvsrc_id, market_view_update_cache.mvsrc_product_id) IN (" . $this->db_mv->inTuple($batch_delete) . ")
        ");
    }


    private function hasVpeZwang(array $market_view_offer): bool
    {
        if ($market_view_offer['vpe_zwang'] === null || $market_view_offer['vpe_zwang'] === '0' || $market_view_offer['vpe_zwang'] === 0 || $market_view_offer['vpe'] === '') {
            return false;
        }

        //die vpe wird 1:1 von den Quellen übernommen, damit ist da relativ viel müll drin
        $vpe = strtolower($market_view_offer['vpe']);
        $vpe = str_replace(['mtr', 'pce', 'stk', 'ct', ''], '', $vpe);
        $vpe = str_replace(',', '.', $vpe);
        $vpe = (int)trim($vpe);

        return $vpe > 1;
    }

    private function updateProductSuppliersBestand(array $market_view_offer): void
    {
        static $supplier_lagers = null;

        if ($supplier_lagers === null) {
            $supplier_lagers = SupplierRepository::getSuppliersLagerIds();
        }

        if (!isset($supplier_lagers[$market_view_offer['supplier_id']])) {
            return;
        }

        $lager_id = $supplier_lagers[$market_view_offer['supplier_id']];

        switch ($market_view_offer['inventory_typ']) {
            case 'inventory':
                $bestand = $market_view_offer['inventory'];
                break;
            case 'availability':
                $bestand = self::getBestandForAvailability($market_view_offer['availability_id']);

                //sonderbehandlung aswo bei k11... die standardmässigen 6 stücken, limitieren spezielle am wochenende, den umsatz bei schnell drehern
                if ($bestand >= 5 && $market_view_offer['supplier_id'] == SuppliersConst::SUPPLIER_ID_EURAS && env::isK11()) {
                    $bestand = 35;
                }
                //

                break;
            default:
                $bestand = 0;
        }

        if ($market_view_offer['matching_status'] === ProductMatching::MATCHING_WRONG) {
            $bestand = 0;
        }

        $grund = 'CSV_UPDATE (' . SupplierRepository::getSupplierName($market_view_offer['supplier_id']) . ')';

        $booking_service = service_loader::getDiContainer()->get(ProductStockBookingService::class);
        $booking_service->bookAbsolute($market_view_offer['product_id'], $lager_id, $bestand, $grund);
    }

    /**
     * Workaround: durch das caching kommt es zu race conditions, zweischen dem offer sync und dem entfernen von matchings bei EAN änderungen.
     * Die Funktion prüft ob das Matching noch gültig ist und wenn nicht wird das jeweilige Angebot aus den Daten entfernt.
     * Das ist nicht sehr flexibel, aber erstmal OK.
     * Technisch sauber wäre es, das caching ganze rauszunehmen. Das würde aber massiv die SELECTs hochjagen.
     *
     * @param array $market_view_offers
     * @return array
     */
    private function clearInvalidCachedMatchingStatus(array $market_view_offers): array
    {
        $matching_ids = array_column($market_view_offers, 'matching_id');

        //annahme: er wird kein wrong verarbeitet! offers mit wrong werden entfernt.
        //-> aktuell ist das so, der status wird nur zum cleanen des bestands genutzt und kommt in dem Fall hier nicht an
        $found_matching_ids = $this->db_mv->query("
            SELECT
                market_view_matching.matching_id
            FROM
                market_view_matching
            WHERE
                market_view_matching.matching_id IN (" . $this->db_mv->in($matching_ids) . ") AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "'
        ")->asSingleArray();

        $missing_matching_ids = array_diff($matching_ids, $found_matching_ids);

        if ($missing_matching_ids) {
            foreach ($market_view_offers as $key => $market_view_offer) {
                if (in_array($market_view_offer['matching_id'], $missing_matching_ids)) {
                    unset($market_view_offers[$key]);
                }
            }
        }

        return $market_view_offers;
    }

    /**
     * löst die Verfügbarkeit in einen Bestand auf.
     * (pessimistisch)
     *
     * @param int $availability_id
     * @return int $bestand
     */
    public static function getBestandForAvailability(int $availability_id): int
    {
        switch ($availability_id) {
            case MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE:
                return 6;
            case MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE:
                return 2;
            default:
                return 0;
        }
    }
}
