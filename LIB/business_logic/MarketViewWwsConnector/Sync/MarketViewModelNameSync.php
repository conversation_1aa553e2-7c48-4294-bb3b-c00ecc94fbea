<?php

namespace wws\MarketViewWwsConnector\Sync;

use db;
use service_loader;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\Product;

class MarketViewModelNameSync
{
    public function run(): void
    {
        $db_mv = db::getInstance('market_view');
        $db = db::getInstance();

        $product_repository = service_loader::getProductRepository();

        $product_ids = $db->query("
            SELECT
                product.product_id
            FROM
                product
            WHERE
                product.model_name = ''
        ")->asSingleArray();

        $chunks = array_chunk($product_ids, 10000);

        foreach ($chunks as $product_ids) {
            $result = $db_mv->query("
                SELECT
                    market_view_matching.product_id,
                    market_view_product.model_name,
                    GROUP_CONCAT(DISTINCT market_view_product.model_name) AS t
                FROM
                    market_view_product INNER JOIN
                    market_view_matching ON (market_view_product.mvsrc_id = market_view_matching.mvsrc_id AND market_view_product.mvsrc_product_id = market_view_matching.mvsrc_product_id)
                WHERE
                    market_view_matching.product_id IN (" . $db_mv->in($product_ids) . ") AND
                    market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                    market_view_product.model_name != ''
                GROUP BY
                    market_view_product.product_id
            ")->asArray();

            if (!$result) {
                continue;
            }

            $products = $product_repository->searchByArray($result, 200);
            $products->edit(function (Product $product, array $data) {
                $product->setModelName($data['model_name']);
            });
        }
    }
}
