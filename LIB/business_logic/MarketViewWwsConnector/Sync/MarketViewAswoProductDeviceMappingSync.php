<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;
use bqp\Exceptions\DevException;
use wws\MarketView\MarketViewConst;
use wws\Supplier\SuppliersConst;

class MarketViewAswoProductDeviceMappingSync
{
    private db_generic $db;
    private db_generic $db_mv;
    private int $mvsrc_id;
    private int $supplier_id;

    private array $delete_queue = [];
    private ExtendedInsertQuery $insert_stmt;

    public function __construct(db_generic $db, db_generic $db_mv)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;
        $this->mvsrc_id = MarketViewConst::MVSRC_ID_EURAS;
        $this->supplier_id = SuppliersConst::SUPPLIER_ID_EURAS;

        $this->insert_stmt = new ExtendedInsertQuery($this->db, 'aswo_product_device_mapping', ['device_id', 'aswo_number']);
        $this->insert_stmt->setAutoexecute(5000);
    }

    public function run(): void
    {
        $all_device_ids = $this->db_mv->query("
            SELECT
                DISTINCT market_view_device.device_id
            FROM
                market_view_device INNER JOIN
                ext_aswo_device ON (
                    ext_aswo_device.mvsrc_device_id = market_view_device.mvsrc_device_id AND
                    ext_aswo_device.mvsrc_id = market_view_device.mvsrc_id
                )
            WHERE
                market_view_device.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_device.device_id > 0 AND
                ext_aswo_device.date_scrap_last IS NOT NULL                
        ")->asSingleArray();

        foreach (array_chunk($all_device_ids, 500) as $device_ids) {
            $this->syncDeviceIds($device_ids);
        }

        $this->flushQueues();
    }

    public function syncDeviceIds(array $device_ids): void
    {
        $market_view_devices = $this->getMarketViewDevicesWithAswoProductNumbers($device_ids);
        //@todo direkt in shopware machen? oder doch hier? -> tendenziell eher shopware... +damit kann schneller hin und her gewechselt werden / -event handling? / -kostet mehr resorucen? / +test global...
        //$market_view_devices = $this->filterAswoOffersThatExistsAsProductInDevice($market_view_devices);

        $wws_devices = $this->getWwsDevicesWithAswoProductNumbers($device_ids);

        foreach ($device_ids as $device_id) {
            $delete = array_diff($wws_devices[$device_id], $market_view_devices[$device_id]);
            $add = array_diff($market_view_devices[$device_id], $wws_devices[$device_id]);

            foreach ($delete as $aswo_number) {
                $this->queueDelete($device_id, $aswo_number);
            }

            foreach ($add as $aswo_number) {
                $this->queueAdd($device_id, $aswo_number);
            }
        }
    }

    public function getMarketViewDevicesWithAswoProductNumbers(array $device_ids): array
    {
        //DISTINCT -> ansonsten kann es zu doppelten Nummern kommen, wenn eine device_id auf mehrere geräte gematcht ist
        $market_view_devices = $this->db_mv->query("
            SELECT
                DISTINCT
                market_view_device.device_id,
                market_view_product_device.mvsrc_product_id
            FROM
                market_view_device INNER JOIN
                market_view_product_device ON (
                    market_view_device.mvsrc_device_id = market_view_product_device.mvsrc_device_id AND
                    market_view_device.mvsrc_id = market_view_product_device.mvsrc_id
                )
            WHERE
                market_view_device.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_device.device_id IN (" . $this->db_mv->in($device_ids) . ") AND
                market_view_product_device.is_online = 1
        ")->asMultiArraySingle('device_id', 'mvsrc_product_id');

        foreach ($device_ids as $device_id) {
            if (!isset($market_view_devices[$device_id])) {
                $market_view_devices[$device_id] = [];
            }
        }

        return $market_view_devices;
    }

    private function filterAswoOffersThatExistsAsProductInDevice(array $devices): array
    {
        $known_devices = $this->db->query("
            SELECT
                product_device_mapping.device_id,
                product_ek.gros_product_id
            FROM
                product_device_mapping INNER JOIN
                product_ek ON (
                    product_device_mapping.product_id = product_ek.product_id AND
                    product_ek.supplier_id = " . $this->supplier_id . "
                )
            WHERE
                product_device_mapping.device_id IN (" . $this->db->in(array_keys($devices)) . ")
        ")->asMultiArraySingle('device_id', 'gros_product_id');

        foreach ($known_devices as $device_id => $aswo_numbers) {
            $devices[$device_id] = array_diff($devices[$device_id], $aswo_numbers);
        }

        return $devices;
    }

    public function getWwsDevicesWithAswoProductNumbers(array $device_ids): array
    {
        $wws_devices = $this->db->query("
            SELECT
                aswo_product_device_mapping.device_id,
                aswo_product_device_mapping.aswo_number
            FROM
                aswo_product_device_mapping
            WHERE
                aswo_product_device_mapping.device_id IN (" . $this->db->in($device_ids) . ")
        ")->asMultiArraySingle('device_id', 'aswo_number');

        foreach ($device_ids as $device_id) {
            if (!isset($wws_devices[$device_id])) {
                $wws_devices[$device_id] = [];
            }
        }

        return $wws_devices;
    }


    private function queueAdd(int $device_id, string $aswo_number): void
    {
        $this->insert_stmt->add(['device_id' => $device_id, 'aswo_number' => $aswo_number]);
    }


    private function queueDelete(int $device_id, string $aswo_number): void
    {
        $this->delete_queue[] = ['device_id' => $device_id, 'aswo_number' => $aswo_number];

        if (count($this->delete_queue) > 1000) {
            $this->flushDeleteQueue();
        }
    }

    private function flushDeleteQueue(): void
    {
        if (!$this->delete_queue) {
            return;
        }

        $this->db->query("
            DELETE FROM
                aswo_product_device_mapping
            WHERE
                (aswo_product_device_mapping.device_id, aswo_product_device_mapping.aswo_number) IN (" . $this->db->inTuple($this->delete_queue) . ")
        ");

        $this->delete_queue = [];
    }

    public function flushQueues(): void
    {
        $this->insert_stmt->flush();
        $this->flushDeleteQueue();
    }

    public function deleteUnneededMappings(): void
    {
        throw new DevException('neh... lassen wir hier weg. machen wir in shopware');

        $this->db->query("
            DELETE
                aswo_product_device_mapping
            FROM
                aswo_product_device_mapping INNER JOIN 
                product_ek ON (             
                    product_ek.gros_product_id = aswo_product_device_mapping.aswo_number AND    
                    product_ek.supplier_id = " . $this->supplier_id . "
                ) INNER JOIN product_device_mapping ON (
                    product_device_mapping.product_id = product_ek.product_id AND
                    product_device_mapping.device_id = aswo_product_device_mapping.device_id
                )
        ");
    }
}
