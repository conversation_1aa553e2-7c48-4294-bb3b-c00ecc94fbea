<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use debug;
use Exception;
use wws\Country\CountryConst;
use wws\Country\CountryNotFoundException;
use wws\Country\CountryRepository;
use wws\Product\ProductBrand\ProductBrand;
use wws\Product\ProductConst;

class MarketViewGpsrSync
{
    private db_generic $db_mv;
    private array $ignore_brand_ids = [];

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;

        $this->ignore_brand_ids = [ProductConst::BRAND_ID_UNBEKANNT, ProductConst::BRAND_ID_SONSTIGE];
    }

    public function run(): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_gpsr.mv_gpsr_id,
                market_view_gpsr.name1,
                market_view_gpsr.name2,
                market_view_gpsr.street,
                market_view_gpsr.post_code,
                market_view_gpsr.city,
                market_view_gpsr.country,
                market_view_gpsr.email,
                market_view_gpsr.phone,
                market_view_gpsr.web,
                market_view_gpsr.misc,
                GROUP_CONCAT(DISTINCT market_view_hersteller.brand_id) AS brand_ids
            FROM
                market_view_gpsr INNER JOIN
                market_view_product ON (market_view_gpsr.mv_gpsr_id = market_view_product.mv_gpsr_id) INNER JOIN
                market_view_hersteller ON (market_view_hersteller.mv_hersteller_id = market_view_product.mv_hersteller_id) 
            WHERE
                market_view_hersteller.brand_id <> 0 AND
                market_view_hersteller.brand_id NOT IN (" . $this->db_mv->in($this->ignore_brand_ids) . ")
            GROUP BY
                market_view_gpsr.mv_gpsr_id
        ")->asArray();

        foreach ($result as $data) {
            $brand_ids = explode(',', $data['brand_ids']);

            foreach ($brand_ids as $brand_id) {
                $product_brand = new ProductBrand($brand_id);

                if ($product_brand->getGpsrStatus() !== ProductBrand::GPSR_STATUS_INVALID) {
                    continue;
                }

                try {
                    $this->copyToProductBrand($data, $product_brand);
                } catch (Exception $e) {
                    debug::dump($e->getMessage());
                    continue;
                }

                $product_brand->save();
            }
        }
    }

    public function getCountryId(string $country_field): int
    {
        try {
            return CountryRepository::getCountryByIsoSign2($country_field)->getCountryId();
        } catch (CountryNotFoundException) {
        }

        $map = [
            'germany' => 'Deutschland',
            'austria' => 'Österreich',
            'großbritannien' => 'Vereinigtes Königreich'
        ];

        $country_field = $map[strtolower($country_field)] ?? $country_field;

        $country_id = CountryRepository::getCountryIdByGermanCountryName($country_field);
        if ($country_id) {
            return $country_id;
        }

        throw new FatalException('Country could not be found ("' . $country_field . '")');
    }

    private function copyToProductBrand(array $data, ProductBrand $product_brand): void
    {
        $product_brand->setGpsrName1($data['name1']);
        $product_brand->setGpsrName2($data['name2']);
        $product_brand->setGpsrStreet($data['street']);
        $product_brand->setGpsrPostalCode($data['post_code']);
        $product_brand->setGpsrCity($data['city']);

        if ($data['country']) {
            $product_brand->setGpsrCountryId($this->getCountryId($data['country']));
        } elseif (is_numeric($data['post_code']) && strlen($data['post_code']) === 5) {
            $product_brand->setGpsrCountryId(CountryConst::COUNTRY_ID_DE);
        } else {
            throw new Exception('Country could not be determined');
        }

        $product_brand->setGpsrEmail($data['email']);
        $product_brand->setGpsrWeb($data['web']);
        $product_brand->setGpsrPhone($data['phone']);
    }

    public function copyByMvGpsrIdToProductBrand(int $mv_gpsr_id, ProductBrand $product_brand): void
    {
        $data = $this->db_mv->singleQuery("
            SELECT
                market_view_gpsr.mv_gpsr_id,
                market_view_gpsr.name1,
                market_view_gpsr.name2,
                market_view_gpsr.street,
                market_view_gpsr.post_code,
                market_view_gpsr.city,
                market_view_gpsr.country,
                market_view_gpsr.email,
                market_view_gpsr.phone,
                market_view_gpsr.web,
                market_view_gpsr.misc
            FROM
                market_view_gpsr
            WHERE
                market_view_gpsr.mv_gpsr_id = " . $mv_gpsr_id . "
        ");

        $this->copyToProductBrand($data, $product_brand);
    }
}
