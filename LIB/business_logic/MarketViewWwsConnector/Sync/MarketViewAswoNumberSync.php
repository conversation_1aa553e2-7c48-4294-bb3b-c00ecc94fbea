<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use bqp\Model\SmartDataEntityNotFoundException;
use debug;
use wws\MarketView\MarketViewConst;
use wws\ProductDevice\ProductDeviceRepository;

class MarketViewAswoNumberSync
{
    private db_generic $db_mv;
    private int $mvsrc_id = MarketViewConst::MVSRC_ID_EURAS;
    private ProductDeviceRepository $repo;

    public function __construct(db_generic $db_mv, ProductDeviceRepository $repo)
    {
        $this->db_mv = $db_mv;
        $this->repo = $repo;
    }

    public function run(): void
    {
        $device_ids = $this->db_mv->query("
            SELECT
                market_view_device.mvsrc_device_id,
                market_view_device.device_id
            FROM
                market_view_device
            WHERE
                market_view_device.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_device.device_id > 0
            GROUP BY
                market_view_device.device_id
        ")->asSingleArray('device_id'); // wir haben entschieden, doppelt gematchte hier bewusst zu ignorieren

        foreach ($device_ids as $device_id => $aswo_number) {
            try {
                $device = $this->repo->load($device_id);
                $device->setAswoNumber($aswo_number);
                $this->repo->save($device);
            } catch (SmartDataEntityNotFoundException $e) {
                debug::dump('Device not found: ' . $device_id);
                debug::dump($e->getMessage());
            }
        }
    }
}
