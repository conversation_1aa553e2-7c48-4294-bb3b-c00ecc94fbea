<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;
use wws\Product\ProductConst;

class WwsProductTypeSync
{
    private db_generic $db;
    private db_generic $db_mv;

    public function __construct(db_generic $db, db_generic $db_mv)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;
    }

    /**
     * Auf Basis des Kategroiemappings von MarketView werden automatisch product_type mappings erstellt. Der product_type wird aus dem Kategoriestamm des WWS genommen.
     *
     * Überschreibt Mappings, sollte aber kein Problem sein.
     */
    public function updateMarketViewProductTypeMappingByCategories(): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_cat_mapping.mv_cat_id,
                market_view_cat_mapping.cat_id
            FROM
                market_view_cat_mapping
            WHERE
                market_view_cat_mapping.cat_tree_id = 1
        ")->asSingleArray('mv_cat_id');


        $cat_ids = array_unique(array_values($result));

        $mapping = $this->db->query("
            SELECT
                product_cat.cat_id,
                product_cat.product_type
            FROM
                product_cat
            WHERE
                product_cat.cat_id IN (" . $this->db->in($cat_ids) . ") AND
                product_cat.product_type NOT IN ('', '" . ProductConst::PRODUCT_TYPE_UNBEKANNT . "')
        ")->asSingleArray('cat_id');


        $insert = new ExtendedInsertQuery($this->db_mv, 'market_view_cat_mapping_product_type', ['mv_cat_id', 'product_type']);
        $insert->setAutoUpdate(true);

        foreach ($result as $mv_cat_id => $cat_id) {
            $product_type = $mapping[$cat_id] ?? null;

            if ($product_type) {
                $insert->add(['mv_cat_id' => $mv_cat_id, 'product_type' => $product_type]);
            }
        }

        $insert->execute();
    }
}
