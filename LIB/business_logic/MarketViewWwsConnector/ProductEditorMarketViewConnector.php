<?php

namespace wws\MarketViewWwsConnector;

use bqp\db\db_generic;
use Exception;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;

class ProductEditorMarketViewConnector
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    /**
     * @return ProductEditorMarketViewConnectorResult
     */
    public function getEmptyResult(): ProductEditorMarketViewConnectorResult
    {
        return new ProductEditorMarketViewConnectorResult();
    }

    /**
     * @param int $product_id
     * @thows ProductEditorMarketViewBridgeException
     * @return ProductEditorMarketViewConnectorResult
     */
    public function getMarketViewProductData(int $product_id): ProductEditorMarketViewConnectorResult
    {
        if (!$product_id) {
            return $this->getEmptyResult();
        }

        $bridge_result = new ProductEditorMarketViewConnectorResult();

        try {
            $result = $this->db_mv->query("
                SELECT
                    market_view_source.supplier_id,

                    market_view_product.mvsrc_id,
                    market_view_product.mvsrc_product_id,

                    market_view_product.product_name,

                    market_view_product.availability_id,
                    market_view_product.mv_availability,

                    market_view_product.versand_netto,

                    market_view_product.date_updated,
                    market_view_product.product_status,
                    
                    market_view_matching.matching_status
                FROM
                    market_view_matching INNER JOIN
                    market_view_product ON (market_view_matching.mvsrc_id = market_view_product.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_product.mvsrc_product_id) INNER JOIN
                    market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id) INNER JOIN
                    market_view_availability ON (market_view_product.availability_id = market_view_availability.availability_id)
                WHERE
                    market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                    market_view_matching.product_id = '" . $product_id . "'
            ");

            foreach ($result as $row) {
                $bridge_result->addOffer($row);
            }
        } catch (Exception $e) {
            throw new ProductEditorMarketViewConnectorException("MarketView ist zurzeit nicht verfügbar.", 0, $e);
        }

        return $bridge_result;
    }
}
