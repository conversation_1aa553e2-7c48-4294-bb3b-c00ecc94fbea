<?php

namespace wws\MarketViewWwsConnector;

use bqp\db\db_generic;

class MarketViewAutocreate
{
    private db_generic $db_mv;

    public function __construct(db_generic $db_mv)
    {
        $this->db_mv = $db_mv;
    }

    public function setAutocreateForMvHersteller(int $mvsrc_id, int $mv_hersteller_id, bool $status): void
    {
        $this->db_mv->query("
			INSERT INTO
				market_view_autocreate_hersteller
			SET
				market_view_autocreate_hersteller.mvsrc_id = '" . $mvsrc_id . "',
				market_view_autocreate_hersteller.mv_hersteller_id = '" . $mv_hersteller_id . "',
				market_view_autocreate_hersteller.status = '" . ($status ? 1 : 0) . "'
			ON DUPLICATE KEY UPDATE
				market_view_autocreate_hersteller.status = VALUES(market_view_autocreate_hersteller.status)
		");
    }
}
