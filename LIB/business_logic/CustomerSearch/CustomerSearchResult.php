<?php

namespace wws\CustomerSearch;

class CustomerSearchResult
{
    protected $search_phrases;
    protected $result;
    protected $type_map;

    public function __construct(array $search_phrase, array $result, array $type_map = [])
    {
        $this->search_phrases = $search_phrase;
        $this->result = $result;

        $this->type_map = $type_map;
    }

    /**
     * @return int[]
     */
    public function getCustomerIds(): array
    {
        return array_keys($this->result);
    }


    public function count(): int
    {
        return count($this->result);
    }

    public function getMatchType(int $customer_id): string
    {
        $daten = $this->result[$customer_id];

        $fulltexts = explode('||', $daten['customer_fulltext']);

        $types = [];

        $phrases = $this->search_phrases;

        foreach ($fulltexts as $fulltext) {
            foreach ($phrases as $key => $search_phrase) {
                if (strlen($fulltext) < 5 || strpos($fulltext, $search_phrase) === false) {
                    continue;
                }

                unset($phrases[$key]); //wenn phrase einmal gefunden, nicht mehr weiter betrachten

                if ($fulltext[4] !== '=') {
                    continue;
                }

                $type = substr($fulltext, 0, 4);
                $types[$type] = $type;
            }
        }

        $result = [];

        foreach ($types as $type) {
            if (isset($this->type_map[$type])) {
                $result[] = $this->type_map[$type];
            } else {
                $result[] = $type;
            }
        }

        return implode(', ', $result);
    }
}
