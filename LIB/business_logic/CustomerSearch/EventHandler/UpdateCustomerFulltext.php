<?php

namespace wws\CustomerSearch\EventHandler;

use bqp\db\db_generic;
use wws\CustomerSearch\CustomerSearchMysqlFulltext;
use wws\Order\Event\EventOrderChange;
use wws\Supplier\Event\EventSupplierOrderChange;
use wws\Tracking\Event\NewTrackingAdded;

class UpdateCustomerFulltext
{
    private CustomerSearchMysqlFulltext $customer_search;
    private db_generic $db;

    public function __construct(CustomerSearchMysqlFulltext $customer_search, db_generic $db)
    {
        $this->customer_search = $customer_search;
        $this->db = $db;
    }

    public function handle(object $event): void
    {
        if (method_exists($event, 'getCustomerId')) {
            $this->customer_search->rebuildIndexForCustomer($event->getCustomerId());
        }
    }

    public function handleOrderChange(EventOrderChange $event_order_change): void
    {
        $entity_changes = $event_order_change->getEntityChanges();

        $is_relevant = $entity_changes->isChange([
            'order.auftnr',
            'order.order_aktiv',
            'order.kunde_zeichen',
            'order.kunde_beleg',
            'order.zahlungs_id',
            'order.payment_referenz',
            'order_addresses.*',
        ]);

        if ($is_relevant) {
            $this->customer_search->rebuildIndexForCustomer($event_order_change->getOrder()->getCustomerId());
        }
    }

    public function handleTrackingAdded(NewTrackingAdded $new_tracking_added): void
    {
        $customer_id = (int)$this->db->fieldQuery("
            SELECT 
                warenausgang_lieferschein.customer_id
            FROM
                warenausgang_lieferschein
            WHERE
                warenausgang_lieferschein.lieferschein_id = '" . $new_tracking_added->getLieferscheinId() . "'
        ");

        $this->customer_search->rebuildIndexForCustomer($customer_id);
    }

    public function handleSupplierOrderChange(EventSupplierOrderChange $supplier_order_change): void
    {
        $order_id = $supplier_order_change->getSupplierOrder()->getOrderId();

        if (!$order_id) {
            return;
        }

        if (!$supplier_order_change->getChanges()->isChange(['externe_referenz_1', 'externe_referenz_2'])) {
            return;
        }

        $customer_id = $this->db->fieldQuery("
            SELECT
                orders.customer_id
            FROM
                orders
            WHERE
                orders.order_id = '" . $order_id . "'       
        ");

        $this->customer_search->rebuildIndexForCustomer($customer_id);
    }
}
