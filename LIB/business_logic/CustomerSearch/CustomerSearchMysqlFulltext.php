<?php

namespace wws\CustomerSearch;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use Commercetools\Api\Models\Customer\CustomerCollection;
use JetBrains\PhpStorm\ArrayShape;
use payment_repository;
use wws\Customer\CustomerConst;

class CustomerSearchMysqlFulltext implements CustomerSearch
{
    public static $type_map = [
        'pref' => 'Zahlungsreferenz',
        'orid' => 'OrderId',
        'waid' => 'OrderItemId',
        'kuze' => 'Kundenzeichen',
        'kube' => 'Kundenbeleg',
        'bele' => 'Belegnummer',
        'usid' => 'UstIdNr',
        'liid' => 'Lieferschein',
        'trid' => 'Tracking',
        'cuid' => 'Kundennummer',
        'adrs' => 'Adressen',
        'sure' => 'Lieferanten-Referenz',
        'sref' => 'Shopreferenz'
    ];

    private db_generic $db;
    private array $shop_ids = [];

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function setContextShopIds(?array $shop_ids): void
    {
        $this->shop_ids = $shop_ids ?? [];
    }

    public function search(string $search_phrase): array
    {
        $search_phrase = $this->normalizeSearchPhrase($search_phrase)['search_string'];

        $result = $this->db->query("
            SELECT
                customer_fulltext_search.customer_id
            FROM
                customer_fulltext_search
            WHERE
                MATCH(customer_fulltext_search.customer_fulltext) AGAINST ('" . $this->db->escape($search_phrase) . "' IN BOOLEAN MODE)
                " . $this->getShopIdAsSqlWhere() . "
            LIMIT
                2500
        ");

        return $result->asSingleArray();
    }

    /**
     * @param string $search_phrase
     * @return CustomerSearchResult
     */
    public function searchExtended(string $search_phrase): CustomerSearchResult
    {
        $search = $this->normalizeSearchPhrase($search_phrase);

        $result = $this->db->query("
            SELECT
                customer_fulltext_search.customer_id,
                MATCH(customer_fulltext_search.customer_fulltext) AGAINST ('" . $this->db->escape($search['search_string']) . "') AS score,
                customer_fulltext_search.customer_fulltext
            FROM
                customer_fulltext_search
            WHERE
                MATCH(customer_fulltext_search.customer_fulltext) AGAINST ('" . $this->db->escape($search['search_string']) . "' IN BOOLEAN MODE)
                " . $this->getShopIdAsSqlWhere() . "
            LIMIT
                2500
        ")->asArray('customer_id');

        $search_result = new CustomerSearchResult($search['search_tokens'], $result, self::$type_map);

        return $search_result;
    }

    #[ArrayShape(['search_string' => "string", 'search_tokens' => "array"])]
    private function normalizeSearchPhrase(string $search_string): array
    {
        $search_string = str_replace(['<', '>', '~', '*', '"', '(', ')'], ' ', $search_string);
        $search_string = trim($search_string, '-+');

        $search_string = $this->normalizeString($search_string);

        $tokens = explode(' ', $search_string);
        $search_string = '';
        $search_tokens = [];
        foreach ($tokens as $token) {
            if (preg_match('~str$~', $token)) { //straße
                $normalized_token = $this->normalizeStreet($token);
                $search_tokens[] = $normalized_token;
                $search_string .= ' +' . $normalized_token;
            } elseif (preg_match('~^[a-z]*$~i', $token)) {
                $search_string .= ' +(' . $token;
                $search_string .= ' ' . metaphone($token) . ')';

                $search_tokens[] = $token;
                $search_tokens[] = metaphone($token);
            } elseif (
                preg_match('~^[-0-9]{3,}$~', $token) ||
                str_contains($token, '-')
            ) {
                $search_string .= ' +"' . $token . '"';
                $search_tokens[] = $token;
            } else {
                $search_string .= ' +' . $token;
                $search_tokens[] = $token;
            }
        }

        $search_string = trim($search_string);

        return ['search_string' => $search_string, 'search_tokens' => $search_tokens];
    }


    public function rebuildIndex(): void
    {
        $result = $this->db->query("
            SELECT
                customers.customer_id
            FROM
                customers
        ");

        foreach ($result as $daten) {
            $this->rebuildIndexForCustomer($daten['customer_id']);
        }
    }

    public function rebuildIndexForCustomer(int $customer_id): void
    {
        static $has_group_concat_len_set = false;

        if ($customer_id === 1) {
            return;
        }

        if (!$has_group_concat_len_set) {
            $this->db->query("SET SESSION group_concat_max_len = 1000000;");
            $has_group_concat_len_set = true;
        }

        $daten = $this->db->singleQuery("
            SELECT
                customers.customer_id,
                customers.customer_nr,
                customers.anrede,
                customers.firma,
                customers.name,
                customers.vorname,
                customers.strasse,
                customers.adresse2,
                customers.plz,
                customers.ort,

                customers.telefon,
                customers.telefon,
                customers.mobil,
                customers.fax,
                customers.email,
                customers.ustidnr,
                customers.shop_id,
                customers.customer_status,
                
                YEAR(MAX(orders.added)) AS max_year,

                einst_countries.name AS land,
                GROUP_CONCAT(DISTINCT orders.order_id SEPARATOR ' ') AS order_ids,
                GROUP_CONCAT(DISTINCT orders.shop_referenz SEPARATOR ' ') AS shop_referenzes,
                GROUP_CONCAT(DISTINCT orders.shop_referenz_2 SEPARATOR ' ') AS shop_referenzes_2,
                GROUP_CONCAT(DISTINCT order_item.auftnr SEPARATOR ' ') AS auftraege,
                GROUP_CONCAT(DISTINCT orders.payment_referenz SEPARATOR ' ') AS payment_referenzes,
                GROUP_CONCAT(DISTINCT orders.payment_referenz_2 SEPARATOR ' ') AS payment_referenzes_2,
                GROUP_CONCAT(DISTINCT orders.payment_referenz_3 SEPARATOR ' ') AS payment_referenzes_3,
                GROUP_CONCAT(DISTINCT orders.zahlungs_id SEPARATOR ' ') AS zahlungsarten_ids,
                GROUP_CONCAT(DISTINCT buchhaltung_rechnung.rechnungs_nr SEPARATOR ' ') AS rechnungs_nrs,
                GROUP_CONCAT(DISTINCT order_item.order_item_id SEPARATOR ' ') AS order_item_ids,
                GROUP_CONCAT(DISTINCT orders.kunde_zeichen SEPARATOR ' ') AS kunde_zeichen,
                GROUP_CONCAT(DISTINCT orders.kunde_beleg SEPARATOR ' ') AS kunde_beleg,
                GROUP_CONCAT(DISTINCT warenausgang_lieferschein.lieferschein_id SEPARATOR ' ') AS lieferschein_ids,
                GROUP_CONCAT(DISTINCT warenausgang_lieferschein_tracking_ids.tracking_id SEPARATOR ' ') AS tracking_ids,
                MAX(orders.order_aktiv) AS orders_aktiv,
                                    
                GROUP_CONCAT(DISTINCT order_addresses.name SEPARATOR ' ') AS adr_name,
                GROUP_CONCAT(DISTINCT order_addresses.vorname SEPARATOR ' ') AS adr_vorname,
                GROUP_CONCAT(DISTINCT order_addresses.firma SEPARATOR ' ') AS adr_firma,
                GROUP_CONCAT(DISTINCT order_addresses.plz SEPARATOR ' ') AS adr_plz,
                GROUP_CONCAT(DISTINCT order_addresses.ort SEPARATOR ' ') AS adr_ort,
                
                GROUP_CONCAT(DISTINCT order_addresses.telefon SEPARATOR '|||') AS adr_telefon,
                GROUP_CONCAT(DISTINCT order_addresses.adresse1 SEPARATOR '|||') AS adr_adresse1,
                GROUP_CONCAT(DISTINCT order_addresses.adresse2 SEPARATOR '|||') AS adr_adresse2                
            FROM
                customers INNER JOIN
                einst_countries ON (customers.country_id = einst_countries.country_id) LEFT JOIN
                orders ON (customers.customer_id = orders.customer_id) LEFT JOIN
                order_item ON (orders.order_id = order_item.order_id) LEFT JOIN
                buchhaltung_rechnung ON (orders.order_id = buchhaltung_rechnung.order_id) LEFT JOIN
                warenausgang_lieferschein ON (orders.order_id = warenausgang_lieferschein.order_id) LEFT JOIN
                warenausgang_lieferschein_tracking_ids ON (warenausgang_lieferschein.lieferschein_id = warenausgang_lieferschein_tracking_ids.lieferschein_id) LEFT JOIN
                order_addresses ON (orders.order_id = order_addresses.order_id)
            WHERE
                customers.customer_id = '$customer_id'
            GROUP BY
                customers.customer_id
        ");

        if (!$daten) {
            throw new DevException('rebuild fulltext index for customer ' . $customer_id . ' failed.');
        }

        //bei Kunden ohne Bestellungen sind die Werte teilweise NULL -> zu leeren strings konvertieren, damit das weiter unten keine Probleme gibt
        foreach ($daten as $key => $value) {
            $daten[$key] = (string)$value;
        }

        $daten['payment_referenzes'] = trim($daten['payment_referenzes'] . ' ' . $daten['payment_referenzes_2'] . ' ' . $daten['payment_referenzes_3']);

        $search = [];

        $search[] = $daten['customer_nr'];
        $search[] = $daten['max_year'];
        $search[] = $daten['anrede'];
        $search[] = $daten['firma'];

        $search[] = $daten['name'];
        $search[] = $daten['vorname'];

        $search[] = metaphone($daten['name']);
        $search[] = metaphone($daten['vorname']);

        $search[] = $this->normalizeStreet($daten['strasse']);
        $search[] = $this->normalizeStreet($daten['adresse2']);
        $search[] = $daten['plz'];
        $search[] = $daten['ort'];
        $search[] = $daten['land'];

        $search[] = preg_replace('~[^0-9]{1,}~', '', $daten['telefon']);
        $search[] = preg_replace('~[^0-9]{1,}~', '', $daten['fax']);
        $search[] = preg_replace('~[^0-9]{1,}~', '', $daten['mobil']);

        $search[] = $daten['email'];

        $search = array_merge($search, explode('@', $daten['email']));

        if ($daten['orders_aktiv'] && $daten['customer_status'] != CustomerConst::CUSTOMER_STATUS_DELETE) {
            $search[] = 'aktiv';
        }

        $search[] = $daten['auftraege'];

        $zahlungsarten = explode(' ', $daten['zahlungsarten_ids']);
        foreach ($zahlungsarten as $zahlungsart) {
            $search[] = payment_repository::getZahlungsart($zahlungsart);
        }

        $search[] = '||cuid= ' . $daten['customer_id'];

        if ($daten['ustidnr']) {
            $search[] = '||usid= ' . $daten['ustidnr'];
        }

        if ($daten['order_ids']) {
            $search[] = '||orid= ' . $daten['order_ids'];
        }
        if ($daten['payment_referenzes']) {
            $search[] = '||pref= ' . $daten['payment_referenzes'];
        }

        $shop_referenzes = trim($daten['shop_referenzes'] . ' ' . $daten['shop_referenzes_2']);
        if ($shop_referenzes) {
            $search[] = '||sref= ' . $shop_referenzes;
        }

        if ($daten['order_item_ids']) {
            $search[] = '||waid= ' . $daten['order_item_ids'];
        }
        if ($daten['kunde_zeichen']) {
            $search[] = '||kuze= ' . $daten['kunde_zeichen'];
            $search[] = '||kuze= ' . str_replace([' ', '.', '+'], '', $daten['kunde_zeichen']);
        }
        if ($daten['kunde_beleg']) {
            $search[] = '||kube= ' . $daten['kunde_beleg'];
            $search[] = '||kube= ' . str_replace([' ', '.', '+'], '', $daten['kunde_beleg']);
        }
        if ($daten['rechnungs_nrs']) {
            $search[] = '||bele= ' . $daten['rechnungs_nrs'];
        }
        if ($daten['lieferschein_ids']) {
            $search[] = '||liid= ' . $daten['lieferschein_ids'];
        }
        if ($daten['tracking_ids']) {
            $search[] = '||trid= ' . $daten['tracking_ids'];
        }

        $supplier_order_reference = $this->getSupplierOrderReferences($customer_id);
        if ($supplier_order_reference) {
            $search[] = '||sure= ' . $supplier_order_reference;
        }

        $search[] = '||adrs= ';

        $search[] = $daten['adr_name'];
        $search[] = $daten['adr_vorname'];
        $search[] = $daten['adr_firma'];
        $search[] = $daten['adr_plz'];
        $search[] = $daten['adr_ort'];

        $tels = explode('|||', $daten['adr_telefon']);
        foreach ($tels as $tel) {
            $search[] = preg_replace('~[^0-9]{1,}~', '', $tel);
        }

        $adrs = explode('|||', $daten['adr_adresse1']);
        foreach ($adrs as $adr) {
            $search[] = $this->normalizeStreet($adr);
        }
        $adrs = explode('|||', $daten['adr_adresse2']);
        foreach ($adrs as $adr) {
            $search[] = $this->normalizeStreet($adr);
        }

        $search = array_filter($search);

        $search_tokens = [];

        foreach ($search as $value) {
            $value = $this->normalizeString($value);

            if (strlen($value) < 3) {
                continue;
            }

            $search_tokens[] = $value;
        }

        $search_tokens = array_unique($search_tokens);

        $this->writeToIndex($customer_id, $daten['shop_id'], $search_tokens);
    }

    private function writeToIndex(int $customer_id, int $shop_id, array $search_tokens): void
    {
        //blindes "insert into ... on duplicate" führt teilweise zu deadlocks.
        //->vorher prüfen um unnötige locks auf den PK zu vermeiden

        $customer_fulltext = implode(' ', $search_tokens);

        $act = $this->db->singleQuery("
            SELECT
                customer_fulltext_search.customer_id,
                customer_fulltext_search.customer_fulltext
            FROM
                customer_fulltext_search
            WHERE
                customer_fulltext_search.customer_id = $customer_id
        ");

        if (!$act) {
            $this->db->query("
                INSERT INTO
                    customer_fulltext_search
                SET
                    customer_fulltext_search.customer_id = $customer_id,
                    customer_fulltext_search.shop_id = $shop_id,
                    customer_fulltext_search.customer_fulltext = '" . $this->db->escape($customer_fulltext) . "'
                AS new_values
                ON DUPLICATE KEY UPDATE
                    customer_fulltext_search.customer_fulltext = new_values.customer_fulltext
            ");
            return;
        }

        if ($act['customer_fulltext'] === $customer_fulltext) {
            return;
        }

        $this->db->query("
            UPDATE
                customer_fulltext_search
            SET
                customer_fulltext_search.customer_fulltext = '" . $this->db->escape($customer_fulltext) . "'
            WHERE
                customer_fulltext_search.customer_id = $customer_id
        ");
    }

    private function normalizeString(string $value): string
    {
        $value = trim($value);
        $value = mb_strtolower($value, 'UTF-8');
        $value = str_replace('ß', 'ss', $value);
        $value = str_replace('ä', 'ae', $value);
        $value = str_replace('ö', 'oe', $value);
        $value = str_replace('ü', 'ue', $value);

        $value = preg_replace_callback('~([\w][-\w.]+@[-\w.]+\.[a-z]{2,4})~u', function ($daten) {
            $mail = $daten[1];

            $mail = str_replace('.', '', $mail);
            $mail = str_replace('-', '', $mail);
            $mail = str_replace('_', '', $mail);
            $mail = str_replace('#', '', $mail);
            $mail = str_replace('~', '', $mail);
            $mail = str_replace('@', 'at', $mail);

            return $mail;
        }, $value);

        $value = str_replace('@', 'at', $value);

        return $value;
    }

    private function normalizeStreet(string $street): string
    {
        $street = $this->normalizeString($street);

        $street = str_replace('str.', 'strasse', $street);

        $street = preg_replace('~str(/s|$)~', 'strasse\\2', $street);

        return $street;
    }

    private function getShopIdAsSqlWhere(): string
    {
        if ($this->shop_ids) {
            return " AND customer_fulltext_search.shop_id IN (" . $this->db->in($this->shop_ids) . ") ";
        }

        return '';
    }

    private function getSupplierOrderReferences(int $customer_id): string
    {
        //referenzen von lieferantenbestellungen (wollte ich oben in den query jetzt nicht noch mit reinknallen)
        //nur auf direktbestellungen begrenzt
        $result = $this->db->query("
            SELECT
                supplier_order.externe_referenz_1,
                supplier_order.externe_referenz_2
            FROM
                orders INNER JOIN
                supplier_order ON (orders.order_id = supplier_order.order_id)
            WHERE
                orders.customer_id = $customer_id
            GROUP BY
                supplier_order.supplier_order_id
        ");

        $references = [];

        foreach ($result as $row) {
            $references[$row['externe_referenz_1']] = $row['externe_referenz_1'];
            $references[$row['externe_referenz_2']] = $row['externe_referenz_2'];
        }
        unset($references['']);

        return implode(' ', $references);
    }
}
