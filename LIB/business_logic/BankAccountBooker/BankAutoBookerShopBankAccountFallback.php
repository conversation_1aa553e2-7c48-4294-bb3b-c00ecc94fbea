<?php

namespace wws\BankAccountBooker;

use bqp\db\db_generic;
use output;
use wws\BankAccount\BankTransaction;
use wws\BankAccount\BankTransactionRepository;
use wws\Order\OrderConst;
use wws\Order\OrderMemo;
use wws\Order\OrderPaymentReceipt;
use wws\Order\OrderRepository;

class BankAutoBookerShopBankAccountFallback
{
    private db_generic $db;
    private int $account_id;
    private BankAutoBookerLog $log;
    private BankTransactionRepository $bank_transaction_repository;
    private OrderRepository $order_repository;

    /**
     * Welche Zahlungsarten auf Doppelzahlungen geprüft werden sollen (bei über 3t Plattformen abgewickelte Bestellungen überweisen Kunden gern doppelt)
     */
    private array $multipay_zahlungs_ids = [];


    public function __construct(int $account_id, BankTransactionRepository $bank_transaction_repository, BankAutoBookerLog $log, db_generic $db, OrderRepository $order_repository)
    {
        $this->db = $db;
        $this->account_id = $account_id;

        $this->bank_transaction_repository = $bank_transaction_repository;

        $this->log = $log;

        $this->multipay_zahlungs_ids = [
            OrderConst::PAYMENT_KREDITKARTE,
            OrderConst::PAYMENT_PAYPAL,
            OrderConst::PAYMENT_AMAZON,
            OrderConst::PAYMENT_CHECK24,
            OrderConst::PAYMENT_CROWDFOX,
            OrderConst::PAYMENT_NACHNAHME
        ];

        $this->order_repository = $order_repository;
    }

    public function bookTransactions()
    {
        $result = $this->db->query("
            SELECT
                buchhaltung_bank_transactions.transaction_id,
                
                orders.zahlungs_id,
                orders.order_amount_net,
                orders.order_amount_gross,
                IF(orders.tax_status = " . OrderConst::TAX_STATUS_NORMAL . ", orders.order_amount_gross, orders.order_amount_net) AS order_amount
            FROM
                buchhaltung_bank_transactions LEFT JOIN
                orders ON (buchhaltung_bank_transactions.order_id = orders.order_id)
            WHERE
                buchhaltung_bank_transactions.account_id = '" . $this->account_id . "' AND
                buchhaltung_bank_transactions.transaction_status IN ('failed')
        ")->addCallback(function ($row) {
            $row['bank_transaction'] = $this->bank_transaction_repository->load($row['transaction_id']);

            return $row;
        })->asArray();

        foreach ($result as $row) {
            $bank_transaction = $row['bank_transaction'];
            unset($row['bank_transaction']);
            $this->processTransaction($bank_transaction, $row);
        }
    }


    protected function processTransaction(BankTransaction $transaction, array $extra_data): void
    {
        if ($this->processCanceledOrderPay($transaction, $extra_data)) {
            return;
        }
        if ($this->processOverpayment($transaction, $extra_data)) {
            return;
        }
        if ($this->processOverpaymentMulti($transaction, $extra_data)) {
            return;
        }
    }

    protected function processCanceledOrderPay(BankTransaction $transaction, array $extra_data): bool
    {
        if ($extra_data['order_amount'] > 0) {
            return false;
        }

        //mit den transaktionen machen wir erstmal garnix -> die müssen ggf. wiederbelebt werden (das automatisch ist heiß -> storno grund prüfen und so :/)

        return true;
    }

    /**
     * sucht Zahlungseingänge bei den die Zahlung häher ist als die Forderung und verbucht diese
     *
     * @param BankTransaction $transaction
     * @param mixed $extra_data
     */
    protected function processOverpayment(BankTransaction $transaction, $extra_data): bool
    {
        if ($transaction->getAmount() - $extra_data['order_amount'] < 0) {
            return false;
        }

        $message = 'Zahlungseingang (' . output::formatPrice($transaction->getAmount()) . ') höher als Forderung (' . output::formatPrice($extra_data['order_amount']) . ').';

        $order = $this->order_repository->load($transaction->getOrderId());
        $payment = new OrderPaymentReceipt();
        $payment->setAmount($transaction->getAmount());
        $payment->setMailPaymentReceipt(true);
        $payment->setBemerkung($message);
        $order->addPaymentReceipt($payment);

        $memo = new OrderMemo($message, OrderMemo::BUCHHALTUNG);
        $memo->setAutoMemo(true);
        $order->addOrderMemo($memo);

        $this->order_repository->save($order);

        $this->log->addEntry($transaction, $this->log::BOOK_OVERPAY, $message);

        $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_BOOKED);
        $this->bank_transaction_repository->save($transaction);

        return true;
    }

    protected function processOverpaymentMulti(BankTransaction $transaction, $extra_data): bool
    {
        if (!in_array($extra_data['zahlungs_id'], $this->multipay_zahlungs_ids)) {
            return false;
        }


        if (abs($transaction->getAmount() - $extra_data['order_amount']) > 0.01) {
            return false;
        }

        $message = 'Zahlungseingang per Überweisung (' . output::formatPrice($transaction->getAmount()) . ') obwohl anderes Zahlungsmittel.';

        $order = $this->order_repository->load($transaction->getOrderId());
        $payment = new OrderPaymentReceipt();
        $payment->setAmount($transaction->getAmount());
        $payment->setZahlungsId(OrderConst::PAYMENT_RECHNUNG);
        $payment->setMailPaymentReceipt(false);
        $payment->setBemerkung($message);
        $order->addPaymentReceipt($payment);

        $memo = new OrderMemo($message, OrderMemo::BUCHHALTUNG);
        $memo->setAutoMemo(true);
        $order->addOrderMemo($memo);

        $this->order_repository->save($order);

        $this->log->addEntry($transaction, $this->log::BOOK_OVERPAY_MULTI, $message);

        $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_BOOKED);
        $this->bank_transaction_repository->save($transaction);

        return true;
    }
}