<?php

namespace wws\BankAccountBooker;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use wws\BankAccount\BankTransaction;

class BankAutoBookerLog
{
    public const BOOK_OK = 'ok';
    public const BOOK_OVERPAY = 'overpay';
    public const BOOK_CANCELED_ORDER = 'canceled_order';
    public const BOOK_OVERPAY_MULTI = 'overpay_multi';
    public const BOOK_UNDERPAY = 'underpay';

    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function addEntry(BankTransaction $bank_transaction, string $status, string $message): void
    {
        $entry = [
            'account_id' => $bank_transaction->getAccountId(),
            'transaction_id' => $bank_transaction->getTransactionId(),
            'transaction_log_date' => DateObj::init()->db('datetime'),
            'transaction_log_status' => $status,
            'message' => $message
        ];

        $this->db->simpleInsert('buchhaltung_bank_transactions_log', $entry);
    }
}
