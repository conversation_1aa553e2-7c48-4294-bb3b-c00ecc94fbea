<?php

namespace wws\BankAccountBooker;

use bqp\Date\DateObj;
use wws\BankAccount\BankTransaction;
use wws\BankAccount\BankTransactionRepository;
use wws\BankAccountBooker\TransactionBooker\TransactionBooker;
use wws\BankAccountBooker\TransactionClassificator\TransactionClassificator;

/**
 * Class BankAutoBookerBankAccount
 *
 * zum buche<PERSON>(Kunden) Bankkonten
 * @package wws\buchhaltung\Bank
 */
class BankAutoBookerShopBankAccount
{
    /**
     * @var int
     */
    private $account_id;

    /**
     * @var null|DateObj
     */
    private $date_start = null;

    /**
     * @var null|DateObj
     */
    private $date_end = null;

    /**
     * @var TransactionClassificator[]
     */
    private $transaction_classificator = [];

    /**
     * @var TransactionBooker
     */
    private $transaction_booker = null;

    /**
     * @var BankTransactionRepository
     */
    private $transaction_repository;

    public function __construct(int $account_id, BankTransactionRepository $transaction_repository)
    {
        $this->account_id = $account_id;
        $this->transaction_repository = $transaction_repository;
    }

    public function setDateRange(DateObj $date_start, DateObj $date_end)
    {
        $this->date_start = $date_start;
        $this->date_end = $date_end;
    }

    public function getAccountId(): int
    {
        return $this->account_id;
    }

    public function addTransactionClassificator(TransactionClassificator $transaction_classificator): void
    {
        $this->transaction_classificator[] = $transaction_classificator;
    }

    public function setTransactionBooker(TransactionBooker $transaction_booker): void
    {
        $this->transaction_booker = $transaction_booker;
    }

    public function classificateTransaction(BankTransaction $transaction): BankTransaction
    {
        foreach ($this->transaction_classificator as $transaction_processor) {
            $result = $transaction_processor->execute($transaction);

            if ($result->isProcessed()) {
                $transaction->setClassificatorName($result->getTransactionClassificatorName());

                $extra = $result->getExtra();

                if ($result->getOrderId()) {
                    $extra['order_id'] = $result->getOrderId();
                }

                if ($result->getExtReference()) {
                    $extra['ext_reference'] = $result->getExtReference();
                }

                $transaction->setTransactionType($result->getTransationType());
                $transaction->setClassificatorExtra($extra);
                break;
            }
        }

        return $transaction;
    }

    public function classificate()
    {
        $sql_date = "";
        if ($this->date_start) {
            $sql_date = " AND buchhaltung_bank_transactions.transaction_date BETWEEN '" . $this->date_start->db() . "' AND '" . $this->date_end->db() . "'";
        }

        $sql_where = "
                buchhaltung_bank_transactions.account_id = " . $this->account_id . " AND
                buchhaltung_bank_transactions.classificator_name = ''
                $sql_date
        ";

        $transactions = $this->transaction_repository->searchBySqlWhere($sql_where);

        foreach ($transactions as $transaction) {
            $transaction = $this->classificateTransaction($transaction);
            $transaction->save();
        }
    }

    public function book()
    {
        $sql_date = "";
        if ($this->date_start) {
            $sql_date = " AND buchhaltung_bank_transactions.transaction_date BETWEEN '" . $this->date_start->db() . "' AND '" . $this->date_end->db() . "'";
        }

        $sql_where = "
                buchhaltung_bank_transactions.account_id = " . $this->account_id . " AND
                buchhaltung_bank_transactions.classificator_name != '' AND
                buchhaltung_bank_transactions.transaction_status = '" . BankTransaction::TRANSACTION_STATUS_UNKNOWN . "'
                $sql_date
        ";

        $transactions = $this->transaction_repository->searchBySqlWhere($sql_where);

        foreach ($transactions as $transaction) {
            $this->transaction_booker->bookTransaction($transaction);
        }
    }
}
