<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use order_repository;
use wws\BankAccount\BankTransaction;

class TransactionClassificatorRueckzahlung implements TransactionClassificator
{
    public function getTransactionClassificatorName(): string
    {
        return 'rueckzahlung';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        if ($transaction->getAmount() > 0) {
            return new TransactionClassificatorResult();
        }

        $result = order_repository::searchAuftnrCustomerIdInText($transaction->getTextReference1());

        if (!$result['auftnr'] || !$result['customer_id']) {
            return new TransactionClassificatorResult();
        }

        $order_id = order_repository::getOrderIdByAuftnr($result['auftnr']);

        if (!$order_id) {
            return new TransactionClassificatorResult();
        }

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setTransationType(BankTransaction::TRANSACTION_TYPE_DEBITOR);
        $result->setProcessed(true);
        $result->setStatus(self::STATUS_OK);
        $result->setOrderId($order_id);

        return $result;
    }
}