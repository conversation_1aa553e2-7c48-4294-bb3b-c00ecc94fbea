<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use bqp\Utils\StringUtils;
use wws\BankAccount\BankTransaction;

class TransactionClassificatorPersonenkonto implements TransactionClassificator
{
    /**
     * @var array
     */
    private $rules;

    public function __construct()
    {
        $this->rules = [
            ['ktonr' => '**********************', 'reference_1_begins' => 'PROV.', 'person_konto_nr' => '851900', 'transaction_type' => BankTransaction::TRANSACTION_TYPE_MISC,],
            ['ktonr' => '**********************', 'reference_1_begins' => 'RSV.', 'person_konto_nr' => '851900', 'transaction_type' => BankTransaction::TRANSACTION_TYPE_MISC],
            ['ktonr' => '**********************', 'reference_1_begins' => 'ST-P.', 'person_konto_nr' => '851900', 'transaction_type' => BankTransaction::TRANSACTION_TYPE_MISC],
            ['ktonr' => '**********************', 'reference_1_begins' => 'RSVP', 'person_konto_nr' => '851900', 'transaction_type' => BankTransaction::TRANSACTION_TYPE_MISC],
            ['ktonr' => '**********************', 'reference_1_begins' => 'SUB', 'person_konto_nr' => '476000', 'transaction_type' => BankTransaction::TRANSACTION_TYPE_MISC, 'posting_key' => '9']
        ];
    }

    public function getTransactionClassificatorName(): string
    {
        return 'personenkonto';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        foreach ($this->rules as $rule) {

            if (isset($rule['ktonr']) && $rule['ktonr'] != $transaction->getExtra('ktonr')) {
                continue;
            }

            if (isset($rule['reference_1_begins']) && !StringUtils::begins($transaction->getTextReference1(), $rule['reference_1_begins'])) {
                continue;
            }

            $result = new TransactionClassificatorResult();
            $result->setExtra('person_konto_nr', $rule['person_konto_nr']);
            $result->setExtra('transaction_type', $rule['transaction_type']);
            if (isset($rule['posting_key'])) {
                $result->setExtra('posting_key', $rule['posting_key']);
            }
            $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
            $result->setProcessed(true);
            $result->setStatus(self::STATUS_OK);

            return $result;
        }

        return new TransactionClassificatorResult();
    }
}