<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use bqp\db\db_generic;
use wws\BankAccount\BankTransaction;
use wws\buchhaltung\Invoice\InvoiceDocument;

class TransactionClassificatorRechnung implements TransactionClassificator
{
    private $rechnungen = [];

    public function __construct(int $shop_id, db_generic $db)
    {
        $zahlungs_ids = [4];

        $this->rechnungen = $db->query("
            SELECT
                buchhaltung_rechnung.order_id,
                buchhaltung_rechnung.rechnungs_nr,
                buchhaltung_rechnung.rechnungs_betrag_brutto,
                buchhaltung_rechnung.rechnungs_betrag_netto
            FROM
                orders INNER JOIN
                buchhaltung_rechnung ON (orders.order_id = buchhaltung_rechnung.order_id)
            WHERE
                orders.zahlungs_id IN (" . $db->in($zahlungs_ids) . ") AND
                orders.shop_id = '" . $shop_id . "' AND
                buchhaltung_rechnung.rechnungs_type = '" . InvoiceDocument::INVOICE_TYPE_INVOICE . "' AND
                buchhaltung_rechnung.rechnungs_datum >= NOW() - INTERVAL 365 DAY
        ")->asArray('rechnungs_nr');
    }


    public function getTransactionClassificatorName(): string
    {
        return 'rechnung_nr';
    }


    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        if ($transaction->getAmount() < 0) {
            return new TransactionClassificatorResult();
        }

        $referenz = trim($transaction->getTextReference1());
        if (!$referenz) {
            $referenz = $transaction->getTextReference2();
        }

        if (!preg_match_all('~(\b|RE|NR)([0-9]{6,7})\b~', $referenz, $matches)) {
            return new TransactionClassificatorResult();
        }

        foreach ($matches[2] as $possible_nr) {
            if (!isset($this->rechnungen[$possible_nr])) {
                $possible_nr = 'RE' . $possible_nr;
                if (!isset($this->rechnungen[$possible_nr])) {
                    continue;
                }
            }

            $match = $this->rechnungen[$possible_nr];

            if (
                abs($match['rechnungs_betrag_brutto'] - $transaction->getAmount()) < 1 ||
                abs($match['rechnungs_betrag_netto'] - $transaction->getAmount()) < 1
            ) {
                $result = new TransactionClassificatorResult();
                $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
                $result->setProcessed(true);
                $result->setOrderId($match['order_id']);
                $result->setTransationType(BankTransaction::TRANSACTION_TYPE_DEBITOR);

                return $result;
            }
        }

        return new TransactionClassificatorResult();
    }
}