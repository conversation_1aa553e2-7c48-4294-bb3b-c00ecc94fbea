<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use order_repository;
use wws\BankAccount\BankTransaction;

class TransactionClassificatorNachnahmeDhl implements TransactionClassificator
{
    public function getTransactionClassificatorName(): string
    {
        return 'dhl_nachnahme';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        if ($transaction->getAmount() < 0) {
            return new TransactionClassificatorResult();
        }

        if (!preg_match('~^' . order_repository::getAuftnrRegexp() . '.*\+Deutsche Post AG~', $transaction->getTextReference1(), $match)) {
            return new TransactionClassificatorResult();
        }

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setProcessed(true);
        $result->setStatus(self::STATUS_OK);
        $result->setTransationType(BankTransaction::TRANSACTION_TYPE_DEBITOR);

        $ref = order_repository::searchAuftnrCustomerIdInTextTolerant($transaction->getTextReference1());

        if ($ref['auftnr']) {
            $order_id = order_repository::getOrderIdByAuftnr($ref['auftnr']);

            $result->setOrderId($order_id);
        }

        return $result;
    }
}