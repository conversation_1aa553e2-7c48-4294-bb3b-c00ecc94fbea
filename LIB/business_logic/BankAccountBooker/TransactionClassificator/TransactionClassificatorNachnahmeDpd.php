<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use order_repository;
use wws\BankAccount\BankTransaction;

class TransactionClassificatorNachnahmeDpd implements TransactionClassificator
{
    public function getTransactionClassificatorName(): string
    {
        return 'dpd_nachnahme';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        if ($transaction->getAmount() < 0) {
            return new TransactionClassificatorResult();
        }

        if (strpos($transaction->getSenderName(), 'DPDGROUP INTERN. SERVICES') === false) {
            return new TransactionClassificatorResult();
        }

        $ref = order_repository::searchAuftnrCustomerIdInTextTolerant($transaction->getTextReference1());

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setProcessed(true);
        $result->setTransationType(BankTransaction::TRANSACTION_TYPE_DEBITOR);

        if ($ref['auftnr']) {
            $order_id = order_repository::getOrderIdByAuftnr($ref['auftnr']);

            $result->setOrderId($order_id);
        }

        return $result;
    }
}