<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use order_repository;
use wws\BankAccount\BankTransaction;

class TransactionClassificatorSofortUeberweisung implements TransactionClassificator
{
    public function getTransactionClassificatorName(): string
    {
        return 'sofort_ueberweisung';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        if ($transaction->getAmount() < 0) {
            return new TransactionClassificatorResult();
        }

        if (!preg_match('~^SU ([0-9]{5,7}) (?<auftnr>' . order_repository::getAuftnrRegexp() . ') ~', $transaction->getTextReference1(), $match)) {
            return new TransactionClassificatorResult();
        }

        $order_id = order_repository::getOrderIdByAuftnr($match['auftnr']);

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setProcessed(true);
        $result->setTransationType(BankTransaction::TRANSACTION_TYPE_DEBITOR);

        if ($order_id) {
            $result->setOrderId($order_id);
            $result->setStatus(self::STATUS_OK);
        } else {
            $result->setStatus(self::STATUS_ERROR, 'Bestellung nicht gefunden.');
        }

        return $result;
    }
}