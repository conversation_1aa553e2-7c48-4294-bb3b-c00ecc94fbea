<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use wws\BankAccount\BankTransaction;

class TransactionClassificatorResult
{
    private $is_processed = false;

    private $transaction_classificator_name;

    private $order_id;

    private $ext_reference = '';

    private $status = '?';
    private $status_reason = '';

    private $extra = [];

    /**
     * @var string
     */
    private $transation_type = BankTransaction::TRANSACTION_TYPE_UNKNOWN;

    private $certainty = 100;

    public function getTransactionClassificatorName(): string
    {
        return $this->transaction_classificator_name;
    }

    public function setTransactionClassificatorName(string $name): void
    {
        $this->transaction_classificator_name = $name;
    }

    public function isProcessed(): bool
    {
        return $this->is_processed;
    }

    public function setProcessed(bool $status): void
    {
        $this->is_processed = $status;
    }

    public function getOrderId(): ?int
    {
        return $this->order_id;
    }

    public function setOrderId(int $order_id): void
    {
        $this->order_id = $order_id;
    }

    public function setStatus(string $status, string $status_reason = ''): void
    {
        $this->status = $status;
        $this->status_reason = $status_reason;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getStatusReason(): string
    {
        return $this->status_reason;
    }

    public function getCertainty(): int
    {
        return $this->certainty;
    }

    public function setCertainty(int $certainty): void
    {
        $this->certainty = $certainty;
    }

    public function getExtReference(): string
    {
        return $this->ext_reference;
    }

    public function setExtReference(string $ext_reference): void
    {
        $this->ext_reference = $ext_reference;
    }

    public function getTransationType(): string
    {
        return $this->transation_type;
    }

    public function setTransationType(string $transation_type): void
    {
        $this->transation_type = $transation_type;
    }

    public function getExtra(): array
    {
        return $this->extra;
    }

    public function setExtra(string $key, string $value): void
    {
        $this->extra[$key] = $value;
    }
}