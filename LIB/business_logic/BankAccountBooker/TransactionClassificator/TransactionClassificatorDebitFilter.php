<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use wws\BankAccount\BankTransaction;

class TransactionClassificatorDebitFilter implements TransactionClassificator
{

    public function getTransactionClassificatorName(): string
    {
        return 'debit_filter';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {

        if ($transaction->getAmount() > 0) {
            return new TransactionClassificatorResult();
        }

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setProcessed(true);
        $result->setStatus(self::STATUS_OK);

        return $result;
    }
}