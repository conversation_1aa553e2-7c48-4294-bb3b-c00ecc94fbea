<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use wws\BankAccount\BankTransaction;

class TransactionClassificatorAswo implements TransactionClassificator
{
    public function getTransactionClassificatorName(): string
    {
        return 'aswo';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {

        if (preg_match('~^ESHOP [0-9]{4}-((N.*[0-9]{9})|NO)$~', $transaction->getTextReference1())) {
            $result = new TransactionClassificatorResult();
            $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
            $result->setProcessed(true);
            $result->setStatus(self::STATUS_OK);

            return $result;
        }

        $reference = '';
        if (preg_match('~e(| |-)shop(| |-)([0-9 ]{10,14})~i', $transaction->getTextReference1(), $match)) {
            $reference = str_replace(' ', '', $match[3]);
        }

        if (!$reference) {
            if (preg_match('~^42[789][0-9]{8}$~i', $transaction->getTextReference1(), $match)) {
                $reference = $match[0];
            }
        }

        if (!$reference) {
            return new TransactionClassificatorResult();
        }

        //ESHOP 2458-N0 *********

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setProcessed(true);
        $result->setStatus(self::STATUS_OK);
        $result->setExtReference($reference);

        return $result;
    }
}