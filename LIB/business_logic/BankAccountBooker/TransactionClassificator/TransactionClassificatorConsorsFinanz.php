<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use wws\BankAccount\BankTransaction;

class TransactionClassificatorConsorsFinanz implements TransactionClassificator
{
    public function getTransactionClassificatorName(): string
    {
        return 'consors_finanz';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        if ($transaction->getAmount() < 0) {
            return new TransactionClassificatorResult();
        }

        if (strpos($transaction->getSenderName(), 'CONSORS') === false) {
            return new TransactionClassificatorResult();
        }

        if (!preg_match('~^VAL\. ([0-9]{6}) COFI-(NR|CARD)\.\s+([0-9]{14})~', $transaction->getTextReference1(), $match)) {
            return new TransactionClassificatorResult();
        }

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setProcessed(true);
        $result->setOrderId($match[1]);
        $result->setTransationType(BankTransaction::TRANSACTION_TYPE_DEBITOR);

        return $result;
    }
}