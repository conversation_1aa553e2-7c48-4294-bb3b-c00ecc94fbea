<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use env;
use wws\BankAccount\BankTransaction;
use wws\buchhaltung\AccountingConsts;

class TransactionClassificatorTransit implements TransactionClassificator
{
    private $bank_accounts = [];

    public function __construct(int $account_id)
    {
        if ($account_id === AccountingConsts::BANK_ACCOUNT_COMMERZ_K11 && env::isK11()) {
            $this->bank_accounts = [
                '**********************', //paypal
                '**********************', //sparkasse k11
                '**********************', //amazon
            ];
        } else {
            $this->bank_accounts = [
                '**********************', //paypal
                '**********************', //paypal
                '**********************', //ecom sparkasse
                '**********************', //check24
                '**********************', //check24
                '**********************', //amazon
                '**********************', //crowdfox
                '**********************', //ecom uraltes comerzbank konto
                '**********************', //real
                '**********************', //ecom comerzbank
                '******************', //ebay
                '**********************' //haupt comerzbank konto
            ];
        }
    }

    public function getTransactionClassificatorName(): string
    {
        return 'transit';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        $ktonr = $transaction->getExtra('ktonr');

        if (in_array($ktonr, $this->bank_accounts)) {
            $result = new TransactionClassificatorResult();
            $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
            $result->setProcessed(true);
            $result->setStatus(self::STATUS_OK);
            $result->setTransationType(BankTransaction::TRANSACTION_TYPE_TRANSIT);

            return $result;
        }

        if (strpos($transaction->getTextReference2(), 'KARTE EINZAHLUNG') !== false) {
            $result = new TransactionClassificatorResult();
            $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
            $result->setProcessed(true);
            $result->setStatus(self::STATUS_OK);
            $result->setTransationType(BankTransaction::TRANSACTION_TYPE_TRANSIT);

            return $result;
        }

        return new TransactionClassificatorResult();
    }
}
