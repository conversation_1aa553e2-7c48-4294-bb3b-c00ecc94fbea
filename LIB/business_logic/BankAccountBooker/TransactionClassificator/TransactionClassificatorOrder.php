<?php

namespace wws\BankAccountBooker\TransactionClassificator;

use bqp\db\db_generic;
use order_repository;
use wws\BankAccount\BankTransaction;
use wws\Order\OrderConst;

class TransactionClassificatorOrder implements TransactionClassificator
{
    private int $shop_id;
    private db_generic $db;

    protected $zahlungs_ids = [];

    public function __construct(int $shop_id, db_generic $db)
    {
        $this->shop_id = $shop_id;
        $this->zahlungs_ids = [OrderConst::PAYMENT_VORKASSE, OrderConst::PAYMENT_RECHNUNG, OrderConst::PAYMENT_NACHNAHME, OrderConst::PAYMENT_BAR, OrderConst::PAYMENT_TEILZAHLUNG, OrderConst::PAYMENT_AMAZON];

        $this->db = $db;
    }


    public function getTransactionClassificatorName(): string
    {
        return 'order';
    }

    public function execute(BankTransaction $transaction): TransactionClassificatorResult
    {
        if ($transaction->getAmount() < 0) {
            return new TransactionClassificatorResult();
        }

        $referenz = trim($transaction->getTextReference1());
        if (!$referenz) {
            $referenz = $transaction->getTextReference2();
        }

        //$result = \order_repository::searchAuftnrCustomerIdInText($referenz);
        $result = order_repository::searchAuftnrCustomerIdInTextTolerant($referenz);

        if (!$result['customer_id'] && !$result['auftnr']) {
            return new TransactionClassificatorResult();
        }

        //bestellung suchen

        $order_id = null;

        if ($result['customer_id'] && $result['auftnr']) {
            $order_id = $this->db->fieldQuery("
                SELECT
                    orders.order_id
                FROM
                    orders
                WHERE
                    orders.customer_id = '" . $this->db->escape($result['customer_id']) . "' AND
                    orders.auftnr = '" . $this->db->escape($result['auftnr']) . "' AND
                    orders.shop_id = '" . $this->shop_id . "'
            ");
        }

        if (!$order_id && $result['auftnr']) {
            $temp_result = $this->db->singleQuery("
                SELECT
                    orders.order_id,
                    
                    orders.tax_status,
                    orders.order_amount_gross,
                    orders.order_amount_net
                FROM
                    orders
                WHERE
                    orders.auftnr = '" . $this->db->escape($result['auftnr']) . "' AND
                    orders.shop_id = '" . $this->shop_id . "' AND
                    orders.zahlungs_id IN (" . $this->db->in($this->zahlungs_ids) . ")
            ");

            if ($temp_result) {
                if (abs($temp_result['order_amount_gross'] - $transaction->getAmount()) < 0.1) {
                    $order_id = $temp_result['order_id'];
                } elseif ($temp_result['tax_status'] > OrderConst::TAX_STATUS_NORMAL and abs($temp_result['order_amount_net'] - $transaction->getAmount()) < 0.1) {
                    $order_id = $temp_result['order_id'];
                }
            }
        }

        if (!$order_id && $result['customer_id']) {
            $temp_result = $this->db->query("
                SELECT
                    orders.order_id,

                    orders.tax_status,
                    orders.order_amount_gross,
                    orders.order_amount_net
                FROM
                    orders
                WHERE
                    orders.customer_id = '" . $this->db->escape($result['customer_id']) . "' AND
                    orders.shop_id = '" . $this->shop_id . "' AND
                    orders.zahlungs_id IN (" . $this->db->in($this->zahlungs_ids) . ")
                GROUP BY
                    orders.order_id
                HAVING
                    ABS(orders.order_amount_gross - " . $transaction->getAmount() . ") < 0.1
            ")->asArray();

            if (count($temp_result) === 1) {
                $order_id = $temp_result[0]['order_id'];
            }
        }

        if (!$order_id) {
            return new TransactionClassificatorResult();
        }

        $result = new TransactionClassificatorResult();
        $result->setTransactionClassificatorName($this->getTransactionClassificatorName());
        $result->setProcessed(true);
        $result->setOrderId($order_id);
        $result->setStatus(self::STATUS_OK);
        $result->setTransationType(BankTransaction::TRANSACTION_TYPE_DEBITOR);

        return $result;
    }
}