<?php

namespace wws\BankAccountBooker;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use env;
use service_loader;
use wws\BankAccount\BankTransaction;
use wws\BankAccount\BankTransactionRepository;
use wws\BankAccountBooker\TransactionBooker\TransactionBooker;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerLegacy;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModule;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModuleForm;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModuleNull;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModuleOrder;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModuleOrderSplit;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModuleOrderUnchecked;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModulePersonKonto;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModuleReset;
use wws\BankAccountBooker\TransactionBooker\TransactionBookerModuleWithoutAccount;
use wws\buchhaltung\AccountingConsts;
use wws\business_structure\Shop;

class BankTransactionBookerFactory
{
    public const MODUS_AUTO = 'auto';
    public const MODUS_INTERACTIVE = 'interactive';

    protected db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }


    /**
     * @param int $account_id
     * @return TransactionBookerLegacy
     */
    public function getTransactionBookerLegacy(int $account_id): TransactionBookerLegacy
    {
        $booker = new TransactionBookerLegacy($this->db);
        $booker->setAccountId($account_id);

        return $booker;
    }

    public function getTransactionBooker(int $account_id): TransactionBooker
    {
        $booker = new TransactionBooker();

        $order_repository = service_loader::getOrderRepository();
        $transaction_repository = service_loader::getDiContainer()->get(BankTransactionRepository::class);

        if (env::isEcom()) {
            switch ($account_id) {
                case AccountingConsts::BANK_ACCOUNT_COMMERZ:
                case AccountingConsts::BANK_ACCOUNT_SPARKASSE:
                    $shop_id = Shop::ALLEGO;
                    break;
                case AccountingConsts::BANK_ACCOUNT_COMMERZ_K11:
                case AccountingConsts::BANK_ACCOUNT_COMMERZ_K11_ASWO:
                    $shop_id = Shop::ERSATZTEILSHOP;
                    break;
                case AccountingConsts::BANK_ACCOUNT_CHECK24:
                case AccountingConsts::BANK_ACCOUNT_KREDITKARTE:
                case AccountingConsts::BANK_ACCOUNT_PAYPAL:
                case AccountingConsts::BANK_ACCOUNT_KREDITKARTE_K11:
                case AccountingConsts::BANK_ACCOUNT_PAYPAL_K11:
                case AccountingConsts::BANK_ACCOUNT_KAUFLAND:
                    //$module = new TransactionBookerModuleOrder($order_repository, $this->db);
                    $module = new TransactionBookerModuleOrderUnchecked($order_repository);
                    $module->setClassificatorFilter(['order', 'order_fallback']);
                    $booker->addTransactionBookerModule($module);

                    $module = new TransactionBookerModuleOrderSplit($order_repository, $this->db);
                    $module->setClassificatorFilter(['order_split']);
                    $booker->addTransactionBookerModule($module);

                    $module = new TransactionBookerModuleNull();
                    $module->setClassificatorFilter(['filter']);
                    $booker->addTransactionBookerModule($module);

                    $module = new TransactionBookerModuleNull();
                    $module->setClassificatorFilter(['transit']);
                    $module->setTransactionStatus(BankTransaction::TRANSACTION_STATUS_BOOKED);
                    $booker->addTransactionBookerModule($module);

                    return $booker;
                default:
                    throw new DevException('no booking profile for ' . $account_id);
            }
        } else {
            switch ($account_id) {
                case AccountingConsts::BANK_ACCOUNT_COMMERZ_K11:
                case AccountingConsts::BANK_ACCOUNT_COMMERZ_K11_ASWO:
                    $shop_id = Shop::ERSATZTEILSHOP;
                    break;
                case AccountingConsts::BANK_ACCOUNT_KREDITKARTE_K11:
                case AccountingConsts::BANK_ACCOUNT_PAYPAL_K11:
                case AccountingConsts::BANK_ACCOUNT_UNZER_OLD_K11:
                case AccountingConsts::BANK_ACCOUNT_UNZER_KK_K11:
                case AccountingConsts::BANK_ACCOUNT_UNZER_SOFORT_K11:
                case AccountingConsts::BANK_ACCOUNT_UNZER_AMEX_K11:
                    //$module = new TransactionBookerModuleOrder($order_repository, $this->db);
                    $module = new TransactionBookerModuleOrderUnchecked($order_repository);
                    $module->setClassificatorFilter(['order', 'order_fallback']);
                    $booker->addTransactionBookerModule($module);

                    $module = new TransactionBookerModuleOrderSplit($order_repository, $this->db);
                    $module->setClassificatorFilter(['order_split']);
                    $booker->addTransactionBookerModule($module);

                    $module = new TransactionBookerModuleNull();
                    $module->setClassificatorFilter(['filter']);
                    $booker->addTransactionBookerModule($module);

                    $module = new TransactionBookerModuleNull();
                    $module->setClassificatorFilter(['transit']);
                    $module->setTransactionStatus(BankTransaction::TRANSACTION_STATUS_BOOKED);
                    $booker->addTransactionBookerModule($module);

                    return $booker;
                default:
                    throw new DevException('no booking profile for ' . $account_id);
            }
        }


        //personenkonto
        $module = service_loader::getDiContainer()->get(TransactionBookerModulePersonKonto::class);
        $module->setClassificatorFilter(['personenkonto']);
        $booker->addTransactionBookerModule($module);

        //transit
        $module = new TransactionBookerModuleNull();
        $module->setClassificatorFilter(['transit']);
        $module->setTransactionStatus(BankTransaction::TRANSACTION_STATUS_BOOKED);
        $booker->addTransactionBookerModule($module);

        //aswo
//        if ($modus === self::MODUS_INTERACTIVE) {
//            $module = new TransactionBookerModuleNull();
//            $module->setClassificatorFilter(['aswo']);
//            $booker->addTransactionBookerModule($module);
//        }

        //sofort_ueberweisung
        //rueckzahlung
        //consors_finanz
        //dpd_nachnahme
        //dhl_nachnahme
        $module = new TransactionBookerModuleOrderUnchecked($order_repository);
        $module->setClassificatorFilter(['sofort_ueberweisung', 'rueckzahlung', 'consors_finanz', 'dpd_nachnahme', 'dhl_nachnahme']);
        $booker->addTransactionBookerModule($module);


        //ebay
        //vorkasse
        //rechnungs_nr
        $module = new TransactionBookerModuleOrder($order_repository, $this->db);
        $module->setErrorMode($module::ERROR_MODE_FAIL);
        $module->setClassificatorFilter(['order', 'vorkasse', 'ebay', 'rechnung_nr', 'order_fallback']);
        $booker->addTransactionBookerModule($module);

        $module = new TransactionBookerModuleOrderSplit($order_repository, $this->db);
        $module->setClassificatorFilter(['order_split']);
        $booker->addTransactionBookerModule($module);

        return $booker;
    }

    /**
     * @param int $account_id
     * @return BankAutoBookerShopBankAccount
     */
    public function getBankAutoBooker(int $account_id): BankAutoBookerShopBankAccount
    {
        switch ($account_id) {
            case AccountingConsts::BANK_ACCOUNT_COMMERZ:
                $shop_id = Shop::ALLEGO;
                break;
            case AccountingConsts::BANK_ACCOUNT_COMMERZ_K11:
            case AccountingConsts::BANK_ACCOUNT_COMMERZ_K11_ASWO:
                $shop_id = Shop::ERSATZTEILSHOP;
                break;
            default:
                throw new DevException('no booking profile for ' . $account_id);
        }

        $transaction_repository = service_loader::getDiContainer()->get(BankTransactionRepository::class);

        $bank_booking = new BankAutoBookerShopBankAccount($account_id, $transaction_repository);

        $processor = new TransactionClassificator\TransactionClassificatorPersonenkonto();
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorSofortUeberweisung();
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorNachnahmeDhl();
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorNachnahmeDpd();
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorRueckzahlung();
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorTransit($account_id);
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorConsorsFinanz();
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorOrder($shop_id, $this->db);
        $bank_booking->addTransactionClassificator($processor);

        if ($shop_id === Shop::ERSATZTEILSHOP) {
            $processor = new TransactionClassificator\TransactionClassificatorAswo();
            $bank_booking->addTransactionClassificator($processor);
        }

        $processor = new TransactionClassificator\TransactionClassificatorRechnung($shop_id, $this->db);
        $bank_booking->addTransactionClassificator($processor);

        $processor = new TransactionClassificator\TransactionClassificatorOrderFallback($shop_id, $this->db);
        $bank_booking->addTransactionClassificator($processor);

        //aktuell gibts nur noch ein relevantes Konto und das muss komplett verbucht werden
        //$processor = new TransactionClassificator\TransactionClassificatorDebitFilter();
        //$bank_booking->addTransactionClassificator($processor);

        $bank_booking->setTransactionBooker($this->getTransactionBooker($account_id));

        return $bank_booking;
    }

    public function getTransactionBookerModule(int $account_id, string $module): TransactionBookerModule
    {
        return service_loader::getDiContainer()->make($module, ['account_id' => $account_id]);
    }

    public function getTransactionBookerModuleForm(int $account_id, string $module): TransactionBookerModuleForm
    {
        return service_loader::getDiContainer()->make($module, ['account_id' => $account_id]);
    }

    /**
     * @return TransactionBookerModuleForm[]
     */
    public function getTransactionBookerModulesForGui(int $account_id): array
    {
        $modules = [];

        $modules['orders'] = $this->getTransactionBookerModuleForm($account_id, TransactionBookerModuleOrder::class);
        $modules['orders_unchecked'] = $this->getTransactionBookerModuleForm($account_id, TransactionBookerModuleOrderUnchecked::class);
        $modules['orders_split'] = $this->getTransactionBookerModuleForm($account_id, TransactionBookerModuleOrderSplit::class);

        $booker = service_loader::getDiContainer()->make(TransactionBookerModuleOrderUnchecked::class, ['account_id' => $account_id]);
        /* @var TransactionBookerModuleOrderUnchecked $booker */
        $booker->setTransactionType(BankTransaction::TRANSACTION_TYPE_FEE);
        $booker->setDescription('Gebühren mit Auftragsbezug');
        $modules['order_fees'] = $booker;


        $booker = new TransactionBookerModuleNull();
        $booker->setTransactionType(BankTransaction::TRANSACTION_TYPE_FEE);
        $booker->setTransactionStatus(BankTransaction::TRANSACTION_STATUS_BOOKED);
        $booker->setDescription('Gebühren ohne Auftragsbezug');
        $modules['fees'] = $booker;


        $modules['free_person_konto'] = $this->getTransactionBookerModuleForm($account_id, TransactionBookerModulePersonKonto::class);
        $modules['datev_without_account'] = $this->getTransactionBookerModuleForm($account_id, TransactionBookerModuleWithoutAccount::class);
        $modules['transaction_reset'] = $this->getTransactionBookerModuleForm($account_id, TransactionBookerModuleReset::class);

        $booker = new TransactionBookerModuleNull();

        $modules['ignore'] = $booker;

        return $modules;
    }
}
