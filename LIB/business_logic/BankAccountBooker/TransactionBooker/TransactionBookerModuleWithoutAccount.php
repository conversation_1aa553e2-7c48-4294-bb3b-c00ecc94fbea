<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\form\form_element_container;
use bqp\form\form_element_html;
use wws\BankAccount\BankTransaction;

class TransactionBookerModuleWithoutAccount extends TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule, TransactionBookerModuleForm
{
    /**
     * @var string
     */
    private $description = 'Klärung in Datev - Buchung ohne Gegenkonto erzeugen';

    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * Bucht die Transaktion
     *
     * @param BankTransaction $transaction
     * @return TransactionBookerResult
     */
    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult
    {
        $this->book($transaction);

        $result = new TransactionBookerResult();
        $result->setBooker($this);
        $result->setStatus($result::STATUS_OK);

        return $result;
    }


    public function book(BankTransaction $transaction, string $transaction_type = BankTransaction::TRANSACTION_TYPE_MISC): void
    {
        $transaction->setTransactionStatus(BankTransaction::TRANSACTION_STATUS_BOOKED);
        $transaction->setPersonKontoNr('-1');
        $transaction->setPostingKey('');
        $transaction->setTransactionType($transaction_type);
        $transaction->save();
    }


    public function buildForm(form_element_container $form): void
    {
        $fieldset = $form->addFieldset('', 'Buchen');

        $html = new form_element_html();
        $html->setValue('<b>Transaktion wird ohne Gegenkonto an Datev übergeben.</b>');

        $fieldset->add($html);
    }

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult
    {
        $this->book($transaction);
        return new TransactionBookerProcessFormResult();
    }
}
