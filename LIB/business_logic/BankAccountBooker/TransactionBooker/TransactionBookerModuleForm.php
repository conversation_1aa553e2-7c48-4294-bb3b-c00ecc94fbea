<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\form\form_element_container;
use wws\BankAccount\BankTransaction;

interface TransactionBookerModuleForm
{
    public function buildForm(form_element_container $form): void;

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult;

    public function getDescription(): string;
}

