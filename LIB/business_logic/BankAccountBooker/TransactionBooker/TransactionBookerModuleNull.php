<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\form\form_element_container;
use bqp\form\form_element_html;
use wws\BankAccount\BankTransaction;

class TransactionBookerModuleNull extends TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule, TransactionBookerModuleForm
{
    /**
     * @var string
     */
    private $transaction_status = BankTransaction::TRANSACTION_STATUS_FILTERED;

    /**
     * @var string
     */
    private $description = 'Transaktion ignorieren';

    private $clear_all_refs = true;

    /**
     * @var null|string
     */
    private $transaction_type = null;

    /**
     * @param string $transaction_type
     */
    public function setTransactionType(string $transaction_type): void
    {
        $this->transaction_type = $transaction_type;
    }

    public function setTransactionStatus(string $transaction_status): void
    {
        $this->transaction_status = $transaction_status;
    }

    /**
     * @param string $description
     */
    public function setDescription(string $description): void
    {
        $this->description = $description;
    }


    /**
     * Bucht die Transaktion
     *
     * @param BankTransaction $transaction
     * @return TransactionBookerResult
     */
    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult
    {
        $result = new TransactionBookerResult();
        $result->setStatus($result::STATUS_OK);

        $this->book($transaction);

        return $result;
    }

    public function book(BankTransaction $transaction): void
    {
        $transaction->setTransactionStatus($this->transaction_status);

        if ($this->transaction_type) {
            $transaction->setTransactionType($this->transaction_type);
        }

        if ($this->clear_all_refs) {
            $transaction->setPersonKontoNr('');
            $transaction->setPostingKey('');
            $transaction->setCustomerId(null);
            $transaction->setOrderId(null);
        }

        $transaction->save();
    }

    public function buildForm(form_element_container $form): void
    {
        $fieldset = $form->addFieldset('', 'Buchen');

        $html = new form_element_html();
        $html->setValue('<b style="color: red">ACHTUNG: Nur als Ausnahme verwenden! Diese Transaktionen wird nicht an die Buchhaltung übergeben.</b>');

        $fieldset->add($html);
    }

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult
    {
        $this->book($transaction);
        return new TransactionBookerProcessFormResult();
    }

    public function getDescription(): string
    {
        return $this->description;
    }
}
