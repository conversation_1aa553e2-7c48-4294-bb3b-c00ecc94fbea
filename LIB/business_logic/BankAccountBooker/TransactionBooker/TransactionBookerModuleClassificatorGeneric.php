<?php

namespace wws\BankAccountBooker\TransactionBooker;

use wws\BankAccount\BankTransaction;
use function in_array;

abstract class TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule
{
    private $classificator_filter = [];

    public function setClassificatorFilter(array $classificator_filter): void
    {
        $this->classificator_filter = $classificator_filter;
    }

    public function isProcessable(BankTransaction $transaction): bool
    {
        return in_array($transaction->getClassificatorName(), $this->classificator_filter);
    }

    public function setTransactionTypeToDebitorIfUnknown(BankTransaction $transaction): void
    {
        if ($transaction->getTransactionType() === $transaction::TRANSACTION_TYPE_UNKNOWN) {
            $transaction->setTransactionType($transaction::TRANSACTION_TYPE_DEBITOR);
        }
    }
}
