<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\db\db_generic;
use bqp\form\form_element_container;
use bqp\form\form_element_html;
use order_repository;
use wws\BankAccount\BankTransaction;
use wws\Order\Form\FormElementOrderSearch;
use wws\Order\OrderPaymentReceipt;
use wws\Order\OrderRepository;

class TransactionBookerModuleOrderSplit extends TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule, TransactionBookerModuleForm
{
    private OrderRepository $order_repository;
    private db_generic $db;

    public function __construct(OrderRepository $order_repository, db_generic $db)
    {
        $this->order_repository = $order_repository;
        $this->db = $db;
    }

    /**
     * Bucht die Transaktion
     *
     * @param BankTransaction $transaction
     * @return TransactionBookerResult
     */
    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult
    {
        $order_ids = $transaction->getClassificatorExtra()['order_ids'];

        $orders = [];
        $refs = [];
        $total_amount = 0;
        $customer_id = null;

        foreach ($order_ids as $order_id) {
            $order = $this->order_repository->load($order_id);

            $total_amount += $order->getRechnungsBetrag();

            $orders[] = $order;

            $refs[] = $order->getAuftnr();
            $customer_id = $order->getCustomerId();
        }

        if (abs($total_amount - $transaction->getAmount()) > 0.1) {
            $result = new TransactionBookerResult();
            $result->setBooker($this);
            $result->setStatus($result::STATUS_FAIL);
            $result->setMessage('Buchungssumme (' . $transaction->getAmount() . ') und Auftragssummen stimmen nicht überein. (' . $total_amount . ')');
            $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_FAILED);
            $transaction->setBookerResult($result);
            $transaction->save();
            return $result;
        }

        foreach ($orders as $order) {
            $payment = new OrderPaymentReceipt();
            $payment->setAmount($order->getRechnungsBetrag());
            $payment->setComplete(true);
            $payment->setBemerkung('Splitbuchung über mehrere Aufträge. ' . $transaction->getAmount());

            $order->addPaymentReceipt($payment);
            $order->save();
        }

        $result = new TransactionBookerResult();
        $result->setBooker($this);
        $result->setStatus($result::STATUS_OK);

        $transaction->setNotice(implode(', ', $refs));
        $transaction->setCustomerId($customer_id);
        $transaction->setTransactionType($transaction::TRANSACTION_TYPE_DEBITOR);
        $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_BOOKED);
        $transaction->setBookerResult($result);
        $transaction->save();

        return $result;
    }

    public function buildForm(form_element_container $form): void
    {
        $fieldset = $form->addFieldset();
        $fieldset->setLegend('Zahlungseingang für mehrere Aufträge');


        $info = new form_element_html();
        $info->setLabel('Info');
        $info->setHtml('
                Das Modul prüft ob die Transaktionssumme mit den Aufträgen identisch ist.
                Abweichungen führen zu Fehlern. Sollte Teilzahlung, Gutschriften etc. in der Summe 
                inbegriffen sein, kann "Zahlungseingang für Auftrag (passive Zuordnung)" verwendet werden.
                Die Zahlung muss nur einen Auftrag zugeorndert. Als "bezahlt markiert" muss händig im Auftrag
                ausgeführt werden. 
        ');
        $fieldset->add($info);

        $auftnrs = $fieldset->add('text', 'auftnrs', 'Aufttragsnummern');

        $auft_search = new FormElementOrderSearch();
        $fieldset->add($auft_search, 'search2', '');

        $auft_search->setShopId(1);
        $auft_search->setTargetElementAppend($auftnrs);

    }

    public function getDescription(): string
    {
        return 'Zahlungeingang für mehrere Aufträge';
    }

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult
    {
        $booker_result = new TransactionBookerProcessFormResult();

        $auftnrs = $data['auftnrs'];
        $auftnrs = preg_split('~[ /,;]+~u', $auftnrs);

        $order_ids = [];


        foreach ($auftnrs as $auftnr) {
            $order_id = order_repository::getOrderIdByAuftnr($auftnr);

            if (!$order_id) {
                $booker_result->setErrorMessage('Die Auftragsnummer "' . $auftnr . '" konnte nicht gefunden werden.');
                return $booker_result;
            }

            $order_ids[] = $order_id;
        }

        if (!$order_ids) {
            $booker_result->setErrorMessage('Bitte wählen Sie mindestens eine Auftrag aus.');
            return $booker_result;
        }

        $extra = [
            'order_ids' => $order_ids
        ];

        $transaction->setClassificatorExtra($extra);

        $result = $this->bookByClassification($transaction);

        if ($result->getStatus() === $result::STATUS_OK) {
            return $booker_result;
        }

        if ($result->getStatus() === $result::STATUS_WARN) {
            $booker_result->setWarning($result->getAsText());
        } else {
            $booker_result->setErrorMessage($result->getAsText());
        }

        return $booker_result;
    }
}
