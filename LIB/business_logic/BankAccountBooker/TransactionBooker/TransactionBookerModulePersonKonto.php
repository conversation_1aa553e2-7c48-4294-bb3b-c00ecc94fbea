<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\form\form_element_container;
use bqp\form\form_element_select;
use bqp\form\form_element_text;
use bqp\form\Validator\FormValidatorEmpty;
use bqp\form\Validator\FormValidatorRegxp;
use wws\BankAccount\BankTransaction;
use wws\BankAccount\BankTransactionRepository;

class TransactionBookerModulePersonKonto extends TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule, TransactionBookerModuleForm
{
    private $possible_transaction_types = [];

    /**
     * @var string
     */
    private $description = 'Buchhaltungs-Konto';


    public function __construct(BankTransactionRepository $repository)
    {
        $this->possible_transaction_types = $repository->getTransactionTypes();
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * Bucht die Transaktion
     *
     * @param BankTransaction $transaction
     * @return TransactionBookerResult
     */
    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult
    {
        $extra = $transaction->getClassificatorExtra();
        $this->book($transaction, $extra['person_konto_nr'], $extra['transaction_type'], $extra['posting_key'] ?? '');

        $result = new TransactionBookerResult();
        $result->setBooker($this);
        $result->setStatus($result::STATUS_OK);

        return $result;
    }

    public function buildForm(form_element_container $form): void
    {
        $fieldset = $form->addFieldset();
        $fieldset->setLabel('Konto');

        $account_element = new form_element_text();
        $account_element->setLabel('Datev-Kontonummer');
        $account_element->setName('person_konto_nr');

        $validator = new FormValidatorRegxp('~^[0-9]{6,7}$~u', 'Die Kontonummer muss 6- oder 7-stellig sein.');

        $account_element->addValidator($validator);

        $fieldset->addElement($account_element);


        $validator = new FormValidatorRegxp('~^[0-9]{1,4}$~u', 'Ungültiger Buchungsschlüssel.');#
        $validator->setCanEmpty(true);

        $posting_key = new form_element_text();
        $posting_key->setLabel('Buchungsschlüssel');
        $posting_key->setName('posting_key');
        $posting_key->addValidator($validator);

        $fieldset->add($posting_key);

        $select = new form_element_select();
        $select->setName('transaction_type');
        $select->setLabel('Transaktions-Typ');
        $select->setOptionsSimple($this->possible_transaction_types);
        $select->addEmptyOption(true);
        $select->addValidator(new FormValidatorEmpty());

        $fieldset->add($select);
    }

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult
    {
        $this->book($transaction, $data['person_konto_nr'], $data['transaction_type'], $data['posting_key']);

        return new TransactionBookerProcessFormResult();
    }

    private function book(BankTransaction $transaction, string $person_konto_nr, string $transaction_type, string $posting_key = ''): void
    {
        $transaction->setTransactionStatus(BankTransaction::TRANSACTION_STATUS_BOOKED);
        $transaction->setPersonKontoNr($person_konto_nr);
        $transaction->setPostingKey($posting_key);
        $transaction->setTransactionType($transaction_type);
        $transaction->save();
    }
}
