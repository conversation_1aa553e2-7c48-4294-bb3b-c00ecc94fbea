<?php

namespace wws\BankAccountBooker\TransactionBooker;

use wws\core\Informer;

class TransactionBookerProcessFormResult
{
    /**
     * @var string
     */
    private $error_message = '';

    /**
     * @var string
     */
    private $warning_message;

    public function setWarning(string $warning_message): void
    {
        $this->warning_message = $warning_message;
    }

    public function setErrorMessage(string $error_message): void
    {
        $this->error_message = $error_message;
    }

    public function getErrorMessage(): string
    {
        return $this->error_message;
    }

    public function isSuccess(): bool
    {
        return $this->error_message === '';
    }

    public function isError(): bool
    {
        return !$this->isSuccess();
    }

    public function applyToInformer(Informer $info): void
    {
        if ($this->isError()) {
            $info->fail($this->error_message);
        }

        if ($this->warning_message) {
            $info->warn($this->warning_message);
        }
    }
}
