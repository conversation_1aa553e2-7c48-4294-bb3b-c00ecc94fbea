<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\form\form_element_container;
use wws\BankAccount\BankTransaction;

class TransactionBookerModuleReset extends TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule, TransactionBookerModuleForm
{


    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult
    {
        return new TransactionBookerResult();
    }

    public function book(BankTransaction $transaction): void
    {
        $transaction->setOrderId(null);
        $transaction->setCustomerId(null);
        $transaction->setPersonKontoNr('');
        $transaction->setPostingKey('');
        $transaction->setClassificatorName('');
        $transaction->setClassificatorExtra([]);
        $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_UNKNOWN);
        $transaction->setTransactionType($transaction::TRANSACTION_TYPE_UNKNOWN);
        $transaction->save();
    }


    public function buildForm(form_element_container $form): void
    {
    }

    public function getDescription(): string
    {
        return 'Buchung zurücksetzen';
    }

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult
    {
        $this->book($transaction);

        return new TransactionBookerProcessFormResult();
    }
}
