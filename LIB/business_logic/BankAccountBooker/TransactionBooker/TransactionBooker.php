<?php

namespace wws\BankAccountBooker\TransactionBooker;

use wws\BankAccount\BankTransaction;

class TransactionBooker
{
    /**
     * @var TransactionBookerModule[]
     */
    private $transaction_booker_modules;


    public function addTransactionBookerModule(TransactionBookerModule $module): void
    {
        $this->transaction_booker_modules[] = $module;
    }

    public function bookTransaction(BankTransaction $transaction): ?TransactionBookerResult
    {
        foreach ($this->transaction_booker_modules as $booker) {
            if ($booker->isProcessable($transaction)) {
                return $booker->bookByClassification($transaction);
            }
        }
        return null;
    }

    public function bookFullToOrder(BankTransaction $transaction, int $order_id): void
    {
        $transaction->setClassificatorName('order');
        $transaction->setClassificatorExtra(['order_id' => $order_id]);

        $this->bookTransaction($transaction);
    }

    public function markAsFiltered(BankTransaction $transaction): void
    {
        $transaction->setClassificatorName('filter');

        $this->bookTransaction($transaction);
    }
}
