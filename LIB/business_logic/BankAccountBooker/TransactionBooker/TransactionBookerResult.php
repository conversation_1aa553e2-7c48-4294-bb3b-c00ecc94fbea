<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\Json;
use function get_class;

class TransactionBookerResult
{
    const STATUS_OK = 'ok';
    const STATUS_WARN = 'warn';
    const STATUS_FAIL = 'fail';

    private $booker = '';
    private $status = '';
    private $message = '';

    public function setBooker(TransactionBookerModule $transaction_booker): void
    {
        $this->booker = get_class($transaction_booker);
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setMessage(string $message): void
    {
        $this->message = $message;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function getAsText(): string
    {
        if (!$this->status) {
            return '';
        }

        return $this->status . ': ' . $this->getMessage();
    }

    public function serialize(): string
    {
        $values = [
            'booker' => $this->booker
        ];

        if ($this->status) {
            $values['status'] = $this->status;
        }

        if ($this->message) {
            $values['message'] = $this->message;
        }

        return json_encode($values);
    }

    public function unserialize(string $json): void
    {
        if (!$json) {
            return;
        }

        $values = Json::decode($json);

        $this->booker = $values['booker'];
    }
}