<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\form\form_element_container;
use wws\BankAccount\BankTransaction;
use wws\business_structure\Shop;
use wws\Order\Form\FormElementOrderSearch;
use wws\Order\OrderRepository;

class TransactionBookerModuleOrderUnchecked extends TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule, TransactionBookerModuleForm
{
    /**
     * @var OrderRepository
     */
    private $order_repository;

    /**
     * Hat aktuell nur einfluss auf processFormRequest(). Es ist derzeit nicht sichergestellt, dass dieses Module nur für Zahlungsbewegung genutzt wird. (Gebühren)
     *
     * @var string
     */
    private $transaction_type = BankTransaction::TRANSACTION_TYPE_DEBITOR;

    private $description = 'Zahlungseingang für Auftrag (passive Zuordnung: keine Prüfung / Auftrag wird nicht geändert)';


    public function __construct(OrderRepository $order_repository_new)
    {
        $this->order_repository = $order_repository_new;
    }

    /**
     * @param string $transaction_type
     */
    public function setTransactionType(string $transaction_type): void
    {
        $this->transaction_type = $transaction_type;
    }

    /**
     * @param string $description
     */
    public function setDescription(string $description): void
    {
        $this->description = $description;
    }


    /**
     * Bucht die Transaktion
     *
     * @param BankTransaction $transaction
     * @return TransactionBookerResult
     */
    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult
    {
        $classificator_values = $transaction->getClassificatorExtra();
        $order_id = $classificator_values['order_id'] ?? null;

        $result = new TransactionBookerResult();
        $result->setBooker($this);

        if (!$order_id) {
            $result->setStatus('fail');
            $result->setMessage('$order_id is missing');

            $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_FAILED);
            $this->setTransactionTypeToDebitorIfUnknown($transaction);
            $transaction->setBookerResult($result);
            $transaction->save();
            return $result;
        }

        //prüfen ob order_id existiert
        $customer_id = $this->order_repository->getCustomerIdByOrderId($order_id);

        if (!$customer_id) {
            $result->setStatus('fail');
            $result->setMessage('$order_id is no valid order.');

            $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_FAILED);
            $transaction->setBookerResult($result);
            $this->setTransactionTypeToDebitorIfUnknown($transaction);
            $transaction->save();
            return $result;
        }

        $result->setStatus($result::STATUS_OK);

        $this->book($transaction, $order_id, $customer_id);
        $this->setTransactionTypeToDebitorIfUnknown($transaction);

        return $result;
    }

    private function book(BankTransaction $transaction, int $order_id, int $customer_id): void
    {
        $transaction->setOrderId($order_id);
        $transaction->setCustomerId($customer_id);
        $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_BOOKED);
        $transaction->save();
    }


    public function buildForm(form_element_container $form): void
    {
        $fieldset = $form->addFieldset();
        $fieldset->setLegend('Zahlungseingang');
        $auftnr_field = $fieldset->add('text', 'auftnr', 'Auftnr');
        //$auftnr_field->addJsEvent('change', 'alert("hallo");');

        $auft_search = new FormElementOrderSearch();
        $auft_search->setDisplayPaymentMethod(true);
        $auft_search->setAmountSearch(true);

        //$auft_search->setShopId($this->user->getUserId());
        $auft_search->setShopId(Shop::ALLEGO);
        $auft_search->setTargetElement($auftnr_field);

        $fieldset->add($auft_search, 'search2', '');
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult
    {
        $booker_result = new TransactionBookerProcessFormResult();

        if (!$data['auftnr']) {
            $booker_result->setErrorMessage('Gebe eine Auftragsnummer an.');
            return $booker_result;
        }

        $order = $this->order_repository->tryGetOrderByAuftnr(trim($data['auftnr']));

        if (!$order) {
            $booker_result->setErrorMessage('Die angegebene Auftragsnummer ist nicht gültig.');
            return $booker_result;
        }

        $extra = [
            'order_id' => $order->getOrderId(),
            'zahlung_erhalten_mail' => !empty($data['zahlung_erhalten_mail'])
        ];

        $transaction->setClassificatorExtra($extra);
        $transaction->setTransactionType($this->transaction_type);

        $this->book($transaction, $order->getOrderId(), $order->getCustomerId());

        return $booker_result;
    }
}
