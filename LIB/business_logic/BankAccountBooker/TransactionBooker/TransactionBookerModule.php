<?php

namespace wws\BankAccountBooker\TransactionBooker;

use wws\BankAccount\BankTransaction;

interface TransactionBookerModule
{
    /**
     * prüft ob eine Transaktion von dem Booker buchbar ist
     *
     * @param BankTransaction $transaction
     * @return bool
     */
    public function isProcessable(BankTransaction $transaction): bool;

    /**
     * Bucht die Transaktion
     *
     * @param BankTransaction $transaction
     * @return TransactionBookerResult
     */
    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult;
}
