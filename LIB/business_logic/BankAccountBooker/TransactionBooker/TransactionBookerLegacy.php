<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\db\db_generic;
use wws\BankAccount\BankTransaction;

/**
 * @deprecated klassifizieren und dann über AutoBooker
 *
 * Class TransactionBookerLegacy
 * @package wws\BankAccountBooker\TransactionBooker
 */
class TransactionBookerLegacy
{
    protected db_generic $db;

    /**
     * @var int
     */
    private $account_id;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    /**
     * @param int $account_id
     */
    public function setAccountId(int $account_id): void
    {
        $this->account_id = $account_id;
    }

    /**
     * @return int
     */
    public function getAccountId(): int
    {
        return $this->account_id;
    }

    public function bookFullToOrder(int $transaction_id, int $order_id): void
    {
        $this->db->query("
            UPDATE
                buchhaltung_bank_transactions
            SET
                buchhaltung_bank_transactions.order_id = " . $order_id . ",
                buchhaltung_bank_transactions.transaction_status = '" . BankTransaction::TRANSACTION_STATUS_BOOKED . "'
            WHERE
                buchhaltung_bank_transactions.transaction_id = " . $transaction_id . "
        ");
    }

    public function bookFullToPersonKontoNr(int $transaction_id, int $person_konto_nr, string $transaction_type = BankTransaction::TRANSACTION_TYPE_MISC, ?int $order_id = null): void
    {
        $this->db->query("
            UPDATE
                buchhaltung_bank_transactions
            SET
                buchhaltung_bank_transactions.order_id = " . $this->db->quote($order_id) . ",
                buchhaltung_bank_transactions.transaction_status = '" . BankTransaction::TRANSACTION_STATUS_BOOKED . "',
                buchhaltung_bank_transactions.transaction_type = '" . $this->db->escape($transaction_type) . "',
                buchhaltung_bank_transactions.person_konto_nr = " . $person_konto_nr . "
            WHERE
                buchhaltung_bank_transactions.transaction_id = " . $transaction_id . "
        ");
    }

    public function bookAsFee(int $transaction_id, ?int $order_id = null): void
    {
        $this->db->query("
            UPDATE
                buchhaltung_bank_transactions
            SET
                buchhaltung_bank_transactions.order_id = " . $this->db->quote($order_id) . ",
                buchhaltung_bank_transactions.transaction_status = '" . BankTransaction::TRANSACTION_STATUS_BOOKED . "',
                buchhaltung_bank_transactions.transaction_type = '" . BankTransaction::TRANSACTION_TYPE_FEE . "'
            WHERE
                buchhaltung_bank_transactions.transaction_id = " . $transaction_id . "
        ");
    }

    public function markAsFiltered(int $transaction_id): void
    {
        $this->db->query("
            UPDATE
                buchhaltung_bank_transactions
            SET
                buchhaltung_bank_transactions.transaction_status = '" . BankTransaction::TRANSACTION_STATUS_FILTERED . "'
            WHERE
                buchhaltung_bank_transactions.transaction_id = " . $transaction_id . "
        ");
    }

    public function bookAstTransit(int $transaction_id): void
    {
        $this->db->query("
            UPDATE
                buchhaltung_bank_transactions
            SET
                buchhaltung_bank_transactions.transaction_type = '" . BankTransaction::TRANSACTION_TYPE_TRANSIT . "',
                buchhaltung_bank_transactions.transaction_status = '" . BankTransaction::TRANSACTION_STATUS_BOOKED . "'
            WHERE
                buchhaltung_bank_transactions.transaction_id = " . $transaction_id . "
        ");
    }
}
