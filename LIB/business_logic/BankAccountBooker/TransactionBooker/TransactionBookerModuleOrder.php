<?php

namespace wws\BankAccountBooker\TransactionBooker;

use bqp\db\db_generic;
use bqp\form\form_element_checkbox;
use bqp\form\form_element_container;
use env;
use wws\BankAccount\BankTransaction;
use wws\Order\Form\FormElementOrderSearch;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderPaymentReceipt;
use wws\Order\OrderRepository;

class TransactionBookerModuleOrder extends TransactionBookerModuleClassificatorGeneric implements TransactionBookerModule, TransactionBookerModuleForm
{
    public const ERROR_MODE_NOTICE = 'notice';
    public const ERROR_MODE_FAIL = 'fail';

    private OrderRepository $order_repository;
    private db_generic $db;


    private string $error_mode = self::ERROR_MODE_FAIL;

    public function __construct(OrderRepository $order_repository, db_generic $db)
    {
        $this->order_repository = $order_repository;
        $this->db = $db;
    }

    public function setErrorMode(string $error_mode): void
    {
        $this->error_mode = $error_mode;
    }

    /**
     * Bucht die Transaktion
     *
     * @param BankTransaction $transaction
     * @return TransactionBookerResult
     */
    public function bookByClassification(BankTransaction $transaction): TransactionBookerResult
    {
        $classificator_values = $transaction->getClassificatorExtra();
        $order_id = $classificator_values['order_id'] ?? null;
        $send_payment_receipt_mail = $classificator_values['zahlung_erhalten_mail'] ?? null;

        $result = new TransactionBookerResult();
        $result->setBooker($this);
        $result->setStatus($result::STATUS_OK);

        if (!$order_id) {
            $result->setStatus($result::STATUS_FAIL);
            $result->setMessage('$order_id is missing');

            $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_FAILED);
            $transaction->setBookerResult($result);
            $this->setTransactionTypeToDebitorIfUnknown($transaction);
            $transaction->save();
            return $result;
        }

        //prüfen ob order_id existiert
        $customer_id = $this->order_repository->getCustomerIdByOrderId($order_id);

        if (!$customer_id) {
            $result->setStatus($result::STATUS_FAIL);
            $result->setMessage('$order_id is no valid order.');

            $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_FAILED);
            $this->setTransactionTypeToDebitorIfUnknown($transaction);
            $transaction->setBookerResult($result);
            $transaction->save();
            return $result;
        }

        $order = $this->order_repository->load($order_id);

        $this->book($transaction, $order, $send_payment_receipt_mail);

        return $result;
    }


    private function book(BankTransaction $transaction, Order $order, ?bool $send_payment_receipt_mail = null): void
    {
        $transaction->setOrderId($order->getOrderId());
        $transaction->setCustomerId($order->getCustomerId());
        $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_BOOKED);
        $transaction->setTransactionType($transaction::TRANSACTION_TYPE_DEBITOR);

        $processed = false;

        if ($order->getOrderType() === OrderConst::ORDER_TYPE_OFFER) {
            //scheinbar bekommen wir auch Zahlungen für Angebote und keiner bekommst mit. So bekommt das zumindestens die Buchhaltung mit.
            $processed = false;
        } else {
            if (abs($order->getRechnungsBetrag() - $transaction->getAmount()) <= 0.01) {
                if ($order->getZahlungsart() == OrderConst::PAYMENT_VORKASSE) {
                    $payment = new OrderPaymentReceipt();
                    $payment->setAmount($transaction->getAmount());
                    $payment->setComplete(true);

                    if ($send_payment_receipt_mail !== null) {
                        $payment->setMailPaymentReceipt($send_payment_receipt_mail);
                    } else {
                        if ($order->getZahlungsartStatus() == OrderConst::PAYMENT_STATUS_VORKASSE_OPEN) {
                            $payment->setMailPaymentReceipt(true);
                        }
                    }

                    $order->addPaymentReceipt($payment);
                    $order->save();
                    $processed = true;
                }

                if ($order->getZahlungsart() == OrderConst::PAYMENT_RECHNUNG || $order->getZahlungsart() == OrderConst::PAYMENT_VERRECHNUNG) {
                    $payment = new OrderPaymentReceipt();
                    $payment->setAmount($transaction->getAmount());
                    $payment->setComplete(true);

                    $payment->setMailPaymentReceipt(false);
                    $payment->setMailPaymentReceiptPartial(false);

                    $order->addPaymentReceipt($payment);
                    $order->save();
                    $processed = true;
                }

                if ($order->getMinStatus() == OrderConst::STATUS_EBAY_ZAHLUNGSABWICKLUNG) {
                    $order->setZahlungsart(OrderConst::PAYMENT_VORKASSE);
                    $order->setStatus(OrderConst::STATUS_AB_LEERFELD);

                    $payment = new OrderPaymentReceipt();
                    $payment->setAmount($transaction->getAmount());
                    $payment->setComplete(true);
                    $payment->setMailPaymentReceipt(true);

                    $order->addPaymentReceipt($payment);
                    $order->save();

                    $processed = true;
                }

                if ($order->getZahlungsart() == OrderConst::PAYMENT_KREDITKARTE) {
                    $processed = true;
                }
            }
        }

        if (env::isK11()) {
            //k11 -> soll rückzahlungen fixen?!
            if (!$processed && $transaction->getAmount() < 0
                && (abs($order->getRechnungsBetrag()) - abs($transaction->getAmount())) <= 0.01) {
                // Vollständige Rückzahlung
                $processed = true;
            }
        }

        if (!$processed) {
            //alle nicht automatisch verarbeiteten zahlungen markieren -> werden über ein weiteres script abgearbeitet
            $transaction->setTransactionStatus($transaction::TRANSACTION_STATUS_FAILED);
        }

        $transaction->save();
    }


    public function buildForm(form_element_container $form): void
    {
        $fieldset = $form->addFieldset();
        $fieldset->setLegend('Zahlungseingang');
        $auftnr_field = $fieldset->add('text', 'auftnr', 'Auftnr');
        //$auftnr_field->addJsEvent('change', 'alert("hallo");');

        $auft_search = new FormElementOrderSearch();
        $auft_search->setDisplayPaymentMethod(true);
        $auft_search->setAmountSearch(true);

        //$auft_search->setShopId($this->user->getUserId());
        $auft_search->setShopId(1);
        $auft_search->setTargetElement($auftnr_field);

        $fieldset->add($auft_search, 'search2', '');

        $checkbox = new form_element_checkbox();
        $checkbox->setChecked(true);
        $fieldset->add($checkbox, 'zahlung_erhalten_mail', 'Mail senden - Zahlungseingang');
    }

    public function processFormRequest(BankTransaction $transaction, array $data): TransactionBookerProcessFormResult
    {
        $booker_result = new TransactionBookerProcessFormResult();

        if (!$data['auftnr']) {
            $booker_result->setErrorMessage('Gebe eine Auftragsnummer an.');
            return $booker_result;
        }

        $order = $this->order_repository->tryGetOrderByAuftnr($data['auftnr']);

        if (!$order) {
            $booker_result->setErrorMessage('Die angegebene Auftragsnummer ist nicht gültig.');
            return $booker_result;
        }

        $extra = [
            'order_id' => $order->getOrderId()
        ];

        $transaction->setClassificatorExtra($extra);

        $this->book($transaction, $order, (bool)$data['zahlung_erhalten_mail']);


        if ($transaction->getTransactionStatus() === $transaction::TRANSACTION_STATUS_FAILED) {
            $booker_result->setWarning('Auftrag konnte nicht gebucht werden. Der Auftrag muss ggf. manuell freigegeben werden.');
        }

        return $booker_result;
    }

    public function getDescription(): string
    {
        return 'Zahlungseingang für Auftrag';
    }
}
