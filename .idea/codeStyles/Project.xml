<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="LINE_SEPARATOR" value="&#10;" />
    <option name="RIGHT_MARGIN" value="280" />
    <option name="SOFT_MARGINS" value="120" />
    <DBN-PSQL>
      <case-options enabled="false">
        <option name="KEYWORD_CASE" value="lower" />
        <option name="FUNCTION_CASE" value="lower" />
        <option name="PARAMETER_CASE" value="lower" />
        <option name="DATATYPE_CASE" value="lower" />
        <option name="OBJECT_CASE" value="preserve" />
      </case-options>
      <formatting-settings enabled="false" />
    </DBN-PSQL>
    <DBN-SQL>
      <case-options enabled="false">
        <option name="KEYWORD_CASE" value="lower" />
        <option name="FUNCTION_CASE" value="lower" />
        <option name="PARAMETER_CASE" value="lower" />
        <option name="DATATYPE_CASE" value="lower" />
        <option name="OBJECT_CASE" value="preserve" />
      </case-options>
      <formatting-settings enabled="false">
        <option name="STATEMENT_SPACING" value="one_line" />
        <option name="CLAUSE_CHOP_DOWN" value="chop_down_if_statement_long" />
        <option name="ITERATION_ELEMENTS_WRAPPING" value="chop_down_if_not_single" />
      </formatting-settings>
    </DBN-SQL>
    <PHPCodeStyleSettings>
      <option name="LOWER_CASE_BOOLEAN_CONST" value="true" />
      <option name="LOWER_CASE_NULL_CONST" value="true" />
      <option name="ELSE_IF_STYLE" value="COMBINE" />
      <option name="VARIABLE_NAMING_STYLE" value="SNAKE_CASE" />
      <option name="KEEP_RPAREN_AND_LBRACE_ON_ONE_LINE" value="true" />
      <option name="SPACES_WITHIN_SHORT_ECHO_TAGS" value="false" />
    </PHPCodeStyleSettings>
    <SqlCodeStyleSettings version="7">
      <option name="KEYWORD_CASE" value="2" />
      <option name="IDENTIFIER_CASE" value="1" />
      <option name="QUERY_EL_LINE" value="101" />
      <option name="QUERY_EL_COMMA" value="2" />
      <option name="QUERY_IN_ONE_STRING" value="1" />
      <option name="SELECT_EL_LINE" value="101" />
      <option name="SELECT_NEW_LINE_AFTER_ALL_DISTINCT" value="true" />
      <option name="SELECT_KEEP_N_ITEMS_IN_LINE" value="0" />
      <option name="SELECT_ALIGN_AS" value="false" />
      <option name="FROM_EL_LINE" value="101" />
      <option name="FROM_EL_COMMA" value="0" />
    </SqlCodeStyleSettings>
    <editorconfig>
      <option name="ENABLED" value="false" />
    </editorconfig>
    <codeStyleSettings language="JavaScript">
      <option name="CLASS_BRACE_STYLE" value="2" />
      <option name="METHOD_BRACE_STYLE" value="2" />
    </codeStyleSettings>
    <codeStyleSettings language="PHP">
      <option name="BLANK_LINES_AFTER_PACKAGE" value="1" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="5" />
      <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
      <option name="METHOD_PARAMETERS_RPAREN_ON_NEXT_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_WRAP" value="5" />
      <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_RBRACE_ON_NEXT_LINE" value="true" />
      <option name="IF_BRACE_FORCE" value="3" />
      <option name="DOWHILE_BRACE_FORCE" value="3" />
      <option name="WHILE_BRACE_FORCE" value="3" />
      <option name="FOR_BRACE_FORCE" value="3" />
      <indentOptions>
        <option name="SMART_TABS" value="true" />
      </indentOptions>
      <arrangement>
        <groups>
          <group>
            <type>GETTERS_AND_SETTERS</type>
            <order>KEEP</order>
          </group>
        </groups>
      </arrangement>
    </codeStyleSettings>
    <codeStyleSettings language="SCSS">
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>