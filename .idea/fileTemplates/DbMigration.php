<?php
use bqp\db\db_generic;
use bqp\db\Migration\DbMigration;
use bqp\db\Migration\DbMigrationResult;
use bqp\db\Migration\DbMigrationTools;

//file-name="m\${YEAR}\${MONTH}\${DAY}\${HOUR}\${MINUTE}\${SECOND}_\${NAME}"

class m${YEAR}${MONTH}${DAY}${HOUR}${MINUTE}${SECOND}_${NAME} implements DbMigration
{
    
    public function run(db_generic \$db, DbMigrationTools \$tools): DbMigrationResult
    {
        //auf idempotenz achten!

        return new DbMigrationResult();
    }

}
