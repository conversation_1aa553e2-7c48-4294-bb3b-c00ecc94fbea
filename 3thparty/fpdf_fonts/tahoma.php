<?php
$type='TrueType';
$name='Tahoma';
$desc=['Ascent'=>1000,'Descent'=>-207,'CapHeight'=>727,'Flags'=>32,'FontBBox'=>'[-600 -207 1338 1034]','ItalicAngle'=>0,'StemV'=>70,'MissingWidth'=>1000];
$up=-83;
$ut=63;
$cw=[
	chr(0)=>1000,chr(1)=>1000,chr(2)=>1000,chr(3)=>1000,chr(4)=>1000,chr(5)=>1000,chr(6)=>1000,chr(7)=>1000,chr(8)=>1000,chr(9)=>1000,chr(10)=>1000,chr(11)=>1000,chr(12)=>1000,chr(13)=>1000,chr(14)=>1000,chr(15)=>1000,chr(16)=>1000,chr(17)=>1000,chr(18)=>1000,chr(19)=>1000,chr(20)=>1000,chr(21)=>1000,
	chr(22)=>1000,chr(23)=>1000,chr(24)=>1000,chr(25)=>1000,chr(26)=>1000,chr(27)=>1000,chr(28)=>1000,chr(29)=>1000,chr(30)=>1000,chr(31)=>1000,' '=>313,'!'=>332,'"'=>401,'#'=>728,'$'=>546,'%'=>977,'&'=>674,'\''=>211,'('=>383,')'=>383,'*'=>546,'+'=>728,
	','=>303,'-'=>363,'.'=>303,'/'=>382,'0'=>546,'1'=>546,'2'=>546,'3'=>546,'4'=>546,'5'=>546,'6'=>546,'7'=>546,'8'=>546,'9'=>546,':'=>354,';'=>354,'<'=>728,'='=>728,'>'=>728,'?'=>474,'@'=>909,'A'=>600,
	'B'=>589,'C'=>601,'D'=>678,'E'=>561,'F'=>521,'G'=>667,'H'=>675,'I'=>373,'J'=>417,'K'=>588,'L'=>498,'M'=>771,'N'=>667,'O'=>708,'P'=>551,'Q'=>708,'R'=>621,'S'=>557,'T'=>584,'U'=>656,'V'=>597,'W'=>902,
	'X'=>581,'Y'=>576,'Z'=>559,'['=>383,'\\'=>382,']'=>383,'^'=>728,'_'=>546,'`'=>546,'a'=>525,'b'=>553,'c'=>461,'d'=>553,'e'=>526,'f'=>318,'g'=>553,'h'=>558,'i'=>229,'j'=>282,'k'=>498,'l'=>229,'m'=>840,
	'n'=>558,'o'=>543,'p'=>553,'q'=>553,'r'=>360,'s'=>446,'t'=>334,'u'=>558,'v'=>498,'w'=>742,'x'=>495,'y'=>498,'z'=>444,'{'=>480,'|'=>382,'}'=>480,'~'=>728,chr(127)=>1000,chr(128)=>1000,chr(129)=>1000,chr(130)=>1000,chr(131)=>1000,
	chr(132)=>1000,chr(133)=>1000,chr(134)=>1000,chr(135)=>1000,chr(136)=>1000,chr(137)=>1000,chr(138)=>1000,chr(139)=>1000,chr(140)=>1000,chr(141)=>1000,chr(142)=>1000,chr(143)=>1000,chr(144)=>1000,chr(145)=>1000,chr(146)=>1000,chr(147)=>1000,chr(148)=>1000,chr(149)=>1000,chr(150)=>1000,chr(151)=>1000,chr(152)=>1000,chr(153)=>1000,
	chr(154)=>1000,chr(155)=>1000,chr(156)=>1000,chr(157)=>1000,chr(158)=>1000,chr(159)=>1000,chr(160)=>313,chr(161)=>600,chr(162)=>546,chr(163)=>518,chr(164)=>546,chr(165)=>498,chr(166)=>557,chr(167)=>546,chr(168)=>546,chr(169)=>557,chr(170)=>557,chr(171)=>584,chr(172)=>559,chr(173)=>363,chr(174)=>559,chr(175)=>559,
	chr(176)=>471,chr(177)=>525,chr(178)=>546,chr(179)=>274,chr(180)=>546,chr(181)=>361,chr(182)=>446,chr(183)=>546,chr(184)=>546,chr(185)=>446,chr(186)=>446,chr(187)=>468,chr(188)=>444,chr(189)=>546,chr(190)=>444,chr(191)=>444,chr(192)=>621,chr(193)=>600,chr(194)=>600,chr(195)=>600,chr(196)=>600,chr(197)=>498,
	chr(198)=>601,chr(199)=>601,chr(200)=>601,chr(201)=>561,chr(202)=>561,chr(203)=>561,chr(204)=>561,chr(205)=>373,chr(206)=>373,chr(207)=>678,chr(208)=>698,chr(209)=>667,chr(210)=>667,chr(211)=>708,chr(212)=>708,chr(213)=>708,chr(214)=>708,chr(215)=>728,chr(216)=>621,chr(217)=>656,chr(218)=>656,chr(219)=>656,
	chr(220)=>656,chr(221)=>576,chr(222)=>584,chr(223)=>548,chr(224)=>360,chr(225)=>525,chr(226)=>525,chr(227)=>525,chr(228)=>525,chr(229)=>229,chr(230)=>461,chr(231)=>461,chr(232)=>461,chr(233)=>526,chr(234)=>526,chr(235)=>526,chr(236)=>526,chr(237)=>229,chr(238)=>229,chr(239)=>687,chr(240)=>573,chr(241)=>558,
	chr(242)=>558,chr(243)=>543,chr(244)=>543,chr(245)=>543,chr(246)=>543,chr(247)=>728,chr(248)=>360,chr(249)=>558,chr(250)=>558,chr(251)=>558,chr(252)=>558,chr(253)=>498,chr(254)=>334,chr(255)=>546];
$enc='iso-8859-2';
$diff='128 /.notdef 130 /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef 142 /.notdef 145 /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef 158 /.notdef /.notdef 161 /Aogonek /breve /Lslash 165 /Lcaron /Sacute 169 /Scaron /Scedilla /Tcaron /Zacute 174 /Zcaron /Zdot 177 /aogonek /ogonek /lslash 181 /lcaron /sacute /caron 185 /scaron /scedilla /tcaron /zacute /hungarumlaut /zcaron /zdot /Racute 195 /Abreve 197 /Lacute /Cacute 200 /Ccaron 202 /Eogonek 204 /Ecaron 207 /Dcaron /Dslash /Nacute /Ncaron 213 /Odblacute 216 /Rcaron /Uring 219 /Udblacute 222 /Tcedilla 224 /racute 227 /abreve 229 /lacute /cacute 232 /ccaron 234 /eogonek 236 /ecaron 239 /dcaron /dmacron /nacute /ncaron 245 /odblacute 248 /rcaron /uring 251 /udblacute 254 /tcedilla /dotaccent';
$file='';
