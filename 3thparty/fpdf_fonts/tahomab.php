<?php
$type='TrueType';
$name='Tahoma-Bold';
$desc=['Ascent'=>1000,'Descent'=>-207,'CapHeight'=>727,'Flags'=>32,'FontBBox'=>'[-670 -207 1625 1065]','ItalicAngle'=>0,'StemV'=>120,'MissingWidth'=>1000];
$up=-70;
$ut=98;
$cw=[
	chr(0)=>1000,chr(1)=>1000,chr(2)=>1000,chr(3)=>1000,chr(4)=>1000,chr(5)=>1000,chr(6)=>1000,chr(7)=>1000,chr(8)=>1000,chr(9)=>1000,chr(10)=>1000,chr(11)=>1000,chr(12)=>1000,chr(13)=>1000,chr(14)=>1000,chr(15)=>1000,chr(16)=>1000,chr(17)=>1000,chr(18)=>1000,chr(19)=>1000,chr(20)=>1000,chr(21)=>1000,
	chr(22)=>1000,chr(23)=>1000,chr(24)=>1000,chr(25)=>1000,chr(26)=>1000,chr(27)=>1000,chr(28)=>1000,chr(29)=>1000,chr(30)=>1000,chr(31)=>1000,' '=>293,'!'=>343,'"'=>489,'#'=>818,'$'=>637,'%'=>1199,'&'=>781,'\''=>275,'('=>454,')'=>454,'*'=>637,'+'=>818,
	','=>313,'-'=>431,'.'=>313,'/'=>577,'0'=>637,'1'=>637,'2'=>637,'3'=>637,'4'=>637,'5'=>637,'6'=>637,'7'=>637,'8'=>637,'9'=>637,':'=>363,';'=>363,'<'=>818,'='=>818,'>'=>818,'?'=>566,'@'=>920,'A'=>685,
	'B'=>686,'C'=>667,'D'=>757,'E'=>615,'F'=>581,'G'=>745,'H'=>764,'I'=>483,'J'=>500,'K'=>696,'L'=>572,'M'=>893,'N'=>771,'O'=>770,'P'=>657,'Q'=>770,'R'=>726,'S'=>633,'T'=>612,'U'=>739,'V'=>675,'W'=>1028,
	'X'=>685,'Y'=>670,'Z'=>623,'['=>454,'\\'=>577,']'=>454,'^'=>818,'_'=>637,'`'=>546,'a'=>599,'b'=>632,'c'=>527,'d'=>629,'e'=>594,'f'=>382,'g'=>629,'h'=>640,'i'=>302,'j'=>363,'k'=>603,'l'=>302,'m'=>954,
	'n'=>640,'o'=>617,'p'=>629,'q'=>629,'r'=>434,'s'=>515,'t'=>416,'u'=>640,'v'=>579,'w'=>890,'x'=>604,'y'=>576,'z'=>526,'{'=>623,'|'=>637,'}'=>623,'~'=>818,chr(127)=>1000,chr(128)=>1000,chr(129)=>1000,chr(130)=>1000,chr(131)=>1000,
	chr(132)=>1000,chr(133)=>1000,chr(134)=>1000,chr(135)=>1000,chr(136)=>1000,chr(137)=>1000,chr(138)=>1000,chr(139)=>1000,chr(140)=>1000,chr(141)=>1000,chr(142)=>1000,chr(143)=>1000,chr(144)=>1000,chr(145)=>1000,chr(146)=>1000,chr(147)=>1000,chr(148)=>1000,chr(149)=>1000,chr(150)=>1000,chr(151)=>1000,chr(152)=>1000,chr(153)=>1000,
	chr(154)=>1000,chr(155)=>1000,chr(156)=>1000,chr(157)=>1000,chr(158)=>1000,chr(159)=>1000,chr(160)=>293,chr(161)=>685,chr(162)=>546,chr(163)=>589,chr(164)=>637,chr(165)=>572,chr(166)=>633,chr(167)=>637,chr(168)=>546,chr(169)=>633,chr(170)=>633,chr(171)=>612,chr(172)=>623,chr(173)=>431,chr(174)=>623,chr(175)=>623,
	chr(176)=>520,chr(177)=>599,chr(178)=>546,chr(179)=>335,chr(180)=>546,chr(181)=>490,chr(182)=>515,chr(183)=>546,chr(184)=>546,chr(185)=>515,chr(186)=>515,chr(187)=>619,chr(188)=>526,chr(189)=>546,chr(190)=>526,chr(191)=>526,chr(192)=>726,chr(193)=>685,chr(194)=>685,chr(195)=>685,chr(196)=>685,chr(197)=>572,
	chr(198)=>667,chr(199)=>667,chr(200)=>667,chr(201)=>615,chr(202)=>615,chr(203)=>615,chr(204)=>615,chr(205)=>483,chr(206)=>483,chr(207)=>757,chr(208)=>774,chr(209)=>771,chr(210)=>771,chr(211)=>770,chr(212)=>770,chr(213)=>770,chr(214)=>770,chr(215)=>818,chr(216)=>726,chr(217)=>739,chr(218)=>739,chr(219)=>739,
	chr(220)=>739,chr(221)=>670,chr(222)=>612,chr(223)=>646,chr(224)=>434,chr(225)=>599,chr(226)=>599,chr(227)=>599,chr(228)=>599,chr(229)=>302,chr(230)=>527,chr(231)=>527,chr(232)=>527,chr(233)=>594,chr(234)=>594,chr(235)=>594,chr(236)=>594,chr(237)=>302,chr(238)=>302,chr(239)=>817,chr(240)=>625,chr(241)=>640,
	chr(242)=>640,chr(243)=>617,chr(244)=>617,chr(245)=>617,chr(246)=>617,chr(247)=>818,chr(248)=>434,chr(249)=>640,chr(250)=>640,chr(251)=>640,chr(252)=>640,chr(253)=>576,chr(254)=>416,chr(255)=>546];
$enc='iso-8859-2';
$diff='128 /.notdef 130 /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef 142 /.notdef 145 /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef 158 /.notdef /.notdef 161 /Aogonek /breve /Lslash 165 /Lcaron /Sacute 169 /Scaron /Scedilla /Tcaron /Zacute 174 /Zcaron /Zdot 177 /aogonek /ogonek /lslash 181 /lcaron /sacute /caron 185 /scaron /scedilla /tcaron /zacute /hungarumlaut /zcaron /zdot /Racute 195 /Abreve 197 /Lacute /Cacute 200 /Ccaron 202 /Eogonek 204 /Ecaron 207 /Dcaron /Dslash /Nacute /Ncaron 213 /Odblacute 216 /Rcaron /Uring 219 /Udblacute 222 /Tcedilla 224 /racute 227 /abreve 229 /lacute /cacute 232 /ccaron 234 /eogonek 236 /ecaron 239 /dcaron /dmacron /nacute /ncaron 245 /odblacute 248 /rcaron /uring 251 /udblacute 254 /tcedilla /dotaccent';
$file='';
