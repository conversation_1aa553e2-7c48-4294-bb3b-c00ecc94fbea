%!PS-AdobeFont-1.0: Helvetica75-Bold Copyright [c] 1988, 1990, 1993 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.
%%CreationDate: Wed Sep 27 11:37:35 2006
% Converted from TrueType font 'HelveticaBd___-.ttf' by ttf2pt1 program
%
%%EndComments
12 dict begin
/FontInfo 9 dict dup begin
/version (Version 1.00) readonly def
/Notice (Copyright [c] 1988, 1990, 1993 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.) readonly def
/FullName (Helvetica75-Bold) readonly def
/FamilyName (Helvetica75) readonly def
/Weight (Bold) readonly def
/ItalicAngle 0.000000 def
/isFixedPitch false def
/UnderlinePosition -75 def
/UnderlineThickness 50 def
end readonly def
/FontName /Helvetica75-Bold def
/PaintType 0 def
/StrokeWidth 0 def
/FontType 1 def
/FontMatrix [0.0010000 0 0 0.0010000 0 0] def
/FontBBox {-57 -218 1078 975} readonly def
/Encoding 256 array
dup 0 /.notdef put
dup 1 /.notdef put
dup 2 /.notdef put
dup 3 /.notdef put
dup 4 /.notdef put
dup 5 /.notdef put
dup 6 /.notdef put
dup 7 /.notdef put
dup 8 /.notdef put
dup 9 /.notdef put
dup 10 /.notdef put
dup 11 /.notdef put
dup 12 /.notdef put
dup 13 /.notdef put
dup 14 /.notdef put
dup 15 /.notdef put
dup 16 /.notdef put
dup 17 /.notdef put
dup 18 /.notdef put
dup 19 /.notdef put
dup 20 /.notdef put
dup 21 /.notdef put
dup 22 /.notdef put
dup 23 /.notdef put
dup 24 /.notdef put
dup 25 /.notdef put
dup 26 /.notdef put
dup 27 /.notdef put
dup 28 /.notdef put
dup 29 /.notdef put
dup 30 /.notdef put
dup 31 /.notdef put
dup 32 /space put
dup 33 /exclam put
dup 34 /quotedbl put
dup 35 /numbersign put
dup 36 /dollar put
dup 37 /percent put
dup 38 /ampersand put
dup 39 /quotesingle put
dup 40 /parenleft put
dup 41 /parenright put
dup 42 /asterisk put
dup 43 /plus put
dup 44 /comma put
dup 45 /hyphen put
dup 46 /period put
dup 47 /slash put
dup 48 /zero put
dup 49 /one put
dup 50 /two put
dup 51 /three put
dup 52 /four put
dup 53 /five put
dup 54 /six put
dup 55 /seven put
dup 56 /eight put
dup 57 /nine put
dup 58 /colon put
dup 59 /semicolon put
dup 60 /less put
dup 61 /equal put
dup 62 /greater put
dup 63 /question put
dup 64 /at put
dup 65 /A put
dup 66 /B put
dup 67 /C put
dup 68 /D put
dup 69 /E put
dup 70 /F put
dup 71 /G put
dup 72 /H put
dup 73 /I put
dup 74 /J put
dup 75 /K put
dup 76 /L put
dup 77 /M put
dup 78 /N put
dup 79 /O put
dup 80 /P put
dup 81 /Q put
dup 82 /R put
dup 83 /S put
dup 84 /T put
dup 85 /U put
dup 86 /V put
dup 87 /W put
dup 88 /X put
dup 89 /Y put
dup 90 /Z put
dup 91 /bracketleft put
dup 92 /backslash put
dup 93 /bracketright put
dup 94 /asciicircum put
dup 95 /underscore put
dup 96 /grave put
dup 97 /a put
dup 98 /b put
dup 99 /c put
dup 100 /d put
dup 101 /e put
dup 102 /f put
dup 103 /g put
dup 104 /h put
dup 105 /i put
dup 106 /j put
dup 107 /k put
dup 108 /l put
dup 109 /m put
dup 110 /n put
dup 111 /o put
dup 112 /p put
dup 113 /q put
dup 114 /r put
dup 115 /s put
dup 116 /t put
dup 117 /u put
dup 118 /v put
dup 119 /w put
dup 120 /x put
dup 121 /y put
dup 122 /z put
dup 123 /braceleft put
dup 124 /bar put
dup 125 /braceright put
dup 126 /asciitilde put
dup 127 /.notdef put
dup 128 /.notdef put
dup 129 /.notdef put
dup 130 /quotesinglbase put
dup 131 /florin put
dup 132 /quotedblbase put
dup 133 /ellipsis put
dup 134 /dagger put
dup 135 /daggerdbl put
dup 136 /circumflex put
dup 137 /perthousand put
dup 138 /Scaron put
dup 139 /guilsinglleft put
dup 140 /OE put
dup 141 /.notdef put
dup 142 /.notdef put
dup 143 /.notdef put
dup 144 /.notdef put
dup 145 /quoteleft put
dup 146 /quoteright put
dup 147 /quotedblleft put
dup 148 /quotedblright put
dup 149 /bullet put
dup 150 /endash put
dup 151 /emdash put
dup 152 /tilde put
dup 153 /trademark put
dup 154 /scaron put
dup 155 /guilsinglright put
dup 156 /oe put
dup 157 /.notdef put
dup 158 /.notdef put
dup 159 /Ydieresis put
dup 160 /.notdef put
dup 161 /exclamdown put
dup 162 /cent put
dup 163 /sterling put
dup 164 /currency put
dup 165 /yen put
dup 166 /brokenbar put
dup 167 /section put
dup 168 /dieresis put
dup 169 /copyright put
dup 170 /ordfeminine put
dup 171 /guillemotleft put
dup 172 /logicalnot put
dup 173 /minus put
dup 174 /registered put
dup 175 /macron put
dup 176 /degree put
dup 177 /plusminus put
dup 178 /twosuperior put
dup 179 /threesuperior put
dup 180 /acute put
dup 181 /mu put
dup 182 /paragraph put
dup 183 /periodcentered put
dup 184 /cedilla put
dup 185 /onesuperior put
dup 186 /ordmasculine put
dup 187 /guillemotright put
dup 188 /onequarter put
dup 189 /onehalf put
dup 190 /threequarters put
dup 191 /questiondown put
dup 192 /Agrave put
dup 193 /Aacute put
dup 194 /Acircumflex put
dup 195 /Atilde put
dup 196 /Adieresis put
dup 197 /Aring put
dup 198 /AE put
dup 199 /Ccedilla put
dup 200 /Egrave put
dup 201 /Eacute put
dup 202 /Ecircumflex put
dup 203 /Edieresis put
dup 204 /Igrave put
dup 205 /Iacute put
dup 206 /Icircumflex put
dup 207 /Idieresis put
dup 208 /Eth put
dup 209 /Ntilde put
dup 210 /Ograve put
dup 211 /Oacute put
dup 212 /Ocircumflex put
dup 213 /Otilde put
dup 214 /Odieresis put
dup 215 /multiply put
dup 216 /Oslash put
dup 217 /Ugrave put
dup 218 /Uacute put
dup 219 /Ucircumflex put
dup 220 /Udieresis put
dup 221 /Yacute put
dup 222 /Thorn put
dup 223 /germandbls put
dup 224 /agrave put
dup 225 /aacute put
dup 226 /acircumflex put
dup 227 /atilde put
dup 228 /adieresis put
dup 229 /aring put
dup 230 /ae put
dup 231 /ccedilla put
dup 232 /egrave put
dup 233 /eacute put
dup 234 /ecircumflex put
dup 235 /edieresis put
dup 236 /igrave put
dup 237 /iacute put
dup 238 /icircumflex put
dup 239 /idieresis put
dup 240 /eth put
dup 241 /ntilde put
dup 242 /ograve put
dup 243 /oacute put
dup 244 /ocircumflex put
dup 245 /otilde put
dup 246 /odieresis put
dup 247 /divide put
dup 248 /oslash put
dup 249 /ugrave put
dup 250 /uacute put
dup 251 /ucircumflex put
dup 252 /udieresis put
dup 253 /yacute put
dup 254 /thorn put
dup 255 /ydieresis put
readonly def
currentdict end
currentfile eexec
dup /Private 16 dict dup begin
/RD{string currentfile exch readstring pop}executeonly def
/ND{noaccess def}executeonly def
/NP{noaccess put}executeonly def
/ForceBold true def
/BlueValues [ -17 0 714 731 508 531 911 920 ] def
/OtherBlues [ -182 -166 ] def
/StdHW [ 65 ] def
/StdVW [ 72 ] def
/StemSnapH [ 21 51 61 65 81 111 144 154 176 263 332 485 ] def
/StemSnapV [ 72 112 130 134 186 202 207 232 248 252 296 389 ] def
/MinFeature {16 16} def
/password 5839 def
/Subrs 5 array
dup 0 {
	3 0 callothersubr pop pop setcurrentpoint return
	} NP
dup 1 {
	0 1 callothersubr return
	} NP
dup 2 {
	0 2 callothersubr return
	} NP
dup 3 {
	return
	} NP
dup 4 {
	1 3 callothersubr pop callsubr return
	} NP
ND
2 index /CharStrings 215 dict dup begin
/.notdef { 
0 502 hsbw
10 480 hstem
9 480 vstem
-1 0 rmoveto
0 500 rlineto
500 0 rlineto
0 -500 rlineto
-500 0 rlineto
closepath
10 10 rmoveto
480 0 rlineto
0 480 rlineto
-480 0 rlineto
0 -480 rlineto
closepath
endchar } ND
/space { 
0 278 hsbw
endchar } ND
/exclam { 
0 278 hsbw
154 60 hstem
213 714 rmoveto
0 -192 rlineto
-39 -308 rlineto
-71 0 rlineto
-38 308 rlineto
0 192 rlineto
148 0 rlineto
closepath
-152 -714 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
-157 0 rlineto
closepath
endchar } ND
/quotedbl { 
0 463 hsbw
192 79 vstem
271 393 rmoveto
0 321 rlineto
107 0 rlineto
0 -321 rlineto
-107 0 rlineto
closepath
-186 0 rmoveto
0 321 rlineto
107 0 rlineto
0 -321 rlineto
-107 0 rlineto
closepath
endchar } ND
/numbersign { 
0 556 hsbw
288 124 hstem
213 96 vstem
88 0 rmoveto
28 198 rlineto
-83 0 rlineto
0 90 rlineto
95 0 rlineto
18 124 rlineto
-83 0 rlineto
0 90 rlineto
95 0 rlineto
28 198 rlineto
97 0 rlineto
-28 -198 rlineto
97 0 rlineto
27 198 rlineto
97 0 rlineto
-27 -198 rlineto
74 0 rlineto
0 -90 rlineto
-87 0 rlineto
-17 -124 rlineto
74 0 rlineto
0 -90 rlineto
-87 0 rlineto
-27 -198 rlineto
-97 0 rlineto
27 198 rlineto
-96 0 rlineto
-28 -198 rlineto
-97 0 rlineto
closepath
251 412 rmoveto
-96 0 rlineto
-18 -124 rlineto
97 0 rlineto
17 124 rlineto
closepath
endchar } ND
/dollar { 
0 556 hsbw
100 192 hstem
498 20 hstem
158 90 vstem
307 108 vstem
248 451 rmoveto
0 163 rlineto
-48 0 -42 -31 0 -52 rrcurveto
0 -44 24 -22 66 -14 rrcurveto
closepath
59 -159 rmoveto
0 -192 rlineto
57 5 51 34 0 59 rrcurveto
0 52 -27 19 -81 23 rrcurveto
closepath
-308 -60 rmoveto
142 0 rlineto
0 -72 43 -48 64 -12 rrcurveto
0 209 rlineto
-6 2 -11 3 -15 4 rrcurveto
-114 30 -86 51 0 118 rrcurveto
0 125 112 82 120 7 rrcurveto
0 77 rlineto
59 0 rlineto
0 -77 rlineto
129 -14 88 -69 8 -130 rrcurveto
-142 0 rlineto
-2 53 -36 43 -45 0 rrcurveto
0 -177 rlineto
12 -3 rlineto
20 -6 13 -3 6 -1 rrcurveto
124 -36 75 -79 0 -117 rrcurveto
0 -102 -116 -98 -134 -9 rrcurveto
0 -85 rlineto
-59 0 rlineto
0 85 rlineto
-150 11 -85 82 -14 156 rrcurveto
closepath
endchar } ND
/percent { 
0 1000 hsbw
61 240 hstem
399 240 hstem
177 128 vstem
693 130 vstem
75 516 rmoveto
0 116 58 82 112 0 rrcurveto
111 0 53 -98 0 -104 rrcurveto
0 -115 -56 -73 -113 0 rrcurveto
-116 0 -49 73 0 119 rrcurveto
closepath
102 -2 rmoveto
0 -58 7 -57 57 0 rrcurveto
55 0 9 63 0 51 rrcurveto
0 40 rlineto
0 36 -16 50 -44 0 rrcurveto
-62 0 -6 -69 0 -56 rrcurveto
closepath
77 -536 rmoveto
414 744 rlineto
85 0 rlineto
-412 -744 rlineto
-87 0 rlineto
closepath
337 205 rmoveto
0 114 58 79 112 0 rrcurveto
110 0 54 -93 0 -104 rrcurveto
0 -115 -57 -78 -112 0 rrcurveto
-111 0 -54 91 0 106 rrcurveto
closepath
102 -1 rmoveto
0 -59 5 -62 59 0 rrcurveto
56 0 10 69 0 51 rrcurveto
0 49 -4 71 -58 0 rrcurveto
-59 0 -9 -63 0 -56 rrcurveto
closepath
endchar } ND
/ampersand { 
0 685 hsbw
103 212 hstem
457 170 hstem
253 129 vstem
382 559 rmoveto
0 37 -31 31 -34 0 rrcurveto
-35 0 -29 -27 0 -36 rrcurveto
0 -41 40 -49 16 -17 rrcurveto
49 32 24 23 0 47 rrcurveto
closepath
7 -393 rmoveto
-122 149 rlineto
-58 -26 -42 -39 0 -53 rrcurveto
0 -52 47 -42 55 0 rrcurveto
48 0 36 21 36 42 rrcurveto
closepath
133 -166 rmoveto
-58 71 rlineto
-58 -58 -63 -27 -80 0 rrcurveto
-128 0 -103 83 0 131 rrcurveto
0 93 66 70 94 44 rrcurveto
-46 56 -22 40 0 56 rrcurveto
0 96 94 67 92 0 rrcurveto
113 0 83 -62 0 -109 rrcurveto
0 -79 -49 -58 -78 -41 rrcurveto
92 -113 rlineto
12 24 9 28 5 33 rrcurveto
123 0 rlineto
-7 -67 -24 -60 -38 -49 rrcurveto
145 -169 rlineto
-174 0 rlineto
closepath
endchar } ND
/quotesingle { 
0 278 hsbw
86 393 rmoveto
0 321 rlineto
107 0 rlineto
0 -321 rlineto
-107 0 rlineto
closepath
endchar } ND
/parenleft { 
0 296 hsbw
184 731 rmoveto
119 0 rlineto
-72 -139 -37 -157 0 -157 rrcurveto
0 -163 36 -155 72 -142 rrcurveto
-118 0 rlineto
-84 139 -48 157 0 154 rrcurveto
0 157 48 184 84 122 rrcurveto
closepath
endchar } ND
/parenright { 
0 296 hsbw
112 -182 rmoveto
-119 0 rlineto
72 138 37 156 0 158 rrcurveto
0 162 -35 156 -73 143 rrcurveto
118 0 rlineto
84 -140 48 -158 0 -153 rrcurveto
0 -158 -48 -184 -84 -120 rrcurveto
closepath
endchar } ND
/asterisk { 
0 407 hsbw
242 714 rmoveto
0 -125 rlineto
116 44 rlineto
27 -77 rlineto
-118 -39 rlineto
74 -98 rlineto
-66 -49 rlineto
-74 103 rlineto
-70 -103 rlineto
-67 49 rlineto
74 98 rlineto
-116 39 rlineto
27 77 rlineto
113 -44 rlineto
0 125 rlineto
80 0 rlineto
closepath
endchar } ND
/plus { 
0 600 hsbw
354 505 rmoveto
0 -199 rlineto
199 0 rlineto
0 -107 rlineto
-199 0 rlineto
0 -199 rlineto
-107 0 rlineto
0 199 rlineto
-199 0 rlineto
0 107 rlineto
199 0 rlineto
0 199 rlineto
107 0 rlineto
closepath
endchar } ND
/comma { 
0 278 hsbw
-94 94 hstem
61 0 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
0 -89 -67 -63 -89 -14 rrcurveto
0 72 rlineto
40 7 31 41 0 46 rrcurveto
-72 0 rlineto
closepath
endchar } ND
/hyphen { 
0 407 hsbw
53 212 rmoveto
0 122 rlineto
301 0 rlineto
0 -122 rlineto
-301 0 rlineto
closepath
endchar } ND
/period { 
0 278 hsbw
60 0 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
-157 0 rlineto
closepath
endchar } ND
/slash { 
0 371 hsbw
-11 -17 rmoveto
279 748 rlineto
114 0 rlineto
-279 -748 rlineto
-114 0 rlineto
closepath
endchar } ND
/zero { 
0 556 hsbw
103 494 hstem
163 230 vstem
21 353 rmoveto
0 193 81 168 175 0 rrcurveto
174 0 84 -167 0 -194 rrcurveto
0 -198 -81 -169 -177 0 rrcurveto
-178 0 -78 171 0 196 rrcurveto
closepath
142 0 rmoveto
0 -111 -1 -139 115 0 rrcurveto
114 0 2 140 0 110 rrcurveto
0 106 -3 138 -113 0 rrcurveto
-114 0 0 -138 0 -106 rrcurveto
closepath
endchar } ND
/one { 
0 556 hsbw
392 700 rmoveto
0 -700 rlineto
-142 0 rlineto
0 454 rlineto
-176 0 rlineto
0 107 rlineto
113 0 78 41 14 98 rrcurveto
113 0 rlineto
closepath
endchar } ND
/two { 
0 556 hsbw
313 279 hstem
173 216 vstem
173 431 rmoveto
-136 0 rlineto
0 170 94 113 160 0 rrcurveto
124 0 116 -91 0 -128 rrcurveto
0 -96 -44 -59 -81 -57 rrcurveto
-6 -3 -7 -5 -9 -7 rrcurveto
-8 -7 -10 -7 -14 -7 rrcurveto
-73 -47 -41 -30 -34 -48 rrcurveto
331 0 rlineto
0 -122 rlineto
-514 0 rlineto
0 147 86 73 135 93 rrcurveto
2 0 2 1 3 4 rrcurveto
3 3 5 4 8 3 rrcurveto
73 45 51 39 0 72 rrcurveto
0 65 -43 43 -62 0 rrcurveto
-80 0 -31 -88 0 -73 rrcurveto
closepath
endchar } ND
/three { 
0 556 hsbw
417 180 hstem
170 207 vstem
228 317 rmoveto
0 100 rlineto
24 0 rlineto
60 0 65 23 0 64 rrcurveto
0 56 -45 37 -54 0 rrcurveto
-68 0 -40 -51 0 -74 rrcurveto
-135 0 rlineto
5 144 94 98 143 0 rrcurveto
110 0 125 -74 0 -120 rrcurveto
0 -70 -36 -56 -62 -17 rrcurveto
0 -2 rlineto
76 -18 48 -63 0 -78 rrcurveto
0 -140 -128 -90 -132 0 rrcurveto
-158 0 -102 95 0 160 rrcurveto
135 0 rlineto
2 -82 43 -56 78 0 rrcurveto
65 0 49 44 0 66 rrcurveto
0 87 -77 17 -85 0 rrcurveto
closepath
endchar } ND
/four { 
0 556 hsbw
279 243 hstem
128 184 vstem
312 0 rmoveto
0 162 rlineto
-296 0 rlineto
0 130 rlineto
304 408 rlineto
127 0 rlineto
0 -421 rlineto
93 0 rlineto
0 -117 rlineto
-93 0 rlineto
0 -162 rlineto
-135 0 rlineto
closepath
0 279 rmoveto
0 243 rlineto
-3 0 rlineto
-181 -243 rlineto
184 0 rlineto
closepath
endchar } ND
/five { 
0 556 hsbw
208 105 hstem
476 107 hstem
168 228 vstem
500 700 rmoveto
0 -117 rlineto
-289 0 rlineto
-28 -159 rlineto
2 -2 rlineto
37 38 44 16 56 0 rrcurveto
135 0 81 -102 0 -136 rrcurveto
0 -139 -122 -113 -138 0 rrcurveto
-135 0 -125 83 0 139 rrcurveto
142 0 rlineto
6 -63 47 -42 62 0 rrcurveto
72 0 49 61 0 74 rrcurveto
0 76 -48 55 -73 0 rrcurveto
-50 0 -28 -16 -29 -40 rrcurveto
-128 0 rlineto
69 387 rlineto
391 0 rlineto
closepath
endchar } ND
/six { 
0 556 hsbw
103 260 hstem
470 50 hstem
173 220 vstem
523 520 rmoveto
-135 0 rlineto
-10 49 -36 38 -48 0 rrcurveto
-100 0 -30 -127 -4 -86 rrcurveto
2 -2 rlineto
38 54 50 24 70 0 rrcurveto
115 0 100 -110 0 -118 rrcurveto
0 -140 -104 -116 -141 0 rrcurveto
-195 0 -74 182 0 180 rrcurveto
0 170 86 196 189 0 rrcurveto
122 0 89 -73 16 -121 rrcurveto
closepath
-237 -157 rmoveto
-72 0 -41 -57 0 -73 rrcurveto
0 -70 45 -60 68 0 rrcurveto
65 0 42 61 0 67 rrcurveto
0 72 -38 60 -69 0 rrcurveto
closepath
endchar } ND
/seven { 
0 556 hsbw
517 700 rmoveto
0 -122 rlineto
-153 -131 -76 -251 0 -196 rrcurveto
-152 0 rlineto
16 205 84 201 135 162 rrcurveto
-332 0 rlineto
0 132 rlineto
478 0 rlineto
closepath
endchar } ND
/eight { 
0 556 hsbw
93 230 hstem
377 2 hstem
418 189 hstem
172 212 vstem
156 212 rmoveto
0 -71 54 -48 70 0 rrcurveto
69 0 51 48 0 70 rrcurveto
0 66 -54 46 -66 0 rrcurveto
-72 0 -52 -42 0 -69 rrcurveto
closepath
-113 311 rmoveto
0 118 121 73 115 0 rrcurveto
122 0 112 -91 0 -97 rrcurveto
0 -70 -37 -56 -65 -21 rrcurveto
0 -2 rlineto
82 -19 49 -64 0 -88 rrcurveto
0 -140 -132 -80 -130 0 rrcurveto
-134 0 -132 77 0 142 rrcurveto
0 89 49 63 83 20 rrcurveto
0 2 rlineto
-67 18 -36 55 0 71 rrcurveto
closepath
129 -13 rmoveto
0 -59 47 -33 60 0 rrcurveto
60 0 45 34 0 58 rrcurveto
0 49 -44 48 -61 0 rrcurveto
-60 0 -47 -37 0 -60 rrcurveto
closepath
endchar } ND
/nine { 
0 556 hsbw
180 50 hstem
337 260 hstem
163 220 vstem
33 180 rmoveto
135 0 rlineto
9 -50 37 -37 48 0 rrcurveto
99 0 28 124 7 89 rrcurveto
-2 2 rlineto
-39 -55 -50 -23 -69 0 rrcurveto
-116 0 -99 109 0 119 rrcurveto
0 135 106 121 139 0 rrcurveto
190 0 79 -185 0 -177 rrcurveto
0 -173 -89 -193 -186 0 rrcurveto
-123 0 -88 72 -16 122 rrcurveto
closepath
237 157 rmoveto
72 0 41 56 0 74 rrcurveto
0 69 -45 61 -68 0 rrcurveto
-64 0 -43 -60 0 -68 rrcurveto
0 -72 37 -60 70 0 rrcurveto
closepath
endchar } ND
/colon { 
0 278 hsbw
154 200 hstem
61 0 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
-157 0 rlineto
closepath
157 508 rmoveto
0 -154 rlineto
-157 0 rlineto
0 154 rlineto
157 0 rlineto
closepath
endchar } ND
/semicolon { 
0 278 hsbw
-94 94 hstem
154 200 hstem
61 0 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
0 -89 -67 -63 -89 -14 rrcurveto
0 72 rlineto
40 7 31 41 0 46 rrcurveto
-72 0 rlineto
closepath
157 508 rmoveto
0 -154 rlineto
-157 0 rlineto
0 154 rlineto
157 0 rlineto
closepath
endchar } ND
/less { 
0 600 hsbw
107 292 hstem
554 514 rmoveto
0 -115 rlineto
-367 -146 rlineto
367 -146 rlineto
0 -115 rlineto
-508 202 rlineto
0 118 rlineto
508 202 rlineto
closepath
endchar } ND
/equal { 
0 600 hsbw
199 107 hstem
553 413 rmoveto
0 -107 rlineto
-505 0 rlineto
0 107 rlineto
505 0 rlineto
closepath
0 -214 rmoveto
0 -107 rlineto
-505 0 rlineto
0 107 rlineto
505 0 rlineto
closepath
endchar } ND
/greater { 
0 600 hsbw
107 292 hstem
46 -8 rmoveto
0 115 rlineto
367 146 rlineto
-367 146 rlineto
0 115 rlineto
508 -202 rlineto
0 -118 rlineto
-508 -202 rlineto
closepath
endchar } ND
/question { 
0 556 hsbw
154 60 hstem
416 198 hstem
179 188 vstem
179 484 rmoveto
-147 0 rlineto
0 135 106 112 137 0 rrcurveto
131 0 118 -82 0 -111 rrcurveto
0 -87 -30 -41 -54 -41 rrcurveto
-3 -1 -3 -2 -2 -3 rrcurveto
-2 -3 -3 -1 -3 -1 rrcurveto
-51 -36 -31 -24 0 -46 rrcurveto
0 -38 rlineto
-135 0 rlineto
0 45 rlineto
0 78 36 40 51 39 rrcurveto
2 0 1 0 0 2 rrcurveto
1 2 2 1 2 1 rrcurveto
46 35 19 21 0 46 rrcurveto
0 60 -31 30 -54 0 rrcurveto
-68 0 -34 -54 -1 -76 rrcurveto
closepath
14 -484 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
-157 0 rlineto
closepath
endchar } ND
/at { 
0 800 hsbw
63 72 hstem
230 236 hstem
561 90 hstem
132 57 vstem
291 201 vstem
622 64 vstem
291 319 rmoveto
0 -54 32 -35 46 0 rrcurveto
70 0 53 81 0 70 rrcurveto
0 49 -35 36 -42 0 rrcurveto
-74 0 -50 -82 0 -65 rrcurveto
closepath
331 226 rmoveto
-63 -258 rlineto
-6 -18 -2 -14 0 -10 rrcurveto
0 -16 5 -8 12 0 rrcurveto
74 0 44 101 0 83 rrcurveto
0 145 -127 101 -144 0 rrcurveto
-162 0 -121 -134 0 -162 rrcurveto
0 -165 130 -127 165 0 rrcurveto
84 0 79 30 52 52 rrcurveto
84 0 rlineto
-68 -103 -112 -59 -125 0 rrcurveto
-207 0 -178 169 0 205 rrcurveto
0 205 176 169 204 0 rrcurveto
173 0 175 -127 0 -178 rrcurveto
0 -149 -128 -141 -107 0 rrcurveto
-42 0 -29 23 -2 34 rrcurveto
-2 0 rlineto
-23 -39 -41 -19 -44 0 rrcurveto
-90 0 -67 84 0 87 rrcurveto
0 121 90 134 128 0 rrcurveto
48 0 47 -22 22 -48 rrcurveto
16 54 rlineto
82 0 rlineto
closepath
endchar } ND
/A { 
0 685 hsbw
276 262 hstem
249 185 vstem
-7 0 rmoveto
271 714 rlineto
161 0 rlineto
267 -714 rlineto
-163 0 rlineto
-54 159 rlineto
-267 0 rlineto
-56 -159 rlineto
-159 0 rlineto
closepath
349 538 rmoveto
-93 -262 rlineto
185 0 rlineto
-90 262 rlineto
-2 0 rlineto
closepath
endchar } ND
/B { 
0 704 hsbw
122 196 hstem
425 167 hstem
226 255 vstem
226 318 rmoveto
0 -196 rlineto
168 0 rlineto
70 0 46 26 0 68 rrcurveto
0 70 -43 32 -70 0 rrcurveto
-171 0 rlineto
closepath
-157 -318 rmoveto
0 714 rlineto
336 0 rlineto
145 0 83 -51 0 -126 rrcurveto
0 -68 -33 -50 -64 -31 rrcurveto
85 -24 46 -67 0 -90 rrcurveto
0 -135 -126 -72 -126 0 rrcurveto
-346 0 rlineto
closepath
157 592 rmoveto
0 -167 rlineto
159 0 rlineto
58 0 38 27 0 58 rrcurveto
0 62 -43 20 -65 0 rrcurveto
-147 0 rlineto
closepath
endchar } ND
/C { 
0 741 hsbw
275 199 hstem
195 356 vstem
697 474 rmoveto
-152 0 rlineto
-10 70 -68 55 -78 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
0 -122 65 -117 129 0 rrcurveto
93 0 57 62 12 98 rrcurveto
152 0 rlineto
-15 -168 -132 -124 -167 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
0 199 145 178 206 0 rrcurveto
151 0 139 -103 18 -154 rrcurveto
closepath
endchar } ND
/D { 
0 741 hsbw
132 450 hstem
226 318 vstem
69 0 rmoveto
0 714 rlineto
308 0 rlineto
193 0 131 -124 0 -229 rrcurveto
0 -198 -122 -163 -202 0 rrcurveto
-308 0 rlineto
closepath
157 582 rmoveto
0 -450 rlineto
140 0 rlineto
110 0 68 96 0 118 rrcurveto
0 153 -57 83 -149 0 rrcurveto
-112 0 rlineto
closepath
endchar } ND
/E { 
0 648 hsbw
132 175 hstem
429 153 hstem
69 0 rmoveto
0 714 rlineto
534 0 rlineto
0 -132 rlineto
-377 0 rlineto
0 -153 rlineto
346 0 rlineto
0 -122 rlineto
-346 0 rlineto
0 -175 rlineto
385 0 rlineto
0 -132 rlineto
-542 0 rlineto
closepath
endchar } ND
/F { 
0 593 hsbw
417 165 hstem
69 0 rmoveto
0 714 rlineto
502 0 rlineto
0 -132 rlineto
-345 0 rlineto
0 -165 rlineto
299 0 rlineto
0 -122 rlineto
-299 0 rlineto
0 -295 rlineto
-157 0 rlineto
closepath
endchar } ND
/G { 
0 759 hsbw
115 154 hstem
386 93 hstem
195 206 vstem
601 0 rmoveto
-16 81 rlineto
-52 -68 -71 -30 -73 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
0 199 145 178 206 0 rrcurveto
142 0 145 -101 14 -151 rrcurveto
-150 0 rlineto
-18 77 -57 43 -76 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
0 -122 65 -117 129 0 rrcurveto
101 0 59 54 10 100 rrcurveto
-158 0 rlineto
0 117 rlineto
300 0 rlineto
0 -386 rlineto
-100 0 rlineto
closepath
endchar } ND
/H { 
0 741 hsbw
226 289 vstem
69 0 rmoveto
0 714 rlineto
157 0 rlineto
0 -274 rlineto
289 0 rlineto
0 274 rlineto
157 0 rlineto
0 -714 rlineto
-157 0 rlineto
0 308 rlineto
-289 0 rlineto
0 -308 rlineto
-157 0 rlineto
closepath
endchar } ND
/I { 
0 295 hsbw
69 0 rmoveto
0 714 rlineto
157 0 rlineto
0 -714 rlineto
-157 0 rlineto
closepath
endchar } ND
/J { 
0 556 hsbw
156 174 vstem
487 714 rmoveto
0 -489 rlineto
0 -132 -84 -110 -154 0 rrcurveto
-127 0 -108 86 0 135 rrcurveto
0 62 rlineto
142 0 rlineto
0 -32 rlineto
0 -78 21 -41 70 0 rrcurveto
66 0 17 50 0 67 rrcurveto
0 482 rlineto
157 0 rlineto
closepath
endchar } ND
/K { 
0 722 hsbw
226 279 vstem
69 0 rmoveto
0 714 rlineto
157 0 rlineto
0 -296 rlineto
279 296 rlineto
196 0 rlineto
-279 -282 rlineto
306 -432 rlineto
-197 0 rlineto
-215 321 rlineto
-90 -91 rlineto
0 -230 rlineto
-157 0 rlineto
closepath
endchar } ND
/L { 
0 593 hsbw
69 0 rmoveto
0 714 rlineto
157 0 rlineto
0 -582 rlineto
348 0 rlineto
0 -132 rlineto
-505 0 rlineto
closepath
endchar } ND
/M { 
0 907 hsbw
216 475 vstem
69 0 rmoveto
0 714 rlineto
221 0 rlineto
167 -491 rlineto
2 0 rlineto
158 491 rlineto
221 0 rlineto
0 -714 rlineto
-147 0 rlineto
0 506 rlineto
-2 0 rlineto
-175 -506 rlineto
-121 0 rlineto
-175 501 rlineto
-2 0 rlineto
0 -501 rlineto
-147 0 rlineto
closepath
endchar } ND
/N { 
0 741 hsbw
216 309 vstem
69 0 rmoveto
0 714 rlineto
156 0 rlineto
298 -479 rlineto
2 0 rlineto
0 479 rlineto
147 0 rlineto
0 -714 rlineto
-157 0 rlineto
-297 478 rlineto
-2 0 rlineto
0 -478 rlineto
-147 0 rlineto
closepath
endchar } ND
/O { 
0 778 hsbw
115 484 hstem
195 388 vstem
38 354 rmoveto
0 199 145 178 206 0 rrcurveto
207 0 144 -177 0 -200 rrcurveto
0 -202 -144 -169 -207 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
closepath
157 0 rmoveto
0 -122 65 -117 129 0 rrcurveto
129 0 65 118 0 121 rrcurveto
0 119 -62 126 -132 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
closepath
endchar } ND
/P { 
0 667 hsbw
378 214 hstem
226 260 vstem
69 0 rmoveto
0 714 rlineto
322 0 rlineto
140 0 107 -99 0 -130 rrcurveto
0 -131 -105 -98 -142 0 rrcurveto
-165 0 rlineto
0 -256 rlineto
-157 0 rlineto
closepath
157 592 rmoveto
0 -214 rlineto
122 0 rlineto
73 0 65 28 0 79 rrcurveto
0 79 -64 28 -74 0 rrcurveto
-122 0 rlineto
closepath
endchar } ND
/Q { 
0 778 hsbw
264 335 hstem
195 388 vstem
736 10 rmoveto
-73 -77 rlineto
-101 92 rlineto
-49 -28 -58 -14 -66 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
0 199 145 178 206 0 rrcurveto
207 0 144 -177 0 -200 rrcurveto
0 -103 -33 -92 -60 -68 rrcurveto
89 -81 rlineto
closepath
-351 176 rmoveto
73 78 rlineto
81 -74 rlineto
29 40 15 55 0 69 rrcurveto
0 119 -62 126 -132 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
0 -122 65 -117 129 0 rrcurveto
33 0 20 0 10 10 rrcurveto
-67 61 rlineto
closepath
endchar } ND
/R { 
0 722 hsbw
336 2 hstem
391 201 hstem
226 280 vstem
69 0 rmoveto
0 714 rlineto
385 0 rlineto
112 0 97 -86 0 -111 rrcurveto
0 -87 -36 -62 -77 -30 rrcurveto
0 -2 rlineto
70 -19 28 -63 6 -85 rrcurveto
0 -22 8 -113 22 -34 rrcurveto
-157 0 rlineto
-12 26 -3 34 -4 53 rrcurveto
-8 107 -15 59 -102 0 rrcurveto
-157 0 rlineto
0 -279 rlineto
-157 0 rlineto
closepath
157 592 rmoveto
0 -201 rlineto
172 0 rlineto
72 0 36 31 0 71 rrcurveto
0 68 -36 31 -72 0 rrcurveto
-172 0 rlineto
closepath
endchar } ND
/S { 
0 649 hsbw
105 504 hstem
176 297 vstem
24 237 rmoveto
152 0 rlineto
0 -90 66 -42 92 0 rrcurveto
59 0 80 19 0 70 rrcurveto
0 78 -81 8 -85 20 rrcurveto
-21 4 -14 4 -11 4 rrcurveto
-115 29 -98 54 0 120 rrcurveto
0 137 136 79 131 0 rrcurveto
142 0 144 -76 0 -154 rrcurveto
-152 0 rlineto
-4 80 -53 28 -84 0 rrcurveto
-59 0 -49 -27 0 -54 rrcurveto
0 -95 91 16 106 -26 rrcurveto
5 0 rlineto
3 -1 5 -1 3 -2 rrcurveto
6 0 rlineto
110 -28 96 -59 0 -120 rrcurveto
0 -136 -109 -93 -188 0 rrcurveto
-156 0 -148 90 0 164 rrcurveto
closepath
endchar } ND
/T { 
0 611 hsbw
227 0 rmoveto
0 582 rlineto
-214 0 rlineto
0 132 rlineto
585 0 rlineto
0 -132 rlineto
-214 0 rlineto
0 -582 rlineto
-157 0 rlineto
closepath
endchar } ND
/U { 
0 741 hsbw
223 295 vstem
675 714 rmoveto
0 -444 rlineto
0 -190 -111 -97 -194 0 rrcurveto
-195 0 -109 96 0 191 rrcurveto
0 444 rlineto
157 0 rlineto
0 -444 rlineto
0 -90 51 -65 96 0 rrcurveto
109 0 39 45 0 110 rrcurveto
0 444 rlineto
157 0 rlineto
closepath
endchar } ND
/V { 
0 630 hsbw
313 2 vstem
638 714 rmoveto
-237 -714 rlineto
-177 0 rlineto
-232 714 rlineto
162 0 rlineto
159 -502 rlineto
2 0 rlineto
161 502 rlineto
162 0 rlineto
closepath
endchar } ND
/W { 
0 944 hsbw
273 2 vstem
470 2 vstem
668 2 vstem
941 714 rmoveto
-192 -714 rlineto
-156 0 rlineto
-121 486 rlineto
-2 0 rlineto
-119 -486 rlineto
-159 0 rlineto
-189 714 rlineto
157 0 rlineto
113 -486 rlineto
2 0 rlineto
124 486 rlineto
147 0 rlineto
122 -492 rlineto
2 0 rlineto
117 492 rlineto
154 0 rlineto
closepath
endchar } ND
/X { 
0 667 hsbw
196 285 vstem
-6 0 rmoveto
250 374 rlineto
-230 340 rlineto
182 0 rlineto
140 -228 rlineto
145 228 rlineto
172 0 rlineto
-229 -341 rlineto
249 -373 rlineto
-187 0 rlineto
-156 247 rlineto
-159 -247 rlineto
-177 0 rlineto
closepath
endchar } ND
/Y { 
0 667 hsbw
168 333 vstem
254 0 rmoveto
0 278 rlineto
-263 436 rlineto
177 0 rlineto
167 -282 rlineto
166 282 rlineto
175 0 rlineto
-265 -440 rlineto
0 -274 rlineto
-157 0 rlineto
closepath
endchar } ND
/Z { 
0 648 hsbw
132 450 hstem
23 0 rmoveto
0 124 rlineto
383 458 rlineto
-353 0 rlineto
0 132 rlineto
562 0 rlineto
0 -124 rlineto
-383 -458 rlineto
393 0 rlineto
0 -132 rlineto
-602 0 rlineto
closepath
endchar } ND
/bracketleft { 
0 333 hsbw
-70 20 hstem
599 20 hstem
68 -182 rmoveto
0 913 rlineto
264 0 rlineto
0 -112 rlineto
-122 0 rlineto
0 -689 rlineto
122 0 rlineto
0 -112 rlineto
-264 0 rlineto
closepath
endchar } ND
/backslash { 
0 371 hsbw
103 731 rmoveto
279 -748 rlineto
-114 0 rlineto
-279 748 rlineto
114 0 rlineto
closepath
endchar } ND
/bracketright { 
0 333 hsbw
-70 20 hstem
599 20 hstem
265 731 rmoveto
0 -913 rlineto
-264 0 rlineto
0 112 rlineto
122 0 rlineto
0 689 rlineto
-122 0 rlineto
0 112 rlineto
264 0 rlineto
closepath
endchar } ND
/asciicircum { 
0 600 hsbw
178 244 vstem
61 315 rmoveto
172 385 rlineto
134 0 rlineto
172 -385 rlineto
-117 0 rlineto
-122 275 rlineto
-122 -275 rlineto
-117 0 rlineto
closepath
endchar } ND
/underscore { 
0 500 hsbw
0 -125 rmoveto
0 50 rlineto
500 0 rlineto
0 -50 rlineto
-500 0 rlineto
closepath
endchar } ND
/grave { 
0 259 hsbw
106 581 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
endchar } ND
/a { 
0 574 hsbw
81 143 hstem
174 202 vstem
376 200 rmoveto
0 53 rlineto
-16 -14 -24 -5 -44 -6 rrcurveto
-7 -1 -7 -1 -5 -2 rrcurveto
-15 0 rlineto
-54 -11 -30 -20 0 -46 rrcurveto
0 -50 33 -16 54 0 rrcurveto
75 0 40 57 0 62 rrcurveto
closepath
-186 158 rmoveto
-142 0 rlineto
7 124 120 49 116 0 rrcurveto
103 0 124 -32 0 -115 rrcurveto
0 -269 rlineto
0 -52 6 -43 12 -20 rrcurveto
-144 0 rlineto
-6 18 -4 17 0 15 rrcurveto
-43 -44 -63 -20 -68 0 rrcurveto
-104 0 -72 53 0 102 rrcurveto
0 118 110 32 112 13 rrcurveto
86 10 36 9 0 43 rrcurveto
0 56 -34 14 -58 0 rrcurveto
-56 0 -33 -24 -5 -54 rrcurveto
closepath
endchar } ND
/b { 
0 611 hsbw
93 331 hstem
191 242 vstem
54 0 rmoveto
0 714 rlineto
142 0 rlineto
0 -260 rlineto
2 0 rlineto
33 50 63 27 67 0 rrcurveto
124 0 90 -129 0 -143 rrcurveto
0 -145 -88 -128 -126 0 rrcurveto
-80 0 -61 25 -29 55 rrcurveto
-2 0 rlineto
0 -66 rlineto
-135 0 rlineto
closepath
379 258 rmoveto
0 94 -43 72 -78 0 rrcurveto
-79 0 -42 -72 0 -94 rrcurveto
0 -94 42 -71 79 0 rrcurveto
78 0 43 71 0 94 rrcurveto
closepath
endchar } ND
/c { 
0 574 hsbw
198 137 hstem
180 231 vstem
545 335 rmoveto
-139 0 rlineto
-10 59 -35 30 -60 0 rrcurveto
-87 0 -34 -90 0 -78 rrcurveto
0 -76 32 -87 86 0 rrcurveto
64 0 39 39 10 66 rrcurveto
137 0 rlineto
-18 -136 -94 -76 -137 0 rrcurveto
-147 0 -114 116 0 149 rrcurveto
0 153 106 127 158 0 rrcurveto
130 0 105 -71 8 -125 rrcurveto
closepath
endchar } ND
/d { 
0 611 hsbw
93 331 hstem
174 247 vstem
423 0 rmoveto
0 66 rlineto
-2 0 rlineto
-32 -55 -56 -25 -69 0 rrcurveto
-149 0 -83 137 0 140 rrcurveto
0 134 87 134 142 0 rrcurveto
62 0 60 -28 31 -49 rrcurveto
2 0 rlineto
0 260 rlineto
142 0 rlineto
0 -714 rlineto
-135 0 rlineto
closepath
-2 260 rmoveto
0 79 -36 85 -86 0 rrcurveto
-90 0 -35 -82 0 -83 rrcurveto
0 -79 38 -87 87 0 rrcurveto
86 0 36 84 0 83 rrcurveto
closepath
endchar } ND
/e { 
0 574 hsbw
160 64 hstem
314 110 hstem
171 231 vstem
544 224 rmoveto
-373 0 rlineto
4 -90 43 -41 80 0 rrcurveto
54 0 48 32 10 35 rrcurveto
125 0 rlineto
-39 -119 -83 -55 -120 0 rrcurveto
-154 0 -110 120 0 152 rrcurveto
0 146 114 127 150 0 rrcurveto
151 0 101 -135 0 -147 rrcurveto
-1 -5 rlineto
0 -20 rlineto
closepath
-373 90 rmoveto
231 0 rlineto
-14 74 -33 36 -66 0 rrcurveto
-70 0 -48 -55 0 -55 rrcurveto
closepath
endchar } ND
/f { 
0 333 hsbw
517 90 hstem
85 0 rmoveto
0 422 rlineto
-85 0 rlineto
0 95 rlineto
85 0 rlineto
0 40 rlineto
0 94 61 63 112 0 rrcurveto
75 0 rlineto
0 -110 rlineto
-17 2 -17 1 -18 0 rrcurveto
-39 0 -15 -17 0 -42 rrcurveto
0 -31 rlineto
98 0 rlineto
0 -95 rlineto
-98 0 rlineto
0 -422 rlineto
-142 0 rlineto
closepath
endchar } ND
/g { 
0 611 hsbw
-30 45 hstem
122 302 hstem
180 236 vstem
551 517 rmoveto
0 -484 rlineto
0 -135 -108 -94 -160 0 rrcurveto
-104 0 -120 55 -4 111 rrcurveto
141 0 rlineto
12 -50 39 -21 55 0 rrcurveto
76 0 38 50 0 78 rrcurveto
0 68 rlineto
-2 0 rlineto
-30 -52 -61 -28 -64 0 rrcurveto
-144 0 -77 122 0 138 rrcurveto
0 130 84 126 138 0 rrcurveto
70 0 50 -25 34 -58 rrcurveto
2 0 rlineto
0 69 rlineto
135 0 rlineto
closepath
-256 -395 rmoveto
77 0 44 64 0 79 rrcurveto
0 80 -35 79 -86 0 rrcurveto
-76 0 -39 -63 0 -84 rrcurveto
0 -76 35 -79 80 0 rrcurveto
closepath
endchar } ND
/h { 
0 593 hsbw
196 201 vstem
54 0 rmoveto
0 714 rlineto
142 0 rlineto
0 -269 rlineto
3 0 rlineto
34 56 61 30 56 0 rrcurveto
126 0 63 -88 0 -125 rrcurveto
0 -318 rlineto
-142 0 rlineto
0 292 rlineto
0 86 -26 41 -65 0 rrcurveto
-78 0 -32 -43 0 -105 rrcurveto
0 -271 rlineto
-142 0 rlineto
closepath
endchar } ND
/i { 
0 258 hsbw
517 80 hstem
58 0 rmoveto
0 517 rlineto
142 0 rlineto
0 -517 rlineto
-142 0 rlineto
closepath
142 714 rmoveto
0 -117 rlineto
-142 0 rlineto
0 117 rlineto
142 0 rlineto
closepath
endchar } ND
/j { 
0 278 hsbw
517 80 hstem
-19 -178 rmoveto
0 117 rlineto
8 -2 11 -2 12 0 rrcurveto
34 0 21 10 0 36 rrcurveto
0 536 rlineto
142 0 rlineto
0 -543 rlineto
0 -110 -44 -45 -138 0 rrcurveto
-20 0 rlineto
-6 1 -10 1 -10 1 rrcurveto
closepath
228 892 rmoveto
0 -117 rlineto
-142 0 rlineto
0 117 rlineto
142 0 rlineto
closepath
endchar } ND
/k { 
0 574 hsbw
209 179 vstem
67 0 rmoveto
0 714 rlineto
142 0 rlineto
0 -383 rlineto
179 186 rlineto
168 0 rlineto
-195 -190 rlineto
217 -327 rlineto
-172 0 rlineto
-142 231 rlineto
-55 -53 rlineto
0 -178 rlineto
-142 0 rlineto
closepath
endchar } ND
/l { 
0 258 hsbw
58 0 rmoveto
0 714 rlineto
142 0 rlineto
0 -714 rlineto
-142 0 rlineto
closepath
endchar } ND
/m { 
0 906 hsbw
192 2 vstem
200 182 vstem
524 182 vstem
58 0 rmoveto
0 517 rlineto
134 0 rlineto
0 -70 rlineto
2 0 rlineto
37 53 55 31 66 0 rrcurveto
69 0 55 -28 27 -58 rrcurveto
33 49 55 37 68 0 rrcurveto
117 0 72 -57 0 -128 rrcurveto
0 -346 rlineto
-142 0 rlineto
0 293 rlineto
0 67 -17 59 -70 0 rrcurveto
-74 0 -21 -65 0 -66 rrcurveto
0 -288 rlineto
-142 0 rlineto
0 290 rlineto
0 72 -12 57 -74 0 rrcurveto
-52 0 -44 -54 0 -65 rrcurveto
0 -300 rlineto
-142 0 rlineto
closepath
endchar } ND
/n { 
0 593 hsbw
189 3 vstem
196 201 vstem
54 0 rmoveto
0 517 rlineto
135 0 rlineto
0 -72 rlineto
3 0 rlineto
34 56 61 30 63 0 rrcurveto
126 0 63 -88 0 -125 rrcurveto
0 -318 rlineto
-142 0 rlineto
0 292 rlineto
0 86 -26 41 -65 0 rrcurveto
-78 0 -32 -43 0 -105 rrcurveto
0 -271 rlineto
-142 0 rlineto
closepath
endchar } ND
/o { 
0 611 hsbw
93 331 hstem
180 251 vstem
38 258 rmoveto
0 165 105 108 162 0 rrcurveto
162 0 106 -108 0 -165 rrcurveto
0 -164 -106 -108 -162 0 rrcurveto
-162 0 -105 108 0 164 rrcurveto
closepath
142 0 rmoveto
0 -83 37 -82 88 0 rrcurveto
88 0 38 83 0 82 rrcurveto
0 82 -38 84 -88 0 rrcurveto
-88 0 -37 -83 0 -83 rrcurveto
closepath
endchar } ND
/p { 
0 611 hsbw
93 331 hstem
191 247 vstem
54 -182 rmoveto
0 699 rlineto
135 0 rlineto
0 -66 rlineto
2 0 rlineto
33 53 55 27 66 0 rrcurveto
150 0 85 -137 0 -141 rrcurveto
0 -138 -86 -129 -141 0 rrcurveto
-63 0 -59 27 -33 51 rrcurveto
-2 0 rlineto
0 -246 rlineto
-142 0 rlineto
closepath
261 275 rmoveto
85 0 38 81 0 83 rrcurveto
0 79 -37 88 -87 0 rrcurveto
-87 0 -36 -86 0 -81 rrcurveto
0 -82 38 -82 86 0 rrcurveto
closepath
endchar } ND
/q { 
0 611 hsbw
93 331 hstem
174 247 vstem
558 517 rmoveto
0 -699 rlineto
-142 0 rlineto
0 247 rlineto
-2 0 rlineto
-31 -54 -66 -25 -66 0 rrcurveto
-130 0 -89 122 0 147 rrcurveto
0 137 86 139 146 0 rrcurveto
68 0 58 -26 31 -54 rrcurveto
2 0 rlineto
0 66 rlineto
135 0 rlineto
closepath
-384 -259 rmoveto
0 -82 35 -83 88 0 rrcurveto
85 0 39 68 0 95 rrcurveto
0 78 -36 90 -85 0 rrcurveto
-86 0 -40 -83 0 -83 rrcurveto
closepath
endchar } ND
/r { 
0 389 hsbw
189 2 vstem
54 0 rmoveto
0 517 rlineto
135 0 rlineto
0 -96 rlineto
2 0 rlineto
25 64 70 46 70 0 rrcurveto
10 0 11 -2 10 -3 rrcurveto
0 -132 rlineto
-12 2 -22 3 -17 0 rrcurveto
-98 0 -42 -66 0 -100 rrcurveto
0 -233 rlineto
-142 0 rlineto
closepath
endchar } ND
/s { 
0 537 hsbw
81 355 hstem
183 176 vstem
29 168 rmoveto
135 0 rlineto
1 -59 46 -28 62 0 rrcurveto
49 0 44 20 0 46 rrcurveto
0 40 -41 15 -120 25 rrcurveto
-82 17 -82 34 0 87 rrcurveto
0 118 114 48 111 0 rrcurveto
112 0 106 -50 10 -115 rrcurveto
-135 0 rlineto
-4 52 -36 18 -57 0 rrcurveto
-44 0 -35 -11 0 -37 rrcurveto
0 -39 41 -11 81 -18 rrcurveto
126 -28 77 -25 0 -101 rrcurveto
0 -127 -119 -53 -117 0 rrcurveto
-121 0 -118 56 -4 126 rrcurveto
closepath
endchar } ND
/t { 
0 352 hsbw
110 312 hstem
234 672 rmoveto
0 -155 rlineto
104 0 rlineto
0 -95 rlineto
-104 0 rlineto
0 -256 rlineto
0 -48 12 -12 48 0 rrcurveto
16 0 14 1 14 3 rrcurveto
0 -111 rlineto
-20 -3 -28 -2 -35 0 rrcurveto
-87 0 -76 31 0 92 rrcurveto
0 305 rlineto
-86 0 rlineto
0 95 rlineto
86 0 rlineto
0 155 rlineto
142 0 rlineto
closepath
endchar } ND
/u { 
0 593 hsbw
196 201 vstem
401 3 vstem
539 517 rmoveto
0 -517 rlineto
-135 0 rlineto
0 72 rlineto
-3 0 rlineto
-35 -56 -61 -30 -62 0 rrcurveto
-130 0 -59 89 0 124 rrcurveto
0 318 rlineto
142 0 rlineto
0 -292 rlineto
0 -87 27 -40 64 0 rrcurveto
77 0 33 42 0 106 rrcurveto
0 271 rlineto
142 0 rlineto
closepath
endchar } ND
/v { 
0 520 hsbw
263 2 vstem
515 517 rmoveto
-175 -517 rlineto
-158 0 rlineto
-177 517 rlineto
149 0 rlineto
109 -353 rlineto
2 0 rlineto
109 353 rlineto
141 0 rlineto
closepath
endchar } ND
/w { 
0 814 hsbw
251 2 vstem
405 2 vstem
565 2 vstem
808 517 rmoveto
-163 -517 rlineto
-146 0 rlineto
-92 347 rlineto
-2 0 rlineto
-88 -347 rlineto
-147 0 rlineto
-164 517 rlineto
150 0 rlineto
95 -351 rlineto
2 0 rlineto
86 351 rlineto
138 0 rlineto
88 -350 rlineto
2 0 rlineto
95 350 rlineto
146 0 rlineto
closepath
endchar } ND
/x { 
0 537 hsbw
178 181 vstem
0 0 rmoveto
186 272 rlineto
-170 245 rlineto
162 0 rlineto
91 -135 rlineto
90 135 rlineto
157 0 rlineto
-170 -242 rlineto
191 -275 rlineto
-162 0 rlineto
-108 163 rlineto
-108 -163 rlineto
-159 0 rlineto
closepath
endchar } ND
/y { 
0 519 hsbw
263 2 vstem
525 517 rmoveto
-216 -582 rlineto
-31 -84 -50 -33 -97 0 rrcurveto
-26 0 -29 2 -32 3 rrcurveto
0 115 rlineto
18 0 rlineto
24 -2 23 -1 18 0 rrcurveto
34 0 21 32 0 33 rrcurveto
0 11 -2 11 -4 10 rrcurveto
-182 485 rlineto
152 0 rlineto
117 -354 rlineto
2 0 rlineto
113 354 rlineto
147 0 rlineto
closepath
endchar } ND
/z { 
0 519 hsbw
107 303 hstem
22 0 rmoveto
0 107 rlineto
269 303 rlineto
-249 0 rlineto
0 107 rlineto
436 0 rlineto
0 -107 rlineto
-269 -303 rlineto
288 0 rlineto
0 -107 rlineto
-475 0 rlineto
closepath
endchar } ND
/braceleft { 
0 333 hsbw
-70 20 hstem
599 20 hstem
333 731 rmoveto
0 -112 rlineto
-54 0 rlineto
-43 0 -11 -32 0 -41 rrcurveto
0 -152 rlineto
0 -67 -45 -46 -54 -5 rrcurveto
0 -3 rlineto
47 -5 52 -43 0 -62 rrcurveto
0 -160 rlineto
0 -42 11 -31 43 0 rrcurveto
54 0 rlineto
0 -112 rlineto
-127 0 rlineto
-60 0 -63 65 0 70 rrcurveto
0 195 rlineto
0 49 -50 34 -42 0 rrcurveto
0 87 rlineto
43 0 49 37 0 53 rrcurveto
0 188 rlineto
0 71 64 64 59 0 rrcurveto
127 0 rlineto
closepath
endchar } ND
/bar { 
0 223 hsbw
58 -214 rmoveto
0 1000 rlineto
107 0 rlineto
0 -1000 rlineto
-107 0 rlineto
closepath
endchar } ND
/braceright { 
0 333 hsbw
-70 20 hstem
599 20 hstem
0 -182 rmoveto
0 112 rlineto
54 0 rlineto
42 0 12 31 0 42 rrcurveto
0 160 rlineto
0 61 47 45 52 4 rrcurveto
0 3 rlineto
-54 4 -45 47 0 67 rrcurveto
0 152 rlineto
0 41 -12 32 -42 0 rrcurveto
-54 0 rlineto
0 112 rlineto
127 0 rlineto
59 0 64 -64 0 -71 rrcurveto
0 -188 rlineto
0 -54 49 -36 43 0 rrcurveto
0 -87 rlineto
-42 0 -50 -33 0 -50 rrcurveto
0 -195 rlineto
0 -72 -63 -63 -60 0 rrcurveto
-127 0 rlineto
closepath
endchar } ND
/asciitilde { 
0 600 hsbw
493 344 rmoveto
40 -93 rlineto
-40 -56 -31 -30 -54 0 rrcurveto
-34 0 -30 11 -38 22 rrcurveto
-38 22 -36 13 -41 0 rrcurveto
-40 0 -24 -30 -20 -42 rrcurveto
-40 93 rlineto
26 50 37 36 62 0 rrcurveto
30 0 35 -12 46 -20 rrcurveto
7 -4 7 -3 7 -2 rrcurveto
38 -18 23 -9 20 0 rrcurveto
36 0 21 25 31 47 rrcurveto
closepath
endchar } ND
/quotesinglbase { 
0 278 hsbw
-101 101 hstem
68 0 rmoveto
0 154 rlineto
142 0 rlineto
0 -153 rlineto
0 -90 -60 -67 -82 -11 rrcurveto
0 66 rlineto
48 16 18 33 0 52 rrcurveto
-66 0 rlineto
closepath
endchar } ND
/florin { 
0 556 hsbw
-63 400 hstem
432 192 hstem
-5 -167 rmoveto
19 107 rlineto
13 -2 13 -1 14 0 rrcurveto
48 0 16 22 8 49 rrcurveto
63 329 rlineto
-92 0 rlineto
18 95 rlineto
91 0 rlineto
24 127 rlineto
24 126 52 46 130 0 rrcurveto
26 0 25 -3 24 -4 rrcurveto
-18 -107 rlineto
-12 4 -13 3 -15 0 rrcurveto
-50 0 -18 -17 -8 -45 rrcurveto
-25 -130 rlineto
98 0 rlineto
-16 -95 rlineto
-100 0 rlineto
-56 -310 rlineto
-24 -125 -58 -72 -127 0 rrcurveto
-22 0 -24 1 -28 2 rrcurveto
closepath
endchar } ND
/quotedblbase { 
0 463 hsbw
-101 101 hstem
196 71 vstem
267 0 rmoveto
0 154 rlineto
142 0 rlineto
0 -153 rlineto
0 -90 -60 -67 -82 -11 rrcurveto
0 66 rlineto
48 16 18 33 0 52 rrcurveto
-66 0 rlineto
closepath
-213 0 rmoveto
0 154 rlineto
142 0 rlineto
0 -153 rlineto
0 -90 -60 -67 -82 -11 rrcurveto
0 66 rlineto
48 16 18 33 0 52 rrcurveto
-66 0 rlineto
closepath
endchar } ND
/ellipsis { 
0 1000 hsbw
245 176 vstem
578 176 vstem
88 0 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
-157 0 rlineto
closepath
333 0 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
-157 0 rlineto
closepath
333 0 rmoveto
0 154 rlineto
157 0 rlineto
0 -154 rlineto
-157 0 rlineto
closepath
endchar } ND
/dagger { 
0 556 hsbw
210 -166 rmoveto
0 566 rlineto
-186 0 rlineto
0 117 rlineto
186 0 rlineto
0 197 rlineto
135 0 rlineto
0 -197 rlineto
187 0 rlineto
0 -117 rlineto
-187 0 rlineto
0 -566 rlineto
-135 0 rlineto
closepath
endchar } ND
/daggerdbl { 
0 556 hsbw
144 256 hstem
210 -166 rmoveto
0 193 rlineto
-186 0 rlineto
0 117 rlineto
186 0 rlineto
0 256 rlineto
-186 0 rlineto
0 117 rlineto
186 0 rlineto
0 197 rlineto
135 0 rlineto
0 -197 rlineto
187 0 rlineto
0 -117 rlineto
-187 0 rlineto
0 -256 rlineto
187 0 rlineto
0 -117 rlineto
-187 0 rlineto
0 -193 rlineto
-135 0 rlineto
closepath
endchar } ND
/circumflex { 
0 259 hsbw
62 126 vstem
-46 581 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
endchar } ND
/perthousand { 
0 1147 hsbw
51 214 hstem
435 214 hstem
159 111 vstem
530 111 732 53 876 111 vstem3
530 172 rmoveto
0 -59 -2 -62 58 0 rrcurveto
37 0 18 31 0 34 rrcurveto
0 84 rlineto
0 30 -18 35 -35 0 rrcurveto
-52 0 -6 -49 0 -44 rrcurveto
closepath
256 -17 rmoveto
0 101 48 74 99 0 rrcurveto
102 0 43 -68 0 -106 rrcurveto
0 -103 -48 -67 -98 0 rrcurveto
-92 0 -54 67 0 102 rrcurveto
closepath
90 17 rmoveto
0 -59 -2 -62 58 0 rrcurveto
37 0 18 31 0 34 rrcurveto
0 84 rlineto
0 30 -18 35 -35 0 rrcurveto
-52 0 -6 -49 0 -44 rrcurveto
closepath
-807 367 rmoveto
0 101 48 74 99 0 rrcurveto
102 0 43 -68 0 -106 rrcurveto
0 -103 -48 -67 -98 0 rrcurveto
-92 0 -54 67 0 102 rrcurveto
closepath
90 17 rmoveto
0 -59 -2 -62 58 0 rrcurveto
37 0 18 31 0 34 rrcurveto
0 84 rlineto
0 30 -18 35 -35 0 rrcurveto
-52 0 -6 -49 0 -44 rrcurveto
closepath
-9 -578 rmoveto
414 744 rlineto
85 0 rlineto
-412 -744 rlineto
-87 0 rlineto
closepath
290 177 rmoveto
0 101 48 74 99 0 rrcurveto
102 0 43 -68 0 -106 rrcurveto
0 -103 -48 -67 -98 0 rrcurveto
-92 0 -54 67 0 102 rrcurveto
closepath
endchar } ND
/Scaron { 
0 649 hsbw
105 504 hstem
731 47 hstem
176 297 vstem
500 920 rmoveto
-110 -142 rlineto
-130 0 rlineto
-111 142 rlineto
112 0 rlineto
64 -81 rlineto
65 81 rlineto
110 0 rlineto
closepath
-476 -683 rmoveto
152 0 rlineto
0 -90 66 -42 92 0 rrcurveto
59 0 80 19 0 70 rrcurveto
0 78 -81 8 -85 20 rrcurveto
-21 4 -14 4 -11 4 rrcurveto
-115 29 -98 54 0 120 rrcurveto
0 137 136 79 131 0 rrcurveto
142 0 144 -76 0 -154 rrcurveto
-152 0 rlineto
-4 80 -53 28 -84 0 rrcurveto
-59 0 -49 -27 0 -54 rrcurveto
0 -95 91 16 106 -26 rrcurveto
5 0 rlineto
3 -1 5 -1 3 -2 rrcurveto
6 0 rlineto
110 -28 96 -59 0 -120 rrcurveto
0 -136 -109 -93 -188 0 rrcurveto
-156 0 -148 90 0 164 rrcurveto
closepath
endchar } ND
/guilsinglleft { 
0 241 hsbw
200 143 hstem
199 80 rmoveto
-157 125 rlineto
0 135 rlineto
157 123 rlineto
0 -120 rlineto
-90 -71 rlineto
90 -72 rlineto
0 -120 rlineto
closepath
endchar } ND
/OE { 
0 1093 hsbw
132 175 hstem
429 153 hstem
195 343 vstem
542 2 vstem
544 0 rmoveto
0 54 rlineto
-2 0 rlineto
-42 -46 -66 -25 -64 0 rrcurveto
-202 0 -130 177 0 193 rrcurveto
0 193 133 185 202 0 rrcurveto
68 0 65 -22 36 -46 rrcurveto
2 0 rlineto
0 51 rlineto
500 0 rlineto
0 -132 rlineto
-354 0 rlineto
0 -153 rlineto
324 0 rlineto
0 -122 rlineto
-324 0 rlineto
0 -175 rlineto
361 0 rlineto
0 -132 rlineto
-507 0 rlineto
closepath
-6 278 rmoveto
0 170 rlineto
0 89 -65 62 -89 0 rrcurveto
-131 0 -58 -125 0 -119 rrcurveto
0 -118 62 -122 124 0 rrcurveto
94 0 63 65 0 98 rrcurveto
closepath
endchar } ND
/quoteleft { 
0 278 hsbw
547 101 hstem
210 547 rmoveto
0 -154 rlineto
-142 0 rlineto
0 153 rlineto
0 90 59 67 83 11 rrcurveto
0 -66 rlineto
-48 -16 -18 -33 0 -52 rrcurveto
66 0 rlineto
closepath
endchar } ND
/quoteright { 
0 278 hsbw
459 101 hstem
68 560 rmoveto
0 154 rlineto
142 0 rlineto
0 -153 rlineto
0 -91 -60 -67 -82 -10 rrcurveto
0 66 rlineto
48 16 18 33 0 52 rrcurveto
-66 0 rlineto
closepath
endchar } ND
/quotedblleft { 
0 463 hsbw
547 101 hstem
196 71 vstem
409 547 rmoveto
0 -154 rlineto
-142 0 rlineto
0 153 rlineto
0 90 59 67 83 11 rrcurveto
0 -66 rlineto
-48 -16 -18 -33 0 -52 rrcurveto
66 0 rlineto
closepath
-213 0 rmoveto
0 -154 rlineto
-142 0 rlineto
0 153 rlineto
0 90 59 67 83 11 rrcurveto
0 -66 rlineto
-48 -16 -18 -33 0 -52 rrcurveto
66 0 rlineto
closepath
endchar } ND
/quotedblright { 
0 463 hsbw
459 101 hstem
196 71 vstem
267 560 rmoveto
0 154 rlineto
142 0 rlineto
0 -153 rlineto
0 -91 -60 -67 -82 -10 rrcurveto
0 66 rlineto
48 16 18 33 0 52 rrcurveto
-66 0 rlineto
closepath
-213 0 rmoveto
0 154 rlineto
142 0 rlineto
0 -153 rlineto
0 -91 -60 -67 -82 -10 rrcurveto
0 66 rlineto
48 16 18 33 0 52 rrcurveto
-66 0 rlineto
closepath
endchar } ND
/bullet { 
0 501 hsbw
72 357 rmoveto
0 94 81 84 97 0 rrcurveto
94 0 85 -83 0 -95 rrcurveto
0 -95 -83 -84 -96 0 rrcurveto
-95 0 -83 82 0 97 rrcurveto
closepath
endchar } ND
/endash { 
0 500 hsbw
0 212 rmoveto
0 122 rlineto
500 0 rlineto
0 -122 rlineto
-500 0 rlineto
closepath
endchar } ND
/emdash { 
0 1000 hsbw
130 212 rmoveto
0 122 rlineto
740 0 rlineto
0 -122 rlineto
-740 0 rlineto
closepath
endchar } ND
/tilde { 
0 259 hsbw
244 714 rmoveto
69 0 rlineto
-1 -4 rlineto
-22 -69 -20 -41 -69 0 rrcurveto
-20 0 -26 5 -33 11 rrcurveto
-3 1 rlineto
-1 0 rlineto
-31 11 -23 6 -15 0 rrcurveto
-26 0 -10 -14 -6 -28 rrcurveto
-61 0 rlineto
15 67 38 55 73 0 rrcurveto
13 0 19 -5 25 -9 rrcurveto
7 -3 rlineto
28 -12 22 -6 16 0 rrcurveto
28 0 14 11 0 24 rrcurveto
closepath
endchar } ND
/trademark { 
0 1000 hsbw
398 53 vstem
547 285 vstem
928 714 rmoveto
0 -412 rlineto
-96 0 rlineto
0 292 rlineto
-2 0 rlineto
-106 -292 rlineto
-69 0 rlineto
-106 292 rlineto
-2 0 rlineto
0 -292 rlineto
-96 0 rlineto
0 412 rlineto
136 0 rlineto
103 -271 rlineto
103 271 rlineto
135 0 rlineto
closepath
-530 0 rmoveto
0 -80 rlineto
-117 0 rlineto
0 -332 rlineto
-102 0 rlineto
0 332 rlineto
-117 0 rlineto
0 80 rlineto
336 0 rlineto
closepath
endchar } ND
/scaron { 
0 537 hsbw
81 355 hstem
531 50 hstem
205 129 vstem
444 723 rmoveto
-110 -142 rlineto
-130 0 rlineto
-111 142 rlineto
112 0 rlineto
64 -81 rlineto
65 81 rlineto
110 0 rlineto
closepath
-415 -555 rmoveto
135 0 rlineto
1 -59 46 -28 62 0 rrcurveto
49 0 44 20 0 46 rrcurveto
0 40 -41 15 -120 25 rrcurveto
-82 17 -82 34 0 87 rrcurveto
0 118 114 48 111 0 rrcurveto
112 0 106 -50 10 -115 rrcurveto
-135 0 rlineto
-4 52 -36 18 -57 0 rrcurveto
-44 0 -35 -11 0 -37 rrcurveto
0 -39 41 -11 81 -18 rrcurveto
126 -28 77 -25 0 -101 rrcurveto
0 -127 -119 -53 -117 0 rrcurveto
-121 0 -118 56 -4 126 rrcurveto
closepath
endchar } ND
/guilsinglright { 
0 241 hsbw
200 143 hstem
42 463 rmoveto
157 -123 rlineto
0 -135 rlineto
-157 -125 rlineto
0 120 rlineto
90 72 rlineto
-90 71 rlineto
0 120 rlineto
closepath
endchar } ND
/oe { 
0 926 hsbw
164 60 hstem
314 110 hstem
180 224 vstem
528 227 vstem
528 314 rmoveto
227 0 rlineto
-3 68 -39 42 -73 0 rrcurveto
-68 0 -42 -46 -2 -64 rrcurveto
closepath
-238 -221 rmoveto
84 0 30 88 0 79 rrcurveto
0 76 -27 88 -84 0 rrcurveto
-85 0 -28 -90 0 -78 rrcurveto
0 -76 30 -87 80 0 rrcurveto
closepath
607 131 rmoveto
-369 0 rlineto
0 -78 43 -53 80 0 rrcurveto
50 0 34 23 20 48 rrcurveto
138 0 rlineto
-34 -111 -91 -67 -117 0 rrcurveto
-79 0 -71 28 -37 60 rrcurveto
-34 -59 -73 -29 -76 0 rrcurveto
-151 0 -92 130 0 145 rrcurveto
0 145 107 125 147 0 rrcurveto
73 0 70 -26 35 -56 rrcurveto
34 50 73 32 66 0 rrcurveto
166 0 88 -150 0 -157 rrcurveto
closepath
endchar } ND
/Ydieresis { 
0 667 hsbw
714 80 hstem
298 71 vstem
369 794 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
91 -794 rmoveto
0 278 rlineto
-263 436 rlineto
177 0 rlineto
167 -282 rlineto
166 282 rlineto
175 0 rlineto
-265 -440 rlineto
0 -274 rlineto
-157 0 rlineto
closepath
endchar } ND
/exclamdown { 
0 278 hsbw
317 60 hstem
218 531 rmoveto
0 -154 rlineto
-157 0 rlineto
0 154 rlineto
157 0 rlineto
closepath
-153 -714 rmoveto
0 192 rlineto
39 308 rlineto
71 0 rlineto
38 -308 rlineto
0 -192 rlineto
-148 0 rlineto
closepath
endchar } ND
/cent { 
0 556 hsbw
203 131 hstem
163 97 vstem
319 85 vstem
260 93 rmoveto
0 330 rlineto
-67 -18 -30 -65 0 -76 rrcurveto
0 -82 27 -68 70 -21 rrcurveto
closepath
0 -208 rmoveto
0 101 rlineto
-141 7 -98 119 0 138 rrcurveto
0 156 88 113 151 12 rrcurveto
0 85 rlineto
59 0 rlineto
0 -85 rlineto
110 0 111 -83 3 -114 rrcurveto
-139 0 rlineto
-4 48 -36 38 -45 4 rrcurveto
0 -331 rlineto
48 9 37 49 2 52 rrcurveto
139 0 rlineto
-11 -124 -91 -82 -124 -11 rrcurveto
0 -101 rlineto
-59 0 rlineto
closepath
endchar } ND
/sterling { 
0 556 hsbw
128 181 hstem
394 220 hstem
197 207 vstem
88 -17 rmoveto
-62 93 rlineto
65 41 48 58 0 67 rrcurveto
0 19 -7 22 -12 26 rrcurveto
-99 0 rlineto
0 85 rlineto
64 0 rlineto
-23 29 -22 54 0 55 rrcurveto
0 112 94 87 162 0 rrcurveto
152 0 91 -89 0 -161 rrcurveto
-135 0 rlineto
0 4 rlineto
0 69 -35 60 -74 0 rrcurveto
-59 0 -39 -36 0 -56 rrcurveto
0 -39 5 -25 36 -64 rrcurveto
133 0 rlineto
0 -85 rlineto
-105 0 rlineto
5 -18 3 -21 0 -23 rrcurveto
0 -58 -32 -41 -50 -41 rrcurveto
2 -2 rlineto
30 18 16 5 22 0 rrcurveto
15 0 23 -3 30 -6 rrcurveto
11 0 rlineto
34 -8 26 -6 18 0 rrcurveto
32 0 22 8 34 35 rrcurveto
60 -101 rlineto
-46 -44 -56 -20 -54 0 rrcurveto
-46 0 -30 6 -33 12 rrcurveto
-5 2 rlineto
-35 12 -22 6 -37 0 rrcurveto
-40 0 -39 -10 -36 -28 rrcurveto
closepath
endchar } ND
/currency { 
0 556 hsbw
209 282 hstem
153 248 vstem
20 149 rmoveto
57 58 rlineto
-31 36 -17 51 0 55 rrcurveto
0 58 16 54 32 32 rrcurveto
-57 58 rlineto
57 57 rlineto
57 -56 rlineto
33 28 57 18 56 0 rrcurveto
53 0 54 -19 30 -26 rrcurveto
58 57 rlineto
61 -61 rlineto
-56 -55 rlineto
28 -32 17 -55 0 -56 rrcurveto
0 -56 -17 -54 -29 -36 rrcurveto
55 -56 rlineto
-55 -55 rlineto
-57 58 rlineto
-32 -32 -55 -18 -56 0 rrcurveto
-59 0 -54 17 -34 32 rrcurveto
-56 -58 rlineto
-56 56 rlineto
closepath
260 342 rmoveto
-80 0 -47 -64 0 -78 rrcurveto
0 -78 47 -62 79 0 rrcurveto
77 0 45 64 0 78 rrcurveto
0 76 -45 64 -76 0 rrcurveto
closepath
endchar } ND
/yen { 
0 556 hsbw
233 75 hstem
154 261 vstem
359 0 rmoveto
-152 0 rlineto
0 138 rlineto
-157 0 rlineto
0 95 rlineto
157 0 rlineto
0 46 rlineto
-14 29 rlineto
-143 0 rlineto
0 95 rlineto
95 0 rlineto
-154 311 rlineto
163 0 rlineto
132 -311 rlineto
129 311 rlineto
162 0 rlineto
-155 -311 rlineto
95 0 rlineto
0 -95 rlineto
-143 0 rlineto
-15 -29 rlineto
0 -46 rlineto
158 0 rlineto
0 -95 rlineto
-158 0 rlineto
0 -138 rlineto
closepath
endchar } ND
/brokenbar { 
0 223 hsbw
161 250 hstem
58 -89 rmoveto
0 250 rlineto
107 0 rlineto
0 -250 rlineto
-107 0 rlineto
closepath
107 750 rmoveto
0 -250 rlineto
-107 0 rlineto
0 250 rlineto
107 0 rlineto
closepath
endchar } ND
/section { 
0 556 hsbw
169 369 hstem
209 161 vstem
377 287 rmoveto
-165 93 rlineto
-2 0 -1 1 -2 2 rrcurveto
-9 4 -7 3 -6 0 rrcurveto
-28 0 -23 -37 0 -30 rrcurveto
0 -21 10 -16 22 -12 rrcurveto
180 -98 rlineto
10 -5 10 -2 10 0 rrcurveto
25 0 21 31 0 24 rrcurveto
0 28 -19 20 -26 15 rrcurveto
closepath
117 251 rmoveto
-124 0 rlineto
0 50 -33 36 -52 0 rrcurveto
-39 0 -37 -27 0 -37 rrcurveto
0 -53 108 -52 62 -21 rrcurveto
88 -39 79 -53 0 -92 rrcurveto
0 -66 -42 -58 -62 -22 rrcurveto
36 -28 19 -40 0 -50 rrcurveto
0 -107 -112 -61 -101 0 rrcurveto
-135 0 -90 64 0 130 rrcurveto
124 0 rlineto
3 -58 33 -29 62 0 rrcurveto
42 0 32 24 0 42 rrcurveto
0 46 -44 27 -97 44 rrcurveto
-67 18 -137 75 0 110 rrcurveto
0 70 39 52 68 24 rrcurveto
-30 32 -13 29 0 40 rrcurveto
0 107 109 66 100 0 rrcurveto
122 0 89 -73 0 -120 rrcurveto
closepath
endchar } ND
/dieresis { 
0 259 hsbw
94 71 vstem
165 597 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
endchar } ND
/copyright { 
0 800 hsbw
57 80 295 124 583 68 hstem3
118 81 295 210 601 81 vstem3
16 357 rmoveto
0 210 181 164 203 0 rrcurveto
200 0 184 -164 0 -210 rrcurveto
0 -213 -182 -161 -202 0 rrcurveto
-205 0 -179 160 0 214 rrcurveto
closepath
102 0 rmoveto
0 -160 126 -134 156 0 rrcurveto
153 0 129 136 0 158 rrcurveto
0 158 -127 136 -155 0 rrcurveto
-157 0 -125 -134 0 -160 rrcurveto
closepath
397 -62 rmoveto
81 0 rlineto
-16 -98 -74 -60 -92 0 rrcurveto
-123 0 -92 102 0 120 rrcurveto
0 132 82 92 131 0 rrcurveto
94 0 73 -57 14 -98 rrcurveto
-78 0 rlineto
-10 46 -41 29 -52 0 rrcurveto
-74 0 -43 -57 0 -85 rrcurveto
0 -80 48 -64 71 0 rrcurveto
56 0 39 30 6 48 rrcurveto
closepath
endchar } ND
/ordfeminine { 
0 344 hsbw
440 77 hstem
112 118 vstem
230 515 rmoveto
0 20 rlineto
-13 -11 -38 -7 -16 0 rrcurveto
-32 -7 -19 -11 0 -22 rrcurveto
0 -27 22 -10 23 0 rrcurveto
45 0 28 32 0 43 rrcurveto
closepath
-108 90 rmoveto
-102 0 rlineto
9 82 63 27 84 0 rrcurveto
67 0 83 -13 0 -76 rrcurveto
0 -166 rlineto
0 -36 4 -25 8 -14 rrcurveto
-101 0 rlineto
-4 10 -2 10 -1 11 rrcurveto
-27 -28 -41 -12 -40 0 rrcurveto
-67 0 -45 33 0 67 rrcurveto
0 62 54 28 60 7 rrcurveto
7 0 9 1 10 2 rrcurveto
58 5 23 6 0 24 rrcurveto
0 30 -22 9 -38 0 rrcurveto
-26 0 -21 -15 -2 -29 rrcurveto
closepath
endchar } ND
/guillemotleft { 
0 444 hsbw
200 143 hstem
109 136 vstem
402 80 rmoveto
-157 125 rlineto
0 135 rlineto
157 123 rlineto
0 -120 rlineto
-90 -71 rlineto
90 -72 rlineto
0 -120 rlineto
closepath
-203 0 rmoveto
-157 125 rlineto
0 135 rlineto
157 123 rlineto
0 -120 rlineto
-90 -71 rlineto
90 -72 rlineto
0 -120 rlineto
closepath
endchar } ND
/logicalnot { 
0 600 hsbw
552 406 rmoveto
0 -307 rlineto
-107 0 rlineto
0 200 rlineto
-397 0 rlineto
0 107 rlineto
504 0 rlineto
closepath
endchar } ND
/minus { 
0 600 hsbw
48 199 rmoveto
0 107 rlineto
505 0 rlineto
0 -107 rlineto
-505 0 rlineto
closepath
endchar } ND
/registered { 
0 800 hsbw
63 86 hstem
393 110 hstem
568 83 hstem
118 133 vstem
334 156 vstem
573 109 vstem
16 358 rmoveto
0 210 181 163 203 0 rrcurveto
200 0 184 -163 0 -210 rrcurveto
0 -213 -182 -162 -202 0 rrcurveto
-205 0 -179 162 0 213 rrcurveto
closepath
102 0 rmoveto
0 -160 126 -135 156 0 rrcurveto
153 0 129 137 0 158 rrcurveto
0 155 -127 138 -155 0 rrcurveto
-157 0 -125 -134 0 -159 rrcurveto
closepath
133 -209 rmoveto
0 419 rlineto
158 0 rlineto
109 0 55 -36 0 -88 rrcurveto
0 -72 -42 -35 -68 -5 rrcurveto
112 -183 rlineto
-87 0 rlineto
-102 179 rlineto
-52 0 rlineto
0 -179 rlineto
-83 0 rlineto
closepath
83 354 rmoveto
0 -110 rlineto
74 0 rlineto
54 0 28 11 0 47 rrcurveto
0 40 -37 12 -49 0 rrcurveto
-70 0 rlineto
closepath
endchar } ND
/macron { 
0 259 hsbw
-57 616 rmoveto
0 75 rlineto
373 0 rlineto
0 -75 rlineto
-373 0 rlineto
closepath
endchar } ND
/degree { 
0 400 hsbw
478 176 hstem
117 166 vstem
52 566 rmoveto
0 82 65 66 83 0 rrcurveto
82 0 66 -66 0 -82 rrcurveto
0 -83 -66 -65 -82 0 rrcurveto
-83 0 -65 65 0 83 rrcurveto
closepath
65 0 rmoveto
0 -48 39 -40 44 0 rrcurveto
44 0 39 40 0 48 rrcurveto
0 48 -39 40 -44 0 rrcurveto
-44 0 -39 -40 0 -48 rrcurveto
closepath
endchar } ND
/plusminus { 
0 600 hsbw
107 30 hstem
354 506 rmoveto
0 -131 rlineto
199 0 rlineto
0 -107 rlineto
-199 0 rlineto
0 -131 rlineto
-107 0 rlineto
0 131 rlineto
-199 0 rlineto
0 107 rlineto
199 0 rlineto
0 131 rlineto
107 0 rlineto
closepath
-306 -506 rmoveto
0 107 rlineto
505 0 rlineto
0 -107 rlineto
-505 0 rlineto
closepath
endchar } ND
/twosuperior { 
0 392 hsbw
353 281 hstem
130 133 vstem
130 540 rmoveto
-102 0 rlineto
0 108 62 66 119 0 rrcurveto
88 0 68 -50 0 -85 rrcurveto
0 -58 -28 -37 -53 -33 rrcurveto
-4 -2 -5 -3 -5 -4 rrcurveto
-5 -4 -6 -4 -8 -3 rrcurveto
-48 -28 -29 -20 -22 -30 rrcurveto
209 0 rlineto
0 -80 rlineto
-334 0 rlineto
0 81 39 53 68 40 rrcurveto
11 5 11 8 11 9 rrcurveto
3 2 5 3 10 6 rrcurveto
49 30 29 22 0 40 rrcurveto
0 36 -25 26 -36 0 rrcurveto
-50 0 -22 -42 0 -52 rrcurveto
closepath
endchar } ND
/threesuperior { 
0 392 hsbw
535 99 hstem
130 129 vstem
164 464 rmoveto
0 71 rlineto
34 0 rlineto
28 0 33 12 0 34 rrcurveto
0 32 -29 21 -33 0 rrcurveto
-42 0 -25 -30 0 -42 rrcurveto
-96 0 rlineto
2 93 61 59 99 0 rrcurveto
85 0 74 -46 0 -77 rrcurveto
0 -42 -24 -34 -42 -10 rrcurveto
0 -2 rlineto
49 -10 32 -38 0 -46 rrcurveto
0 -92 -75 -53 -98 0 rrcurveto
-110 0 -65 56 0 104 rrcurveto
96 0 rlineto
2 -46 27 -34 48 0 rrcurveto
40 0 33 27 0 36 rrcurveto
0 36 -35 18 -30 2 rrcurveto
-5 1 rlineto
-34 0 rlineto
closepath
endchar } ND
/acute { 
0 259 hsbw
307 723 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
endchar } ND
/mu { 
0 593 hsbw
196 201 vstem
401 3 vstem
539 517 rmoveto
0 -517 rlineto
-135 0 rlineto
0 72 rlineto
-3 0 rlineto
-34 -54 -59 -32 -37 0 rrcurveto
-38 0 -24 10 -13 14 rrcurveto
0 -192 rlineto
-142 0 rlineto
0 699 rlineto
142 0 rlineto
0 -292 rlineto
0 -87 27 -40 64 0 rrcurveto
77 0 33 42 0 106 rrcurveto
0 271 rlineto
142 0 rlineto
closepath
endchar } ND
/paragraph { 
0 620 hsbw
323 110 vstem
221 -166 rmoveto
0 523 rlineto
-138 0 -81 68 0 107 rrcurveto
0 132 115 50 125 0 rrcurveto
293 0 rlineto
0 -880 rlineto
-102 0 rlineto
0 800 rlineto
-110 0 rlineto
0 -800 rlineto
-102 0 rlineto
closepath
endchar } ND
/periodcentered { 
0 278 hsbw
55 287 rmoveto
0 46 38 39 47 0 rrcurveto
45 0 39 -39 0 -46 rrcurveto
0 -43 -39 -41 -44 0 rrcurveto
-47 0 -39 38 0 46 rrcurveto
closepath
endchar } ND
/cedilla { 
0 259 hsbw
-166 66 hstem
125 2 vstem
12 -196 rmoveto
21 48 rlineto
28 -12 28 -6 28 0 rrcurveto
26 0 14 12 0 22 rrcurveto
0 23 -15 9 -25 0 rrcurveto
-14 0 -14 -2 -12 -5 rrcurveto
-21 24 rlineto
59 85 rlineto
49 0 rlineto
-39 -55 rlineto
2 0 rlineto
6 4 12 1 16 0 rrcurveto
50 0 42 -24 0 -46 rrcurveto
0 -72 -49 -28 -68 0 rrcurveto
-54 0 -23 6 -42 14 rrcurveto
-5 2 rlineto
closepath
endchar } ND
/onesuperior { 
0 392 hsbw
289 705 rmoveto
0 -432 rlineto
-102 0 rlineto
0 274 rlineto
-110 0 rlineto
0 75 rlineto
73 0 47 24 8 59 rrcurveto
84 0 rlineto
closepath
endchar } ND
/ordmasculine { 
0 367 hsbw
450 189 hstem
119 129 vstem
12 544 rmoveto
0 104 67 66 104 0 rrcurveto
105 0 67 -66 0 -104 rrcurveto
0 -104 -67 -65 -105 0 rrcurveto
-104 0 -67 65 0 104 rrcurveto
closepath
107 0 rmoveto
0 -50 22 -44 42 0 rrcurveto
42 0 23 44 0 50 rrcurveto
0 52 -23 43 -42 0 rrcurveto
-42 0 -22 -43 0 -52 rrcurveto
closepath
endchar } ND
/guillemotright { 
0 444 hsbw
200 143 hstem
199 136 vstem
245 463 rmoveto
157 -123 rlineto
0 -135 rlineto
-157 -125 rlineto
0 120 rlineto
90 72 rlineto
-90 71 rlineto
0 120 rlineto
closepath
-203 0 rmoveto
157 -123 rlineto
0 -135 rlineto
-157 -125 rlineto
0 120 rlineto
90 72 rlineto
-90 71 rlineto
0 120 rlineto
closepath
endchar } ND
/onequarter { 
0 892 hsbw
169 144 hstem
573 116 vstem
152 -22 rmoveto
414 744 rlineto
85 0 rlineto
-412 -744 rlineto
-87 0 rlineto
closepath
84 727 rmoveto
0 -432 rlineto
-102 0 rlineto
0 274 rlineto
-110 0 rlineto
0 75 rlineto
73 0 47 24 8 59 rrcurveto
84 0 rlineto
closepath
453 -705 rmoveto
0 89 rlineto
-192 0 rlineto
0 93 rlineto
188 238 rlineto
100 0 rlineto
0 -251 rlineto
57 0 rlineto
0 -80 rlineto
-57 0 rlineto
0 -89 rlineto
-96 0 rlineto
closepath
0 169 rmoveto
0 144 rlineto
-2 0 rlineto
-114 -144 rlineto
116 0 rlineto
closepath
endchar } ND
/onehalf { 
0 889 hsbw
80 281 hstem
236 273 vstem
611 133 vstem
152 -22 rmoveto
414 744 rlineto
85 0 rlineto
-412 -744 rlineto
-87 0 rlineto
closepath
84 727 rmoveto
0 -432 rlineto
-102 0 rlineto
0 274 rlineto
-110 0 rlineto
0 75 rlineto
73 0 47 24 8 59 rrcurveto
84 0 rlineto
closepath
375 -438 rmoveto
-102 0 rlineto
0 108 62 66 119 0 rrcurveto
88 0 68 -50 0 -85 rrcurveto
0 -58 -28 -37 -53 -33 rrcurveto
-4 -2 -5 -3 -5 -4 rrcurveto
-5 -4 -6 -4 -8 -3 rrcurveto
-48 -28 -29 -20 -22 -30 rrcurveto
209 0 rlineto
0 -80 rlineto
-334 0 rlineto
0 81 39 53 68 40 rrcurveto
11 5 11 8 11 9 rrcurveto
3 2 5 3 10 6 rrcurveto
49 30 29 22 0 40 rrcurveto
0 36 -25 26 -36 0 rrcurveto
-50 0 -22 -42 0 -52 rrcurveto
closepath
endchar } ND
/threequarters { 
0 889 hsbw
169 144 hstem
535 99 hstem
147 129 vstem
593 116 vstem
239 -22 rmoveto
414 744 rlineto
85 0 rlineto
-412 -744 rlineto
-87 0 rlineto
closepath
-58 486 rmoveto
0 71 rlineto
34 0 rlineto
28 0 33 12 0 34 rrcurveto
0 32 -29 21 -33 0 rrcurveto
-42 0 -25 -30 0 -42 rrcurveto
-96 0 rlineto
2 93 61 59 99 0 rrcurveto
85 0 74 -46 0 -77 rrcurveto
0 -42 -24 -34 -42 -10 rrcurveto
0 -2 rlineto
49 -10 32 -38 0 -46 rrcurveto
0 -92 -75 -53 -98 0 rrcurveto
-110 0 -65 56 0 104 rrcurveto
96 0 rlineto
2 -46 27 -34 48 0 rrcurveto
40 0 33 27 0 36 rrcurveto
0 36 -35 18 -30 2 rrcurveto
-5 1 rlineto
-34 0 rlineto
closepath
528 -464 rmoveto
0 89 rlineto
-192 0 rlineto
0 93 rlineto
188 238 rlineto
100 0 rlineto
0 -251 rlineto
57 0 rlineto
0 -80 rlineto
-57 0 rlineto
0 -89 rlineto
-96 0 rlineto
closepath
0 169 rmoveto
0 144 rlineto
-2 0 rlineto
-114 -144 rlineto
116 0 rlineto
closepath
endchar } ND
/questiondown { 
0 556 hsbw
321 56 hstem
189 186 vstem
375 51 rmoveto
147 0 rlineto
-4 -137 -101 -110 -136 0 rrcurveto
-134 0 -115 83 0 110 rrcurveto
0 89 12 67 80 19 rrcurveto
31 15 59 47 0 49 rrcurveto
0 38 rlineto
135 0 rlineto
0 -45 rlineto
0 -82 -37 -41 -58 -40 rrcurveto
-48 -34 -17 -23 0 -45 rrcurveto
0 -60 30 -30 55 0 rrcurveto
66 0 33 54 2 76 rrcurveto
closepath
-12 480 rmoveto
0 -154 rlineto
-157 0 rlineto
0 154 rlineto
157 0 rlineto
closepath
endchar } ND
/Agrave { 
0 685 hsbw
276 262 hstem
714 64 hstem
249 185 vstem
319 778 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
-326 -778 rmoveto
271 714 rlineto
161 0 rlineto
267 -714 rlineto
-163 0 rlineto
-54 159 rlineto
-267 0 rlineto
-56 -159 rlineto
-159 0 rlineto
closepath
349 538 rmoveto
-93 -262 rlineto
185 0 rlineto
-90 262 rlineto
-2 0 rlineto
closepath
endchar } ND
/Aacute { 
0 685 hsbw
276 262 hstem
714 64 hstem
249 185 vstem
520 920 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-527 -920 rmoveto
271 714 rlineto
161 0 rlineto
267 -714 rlineto
-163 0 rlineto
-54 159 rlineto
-267 0 rlineto
-56 -159 rlineto
-159 0 rlineto
closepath
349 538 rmoveto
-93 -262 rlineto
185 0 rlineto
-90 262 rlineto
-2 0 rlineto
closepath
endchar } ND
/Acircumflex { 
0 685 hsbw
276 262 hstem
714 64 hstem
249 185 vstem
167 778 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
-174 -778 rmoveto
271 714 rlineto
161 0 rlineto
267 -714 rlineto
-163 0 rlineto
-54 159 rlineto
-267 0 rlineto
-56 -159 rlineto
-159 0 rlineto
closepath
349 538 rmoveto
-93 -262 rlineto
185 0 rlineto
-90 262 rlineto
-2 0 rlineto
closepath
endchar } ND
/Atilde { 
0 685 hsbw
276 262 hstem
714 83 hstem
249 185 vstem
457 911 rmoveto
69 0 rlineto
-1 -4 rlineto
-22 -69 -20 -41 -69 0 rrcurveto
-20 0 -26 5 -33 11 rrcurveto
-3 1 rlineto
-1 0 rlineto
-31 11 -23 6 -15 0 rrcurveto
-26 0 -10 -14 -6 -28 rrcurveto
-61 0 rlineto
15 67 38 55 73 0 rrcurveto
13 0 19 -5 25 -9 rrcurveto
7 -3 rlineto
28 -12 22 -6 16 0 rrcurveto
28 0 14 11 0 24 rrcurveto
closepath
-464 -911 rmoveto
271 714 rlineto
161 0 rlineto
267 -714 rlineto
-163 0 rlineto
-54 159 rlineto
-267 0 rlineto
-56 -159 rlineto
-159 0 rlineto
closepath
349 538 rmoveto
-93 -262 rlineto
185 0 rlineto
-90 262 rlineto
-2 0 rlineto
closepath
endchar } ND
/Adieresis { 
0 685 hsbw
276 262 hstem
714 80 hstem
307 71 vstem
378 794 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-179 -794 rmoveto
271 714 rlineto
161 0 rlineto
267 -714 rlineto
-163 0 rlineto
-54 159 rlineto
-267 0 rlineto
-56 -159 rlineto
-159 0 rlineto
closepath
349 538 rmoveto
-93 -262 rlineto
185 0 rlineto
-90 262 rlineto
-2 0 rlineto
closepath
endchar } ND
/Aring { 
0 685 hsbw
276 262 hstem
714 17 hstem
783 140 hstem
279 127 vstem
279 853 rmoveto
0 -40 25 -30 38 0 rrcurveto
37 0 27 30 0 40 rrcurveto
0 40 -27 30 -37 0 rrcurveto
-38 0 -25 -30 0 -40 rrcurveto
closepath
-59 0 rmoveto
0 66 55 56 67 0 rrcurveto
68 0 55 -56 0 -66 rrcurveto
0 -67 -55 -55 -68 0 rrcurveto
-67 0 -55 55 0 67 rrcurveto
closepath
-227 -853 rmoveto
271 714 rlineto
161 0 rlineto
267 -714 rlineto
-163 0 rlineto
-54 159 rlineto
-267 0 rlineto
-56 -159 rlineto
-159 0 rlineto
closepath
349 538 rmoveto
-93 -262 rlineto
185 0 rlineto
-90 262 rlineto
-2 0 rlineto
closepath
endchar } ND
/AE { 
0 981 hsbw
132 175 hstem
429 153 hstem
280 172 vstem
418 582 rmoveto
-138 -306 rlineto
172 0 rlineto
0 306 rlineto
-34 0 rlineto
closepath
-429 -582 rmoveto
341 714 rlineto
606 0 rlineto
0 -132 rlineto
-332 0 rlineto
0 -153 rlineto
312 0 rlineto
0 -122 rlineto
-312 0 rlineto
0 -175 rlineto
340 0 rlineto
0 -132 rlineto
-492 0 rlineto
0 159 rlineto
-224 0 rlineto
-72 -159 rlineto
-167 0 rlineto
closepath
endchar } ND
/Ccedilla { 
0 741 hsbw
-166 66 hstem
275 199 hstem
366 2 vstem
697 474 rmoveto
-152 0 rlineto
-10 70 -68 55 -78 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
0 -122 65 -117 129 0 rrcurveto
93 0 57 62 12 98 rrcurveto
152 0 rlineto
-16 -178 -121 -112 -174 -2 rrcurveto
-26 -36 rlineto
2 0 rlineto
6 4 12 1 16 0 rrcurveto
50 0 42 -24 0 -46 rrcurveto
0 -72 -49 -28 -68 0 rrcurveto
-54 0 -23 6 -42 14 rrcurveto
-5 2 rlineto
21 48 rlineto
28 -12 28 -6 28 0 rrcurveto
26 0 14 12 0 22 rrcurveto
0 23 -15 9 -25 0 rrcurveto
-14 0 -14 -2 -12 -5 rrcurveto
-21 24 rlineto
48 69 rlineto
-188 19 -119 166 0 183 rrcurveto
0 199 145 178 206 0 rrcurveto
151 0 139 -103 18 -154 rrcurveto
closepath
endchar } ND
/Egrave { 
0 648 hsbw
132 175 hstem
429 153 hstem
714 64 hstem
301 778 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
-232 -778 rmoveto
0 714 rlineto
534 0 rlineto
0 -132 rlineto
-377 0 rlineto
0 -153 rlineto
346 0 rlineto
0 -122 rlineto
-346 0 rlineto
0 -175 rlineto
385 0 rlineto
0 -132 rlineto
-542 0 rlineto
closepath
endchar } ND
/Eacute { 
0 648 hsbw
132 175 hstem
429 153 hstem
714 64 hstem
502 920 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-433 -920 rmoveto
0 714 rlineto
534 0 rlineto
0 -132 rlineto
-377 0 rlineto
0 -153 rlineto
346 0 rlineto
0 -122 rlineto
-346 0 rlineto
0 -175 rlineto
385 0 rlineto
0 -132 rlineto
-542 0 rlineto
closepath
endchar } ND
/Ecircumflex { 
0 648 hsbw
132 175 hstem
429 153 hstem
714 64 hstem
257 126 vstem
149 778 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
-80 -778 rmoveto
0 714 rlineto
534 0 rlineto
0 -132 rlineto
-377 0 rlineto
0 -153 rlineto
346 0 rlineto
0 -122 rlineto
-346 0 rlineto
0 -175 rlineto
385 0 rlineto
0 -132 rlineto
-542 0 rlineto
closepath
endchar } ND
/Edieresis { 
0 648 hsbw
132 175 hstem
429 153 hstem
714 80 hstem
289 71 vstem
360 794 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-85 -794 rmoveto
0 714 rlineto
534 0 rlineto
0 -132 rlineto
-377 0 rlineto
0 -153 rlineto
346 0 rlineto
0 -122 rlineto
-346 0 rlineto
0 -175 rlineto
385 0 rlineto
0 -132 rlineto
-542 0 rlineto
closepath
endchar } ND
/Igrave { 
0 295 hsbw
714 64 hstem
124 778 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
-55 -778 rmoveto
0 714 rlineto
157 0 rlineto
0 -714 rlineto
-157 0 rlineto
closepath
endchar } ND
/Iacute { 
0 295 hsbw
714 64 hstem
325 920 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-256 -920 rmoveto
0 714 rlineto
157 0 rlineto
0 -714 rlineto
-157 0 rlineto
closepath
endchar } ND
/Icircumflex { 
0 295 hsbw
714 64 hstem
80 126 vstem
-28 778 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
97 -778 rmoveto
0 714 rlineto
157 0 rlineto
0 -714 rlineto
-157 0 rlineto
closepath
endchar } ND
/Idieresis { 
0 295 hsbw
714 80 hstem
112 71 vstem
183 794 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
92 -794 rmoveto
0 714 rlineto
157 0 rlineto
0 -714 rlineto
-157 0 rlineto
closepath
endchar } ND
/Eth { 
0 741 hsbw
132 178 hstem
427 155 hstem
386 158 vstem
69 0 rmoveto
0 311 rlineto
-66 0 rlineto
0 116 rlineto
66 0 rlineto
0 287 rlineto
308 0 rlineto
193 0 131 -124 0 -229 rrcurveto
0 -198 -122 -163 -202 0 rrcurveto
-308 0 rlineto
closepath
157 582 rmoveto
0 -155 rlineto
160 0 rlineto
0 -117 rlineto
-160 0 rlineto
0 -178 rlineto
140 0 rlineto
110 0 68 96 0 118 rrcurveto
0 153 -57 83 -149 0 rrcurveto
-112 0 rlineto
closepath
endchar } ND
/Ntilde { 
0 741 hsbw
714 75 hstem
216 309 vstem
485 911 rmoveto
69 0 rlineto
-1 -4 rlineto
-22 -69 -20 -41 -69 0 rrcurveto
-20 0 -26 5 -33 11 rrcurveto
-3 1 rlineto
-1 0 rlineto
-31 11 -23 6 -15 0 rrcurveto
-26 0 -10 -14 -6 -28 rrcurveto
-61 0 rlineto
15 67 38 55 73 0 rrcurveto
13 0 19 -5 25 -9 rrcurveto
7 -3 rlineto
28 -12 22 -6 16 0 rrcurveto
28 0 14 11 0 24 rrcurveto
closepath
-416 -911 rmoveto
0 714 rlineto
156 0 rlineto
298 -479 rlineto
2 0 rlineto
0 479 rlineto
147 0 rlineto
0 -714 rlineto
-157 0 rlineto
-297 478 rlineto
-2 0 rlineto
0 -478 rlineto
-147 0 rlineto
closepath
endchar } ND
/Ograve { 
0 778 hsbw
115 484 hstem
731 47 hstem
195 388 vstem
366 778 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
-328 -424 rmoveto
0 199 145 178 206 0 rrcurveto
207 0 144 -177 0 -200 rrcurveto
0 -202 -144 -169 -207 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
closepath
157 0 rmoveto
0 -122 65 -117 129 0 rrcurveto
129 0 65 118 0 121 rrcurveto
0 119 -62 126 -132 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
closepath
endchar } ND
/Oacute { 
0 778 hsbw
115 484 hstem
731 47 hstem
195 388 vstem
567 920 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-529 -566 rmoveto
0 199 145 178 206 0 rrcurveto
207 0 144 -177 0 -200 rrcurveto
0 -202 -144 -169 -207 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
closepath
157 0 rmoveto
0 -122 65 -117 129 0 rrcurveto
129 0 65 118 0 121 rrcurveto
0 119 -62 126 -132 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
closepath
endchar } ND
/Ocircumflex { 
0 778 hsbw
115 484 hstem
731 47 hstem
195 388 vstem
214 778 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
-176 -424 rmoveto
0 199 145 178 206 0 rrcurveto
207 0 144 -177 0 -200 rrcurveto
0 -202 -144 -169 -207 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
closepath
157 0 rmoveto
0 -122 65 -117 129 0 rrcurveto
129 0 65 118 0 121 rrcurveto
0 119 -62 126 -132 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
closepath
endchar } ND
/Otilde { 
0 778 hsbw
115 484 hstem
731 58 hstem
195 388 vstem
504 911 rmoveto
69 0 rlineto
-1 -4 rlineto
-22 -69 -20 -41 -69 0 rrcurveto
-20 0 -26 5 -33 11 rrcurveto
-3 1 rlineto
-1 0 rlineto
-31 11 -23 6 -15 0 rrcurveto
-26 0 -10 -14 -6 -28 rrcurveto
-61 0 rlineto
15 67 38 55 73 0 rrcurveto
13 0 19 -5 25 -9 rrcurveto
7 -3 rlineto
28 -12 22 -6 16 0 rrcurveto
28 0 14 11 0 24 rrcurveto
closepath
-466 -557 rmoveto
0 199 145 178 206 0 rrcurveto
207 0 144 -177 0 -200 rrcurveto
0 -202 -144 -169 -207 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
closepath
157 0 rmoveto
0 -122 65 -117 129 0 rrcurveto
129 0 65 118 0 121 rrcurveto
0 119 -62 126 -132 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
closepath
endchar } ND
/Odieresis { 
0 778 hsbw
115 484 hstem
731 63 hstem
354 71 vstem
425 794 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-181 -440 rmoveto
0 199 145 178 206 0 rrcurveto
207 0 144 -177 0 -200 rrcurveto
0 -202 -144 -169 -207 0 rrcurveto
-206 0 -145 170 0 201 rrcurveto
closepath
157 0 rmoveto
0 -122 65 -117 129 0 rrcurveto
129 0 65 118 0 121 rrcurveto
0 119 -62 126 -132 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
closepath
endchar } ND
/multiply { 
0 600 hsbw
140 17 rmoveto
-76 76 rlineto
161 160 rlineto
-159 160 rlineto
76 76 rlineto
159 -160 rlineto
160 160 rlineto
76 -76 rlineto
-161 -160 rlineto
161 -160 rlineto
-76 -76 rlineto
-160 161 rlineto
-161 -161 rlineto
closepath
endchar } ND
/Oslash { 
0 778 hsbw
115 484 hstem
195 388 vstem
34 10 rmoveto
86 95 rlineto
-54 65 -28 88 0 96 rrcurveto
0 199 145 178 206 0 rrcurveto
90 0 75 -26 60 -51 rrcurveto
80 89 rlineto
50 -44 rlineto
-84 -93 rlineto
52 -66 28 -88 0 -98 rrcurveto
0 -202 -144 -169 -207 0 rrcurveto
-90 0 -76 28 -57 48 rrcurveto
-83 -92 rlineto
-49 43 rlineto
closepath
189 209 rmoveto
296 329 rlineto
-33 34 -43 17 -54 0 rrcurveto
-130 0 -64 -125 0 -120 rrcurveto
0 -52 9 -45 19 -38 rrcurveto
closepath
333 271 rmoveto
-295 -326 rlineto
32 -33 43 -16 53 0 rrcurveto
129 0 65 118 0 121 rrcurveto
0 52 -9 45 -18 39 rrcurveto
closepath
endchar } ND
/Ugrave { 
0 741 hsbw
115 663 hstem
223 295 vstem
347 778 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
328 -64 rmoveto
0 -444 rlineto
0 -190 -111 -97 -194 0 rrcurveto
-195 0 -109 96 0 191 rrcurveto
0 444 rlineto
157 0 rlineto
0 -444 rlineto
0 -90 51 -65 96 0 rrcurveto
109 0 39 45 0 110 rrcurveto
0 444 rlineto
157 0 rlineto
closepath
endchar } ND
/Uacute { 
0 741 hsbw
115 663 hstem
223 295 vstem
548 920 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
127 -206 rmoveto
0 -444 rlineto
0 -190 -111 -97 -194 0 rrcurveto
-195 0 -109 96 0 191 rrcurveto
0 444 rlineto
157 0 rlineto
0 -444 rlineto
0 -90 51 -65 96 0 rrcurveto
109 0 39 45 0 110 rrcurveto
0 444 rlineto
157 0 rlineto
closepath
endchar } ND
/Ucircumflex { 
0 741 hsbw
714 64 hstem
223 295 vstem
195 778 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
480 -64 rmoveto
0 -444 rlineto
0 -190 -111 -97 -194 0 rrcurveto
-195 0 -109 96 0 191 rrcurveto
0 444 rlineto
157 0 rlineto
0 -444 rlineto
0 -90 51 -65 96 0 rrcurveto
109 0 39 45 0 110 rrcurveto
0 444 rlineto
157 0 rlineto
closepath
endchar } ND
/Udieresis { 
0 741 hsbw
714 80 hstem
335 71 vstem
406 794 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
475 -80 rmoveto
0 -444 rlineto
0 -190 -111 -97 -194 0 rrcurveto
-195 0 -109 96 0 191 rrcurveto
0 444 rlineto
157 0 rlineto
0 -444 rlineto
0 -90 51 -65 96 0 rrcurveto
109 0 39 45 0 110 rrcurveto
0 444 rlineto
157 0 rlineto
closepath
endchar } ND
/Yacute { 
0 667 hsbw
432 346 hstem
168 333 vstem
511 920 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-257 -920 rmoveto
0 278 rlineto
-263 436 rlineto
177 0 rlineto
167 -282 rlineto
166 282 rlineto
175 0 rlineto
-265 -440 rlineto
0 -274 rlineto
-157 0 rlineto
closepath
endchar } ND
/Thorn { 
0 667 hsbw
281 214 hstem
226 260 vstem
69 0 rmoveto
0 714 rlineto
157 0 rlineto
0 -97 rlineto
165 0 rlineto
140 0 107 -99 0 -130 rrcurveto
0 -131 -105 -98 -142 0 rrcurveto
-165 0 rlineto
0 -159 rlineto
-157 0 rlineto
closepath
157 495 rmoveto
0 -214 rlineto
122 0 rlineto
73 0 65 28 0 79 rrcurveto
0 79 -64 28 -74 0 rrcurveto
-122 0 rlineto
closepath
endchar } ND
/germandbls { 
0 611 hsbw
108 238 hstem
398 2 hstem
438 176 hstem
198 58 vstem
56 0 rmoveto
0 491 rlineto
0 135 84 105 151 0 rrcurveto
114 0 113 -68 0 -124 rrcurveto
0 -63 -39 -59 -54 -17 rrcurveto
0 -2 rlineto
84 -19 46 -71 0 -89 rrcurveto
0 -122 -94 -111 -123 0 rrcurveto
-28 0 -27 2 -27 4 rrcurveto
0 116 rlineto
10 -4 14 -1 17 0 rrcurveto
66 0 50 53 0 68 rrcurveto
0 74 -41 48 -81 0 rrcurveto
-35 0 rlineto
0 92 rlineto
22 0 rlineto
58 0 47 28 0 59 rrcurveto
0 60 -48 29 -48 0 rrcurveto
-56 0 -33 -38 0 -60 rrcurveto
0 -516 rlineto
-142 0 rlineto
closepath
endchar } ND
/agrave { 
0 574 hsbw
81 143 hstem
531 50 hstem
174 202 vstem
264 581 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
112 -381 rmoveto
0 53 rlineto
-16 -14 -24 -5 -44 -6 rrcurveto
-7 -1 -7 -1 -5 -2 rrcurveto
-15 0 rlineto
-54 -11 -30 -20 0 -46 rrcurveto
0 -50 33 -16 54 0 rrcurveto
75 0 40 57 0 62 rrcurveto
closepath
-186 158 rmoveto
-142 0 rlineto
7 124 120 49 116 0 rrcurveto
103 0 124 -32 0 -115 rrcurveto
0 -269 rlineto
0 -52 6 -43 12 -20 rrcurveto
-144 0 rlineto
-6 18 -4 17 0 15 rrcurveto
-43 -44 -63 -20 -68 0 rrcurveto
-104 0 -72 53 0 102 rrcurveto
0 118 110 32 112 13 rrcurveto
86 10 36 9 0 43 rrcurveto
0 56 -34 14 -58 0 rrcurveto
-56 0 -33 -24 -5 -54 rrcurveto
closepath
endchar } ND
/aacute { 
0 574 hsbw
81 143 hstem
531 50 hstem
174 202 vstem
465 723 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-89 -523 rmoveto
0 53 rlineto
-16 -14 -24 -5 -44 -6 rrcurveto
-7 -1 -7 -1 -5 -2 rrcurveto
-15 0 rlineto
-54 -11 -30 -20 0 -46 rrcurveto
0 -50 33 -16 54 0 rrcurveto
75 0 40 57 0 62 rrcurveto
closepath
-186 158 rmoveto
-142 0 rlineto
7 124 120 49 116 0 rrcurveto
103 0 124 -32 0 -115 rrcurveto
0 -269 rlineto
0 -52 6 -43 12 -20 rrcurveto
-144 0 rlineto
-6 18 -4 17 0 15 rrcurveto
-43 -44 -63 -20 -68 0 rrcurveto
-104 0 -72 53 0 102 rrcurveto
0 118 110 32 112 13 rrcurveto
86 10 36 9 0 43 rrcurveto
0 56 -34 14 -58 0 rrcurveto
-56 0 -33 -24 -5 -54 rrcurveto
closepath
endchar } ND
/acircumflex { 
0 574 hsbw
81 143 hstem
531 50 hstem
174 202 vstem
112 581 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
264 -381 rmoveto
0 53 rlineto
-16 -14 -24 -5 -44 -6 rrcurveto
-7 -1 -7 -1 -5 -2 rrcurveto
-15 0 rlineto
-54 -11 -30 -20 0 -46 rrcurveto
0 -50 33 -16 54 0 rrcurveto
75 0 40 57 0 62 rrcurveto
closepath
-186 158 rmoveto
-142 0 rlineto
7 124 120 49 116 0 rrcurveto
103 0 124 -32 0 -115 rrcurveto
0 -269 rlineto
0 -52 6 -43 12 -20 rrcurveto
-144 0 rlineto
-6 18 -4 17 0 15 rrcurveto
-43 -44 -63 -20 -68 0 rrcurveto
-104 0 -72 53 0 102 rrcurveto
0 118 110 32 112 13 rrcurveto
86 10 36 9 0 43 rrcurveto
0 56 -34 14 -58 0 rrcurveto
-56 0 -33 -24 -5 -54 rrcurveto
closepath
endchar } ND
/atilde { 
0 574 hsbw
81 143 hstem
531 69 hstem
174 202 vstem
402 714 rmoveto
69 0 rlineto
-1 -4 rlineto
-22 -69 -20 -41 -69 0 rrcurveto
-20 0 -26 5 -33 11 rrcurveto
-3 1 rlineto
-1 0 rlineto
-31 11 -23 6 -15 0 rrcurveto
-26 0 -10 -14 -6 -28 rrcurveto
-61 0 rlineto
15 67 38 55 73 0 rrcurveto
13 0 19 -5 25 -9 rrcurveto
7 -3 rlineto
28 -12 22 -6 16 0 rrcurveto
28 0 14 11 0 24 rrcurveto
closepath
-26 -514 rmoveto
0 53 rlineto
-16 -14 -24 -5 -44 -6 rrcurveto
-7 -1 -7 -1 -5 -2 rrcurveto
-15 0 rlineto
-54 -11 -30 -20 0 -46 rrcurveto
0 -50 33 -16 54 0 rrcurveto
75 0 40 57 0 62 rrcurveto
closepath
-186 158 rmoveto
-142 0 rlineto
7 124 120 49 116 0 rrcurveto
103 0 124 -32 0 -115 rrcurveto
0 -269 rlineto
0 -52 6 -43 12 -20 rrcurveto
-144 0 rlineto
-6 18 -4 17 0 15 rrcurveto
-43 -44 -63 -20 -68 0 rrcurveto
-104 0 -72 53 0 102 rrcurveto
0 118 110 32 112 13 rrcurveto
86 10 36 9 0 43 rrcurveto
0 56 -34 14 -58 0 rrcurveto
-56 0 -33 -24 -5 -54 rrcurveto
closepath
endchar } ND
/adieresis { 
0 574 hsbw
81 143 hstem
531 66 hstem
252 71 vstem
323 597 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
259 -397 rmoveto
0 53 rlineto
-16 -14 -24 -5 -44 -6 rrcurveto
-7 -1 -7 -1 -5 -2 rrcurveto
-15 0 rlineto
-54 -11 -30 -20 0 -46 rrcurveto
0 -50 33 -16 54 0 rrcurveto
75 0 40 57 0 62 rrcurveto
closepath
-186 158 rmoveto
-142 0 rlineto
7 124 120 49 116 0 rrcurveto
103 0 124 -32 0 -115 rrcurveto
0 -269 rlineto
0 -52 6 -43 12 -20 rrcurveto
-144 0 rlineto
-6 18 -4 17 0 15 rrcurveto
-43 -44 -63 -20 -68 0 rrcurveto
-104 0 -72 53 0 102 rrcurveto
0 118 110 32 112 13 rrcurveto
86 10 36 9 0 43 rrcurveto
0 56 -34 14 -58 0 rrcurveto
-56 0 -33 -24 -5 -54 rrcurveto
closepath
endchar } ND
/aring { 
0 574 hsbw
81 143 hstem
531 3 hstem
586 140 hstem
224 127 vstem
224 656 rmoveto
0 -40 25 -30 38 0 rrcurveto
37 0 27 30 0 40 rrcurveto
0 40 -27 30 -37 0 rrcurveto
-38 0 -25 -30 0 -40 rrcurveto
closepath
-59 0 rmoveto
0 66 55 56 67 0 rrcurveto
68 0 55 -56 0 -66 rrcurveto
0 -67 -55 -55 -68 0 rrcurveto
-67 0 -55 55 0 67 rrcurveto
closepath
211 -456 rmoveto
0 53 rlineto
-16 -14 -24 -5 -44 -6 rrcurveto
-7 -1 -7 -1 -5 -2 rrcurveto
-15 0 rlineto
-54 -11 -30 -20 0 -46 rrcurveto
0 -50 33 -16 54 0 rrcurveto
75 0 40 57 0 62 rrcurveto
closepath
-186 158 rmoveto
-142 0 rlineto
7 124 120 49 116 0 rrcurveto
103 0 124 -32 0 -115 rrcurveto
0 -269 rlineto
0 -52 6 -43 12 -20 rrcurveto
-144 0 rlineto
-6 18 -4 17 0 15 rrcurveto
-43 -44 -63 -20 -68 0 rrcurveto
-104 0 -72 53 0 102 rrcurveto
0 118 110 32 112 13 rrcurveto
86 10 36 9 0 43 rrcurveto
0 56 -34 14 -58 0 rrcurveto
-56 0 -33 -24 -5 -54 rrcurveto
closepath
endchar } ND
/ae { 
0 907 hsbw
162 62 hstem
314 122 hstem
180 205 vstem
509 227 vstem
385 188 rmoveto
0 64 rlineto
-19 -15 -27 -6 -45 -6 rrcurveto
-6 -1 -7 -1 -4 -2 rrcurveto
-14 0 rlineto
-52 0 -31 -28 0 -45 rrcurveto
0 -48 38 -19 50 0 rrcurveto
60 0 57 36 0 71 rrcurveto
closepath
493 36 rmoveto
-369 0 rlineto
0 -76 49 -67 74 0 rrcurveto
60 0 33 29 10 52 rrcurveto
138 0 rlineto
-28 -108 -97 -68 -113 0 rrcurveto
-79 0 -73 28 -41 60 rrcurveto
-46 -59 -81 -29 -84 0 rrcurveto
-99 0 -94 48 0 106 rrcurveto
0 100 79 44 94 14 rrcurveto
37 5 rlineto
109 16 28 7 0 44 rrcurveto
0 49 -38 17 -54 0 rrcurveto
-59 0 -39 -24 -1 -56 rrcurveto
-142 0 rlineto
3 122 125 53 116 0 rrcurveto
69 0 68 -21 34 -49 rrcurveto
41 48 52 22 80 0 rrcurveto
163 0 75 -153 0 -154 rrcurveto
closepath
-369 90 rmoveto
227 0 rlineto
0 68 -43 54 -68 0 rrcurveto
-70 0 -45 -56 -1 -66 rrcurveto
closepath
endchar } ND
/ccedilla { 
0 574 hsbw
-166 66 hstem
198 137 hstem
283 2 vstem
545 335 rmoveto
-139 0 rlineto
-10 59 -35 30 -60 0 rrcurveto
-87 0 -34 -90 0 -78 rrcurveto
0 -76 32 -87 86 0 rrcurveto
64 0 39 39 10 66 rrcurveto
137 0 rlineto
-18 -132 -87 -75 -132 -4 rrcurveto
-28 -40 rlineto
2 0 rlineto
6 4 12 1 16 0 rrcurveto
50 0 42 -24 0 -46 rrcurveto
0 -72 -49 -28 -68 0 rrcurveto
-54 0 -23 6 -42 14 rrcurveto
-5 2 rlineto
21 48 rlineto
28 -12 28 -6 28 0 rrcurveto
26 0 14 12 0 22 rrcurveto
0 23 -15 9 -25 0 rrcurveto
-14 0 -14 -2 -12 -5 rrcurveto
-21 24 rlineto
50 72 rlineto
-136 13 -90 106 0 143 rrcurveto
0 153 106 127 158 0 rrcurveto
130 0 105 -71 8 -125 rrcurveto
closepath
endchar } ND
/egrave { 
0 574 hsbw
160 64 hstem
314 110 hstem
531 50 hstem
171 231 vstem
264 581 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
280 -357 rmoveto
-373 0 rlineto
4 -90 43 -41 80 0 rrcurveto
54 0 48 32 10 35 rrcurveto
125 0 rlineto
-39 -119 -83 -55 -120 0 rrcurveto
-154 0 -110 120 0 152 rrcurveto
0 146 114 127 150 0 rrcurveto
151 0 101 -135 0 -147 rrcurveto
-1 -5 rlineto
0 -20 rlineto
closepath
-373 90 rmoveto
231 0 rlineto
-14 74 -33 36 -66 0 rrcurveto
-70 0 -48 -55 0 -55 rrcurveto
closepath
endchar } ND
/eacute { 
0 574 hsbw
160 64 hstem
314 110 hstem
531 50 hstem
171 231 vstem
465 723 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
79 -499 rmoveto
-373 0 rlineto
4 -90 43 -41 80 0 rrcurveto
54 0 48 32 10 35 rrcurveto
125 0 rlineto
-39 -119 -83 -55 -120 0 rrcurveto
-154 0 -110 120 0 152 rrcurveto
0 146 114 127 150 0 rrcurveto
151 0 101 -135 0 -147 rrcurveto
-1 -5 rlineto
0 -20 rlineto
closepath
-373 90 rmoveto
231 0 rlineto
-14 74 -33 36 -66 0 rrcurveto
-70 0 -48 -55 0 -55 rrcurveto
closepath
endchar } ND
/ecircumflex { 
0 574 hsbw
160 64 hstem
314 110 hstem
531 50 hstem
171 231 vstem
112 581 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
432 -357 rmoveto
-373 0 rlineto
4 -90 43 -41 80 0 rrcurveto
54 0 48 32 10 35 rrcurveto
125 0 rlineto
-39 -119 -83 -55 -120 0 rrcurveto
-154 0 -110 120 0 152 rrcurveto
0 146 114 127 150 0 rrcurveto
151 0 101 -135 0 -147 rrcurveto
-1 -5 rlineto
0 -20 rlineto
closepath
-373 90 rmoveto
231 0 rlineto
-14 74 -33 36 -66 0 rrcurveto
-70 0 -48 -55 0 -55 rrcurveto
closepath
endchar } ND
/edieresis { 
0 574 hsbw
160 64 hstem
314 110 hstem
531 66 hstem
252 71 vstem
323 597 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
427 -373 rmoveto
-373 0 rlineto
4 -90 43 -41 80 0 rrcurveto
54 0 48 32 10 35 rrcurveto
125 0 rlineto
-39 -119 -83 -55 -120 0 rrcurveto
-154 0 -110 120 0 152 rrcurveto
0 146 114 127 150 0 rrcurveto
151 0 101 -135 0 -147 rrcurveto
-1 -5 rlineto
0 -20 rlineto
closepath
-373 90 rmoveto
231 0 rlineto
-14 74 -33 36 -66 0 rrcurveto
-70 0 -48 -55 0 -55 rrcurveto
closepath
endchar } ND
/igrave { 
0 258 hsbw
517 64 hstem
106 581 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
-48 -581 rmoveto
0 517 rlineto
142 0 rlineto
0 -517 rlineto
-142 0 rlineto
closepath
endchar } ND
/iacute { 
0 258 hsbw
517 64 hstem
307 723 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-249 -723 rmoveto
0 517 rlineto
142 0 rlineto
0 -517 rlineto
-142 0 rlineto
closepath
endchar } ND
/icircumflex { 
0 258 hsbw
517 64 hstem
62 126 vstem
-46 581 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
104 -581 rmoveto
0 517 rlineto
142 0 rlineto
0 -517 rlineto
-142 0 rlineto
closepath
endchar } ND
/idieresis { 
0 258 hsbw
517 80 hstem
94 71 vstem
165 597 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
99 -597 rmoveto
0 517 rlineto
142 0 rlineto
0 -517 rlineto
-142 0 rlineto
closepath
endchar } ND
/eth { 
0 611 hsbw
93 318 hstem
506 31 hstem
180 251 vstem
305 93 rmoveto
88 0 38 83 0 82 rrcurveto
0 74 -44 79 -83 0 rrcurveto
-83 0 -41 -78 0 -75 rrcurveto
0 -83 37 -82 88 0 rrcurveto
closepath
-172 500 rmoveto
107 54 rlineto
-30 20 -28 17 -26 13 rrcurveto
92 69 rlineto
36 -20 33 -22 30 -24 rrcurveto
123 62 rlineto
52 -59 rlineto
-114 -57 rlineto
108 -104 57 -116 0 -153 rrcurveto
0 -155 -110 -132 -158 0 rrcurveto
-150 0 -117 113 0 148 rrcurveto
0 129 89 130 141 0 rrcurveto
44 0 48 -11 43 -29 rrcurveto
2 0 rlineto
-22 49 -35 39 -45 39 rrcurveto
-112 -56 rlineto
-58 56 rlineto
closepath
endchar } ND
/ntilde { 
0 593 hsbw
531 69 hstem
189 3 vstem
196 201 vstem
411 714 rmoveto
69 0 rlineto
-1 -4 rlineto
-22 -69 -20 -41 -69 0 rrcurveto
-20 0 -26 5 -33 11 rrcurveto
-3 1 rlineto
-1 0 rlineto
-31 11 -23 6 -15 0 rrcurveto
-26 0 -10 -14 -6 -28 rrcurveto
-61 0 rlineto
15 67 38 55 73 0 rrcurveto
13 0 19 -5 25 -9 rrcurveto
7 -3 rlineto
28 -12 22 -6 16 0 rrcurveto
28 0 14 11 0 24 rrcurveto
closepath
-357 -714 rmoveto
0 517 rlineto
135 0 rlineto
0 -72 rlineto
3 0 rlineto
34 56 61 30 63 0 rrcurveto
126 0 63 -88 0 -125 rrcurveto
0 -318 rlineto
-142 0 rlineto
0 292 rlineto
0 86 -26 41 -65 0 rrcurveto
-78 0 -32 -43 0 -105 rrcurveto
0 -271 rlineto
-142 0 rlineto
closepath
endchar } ND
/ograve { 
0 611 hsbw
93 331 hstem
531 50 hstem
180 251 vstem
282 581 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
-244 -323 rmoveto
0 165 105 108 162 0 rrcurveto
162 0 106 -108 0 -165 rrcurveto
0 -164 -106 -108 -162 0 rrcurveto
-162 0 -105 108 0 164 rrcurveto
closepath
142 0 rmoveto
0 -83 37 -82 88 0 rrcurveto
88 0 38 83 0 82 rrcurveto
0 82 -38 84 -88 0 rrcurveto
-88 0 -37 -83 0 -83 rrcurveto
closepath
endchar } ND
/oacute { 
0 611 hsbw
93 331 hstem
531 50 hstem
180 251 vstem
483 723 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
-445 -465 rmoveto
0 165 105 108 162 0 rrcurveto
162 0 106 -108 0 -165 rrcurveto
0 -164 -106 -108 -162 0 rrcurveto
-162 0 -105 108 0 164 rrcurveto
closepath
142 0 rmoveto
0 -83 37 -82 88 0 rrcurveto
88 0 38 83 0 82 rrcurveto
0 82 -38 84 -88 0 rrcurveto
-88 0 -37 -83 0 -83 rrcurveto
closepath
endchar } ND
/ocircumflex { 
0 611 hsbw
93 331 hstem
531 50 hstem
180 251 vstem
130 581 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
-92 -323 rmoveto
0 165 105 108 162 0 rrcurveto
162 0 106 -108 0 -165 rrcurveto
0 -164 -106 -108 -162 0 rrcurveto
-162 0 -105 108 0 164 rrcurveto
closepath
142 0 rmoveto
0 -83 37 -82 88 0 rrcurveto
88 0 38 83 0 82 rrcurveto
0 82 -38 84 -88 0 rrcurveto
-88 0 -37 -83 0 -83 rrcurveto
closepath
endchar } ND
/otilde { 
0 611 hsbw
93 331 hstem
531 61 hstem
180 251 vstem
420 714 rmoveto
69 0 rlineto
-1 -4 rlineto
-22 -69 -20 -41 -69 0 rrcurveto
-20 0 -26 5 -33 11 rrcurveto
-3 1 rlineto
-1 0 rlineto
-31 11 -23 6 -15 0 rrcurveto
-26 0 -10 -14 -6 -28 rrcurveto
-61 0 rlineto
15 67 38 55 73 0 rrcurveto
13 0 19 -5 25 -9 rrcurveto
7 -3 rlineto
28 -12 22 -6 16 0 rrcurveto
28 0 14 11 0 24 rrcurveto
closepath
-382 -456 rmoveto
0 165 105 108 162 0 rrcurveto
162 0 106 -108 0 -165 rrcurveto
0 -164 -106 -108 -162 0 rrcurveto
-162 0 -105 108 0 164 rrcurveto
closepath
142 0 rmoveto
0 -83 37 -82 88 0 rrcurveto
88 0 38 83 0 82 rrcurveto
0 82 -38 84 -88 0 rrcurveto
-88 0 -37 -83 0 -83 rrcurveto
closepath
endchar } ND
/odieresis { 
0 611 hsbw
93 331 hstem
531 66 hstem
270 71 vstem
341 597 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-97 -339 rmoveto
0 165 105 108 162 0 rrcurveto
162 0 106 -108 0 -165 rrcurveto
0 -164 -106 -108 -162 0 rrcurveto
-162 0 -105 108 0 164 rrcurveto
closepath
142 0 rmoveto
0 -83 37 -82 88 0 rrcurveto
88 0 38 83 0 82 rrcurveto
0 82 -38 84 -88 0 rrcurveto
-88 0 -37 -83 0 -83 rrcurveto
closepath
endchar } ND
/divide { 
0 600 hsbw
129 70 hstem
306 70 hstem
48 199 rmoveto
0 107 rlineto
505 0 rlineto
0 -107 rlineto
-505 0 rlineto
closepath
168 261 rmoveto
0 46 38 39 47 0 rrcurveto
45 0 39 -39 0 -46 rrcurveto
0 -43 -39 -41 -44 0 rrcurveto
-47 0 -39 38 0 46 rrcurveto
closepath
0 -415 rmoveto
0 46 38 38 47 0 rrcurveto
45 0 39 -39 0 -45 rrcurveto
0 -43 -39 -42 -44 0 rrcurveto
-47 0 -39 39 0 46 rrcurveto
closepath
endchar } ND
/oslash { 
0 611 hsbw
93 331 hstem
180 251 vstem
195 172 rmoveto
193 220 rlineto
-20 21 -28 11 -35 0 rrcurveto
-88 0 -37 -83 0 -83 rrcurveto
0 -35 5 -29 10 -22 rrcurveto
closepath
220 177 rmoveto
-195 -222 rlineto
20 -23 29 -11 36 0 rrcurveto
88 0 38 83 0 82 rrcurveto
0 34 -6 31 -10 26 rrcurveto
closepath
-371 -348 rmoveto
60 68 rlineto
-44 48 -22 63 0 78 rrcurveto
0 165 105 108 162 0 rrcurveto
63 0 54 -16 46 -32 rrcurveto
53 61 rlineto
37 -32 rlineto
-54 -61 rlineto
44 -47 25 -68 0 -78 rrcurveto
0 -164 -106 -108 -162 0 rrcurveto
-65 0 -55 16 -45 34 rrcurveto
-59 -67 rlineto
-37 32 rlineto
closepath
endchar } ND
/ugrave { 
0 593 hsbw
98 483 hstem
196 201 vstem
401 3 vstem
273 581 rmoveto
-154 142 rlineto
157 0 rlineto
96 -142 rlineto
-99 0 rlineto
closepath
266 -64 rmoveto
0 -517 rlineto
-135 0 rlineto
0 72 rlineto
-3 0 rlineto
-35 -56 -61 -30 -62 0 rrcurveto
-130 0 -59 89 0 124 rrcurveto
0 318 rlineto
142 0 rlineto
0 -292 rlineto
0 -87 27 -40 64 0 rrcurveto
77 0 33 42 0 106 rrcurveto
0 271 rlineto
142 0 rlineto
closepath
endchar } ND
/uacute { 
0 593 hsbw
98 483 hstem
196 201 vstem
401 3 vstem
474 723 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
65 -206 rmoveto
0 -517 rlineto
-135 0 rlineto
0 72 rlineto
-3 0 rlineto
-35 -56 -61 -30 -62 0 rrcurveto
-130 0 -59 89 0 124 rrcurveto
0 318 rlineto
142 0 rlineto
0 -292 rlineto
0 -87 27 -40 64 0 rrcurveto
77 0 33 42 0 106 rrcurveto
0 271 rlineto
142 0 rlineto
closepath
endchar } ND
/ucircumflex { 
0 593 hsbw
517 64 hstem
196 201 vstem
401 3 vstem
121 581 rmoveto
111 142 rlineto
130 0 rlineto
110 -142 rlineto
-117 0 rlineto
-62 81 rlineto
-64 -81 rlineto
-108 0 rlineto
closepath
418 -64 rmoveto
0 -517 rlineto
-135 0 rlineto
0 72 rlineto
-3 0 rlineto
-35 -56 -61 -30 -62 0 rrcurveto
-130 0 -59 89 0 124 rrcurveto
0 318 rlineto
142 0 rlineto
0 -292 rlineto
0 -87 27 -40 64 0 rrcurveto
77 0 33 42 0 106 rrcurveto
0 271 rlineto
142 0 rlineto
closepath
endchar } ND
/udieresis { 
0 593 hsbw
517 80 hstem
261 71 vstem
401 3 vstem
332 597 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
413 -80 rmoveto
0 -517 rlineto
-135 0 rlineto
0 72 rlineto
-3 0 rlineto
-35 -56 -61 -30 -62 0 rrcurveto
-130 0 -59 89 0 124 rrcurveto
0 318 rlineto
142 0 rlineto
0 -292 rlineto
0 -87 27 -40 64 0 rrcurveto
77 0 33 42 0 106 rrcurveto
0 271 rlineto
142 0 rlineto
closepath
endchar } ND
/yacute { 
0 519 hsbw
163 418 hstem
263 2 vstem
437 723 rmoveto
-154 -142 rlineto
-99 0 rlineto
96 142 rlineto
157 0 rlineto
closepath
88 -206 rmoveto
-216 -582 rlineto
-31 -84 -50 -33 -97 0 rrcurveto
-26 0 -29 2 -32 3 rrcurveto
0 115 rlineto
18 0 rlineto
24 -2 23 -1 18 0 rrcurveto
34 0 21 32 0 33 rrcurveto
0 11 -2 11 -4 10 rrcurveto
-182 485 rlineto
152 0 rlineto
117 -354 rlineto
2 0 rlineto
113 354 rlineto
147 0 rlineto
closepath
endchar } ND
/thorn { 
0 611 hsbw
93 331 hstem
191 247 vstem
54 -182 rmoveto
0 896 rlineto
142 0 rlineto
0 -263 rlineto
2 0 rlineto
33 53 55 27 59 0 rrcurveto
150 0 85 -137 0 -141 rrcurveto
0 -138 -86 -129 -141 0 rrcurveto
-63 0 -59 27 -33 51 rrcurveto
-2 0 rlineto
0 -246 rlineto
-142 0 rlineto
closepath
384 439 rmoveto
0 79 -37 88 -87 0 rrcurveto
-87 0 -36 -86 0 -81 rrcurveto
0 -82 38 -82 86 0 rrcurveto
85 0 38 81 0 83 rrcurveto
closepath
endchar } ND
/ydieresis { 
0 519 hsbw
517 80 hstem
224 71 vstem
295 597 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
-206 0 rmoveto
0 117 rlineto
135 0 rlineto
0 -117 rlineto
-135 0 rlineto
closepath
436 -80 rmoveto
-216 -582 rlineto
-31 -84 -50 -33 -97 0 rrcurveto
-26 0 -29 2 -32 3 rrcurveto
0 115 rlineto
18 0 rlineto
24 -2 23 -1 18 0 rrcurveto
34 0 21 32 0 33 rrcurveto
0 11 -2 11 -4 10 rrcurveto
-182 485 rlineto
152 0 rlineto
117 -354 rlineto
2 0 rlineto
113 354 rlineto
147 0 rlineto
closepath
endchar } ND
end
end
readonly put
noaccess put
dup/FontName get exch definefont pop
mark currentfile closefile
cleartomark
