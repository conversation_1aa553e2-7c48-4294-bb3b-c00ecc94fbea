%!PS-AdobeFont-1.0: Helvetica95-Black Copyright [c] 1988, 1990, 1993 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.
%%CreationDate: Wed Sep 27 11:50:15 2006
% Converted from TrueType font 'HelveticaBlk___-.ttf' by ttf2pt1 program
%
%%EndComments
12 dict begin
/FontInfo 9 dict dup begin
/version (Version 1.00) readonly def
/Notice (Copyright [c] 1988, 1990, 1993 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.) readonly def
/FullName (Helvetica95-Black) readonly def
/FamilyName (Helvetica95-Black) readonly def
/Weight (Black) readonly def
/ItalicAngle 0.000000 def
/isFixedPitch false def
/UnderlinePosition -75 def
/UnderlineThickness 50 def
end readonly def
/FontName /Helvetica95-Black def
/PaintType 0 def
/StrokeWidth 0 def
/FontType 1 def
/FontMatrix [0.0010000 0 0 0.0010000 0 0] def
/FontBBox {-66 -232 1101 953} readonly def
/Encoding 256 array
dup 0 /.notdef put
dup 1 /.notdef put
dup 2 /.notdef put
dup 3 /.notdef put
dup 4 /.notdef put
dup 5 /.notdef put
dup 6 /.notdef put
dup 7 /.notdef put
dup 8 /.notdef put
dup 9 /.notdef put
dup 10 /.notdef put
dup 11 /.notdef put
dup 12 /.notdef put
dup 13 /.notdef put
dup 14 /.notdef put
dup 15 /.notdef put
dup 16 /.notdef put
dup 17 /.notdef put
dup 18 /.notdef put
dup 19 /.notdef put
dup 20 /.notdef put
dup 21 /.notdef put
dup 22 /.notdef put
dup 23 /.notdef put
dup 24 /.notdef put
dup 25 /.notdef put
dup 26 /.notdef put
dup 27 /.notdef put
dup 28 /.notdef put
dup 29 /.notdef put
dup 30 /.notdef put
dup 31 /.notdef put
dup 32 /space put
dup 33 /exclam put
dup 34 /quotedbl put
dup 35 /numbersign put
dup 36 /dollar put
dup 37 /percent put
dup 38 /ampersand put
dup 39 /quotesingle put
dup 40 /parenleft put
dup 41 /parenright put
dup 42 /asterisk put
dup 43 /plus put
dup 44 /comma put
dup 45 /hyphen put
dup 46 /period put
dup 47 /slash put
dup 48 /zero put
dup 49 /one put
dup 50 /two put
dup 51 /three put
dup 52 /four put
dup 53 /five put
dup 54 /six put
dup 55 /seven put
dup 56 /eight put
dup 57 /nine put
dup 58 /colon put
dup 59 /semicolon put
dup 60 /less put
dup 61 /equal put
dup 62 /greater put
dup 63 /question put
dup 64 /at put
dup 65 /A put
dup 66 /B put
dup 67 /C put
dup 68 /D put
dup 69 /E put
dup 70 /F put
dup 71 /G put
dup 72 /H put
dup 73 /I put
dup 74 /J put
dup 75 /K put
dup 76 /L put
dup 77 /M put
dup 78 /N put
dup 79 /O put
dup 80 /P put
dup 81 /Q put
dup 82 /R put
dup 83 /S put
dup 84 /T put
dup 85 /U put
dup 86 /V put
dup 87 /W put
dup 88 /X put
dup 89 /Y put
dup 90 /Z put
dup 91 /bracketleft put
dup 92 /backslash put
dup 93 /bracketright put
dup 94 /asciicircum put
dup 95 /underscore put
dup 96 /grave put
dup 97 /a put
dup 98 /b put
dup 99 /c put
dup 100 /d put
dup 101 /e put
dup 102 /f put
dup 103 /g put
dup 104 /h put
dup 105 /i put
dup 106 /j put
dup 107 /k put
dup 108 /l put
dup 109 /m put
dup 110 /n put
dup 111 /o put
dup 112 /p put
dup 113 /q put
dup 114 /r put
dup 115 /s put
dup 116 /t put
dup 117 /u put
dup 118 /v put
dup 119 /w put
dup 120 /x put
dup 121 /y put
dup 122 /z put
dup 123 /braceleft put
dup 124 /bar put
dup 125 /braceright put
dup 126 /asciitilde put
dup 127 /.notdef put
dup 128 /.notdef put
dup 129 /.notdef put
dup 130 /quotesinglbase put
dup 131 /florin put
dup 132 /quotedblbase put
dup 133 /ellipsis put
dup 134 /dagger put
dup 135 /daggerdbl put
dup 136 /circumflex put
dup 137 /perthousand put
dup 138 /Scaron put
dup 139 /guilsinglleft put
dup 140 /OE put
dup 141 /.notdef put
dup 142 /.notdef put
dup 143 /.notdef put
dup 144 /.notdef put
dup 145 /quoteleft put
dup 146 /quoteright put
dup 147 /quotedblleft put
dup 148 /quotedblright put
dup 149 /bullet put
dup 150 /endash put
dup 151 /emdash put
dup 152 /tilde put
dup 153 /trademark put
dup 154 /scaron put
dup 155 /guilsinglright put
dup 156 /oe put
dup 157 /.notdef put
dup 158 /.notdef put
dup 159 /Ydieresis put
dup 160 /.notdef put
dup 161 /exclamdown put
dup 162 /cent put
dup 163 /sterling put
dup 164 /currency put
dup 165 /yen put
dup 166 /brokenbar put
dup 167 /section put
dup 168 /dieresis put
dup 169 /copyright put
dup 170 /ordfeminine put
dup 171 /guillemotleft put
dup 172 /logicalnot put
dup 173 /minus put
dup 174 /registered put
dup 175 /macron put
dup 176 /degree put
dup 177 /plusminus put
dup 178 /twosuperior put
dup 179 /threesuperior put
dup 180 /acute put
dup 181 /mu put
dup 182 /paragraph put
dup 183 /periodcentered put
dup 184 /cedilla put
dup 185 /onesuperior put
dup 186 /ordmasculine put
dup 187 /guillemotright put
dup 188 /onequarter put
dup 189 /onehalf put
dup 190 /threequarters put
dup 191 /questiondown put
dup 192 /Agrave put
dup 193 /Aacute put
dup 194 /Acircumflex put
dup 195 /Atilde put
dup 196 /Adieresis put
dup 197 /Aring put
dup 198 /AE put
dup 199 /Ccedilla put
dup 200 /Egrave put
dup 201 /Eacute put
dup 202 /Ecircumflex put
dup 203 /Edieresis put
dup 204 /Igrave put
dup 205 /Iacute put
dup 206 /Icircumflex put
dup 207 /Idieresis put
dup 208 /Eth put
dup 209 /Ntilde put
dup 210 /Ograve put
dup 211 /Oacute put
dup 212 /Ocircumflex put
dup 213 /Otilde put
dup 214 /Odieresis put
dup 215 /multiply put
dup 216 /Oslash put
dup 217 /Ugrave put
dup 218 /Uacute put
dup 219 /Ucircumflex put
dup 220 /Udieresis put
dup 221 /Yacute put
dup 222 /Thorn put
dup 223 /germandbls put
dup 224 /agrave put
dup 225 /aacute put
dup 226 /acircumflex put
dup 227 /atilde put
dup 228 /adieresis put
dup 229 /aring put
dup 230 /ae put
dup 231 /ccedilla put
dup 232 /egrave put
dup 233 /eacute put
dup 234 /ecircumflex put
dup 235 /edieresis put
dup 236 /igrave put
dup 237 /iacute put
dup 238 /icircumflex put
dup 239 /idieresis put
dup 240 /eth put
dup 241 /ntilde put
dup 242 /ograve put
dup 243 /oacute put
dup 244 /ocircumflex put
dup 245 /otilde put
dup 246 /odieresis put
dup 247 /divide put
dup 248 /oslash put
dup 249 /ugrave put
dup 250 /uacute put
dup 251 /ucircumflex put
dup 252 /udieresis put
dup 253 /yacute put
dup 254 /thorn put
dup 255 /ydieresis put
readonly def
currentdict end
currentfile eexec
dup /Private 16 dict dup begin
/RD{string currentfile exch readstring pop}executeonly def
/ND{noaccess def}executeonly def
/NP{noaccess put}executeonly def
/ForceBold false def
/BlueValues [ -17 0 714 731 513 532 689 704 899 913 ] def
/OtherBlues [ -181 -166 ] def
/StdHW [ 51 ] def
/StdVW [ 58 ] def
/StemSnapH [ 34 38 43 51 56 65 73 89 221 258 312 393 ] def
/StemSnapV [ 58 61 83 91 133 136 145 171 175 190 212 285 ] def
/MinFeature {16 16} def
/password 5839 def
/Subrs 5 array
dup 0 {
	3 0 callothersubr pop pop setcurrentpoint return
	} NP
dup 1 {
	0 1 callothersubr return
	} NP
dup 2 {
	0 2 callothersubr return
	} NP
dup 3 {
	return
	} NP
dup 4 {
	1 3 callothersubr pop callsubr return
	} NP
ND
2 index /CharStrings 215 dict dup begin
/.notdef { 
0 502 hsbw
10 480 hstem
9 480 vstem
-1 0 rmoveto
0 500 rlineto
500 0 rlineto
0 -500 rlineto
-500 0 rlineto
closepath
10 10 rmoveto
480 0 rlineto
0 480 rlineto
-480 0 rlineto
0 -480 rlineto
closepath
endchar } ND
/space { 
0 334 hsbw
endchar } ND
/exclam { 
0 315 hsbw
198 59 hstem
53 0 rmoveto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
-208 0 rlineto
closepath
208 714 rmoveto
0 -168 rlineto
-55 -289 rlineto
-97 0 rlineto
-56 289 rlineto
0 168 rlineto
208 0 rlineto
closepath
endchar } ND
/quotedbl { 
0 519 hsbw
230 60 vstem
290 335 rmoveto
0 379 rlineto
153 0 rlineto
0 -379 rlineto
-153 0 rlineto
closepath
-213 0 rmoveto
0 379 rlineto
153 0 rlineto
0 -379 rlineto
-153 0 rlineto
closepath
endchar } ND
/numbersign { 
0 668 hsbw
302 96 hstem
254 71 vstem
350 71 vstem
380 398 rmoveto
-71 0 rlineto
-13 -96 rlineto
71 0 rlineto
13 96 rlineto
closepath
-259 -398 rmoveto
26 185 rlineto
-86 0 rlineto
16 117 rlineto
86 0 rlineto
13 96 rlineto
-85 0 rlineto
16 117 rlineto
85 0 rlineto
25 181 rlineto
133 0 rlineto
-25 -181 rlineto
71 0 rlineto
25 181 rlineto
133 0 rlineto
-25 -181 rlineto
78 0 rlineto
-16 -117 rlineto
-78 0 rlineto
-13 -96 rlineto
77 0 rlineto
-16 -117 rlineto
-77 0 rlineto
-26 -185 rlineto
-133 0 rlineto
26 185 rlineto
-71 0 rlineto
-26 -185 rlineto
-133 0 rlineto
closepath
endchar } ND
/dollar { 
0 668 hsbw
141 127 hstem
473 100 hstem
240 71 vstem
363 83 vstem
311 473 rmoveto
0 100 rlineto
-44 0 -27 -10 0 -36 rrcurveto
0 -27 23 -18 48 -9 rrcurveto
closepath
52 -205 rmoveto
0 -127 rlineto
50 4 33 19 0 38 rrcurveto
0 32 -25 15 -58 19 rrcurveto
closepath
-52 -384 rmoveto
0 99 rlineto
-167 0 -126 106 -4 149 rrcurveto
214 0 rlineto
0 -54 28 -40 55 -3 rrcurveto
0 141 rlineto
-6 1 -10 3 -15 5 rrcurveto
-12 3 -10 3 -10 3 rrcurveto
-9 3 -7 2 -7 2 rrcurveto
-107 32 -76 49 0 109 rrcurveto
0 145 136 89 133 0 rrcurveto
0 80 rlineto
52 0 rlineto
0 -80 rlineto
139 0 125 -87 0 -146 rrcurveto
-191 0 rlineto
-6 50 -15 14 -52 11 rrcurveto
0 -112 rlineto
166 -37 125 -50 0 -160 rrcurveto
0 -140 -135 -91 -156 0 rrcurveto
0 -99 rlineto
-52 0 rlineto
closepath
endchar } ND
/percent { 
0 1019 hsbw
79 206 hstem
411 206 hstem
198 90 vstem
732 90 vstem
245 -26 rmoveto
410 748 rlineto
110 0 rlineto
-410 -748 rlineto
-110 0 rlineto
closepath
-178 540 rmoveto
0 117 58 73 118 0 rrcurveto
118 0 58 -73 0 -117 rrcurveto
0 -118 -58 -72 -118 0 rrcurveto
-118 0 -58 72 0 118 rrcurveto
closepath
130 34 rmoveto
0 -74 rlineto
0 -28 13 -35 33 0 rrcurveto
30 0 15 36 0 27 rrcurveto
0 79 rlineto
0 27 -14 37 -31 0 rrcurveto
-34 0 -11 -36 0 -28 rrcurveto
-1 -5 rlineto
closepath
404 -366 rmoveto
0 117 58 73 118 0 rrcurveto
118 0 58 -73 0 -117 rrcurveto
0 -118 -58 -72 -118 0 rrcurveto
-118 0 -58 72 0 118 rrcurveto
closepath
130 34 rmoveto
0 -74 rlineto
0 -28 13 -35 33 0 rrcurveto
30 0 15 36 0 27 rrcurveto
0 79 rlineto
0 27 -14 37 -31 0 rrcurveto
-34 0 -11 -36 0 -28 rrcurveto
-1 -5 rlineto
closepath
endchar } ND
/ampersand { 
0 741 hsbw
141 144 hstem
476 122 hstem
309 101 vstem
309 549 rmoveto
0 -20 14 -20 24 -26 rrcurveto
6 -7 rlineto
30 14 27 29 0 31 rrcurveto
0 26 -20 22 -28 0 rrcurveto
-27 0 -26 -20 0 -29 rrcurveto
closepath
69 -365 rmoveto
-83 101 rlineto
-39 -20 -30 -25 0 -32 rrcurveto
0 -38 33 -29 35 0 rrcurveto
29 0 25 16 30 27 rrcurveto
closepath
145 -184 rmoveto
-40 50 rlineto
-63 -50 -59 -17 -82 0 rrcurveto
-131 0 -112 100 0 132 rrcurveto
0 86 67 68 92 47 rrcurveto
-35 38 -21 44 0 48 rrcurveto
0 108 107 77 105 0 rrcurveto
138 0 91 -63 0 -122 rrcurveto
0 -76 -57 -68 -74 -40 rrcurveto
55 -68 rlineto
14 18 9 18 3 18 rrcurveto
165 0 rlineto
-11 -64 -29 -63 -44 -46 rrcurveto
146 -175 rlineto
-234 0 rlineto
closepath
endchar } ND
/quotesingle { 
0 296 hsbw
72 335 rmoveto
0 379 rlineto
153 0 rlineto
0 -379 rlineto
-153 0 rlineto
closepath
endchar } ND
/parenleft { 
0 315 hsbw
150 731 rmoveto
171 0 rlineto
-64 -156 -25 -140 0 -157 rrcurveto
0 -154 25 -130 64 -162 rrcurveto
-171 0 rlineto
-75 154 -33 134 0 158 rrcurveto
0 160 36 151 72 142 rrcurveto
closepath
endchar } ND
/parenright { 
0 315 hsbw
167 -168 rmoveto
-173 0 rlineto
61 139 28 144 0 163 rrcurveto
0 164 -25 125 -64 164 rrcurveto
173 0 rlineto
70 -142 36 -154 0 -157 rrcurveto
0 -164 -32 -120 -74 -162 rrcurveto
closepath
endchar } ND
/asterisk { 
0 463 hsbw
288 714 rmoveto
0 -132 rlineto
121 43 rlineto
38 -105 rlineto
-128 -41 rlineto
80 -105 rlineto
-90 -71 rlineto
-79 114 rlineto
-80 -114 rlineto
-90 71 rlineto
85 105 rlineto
-128 41 rlineto
36 105 rlineto
125 -43 rlineto
0 132 rlineto
110 0 rlineto
closepath
endchar } ND
/plus { 
0 600 hsbw
48 176 rmoveto
0 153 rlineto
176 0 rlineto
0 176 rlineto
153 0 rlineto
0 -176 rlineto
176 0 rlineto
0 -153 rlineto
-176 0 rlineto
0 -176 rlineto
-153 0 rlineto
0 176 rlineto
-176 0 rlineto
closepath
endchar } ND
/comma { 
0 334 hsbw
-94 94 hstem
63 0 rmoveto
0 198 rlineto
208 0 rlineto
0 -157 rlineto
0 -136 -92 -86 -116 0 rrcurveto
0 87 rlineto
48 4 38 41 3 49 rrcurveto
-89 0 rlineto
closepath
endchar } ND
/hyphen { 
0 407 hsbw
47 185 rmoveto
0 164 rlineto
313 0 rlineto
0 -164 rlineto
-313 0 rlineto
closepath
endchar } ND
/period { 
0 334 hsbw
63 0 rmoveto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
-208 0 rlineto
closepath
endchar } ND
/slash { 
0 426 hsbw
-1 -17 rmoveto
280 748 rlineto
152 0 rlineto
-280 -748 rlineto
-152 0 rlineto
closepath
endchar } ND
/zero { 
0 668 hsbw
145 406 hstem
228 212 vstem
228 348 rmoveto
0 -95 6 -108 99 0 rrcurveto
97 0 10 115 0 88 rrcurveto
0 89 -10 114 -95 0 rrcurveto
-97 0 -10 -107 0 -96 rrcurveto
closepath
-190 0 rmoveto
0 197 127 159 168 0 rrcurveto
167 0 130 -158 0 -198 rrcurveto
0 -198 -127 -158 -169 0 rrcurveto
-168 0 -128 159 0 197 rrcurveto
closepath
endchar } ND
/one { 
0 668 hsbw
279 0 rmoveto
0 401 rlineto
-163 0 rlineto
0 144 rlineto
102 0 98 42 7 109 rrcurveto
164 0 rlineto
0 -696 rlineto
-208 0 rlineto
closepath
endchar } ND
/two { 
0 668 hsbw
170 376 hstem
237 189 vstem
237 403 rmoveto
-188 0 rlineto
0 24 rlineto
0 174 113 103 182 0 rrcurveto
174 0 110 -75 0 -144 rrcurveto
0 -139 -100 -66 -132 -67 rrcurveto
-18 -9 -16 -8 -11 -8 rrcurveto
-11 -8 -9 -6 -8 -3 rrcurveto
0 -1 rlineto
315 0 rlineto
0 -170 rlineto
-603 0 rlineto
0 17 rlineto
0 147 92 74 171 102 rrcurveto
34 20 rlineto
12 7 rlineto
50 30 32 24 0 40 rrcurveto
0 58 -34 27 -50 0 rrcurveto
-75 0 -30 -50 0 -93 rrcurveto
closepath
endchar } ND
/three { 
0 668 hsbw
150 145 hstem
430 121 hstem
242 187 vstem
242 464 rmoveto
-188 0 rlineto
1 161 134 79 155 0 rrcurveto
132 0 137 -80 0 -99 rrcurveto
0 -75 -32 -59 -56 -14 rrcurveto
-1 0 -2 0 0 -1 rrcurveto
-1 0 rlineto
0 -2 rlineto
0 -1 4 -2 6 -2 rrcurveto
73 -24 31 -55 0 -79 rrcurveto
0 -131 -135 -88 -157 0 rrcurveto
-181 0 -129 111 0 138 rrcurveto
0 23 rlineto
191 0 rlineto
0 -77 36 -37 74 0 rrcurveto
60 0 33 23 0 55 rrcurveto
0 56 -37 14 -56 0 rrcurveto
-25 0 rlineto
-8 -2 -8 -1 -9 0 rrcurveto
-8 0 rlineto
0 135 rlineto
15 0 rlineto
14 -2 16 -1 13 0 rrcurveto
47 0 48 16 0 47 rrcurveto
0 40 -43 21 -48 0 rrcurveto
-56 0 -40 -30 0 -57 rrcurveto
closepath
endchar } ND
/four { 
0 668 hsbw
308 195 hstem
169 170 vstem
339 503 rmoveto
-170 -195 rlineto
170 0 rlineto
0 195 rlineto
closepath
0 -503 rmoveto
0 138 rlineto
-309 0 rlineto
0 188 rlineto
318 370 rlineto
199 0 rlineto
0 -388 rlineto
94 0 rlineto
0 -170 rlineto
-94 0 rlineto
0 -138 rlineto
-208 0 rlineto
closepath
endchar } ND
/five { 
0 668 hsbw
219 76 hstem
473 53 hstem
225 201 vstem
589 696 rmoveto
0 -170 rlineto
-324 0 rlineto
-20 -92 rlineto
2 -2 rlineto
40 29 53 12 59 0 rrcurveto
130 0 97 -96 0 -140 rrcurveto
0 -146 -116 -99 -196 0 rrcurveto
-162 0 -137 92 0 123 rrcurveto
0 3 rlineto
1 0 0 2 0 2 rrcurveto
0 5 rlineto
197 0 rlineto
9 -50 43 -19 55 0 rrcurveto
60 0 46 39 0 54 rrcurveto
0 60 -39 37 -61 0 rrcurveto
-48 0 -33 -15 -20 -30 rrcurveto
-186 0 rlineto
72 401 rlineto
478 0 rlineto
closepath
endchar } ND
/six { 
0 668 hsbw
150 188 hstem
471 39 hstem
245 197 vstem
340 338 rmoveto
-54 0 -41 -40 0 -52 rrcurveto
0 -55 40 -41 55 0 rrcurveto
58 0 44 42 0 54 rrcurveto
0 50 -42 42 -60 0 rrcurveto
closepath
279 172 rmoveto
-183 0 rlineto
-11 29 -29 21 -43 0 rrcurveto
-71 0 -42 -66 -8 -77 rrcurveto
44 41 49 13 68 0 rrcurveto
136 0 97 -88 0 -134 rrcurveto
0 -166 -114 -91 -172 0 rrcurveto
-187 0 -110 155 0 176 rrcurveto
0 199 110 182 204 0 rrcurveto
153 0 87 -61 22 -133 rrcurveto
closepath
endchar } ND
/seven { 
0 668 hsbw
506 20 hstem
601 696 rmoveto
0 -162 rlineto
-153 -142 -57 -197 0 -195 rrcurveto
-221 0 rlineto
0 88 52 209 90 120 rrcurveto
9 12 7 10 6 9 rrcurveto
6 10 6 9 6 6 rrcurveto
4 8 4 6 6 5 rrcurveto
4 5 4 5 3 4 rrcurveto
6 7 5 5 3 3 rrcurveto
3 2 2 2 1 1 rrcurveto
-333 0 rlineto
0 170 rlineto
537 0 rlineto
closepath
endchar } ND
/eight { 
0 668 hsbw
136 167 hstem
365 2 hstem
420 151 hstem
233 204 vstem
222 213 rmoveto
0 -55 51 -22 60 0 rrcurveto
50 0 62 23 0 54 rrcurveto
0 62 -45 28 -65 0 rrcurveto
-67 0 -46 -26 0 -64 rrcurveto
closepath
-167 300 rmoveto
0 140 153 51 125 0 rrcurveto
181 0 101 -83 0 -108 rrcurveto
0 -72 -44 -60 -65 -14 rrcurveto
0 -2 rlineto
78 -8 59 -61 0 -84 rrcurveto
0 -159 -168 -61 -142 0 rrcurveto
-148 0 -161 59 0 161 rrcurveto
0 89 64 51 75 13 rrcurveto
0 2 rlineto
-74 20 -34 50 0 76 rrcurveto
closepath
178 -20 rmoveto
0 -51 44 -22 55 0 rrcurveto
58 0 47 21 0 52 rrcurveto
0 60 -42 18 -62 0 rrcurveto
-60 0 -40 -20 0 -58 rrcurveto
closepath
endchar } ND
/nine { 
0 668 hsbw
183 40 hstem
356 190 hstem
225 200 vstem
327 546 rmoveto
-55 0 -47 -42 0 -54 rrcurveto
0 -52 46 -42 54 0 rrcurveto
56 0 44 38 0 55 rrcurveto
0 54 -44 43 -54 0 rrcurveto
closepath
-279 -363 rmoveto
183 0 rlineto
7 -32 35 -15 37 0 rrcurveto
76 0 34 68 15 72 rrcurveto
-2 2 rlineto
-43 -37 -52 -18 -61 0 rrcurveto
-140 0 -96 82 0 139 rrcurveto
0 158 141 102 152 0 rrcurveto
182 0 111 -159 0 -172 rrcurveto
0 -202 -112 -179 -205 0 rrcurveto
-140 0 -102 58 -20 133 rrcurveto
closepath
endchar } ND
/colon { 
0 334 hsbw
198 123 hstem
63 321 rmoveto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
-208 0 rlineto
closepath
0 -321 rmoveto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
-208 0 rlineto
closepath
endchar } ND
/semicolon { 
0 334 hsbw
-94 94 hstem
198 123 hstem
63 0 rmoveto
0 198 rlineto
208 0 rlineto
0 -157 rlineto
0 -136 -92 -86 -116 0 rrcurveto
0 87 rlineto
49 4 37 42 3 48 rrcurveto
-89 0 rlineto
closepath
208 519 rmoveto
0 -198 rlineto
-208 0 rlineto
0 198 rlineto
208 0 rlineto
closepath
endchar } ND
/less { 
0 600 hsbw
153 199 hstem
553 513 rmoveto
0 -161 rlineto
-272 -99 rlineto
272 -100 rlineto
0 -161 rlineto
-505 197 rlineto
0 126 rlineto
505 198 rlineto
closepath
endchar } ND
/equal { 
0 600 hsbw
209 88 hstem
553 450 rmoveto
0 -153 rlineto
-505 0 rlineto
0 153 rlineto
505 0 rlineto
closepath
0 -241 rmoveto
0 -153 rlineto
-505 0 rlineto
0 153 rlineto
505 0 rlineto
closepath
endchar } ND
/greater { 
0 600 hsbw
153 199 hstem
48 -8 rmoveto
0 161 rlineto
272 99 rlineto
-272 100 rlineto
0 161 rlineto
505 -197 rlineto
0 -126 rlineto
-505 -198 rlineto
closepath
endchar } ND
/question { 
0 574 hsbw
198 58 hstem
224 121 vstem
224 476 rmoveto
-209 0 rlineto
8 143 113 112 157 0 rrcurveto
140 0 126 -86 0 -120 rrcurveto
0 -83 -33 -44 -80 -40 rrcurveto
-44 -22 -35 -23 0 -57 rrcurveto
-185 0 rlineto
0 29 rlineto
0 64 9 56 84 38 rrcurveto
44 20 26 15 0 32 rrcurveto
0 34 -20 17 -38 0 rrcurveto
-42 0 -21 -32 0 -42 rrcurveto
0 -11 rlineto
closepath
-54 -476 rmoveto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
-208 0 rlineto
closepath
endchar } ND
/at { 
0 800 hsbw
86 51 hstem
259 174 hstem
555 73 hstem
127 60 vstem
320 161 vstem
622 50 vstem
320 334 rmoveto
0 -40 27 -35 39 0 rrcurveto
53 0 42 46 0 52 rrcurveto
0 44 -31 32 -41 0 rrcurveto
-48 0 -41 -46 0 -53 rrcurveto
closepath
302 203 rmoveto
-47 -239 rlineto
-3 -11 -1 -11 0 -10 rrcurveto
0 -16 4 -8 10 0 rrcurveto
42 0 45 56 0 104 rrcurveto
0 150 -102 76 -156 0 rrcurveto
-153 0 -134 -120 0 -152 rrcurveto
0 -156 133 -114 150 0 rrcurveto
93 0 59 18 52 39 rrcurveto
114 0 rlineto
-72 -107 -106 -53 -140 0 rrcurveto
-209 0 -177 162 0 211 rrcurveto
0 218 179 157 214 0 rrcurveto
186 0 172 -116 0 -182 rrcurveto
0 -166 -150 -130 -112 0 rrcurveto
-34 0 -21 16 -2 34 rrcurveto
-2 0 rlineto
-19 -31 -33 -19 -49 0 rrcurveto
-100 0 -66 78 0 103 rrcurveto
0 119 86 118 122 0 rrcurveto
53 0 41 -20 28 -39 rrcurveto
10 41 rlineto
95 0 rlineto
closepath
endchar } ND
/A { 
0 722 hsbw
255 220 hstem
293 135 vstem
293 255 rmoveto
135 0 rlineto
-64 220 rlineto
-2 0 rlineto
-69 -220 rlineto
closepath
-301 -255 rmoveto
263 714 rlineto
215 0 rlineto
260 -714 rlineto
-228 0 rlineto
-30 102 rlineto
-227 0 rlineto
-32 -102 rlineto
-221 0 rlineto
closepath
endchar } ND
/B { 
0 741 hsbw
170 133 hstem
436 108 hstem
277 191 vstem
277 303 rmoveto
0 -133 rlineto
129 0 rlineto
52 0 34 17 0 51 rrcurveto
0 43 -26 22 -51 0 rrcurveto
-138 0 rlineto
closepath
-220 -303 rmoveto
0 714 rlineto
391 0 rlineto
126 0 102 -73 0 -106 rrcurveto
0 -68 -31 -40 -57 -32 rrcurveto
73 -26 51 -55 0 -97 rrcurveto
0 -135 -124 -82 -126 0 rrcurveto
-405 0 rlineto
closepath
220 544 rmoveto
0 -108 rlineto
116 0 rlineto
49 0 26 15 0 44 rrcurveto
0 34 -27 15 -48 0 rrcurveto
-116 0 rlineto
closepath
endchar } ND
/C { 
0 759 hsbw
280 168 hstem
256 252 vstem
721 448 rmoveto
-214 0 rlineto
-2 4 -1 4 0 6 rrcurveto
-9 46 -44 45 -59 0 rrcurveto
-100 0 -36 -105 0 -91 rrcurveto
0 -95 36 -101 100 0 rrcurveto
69 0 32 44 15 75 rrcurveto
216 0 rlineto
0 -164 -151 -133 -175 0 rrcurveto
-214 0 -148 169 0 205 rrcurveto
0 210 151 164 211 0 rrcurveto
193 0 111 -102 19 -181 rrcurveto
closepath
endchar } ND
/D { 
0 778 hsbw
183 348 hstem
277 243 vstem
57 0 rmoveto
0 714 rlineto
361 0 rlineto
203 0 119 -168 0 -188 rrcurveto
0 -226 -123 -132 -242 0 rrcurveto
-318 0 rlineto
closepath
220 531 rmoveto
0 -348 rlineto
86 0 rlineto
104 0 53 89 0 89 rrcurveto
0 81 -64 89 -109 0 rrcurveto
-70 0 rlineto
closepath
endchar } ND
/E { 
0 704 hsbw
183 89 hstem
442 89 hstem
57 0 rmoveto
0 714 rlineto
591 0 rlineto
0 -183 rlineto
-371 0 rlineto
0 -89 rlineto
337 0 rlineto
0 -170 rlineto
-337 0 rlineto
0 -89 rlineto
382 0 rlineto
0 -183 rlineto
-602 0 rlineto
closepath
endchar } ND
/F { 
0 630 hsbw
442 89 hstem
57 0 rmoveto
0 714 rlineto
552 0 rlineto
0 -183 rlineto
-332 0 rlineto
0 -89 rlineto
285 0 rlineto
0 -170 rlineto
-285 0 rlineto
0 -272 rlineto
-220 0 rlineto
closepath
endchar } ND
/G { 
0 778 hsbw
161 79 hstem
393 71 hstem
256 178 vstem
587 0 rmoveto
-7 73 rlineto
-47 -60 -75 -30 -81 0 rrcurveto
-202 0 -139 171 0 196 rrcurveto
0 215 163 166 189 0 rrcurveto
180 0 117 -98 32 -169 rrcurveto
-211 0 rlineto
-8 52 -44 37 -52 0 rrcurveto
-118 0 -28 -105 0 -103 rrcurveto
0 -91 54 -93 107 0 rrcurveto
54 0 49 28 11 51 rrcurveto
-97 0 rlineto
0 153 rlineto
292 0 rlineto
0 -393 rlineto
-139 0 rlineto
closepath
endchar } ND
/H { 
0 759 hsbw
277 206 vstem
57 0 rmoveto
0 714 rlineto
220 0 rlineto
0 -248 rlineto
206 0 rlineto
0 248 rlineto
220 0 rlineto
0 -714 rlineto
-220 0 rlineto
0 283 rlineto
-206 0 rlineto
0 -283 rlineto
-220 0 rlineto
closepath
endchar } ND
/I { 
0 333 hsbw
57 0 rmoveto
0 714 rlineto
220 0 rlineto
0 -714 rlineto
-220 0 rlineto
closepath
endchar } ND
/J { 
0 611 hsbw
223 112 vstem
555 714 rmoveto
0 -457 rlineto
0 -194 -86 -80 -182 0 rrcurveto
-176 0 -86 95 0 162 rrcurveto
0 60 rlineto
198 0 rlineto
0 -85 rlineto
0 -34 22 -28 37 0 rrcurveto
50 0 3 52 0 49 rrcurveto
0 460 rlineto
220 0 rlineto
closepath
endchar } ND
/K { 
0 778 hsbw
283 2 vstem
63 0 rmoveto
0 714 rlineto
220 0 rlineto
0 -246 rlineto
2 0 rlineto
196 246 rlineto
271 0 rlineto
-259 -279 rlineto
301 -435 rlineto
-274 0 rlineto
-176 278 rlineto
-61 -67 rlineto
0 -211 rlineto
-220 0 rlineto
closepath
endchar } ND
/L { 
0 630 hsbw
57 0 rmoveto
0 714 rlineto
220 0 rlineto
0 -531 rlineto
316 0 rlineto
0 -183 rlineto
-536 0 rlineto
closepath
endchar } ND
/M { 
0 944 hsbw
264 416 vstem
56 0 rmoveto
0 714 rlineto
313 0 rlineto
102 -420 rlineto
2 0 rlineto
102 420 rlineto
313 0 rlineto
0 -714 rlineto
-208 0 rlineto
0 458 rlineto
-2 0 rlineto
-124 -458 rlineto
-164 0 rlineto
-124 458 rlineto
-2 0 rlineto
0 -458 rlineto
-208 0 rlineto
closepath
endchar } ND
/N { 
0 759 hsbw
266 227 vstem
58 0 rmoveto
0 714 rlineto
225 0 rlineto
208 -382 rlineto
2 0 rlineto
0 382 rlineto
208 0 rlineto
0 -714 rlineto
-214 0 rlineto
-219 390 rlineto
-2 0 rlineto
0 -390 rlineto
-208 0 rlineto
closepath
endchar } ND
/O { 
0 796 hsbw
161 392 hstem
256 284 vstem
36 357 rmoveto
0 205 154 169 208 0 rrcurveto
205 0 157 -168 0 -206 rrcurveto
0 -206 -156 -168 -206 0 rrcurveto
-210 0 -152 169 0 205 rrcurveto
closepath
220 0 rmoveto
0 -107 54 -89 88 0 rrcurveto
88 0 54 90 0 106 rrcurveto
0 103 -54 93 -88 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
closepath
endchar } ND
/P { 
0 704 hsbw
386 145 hstem
277 202 vstem
57 0 rmoveto
0 714 rlineto
368 0 rlineto
149 0 113 -116 0 -128 rrcurveto
0 -167 -110 -87 -170 0 rrcurveto
-130 0 rlineto
0 -216 rlineto
-220 0 rlineto
closepath
220 531 rmoveto
0 -145 rlineto
85 0 rlineto
60 0 57 11 0 65 rrcurveto
0 56 -49 13 -55 0 rrcurveto
-98 0 rlineto
closepath
endchar } ND
/Q { 
0 796 hsbw
323 230 hstem
256 284 vstem
367 227 rmoveto
99 96 rlineto
60 -61 rlineto
9 26 5 31 0 38 rrcurveto
0 103 -54 93 -88 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
0 -107 54 -89 88 0 rrcurveto
9 0 10 1 10 3 rrcurveto
-60 62 rlineto
closepath
395 -207 rmoveto
-99 -98 rlineto
-96 99 rlineto
-39 -24 -62 -14 -68 0 rrcurveto
-210 0 -152 169 0 205 rrcurveto
0 205 154 169 208 0 rrcurveto
205 0 157 -168 0 -206 rrcurveto
0 -98 -30 -86 -53 -66 rrcurveto
85 -87 rlineto
closepath
endchar } ND
/R { 
0 741 hsbw
402 142 hstem
277 209 vstem
57 0 rmoveto
0 714 rlineto
419 0 rlineto
132 0 101 -75 0 -129 rrcurveto
0 -79 -38 -75 -69 -25 rrcurveto
60 -23 47 -65 0 -90 rrcurveto
0 -24 rlineto
0 -62 7 -43 15 -24 rrcurveto
-220 0 rlineto
-8 27 -10 37 0 46 rrcurveto
0 1 rlineto
0 11 -1 10 -2 10 rrcurveto
-2 8 -2 8 0 8 rrcurveto
-6 44 -33 39 -53 0 rrcurveto
-117 0 rlineto
0 -249 rlineto
-220 0 rlineto
closepath
220 544 rmoveto
0 -142 rlineto
115 0 rlineto
46 0 51 16 0 55 rrcurveto
0 48 -29 23 -80 0 rrcurveto
-103 0 rlineto
closepath
endchar } ND
/S { 
0 667 hsbw
141 432 hstem
248 170 vstem
12 240 rmoveto
220 0 rlineto
0 -65 45 -34 68 0 rrcurveto
41 0 50 18 0 47 rrcurveto
0 37 -36 10 -40 12 rrcurveto
-8 4 -12 3 -14 3 rrcurveto
-14 3 -14 4 -16 6 rrcurveto
-12 3 rlineto
-135 37 -95 40 0 127 rrcurveto
0 151 144 85 144 0 rrcurveto
148 0 143 -74 7 -159 rrcurveto
-208 0 rlineto
0 10 rlineto
0 41 -47 24 -44 0 rrcurveto
-40 0 -39 -12 0 -36 rrcurveto
0 -45 121 -28 61 -11 rrcurveto
144 -26 82 -61 0 -125 rrcurveto
0 -174 -170 -72 -160 0 rrcurveto
-135 0 -179 80 0 177 rrcurveto
closepath
endchar } ND
/T { 
0 668 hsbw
511 20 hstem
224 0 rmoveto
0 531 rlineto
-200 0 rlineto
0 183 rlineto
620 0 rlineto
0 -183 rlineto
-200 0 rlineto
0 -531 rlineto
-220 0 rlineto
closepath
endchar } ND
/U { 
0 759 hsbw
274 211 vstem
705 714 rmoveto
0 -438 rlineto
0 -196 -109 -97 -216 0 rrcurveto
-218 0 -108 97 0 196 rrcurveto
0 438 rlineto
220 0 rlineto
0 -389 rlineto
0 -94 15 -70 92 0 rrcurveto
87 0 17 72 0 92 rrcurveto
0 389 rlineto
220 0 rlineto
closepath
endchar } ND
/V { 
0 648 hsbw
322 2 vstem
665 714 rmoveto
-223 -714 rlineto
-246 0 rlineto
-213 714 rlineto
222 0 rlineto
117 -433 rlineto
2 0 rlineto
115 433 rlineto
226 0 rlineto
closepath
endchar } ND
/W { 
0 963 hsbw
296 2 vstem
477 2 vstem
662 2 vstem
970 714 rmoveto
-200 -714 rlineto
-214 0 rlineto
-77 435 rlineto
-2 0 rlineto
-75 -435 rlineto
-215 0 rlineto
-194 714 rlineto
216 0 rlineto
87 -441 rlineto
2 0 rlineto
86 441 rlineto
194 0 rlineto
84 -447 rlineto
2 0 rlineto
89 447 rlineto
217 0 rlineto
closepath
endchar } ND
/X { 
0 741 hsbw
269 214 vstem
-12 0 rmoveto
258 380 rlineto
-235 334 rlineto
258 0 rlineto
108 -194 rlineto
106 194 rlineto
244 0 rlineto
-229 -336 rlineto
255 -378 rlineto
-265 0 rlineto
-122 213 rlineto
-126 -213 rlineto
-252 0 rlineto
closepath
endchar } ND
/Y { 
0 667 hsbw
213 243 vstem
221 0 rmoveto
0 263 rlineto
-251 451 rlineto
243 0 rlineto
119 -263 rlineto
124 263 rlineto
241 0 rlineto
-256 -451 rlineto
0 -263 rlineto
-220 0 rlineto
closepath
endchar } ND
/Z { 
0 685 hsbw
183 348 hstem
24 0 rmoveto
0 172 rlineto
332 359 rlineto
-313 0 rlineto
0 183 rlineto
603 0 rlineto
0 -160 rlineto
-335 -371 rlineto
351 0 rlineto
0 -183 rlineto
-638 0 rlineto
closepath
endchar } ND
/bracketleft { 
0 407 hsbw
-15 20 hstem
38 -168 rmoveto
0 899 rlineto
332 0 rlineto
0 -153 rlineto
-154 0 rlineto
0 -593 rlineto
154 0 rlineto
0 -153 rlineto
-332 0 rlineto
closepath
endchar } ND
/backslash { 
0 426 hsbw
147 731 rmoveto
280 -748 rlineto
-152 0 rlineto
-280 748 rlineto
152 0 rlineto
closepath
endchar } ND
/bracketright { 
0 407 hsbw
-15 20 hstem
370 731 rmoveto
0 -899 rlineto
-332 0 rlineto
0 153 rlineto
154 0 rlineto
0 593 rlineto
-154 0 rlineto
0 153 rlineto
332 0 rlineto
closepath
endchar } ND
/asciicircum { 
0 600 hsbw
500 20 hstem
218 164 vstem
65 331 rmoveto
174 365 rlineto
122 0 rlineto
174 -365 rlineto
-153 0 rlineto
-82 189 rlineto
-82 -189 rlineto
-153 0 rlineto
closepath
endchar } ND
/underscore { 
0 500 hsbw
0 -125 rmoveto
0 50 rlineto
500 0 rlineto
0 -50 rlineto
-500 0 rlineto
closepath
endchar } ND
/grave { 
0 278 hsbw
105 569 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
endchar } ND
/a { 
0 611 hsbw
104 311 hstem
225 145 vstem
373 2 vstem
370 185 rmoveto
0 48 rlineto
-19 -10 -27 -8 -32 -7 rrcurveto
-49 -11 -18 -12 0 -33 rrcurveto
0 -26 27 -22 35 0 rrcurveto
46 0 37 29 0 52 rrcurveto
closepath
190 144 rmoveto
0 -213 rlineto
0 -48 4 -39 20 -29 rrcurveto
-202 0 rlineto
-5 12 -2 17 0 20 rrcurveto
-2 0 rlineto
-40 -46 -57 -16 -70 0 rrcurveto
-107 0 -76 53 0 104 rrcurveto
0 124 117 33 122 12 rrcurveto
60 6 48 -2 0 50 rrcurveto
0 32 -29 16 -36 0 rrcurveto
-55 0 -18 -24 -1 -29 rrcurveto
-184 0 rlineto
3 133 136 37 128 0 rrcurveto
154 0 92 -49 0 -154 rrcurveto
closepath
endchar } ND
/b { 
0 648 hsbw
131 257 hstem
245 174 vstem
419 260 rmoveto
0 58 -23 70 -64 0 rrcurveto
-66 0 -21 -69 0 -59 rrcurveto
0 -62 21 -67 66 0 rrcurveto
63 0 24 68 0 61 rrcurveto
closepath
-365 -260 rmoveto
0 714 rlineto
198 0 rlineto
0 -252 rlineto
2 0 rlineto
30 44 53 26 59 0 rrcurveto
148 0 73 -137 0 -133 rrcurveto
0 -133 -78 -142 -140 0 rrcurveto
-70 0 -43 17 -39 53 rrcurveto
-2 0 rlineto
0 -57 rlineto
-191 0 rlineto
closepath
endchar } ND
/c { 
0 611 hsbw
207 115 hstem
230 159 vstem
577 322 rmoveto
-188 0 rlineto
-4 43 -26 23 -42 0 rrcurveto
-68 0 -19 -64 0 -65 rrcurveto
0 -65 19 -63 68 0 rrcurveto
41 0 32 33 4 43 rrcurveto
189 0 rlineto
-19 -138 -107 -82 -144 0 rrcurveto
-154 0 -127 119 0 153 rrcurveto
0 153 130 120 151 0 rrcurveto
144 0 108 -73 12 -137 rrcurveto
closepath
endchar } ND
/d { 
0 648 hsbw
131 257 hstem
229 174 vstem
594 714 rmoveto
0 -714 rlineto
-191 0 rlineto
0 57 rlineto
-2 0 rlineto
-4 -6 -4 -6 -5 -4 rrcurveto
-4 -3 -4 -4 -2 -5 rrcurveto
-25 -33 -51 -9 -53 0 rrcurveto
-144 0 -74 143 0 132 rrcurveto
0 133 71 137 150 0 rrcurveto
58 0 54 -26 30 -44 rrcurveto
2 0 rlineto
0 252 rlineto
198 0 rlineto
closepath
-191 -454 rmoveto
0 58 -23 70 -64 0 rrcurveto
-66 0 -21 -69 0 -59 rrcurveto
0 -62 21 -67 66 0 rrcurveto
63 0 24 68 0 61 rrcurveto
closepath
endchar } ND
/e { 
0 630 hsbw
151 64 hstem
318 87 hstem
222 189 vstem
222 318 rmoveto
189 0 rlineto
0 49 -37 38 -50 0 rrcurveto
-59 0 -34 -29 -9 -58 rrcurveto
closepath
379 -103 rmoveto
-380 0 rlineto
5 -62 36 -39 60 0 rrcurveto
34 0 34 13 15 24 rrcurveto
185 0 rlineto
-39 -108 -104 -56 -124 0 rrcurveto
-160 0 -131 108 0 163 rrcurveto
0 147 122 127 152 0 rrcurveto
201 0 94 -111 0 -206 rrcurveto
closepath
endchar } ND
/f { 
0 389 hsbw
519 55 hstem
87 0 rmoveto
0 397 rlineto
-81 0 rlineto
0 122 rlineto
81 0 rlineto
0 152 53 43 161 0 rrcurveto
89 0 rlineto
0 -140 rlineto
-10 0 -15 7 -10 0 rrcurveto
-13 0 rlineto
-46 0 -15 -13 0 -49 rrcurveto
104 0 rlineto
0 -122 rlineto
-100 0 rlineto
0 -397 rlineto
-198 0 rlineto
closepath
endchar } ND
/g { 
0 630 hsbw
-8 39 hstem
36 67 hstem
158 230 hstem
220 170 vstem
220 285 rmoveto
0 -61 24 -66 65 0 rrcurveto
66 0 15 64 0 64 rrcurveto
0 60 -30 42 -56 0 rrcurveto
-54 0 -30 -45 0 -58 rrcurveto
closepath
363 234 rmoveto
0 -452 rlineto
0 -149 -103 -99 -167 0 rrcurveto
-126 0 -129 44 -16 129 rrcurveto
196 0 rlineto
1 -2 3 -5 5 -8 rrcurveto
2 -3 rlineto
14 -21 21 -17 23 0 rrcurveto
43 0 26 40 5 40 rrcurveto
2 7 0 8 1 5 rrcurveto
0 67 rlineto
-1 0 rlineto
-34 -48 -46 -24 -59 0 rrcurveto
-144 0 -78 117 0 136 rrcurveto
0 142 83 106 142 0 rrcurveto
61 0 53 -26 22 -50 rrcurveto
2 0 rlineto
0 63 rlineto
198 0 rlineto
closepath
endchar } ND
/h { 
0 648 hsbw
252 144 vstem
54 0 rmoveto
0 714 rlineto
198 0 rlineto
0 -251 rlineto
2 0 rlineto
37 44 46 25 79 0 rrcurveto
100 0 78 -63 0 -113 rrcurveto
0 -356 rlineto
-198 0 rlineto
0 272 rlineto
0 69 -12 33 -53 0 rrcurveto
-51 0 -28 -43 0 -57 rrcurveto
0 -274 rlineto
-198 0 rlineto
closepath
endchar } ND
/i { 
0 315 hsbw
519 56 hstem
58 0 rmoveto
0 519 rlineto
198 0 rlineto
0 -519 rlineto
-198 0 rlineto
closepath
198 714 rmoveto
0 -139 rlineto
-198 0 rlineto
0 139 rlineto
198 0 rlineto
closepath
endchar } ND
/j { 
0 315 hsbw
-10 20 hstem
519 56 hstem
-30 -168 rmoveto
0 158 rlineto
33 0 rlineto
41 0 14 14 0 50 rrcurveto
0 465 rlineto
198 0 rlineto
0 -484 rlineto
0 -173 -50 -30 -143 0 rrcurveto
-93 0 rlineto
closepath
286 882 rmoveto
0 -139 rlineto
-198 0 rlineto
0 139 rlineto
198 0 rlineto
closepath
endchar } ND
/k { 
0 611 hsbw
256 126 vstem
58 0 rmoveto
0 714 rlineto
198 0 rlineto
0 -345 rlineto
126 150 rlineto
224 0 rlineto
-185 -196 rlineto
214 -323 rlineto
-238 0 rlineto
-106 188 rlineto
-35 -38 rlineto
0 -150 rlineto
-198 0 rlineto
closepath
endchar } ND
/l { 
0 315 hsbw
58 0 rmoveto
0 714 rlineto
198 0 rlineto
0 -714 rlineto
-198 0 rlineto
closepath
endchar } ND
/m { 
0 963 hsbw
244 2 vstem
251 132 vstem
581 132 vstem
53 0 rmoveto
0 519 rlineto
191 0 rlineto
0 -69 rlineto
2 0 rlineto
38 57 57 25 75 0 rrcurveto
58 0 59 -31 23 -50 rrcurveto
2 2 0 0 3 4 rrcurveto
37 54 63 21 72 0 rrcurveto
107 0 71 -80 0 -96 rrcurveto
0 -356 rlineto
-198 0 rlineto
0 281 rlineto
0 61 -11 32 -54 0 rrcurveto
-47 0 -20 -35 0 -58 rrcurveto
0 -281 rlineto
-198 0 rlineto
0 281 rlineto
0 61 -11 32 -54 0 rrcurveto
-47 0 -20 -35 0 -58 rrcurveto
0 -281 rlineto
-198 0 rlineto
closepath
endchar } ND
/n { 
0 648 hsbw
245 2 vstem
252 144 vstem
54 0 rmoveto
0 519 rlineto
191 0 rlineto
0 -66 rlineto
2 0 rlineto
37 52 56 27 76 0 rrcurveto
100 0 78 -63 0 -113 rrcurveto
0 -356 rlineto
-198 0 rlineto
0 272 rlineto
0 69 -12 33 -53 0 rrcurveto
-51 0 -28 -43 0 -57 rrcurveto
0 -274 rlineto
-198 0 rlineto
closepath
endchar } ND
/o { 
0 630 hsbw
131 257 hstem
230 170 vstem
598 260 rmoveto
0 -154 -118 -119 -165 0 rrcurveto
-167 0 -116 119 0 154 rrcurveto
0 150 117 122 166 0 rrcurveto
163 0 120 -122 0 -150 rrcurveto
closepath
-198 0 rmoveto
0 61 -17 67 -68 0 rrcurveto
-68 0 -17 -66 0 -62 rrcurveto
0 -61 17 -68 68 0 rrcurveto
68 0 17 69 0 60 rrcurveto
closepath
endchar } ND
/p { 
0 648 hsbw
131 257 hstem
245 174 vstem
245 259 rmoveto
0 -63 22 -65 65 0 rrcurveto
63 0 24 67 0 61 rrcurveto
0 61 -23 68 -64 0 rrcurveto
-67 0 -20 -67 0 -62 rrcurveto
closepath
-191 -427 rmoveto
0 687 rlineto
191 0 rlineto
0 -57 rlineto
2 0 rlineto
39 53 44 17 69 0 rrcurveto
138 0 80 -146 0 -129 rrcurveto
0 -137 -72 -133 -149 0 rrcurveto
-59 0 -53 26 -30 44 rrcurveto
-2 0 rlineto
0 -225 rlineto
-198 0 rlineto
closepath
endchar } ND
/q { 
0 648 hsbw
131 257 hstem
229 174 vstem
594 519 rmoveto
0 -687 rlineto
-198 0 rlineto
0 225 rlineto
-2 0 rlineto
-30 -44 -54 -26 -58 0 rrcurveto
-151 0 -70 134 0 136 rrcurveto
0 128 76 147 142 0 rrcurveto
52 0 49 -14 28 -29 rrcurveto
3 -5 3 -4 5 -4 rrcurveto
4 -4 4 -5 4 -5 rrcurveto
2 0 rlineto
0 57 rlineto
191 0 rlineto
closepath
-191 -260 rmoveto
0 78 -23 51 -64 0 rrcurveto
-67 0 -20 -67 0 -62 rrcurveto
0 -63 22 -65 65 0 rrcurveto
63 0 24 67 0 61 rrcurveto
closepath
endchar } ND
/r { 
0 444 hsbw
244 2 vstem
54 0 rmoveto
0 519 rlineto
190 0 rlineto
0 -83 rlineto
2 0 rlineto
33 65 43 31 70 0 rrcurveto
18 0 20 -3 20 -5 rrcurveto
0 -175 rlineto
-26 9 -16 4 -36 0 rrcurveto
-80 0 -40 -49 0 -108 rrcurveto
0 -205 rlineto
-198 0 rlineto
closepath
endchar } ND
/s { 
0 574 hsbw
109 313 hstem
227 127 vstem
534 368 rmoveto
-180 0 rlineto
-2 31 -31 23 -34 0 rrcurveto
-35 0 -25 -10 0 -30 rrcurveto
0 -40 39 6 53 -9 rrcurveto
136 -23 93 -27 0 -119 rrcurveto
0 -134 -138 -49 -126 0 rrcurveto
-120 0 -139 55 0 128 rrcurveto
189 0 rlineto
1 -37 30 -24 46 0 rrcurveto
32 0 35 13 0 27 rrcurveto
0 30 -17 9 -92 16 rrcurveto
-150 26 -62 47 0 90 rrcurveto
0 122 137 43 108 0 rrcurveto
115 0 130 -40 7 -124 rrcurveto
closepath
endchar } ND
/t { 
0 407 hsbw
150 247 hstem
292 677 rmoveto
0 -158 rlineto
106 0 rlineto
0 -122 rlineto
-106 0 rlineto
0 -181 rlineto
0 -51 13 -18 48 0 rrcurveto
15 0 15 1 15 2 rrcurveto
0 -150 rlineto
-14 0 -20 -1 -28 -2 rrcurveto
-29 -2 -23 -1 -13 0 rrcurveto
-104 0 -73 46 0 109 rrcurveto
0 248 rlineto
-88 0 rlineto
0 122 rlineto
88 0 rlineto
0 158 rlineto
198 0 rlineto
closepath
endchar } ND
/u { 
0 648 hsbw
252 144 vstem
401 2 vstem
594 519 rmoveto
0 -519 rlineto
-191 0 rlineto
0 66 rlineto
-2 0 rlineto
-38 -53 -56 -26 -75 0 rrcurveto
-100 0 -78 62 0 114 rrcurveto
0 356 rlineto
198 0 rlineto
0 -272 rlineto
0 -70 11 -32 54 0 rrcurveto
45 0 34 43 0 57 rrcurveto
0 274 rlineto
198 0 rlineto
closepath
endchar } ND
/v { 
0 556 hsbw
280 2 vstem
566 519 rmoveto
-172 -519 rlineto
-234 0 rlineto
-170 519 rlineto
209 0 rlineto
81 -316 rlineto
2 0 rlineto
82 316 rlineto
202 0 rlineto
closepath
endchar } ND
/w { 
0 870 hsbw
274 2 vstem
433 2 vstem
596 4 vstem
878 519 rmoveto
-165 -519 rlineto
-203 0 rlineto
-75 319 rlineto
-2 0 rlineto
-77 -319 rlineto
-203 0 rlineto
-162 519 rlineto
206 0 rlineto
77 -321 rlineto
2 0 rlineto
66 321 rlineto
185 0 rlineto
69 -321 rlineto
4 0 rlineto
72 321 rlineto
206 0 rlineto
closepath
endchar } ND
/x { 
0 556 hsbw
222 115 vstem
-14 0 rmoveto
181 273 rlineto
-163 246 rlineto
218 0 rlineto
57 -103 rlineto
58 103 rlineto
212 0 rlineto
-163 -246 rlineto
184 -273 rlineto
-218 0 rlineto
-76 127 rlineto
-73 -127 rlineto
-217 0 rlineto
closepath
endchar } ND
/y { 
0 556 hsbw
-10 20 hstem
280 2 vstem
571 519 rmoveto
-172 -479 rlineto
-1 -2 -1 -2 0 -3 rrcurveto
-3 -9 rlineto
-54 -162 -43 -30 -172 0 rrcurveto
-90 0 rlineto
0 158 rlineto
46 0 rlineto
69 0 10 -1 0 45 rrcurveto
0 4 -3 11 -5 16 rrcurveto
-2 8 -3 8 -4 9 rrcurveto
-3 9 -3 9 -3 10 rrcurveto
-8 21 -16 42 -24 65 rrcurveto
-4 5 -2 6 -1 6 rrcurveto
-2 6 -2 6 -2 5 rrcurveto
-88 239 rlineto
212 0 rlineto
83 -298 rlineto
2 0 rlineto
84 298 rlineto
205 0 rlineto
closepath
endchar } ND
/z { 
0 556 hsbw
144 223 hstem
17 0 rmoveto
0 153 rlineto
243 222 rlineto
-221 0 rlineto
0 144 rlineto
475 0 rlineto
0 -152 rlineto
-237 -223 rlineto
258 0 rlineto
0 -144 rlineto
-518 0 rlineto
closepath
endchar } ND
/braceleft { 
0 407 hsbw
-15 20 hstem
278 4 hstem
369 731 rmoveto
0 -153 rlineto
-35 0 rlineto
-42 0 -9 -27 0 -34 rrcurveto
0 -145 rlineto
0 -63 -65 -27 -54 0 rrcurveto
0 -4 rlineto
23 0 rlineto
52 0 44 -38 0 -58 rrcurveto
0 -136 rlineto
0 -35 10 -26 48 0 rrcurveto
28 0 rlineto
0 -153 rlineto
-114 0 rlineto
-68 0 -74 52 0 76 rrcurveto
0 180 rlineto
0 44 -46 21 -38 0 rrcurveto
0 153 rlineto
35 0 49 15 0 42 rrcurveto
0 188 rlineto
0 76 75 52 67 0 rrcurveto
114 0 rlineto
closepath
endchar } ND
/bar { 
0 222 hsbw
35 -214 rmoveto
0 1000 rlineto
153 0 rlineto
0 -1000 rlineto
-153 0 rlineto
closepath
endchar } ND
/braceright { 
0 407 hsbw
-15 20 hstem
281 2 hstem
38 -168 rmoveto
0 153 rlineto
28 0 rlineto
48 0 10 26 0 35 rrcurveto
0 145 rlineto
0 62 62 25 57 3 rrcurveto
0 2 rlineto
-69 3 -50 26 0 69 rrcurveto
0 136 rlineto
0 34 -10 27 -48 0 rrcurveto
-28 0 rlineto
0 153 rlineto
114 0 rlineto
67 0 75 -51 0 -77 rrcurveto
0 -180 rlineto
0 -47 48 -18 36 0 rrcurveto
0 -153 rlineto
-35 0 -49 -14 0 -43 rrcurveto
0 -188 rlineto
0 -77 -74 -51 -68 0 rrcurveto
-114 0 rlineto
closepath
endchar } ND
/asciitilde { 
0 600 hsbw
495 367 rmoveto
36 -138 rlineto
-40 -56 -31 -30 -54 0 rrcurveto
-34 0 -30 11 -38 22 rrcurveto
-38 22 -35 12 -42 0 rrcurveto
-40 0 -24 -29 -20 -42 rrcurveto
-36 138 rlineto
26 50 37 36 62 0 rrcurveto
30 0 35 -12 46 -20 rrcurveto
17 -7 rlineto
41 -19 24 -9 20 0 rrcurveto
36 0 22 25 30 46 rrcurveto
closepath
endchar } ND
/quotesinglbase { 
0 296 hsbw
-94 94 hstem
240 198 rmoveto
0 -164 rlineto
0 -122 -62 -74 -122 -19 rrcurveto
0 87 rlineto
57 7 23 33 2 54 rrcurveto
-82 0 rlineto
0 198 rlineto
184 0 rlineto
closepath
endchar } ND
/florin { 
0 668 hsbw
401 186 hstem
3 -176 rmoveto
39 142 rlineto
8 -2 7 -1 7 0 rrcurveto
52 0 16 22 16 57 rrcurveto
64 237 rlineto
-95 0 rlineto
34 122 rlineto
95 0 rlineto
32 119 rlineto
37 138 66 73 132 0 rrcurveto
36 0 38 -2 38 -4 rrcurveto
-38 -141 rlineto
-14 2 -14 1 -13 0 rrcurveto
-33 0 -21 -16 -9 -31 rrcurveto
-34 -139 rlineto
95 0 rlineto
-34 -122 rlineto
-94 0 rlineto
-73 -268 rlineto
-39 -135 -83 -57 -140 0 rrcurveto
-13 0 -19 1 -26 2 rrcurveto
-5 2 rlineto
-15 0 rlineto
closepath
endchar } ND
/quotedblbase { 
0 519 hsbw
-94 94 hstem
230 60 vstem
474 198 rmoveto
0 -164 rlineto
0 -122 -62 -74 -122 -19 rrcurveto
0 87 rlineto
57 7 23 33 2 54 rrcurveto
-82 0 rlineto
0 198 rlineto
184 0 rlineto
closepath
-244 0 rmoveto
0 -164 rlineto
0 -122 -62 -74 -122 -19 rrcurveto
0 87 rlineto
57 7 23 33 2 54 rrcurveto
-82 0 rlineto
0 198 rlineto
184 0 rlineto
closepath
endchar } ND
/ellipsis { 
0 1000 hsbw
271 125 vstem
604 125 vstem
271 0 rmoveto
-208 0 rlineto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
closepath
666 0 rmoveto
-208 0 rlineto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
closepath
-333 0 rmoveto
-208 0 rlineto
0 198 rlineto
208 0 rlineto
0 -198 rlineto
closepath
endchar } ND
/dagger { 
0 667 hsbw
235 -174 rmoveto
0 545 rlineto
-175 0 rlineto
0 158 rlineto
175 0 rlineto
0 185 rlineto
198 0 rlineto
0 -185 rlineto
175 0 rlineto
0 -158 rlineto
-175 0 rlineto
0 -545 rlineto
-198 0 rlineto
closepath
endchar } ND
/daggerdbl { 
0 667 hsbw
171 200 hstem
235 -174 rmoveto
0 187 rlineto
-175 0 rlineto
0 158 rlineto
175 0 rlineto
0 200 rlineto
-175 0 rlineto
0 158 rlineto
175 0 rlineto
0 185 rlineto
198 0 rlineto
0 -185 rlineto
175 0 rlineto
0 -158 rlineto
-175 0 rlineto
0 -200 rlineto
175 0 rlineto
0 -158 rlineto
-175 0 rlineto
0 -187 rlineto
-198 0 rlineto
closepath
endchar } ND
/circumflex { 
0 278 hsbw
83 113 vstem
-64 569 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
endchar } ND
/perthousand { 
0 1167 hsbw
79 173 hstem
444 173 hstem
169 82 vstem
570 82 756 55 915 82 vstem3
149 -26 rmoveto
410 748 rlineto
110 0 rlineto
-410 -748 rlineto
-110 0 rlineto
closepath
317 192 rmoveto
0 102 49 71 96 0 rrcurveto
96 0 49 -71 0 -102 rrcurveto
0 -104 -49 -70 -96 0 rrcurveto
-96 0 -49 70 0 104 rrcurveto
closepath
104 -1 rmoveto
0 -52 5 -34 36 0 rrcurveto
36 0 5 34 0 52 rrcurveto
0 52 -5 35 -36 0 rrcurveto
-41 0 0 -38 0 -49 rrcurveto
closepath
345 0 rmoveto
0 -52 5 -34 36 0 rrcurveto
36 0 5 34 0 52 rrcurveto
0 52 -5 35 -36 0 rrcurveto
-41 0 0 -38 0 -49 rrcurveto
closepath
-104 1 rmoveto
0 102 49 71 96 0 rrcurveto
96 0 49 -71 0 -102 rrcurveto
0 -104 -49 -70 -96 0 rrcurveto
-96 0 -49 70 0 104 rrcurveto
closepath
-746 365 rmoveto
0 102 49 71 96 0 rrcurveto
96 0 49 -71 0 -102 rrcurveto
0 -104 -49 -70 -96 0 rrcurveto
-96 0 -49 70 0 104 rrcurveto
closepath
104 -1 rmoveto
0 -52 5 -34 36 0 rrcurveto
36 0 5 34 0 52 rrcurveto
0 52 -5 35 -36 0 rrcurveto
-41 0 0 -38 0 -49 rrcurveto
closepath
endchar } ND
/Scaron { 
0 667 hsbw
141 432 hstem
731 33 hstem
248 170 vstem
538 913 rmoveto
-122 -149 rlineto
-164 0 rlineto
-121 149 rlineto
147 0 rlineto
56 -76 rlineto
57 76 rlineto
147 0 rlineto
closepath
-526 -673 rmoveto
220 0 rlineto
0 -65 45 -34 68 0 rrcurveto
41 0 50 18 0 47 rrcurveto
0 37 -36 10 -40 12 rrcurveto
-8 4 -12 3 -14 3 rrcurveto
-14 3 -14 4 -16 6 rrcurveto
-12 3 rlineto
-135 37 -95 40 0 127 rrcurveto
0 151 144 85 144 0 rrcurveto
148 0 143 -74 7 -159 rrcurveto
-208 0 rlineto
0 10 rlineto
0 41 -47 24 -44 0 rrcurveto
-40 0 -39 -12 0 -36 rrcurveto
0 -45 121 -28 61 -11 rrcurveto
144 -26 82 -61 0 -125 rrcurveto
0 -174 -170 -72 -160 0 rrcurveto
-135 0 -179 80 0 177 rrcurveto
closepath
endchar } ND
/guilsinglleft { 
0 241 hsbw
205 121 hstem
202 48 rmoveto
-163 129 rlineto
0 177 rlineto
163 129 rlineto
0 -157 rlineto
-77 -61 rlineto
77 -60 rlineto
0 -157 rlineto
closepath
endchar } ND
/OE { 
0 1130 hsbw
183 89 hstem
442 89 hstem
256 278 vstem
535 0 rmoveto
0 35 rlineto
-32 -35 -75 -17 -57 0 rrcurveto
-195 0 -140 177 0 190 rrcurveto
0 194 141 187 203 0 rrcurveto
61 0 55 -17 39 -38 rrcurveto
0 38 rlineto
540 0 rlineto
0 -183 rlineto
-339 0 rlineto
0 -89 rlineto
310 0 rlineto
0 -170 rlineto
-310 0 rlineto
0 -89 rlineto
349 0 rlineto
0 -183 rlineto
-550 0 rlineto
closepath
-1 362 rmoveto
0 108 -66 83 -73 0 rrcurveto
-78 0 -61 -84 0 -107 rrcurveto
0 -112 59 -89 80 0 rrcurveto
80 0 59 90 0 111 rrcurveto
closepath
endchar } ND
/quoteleft { 
0 296 hsbw
533 94 hstem
56 335 rmoveto
0 164 rlineto
0 121 60 76 124 18 rrcurveto
0 -87 rlineto
-58 -7 -23 -33 -1 -54 rrcurveto
82 0 rlineto
0 -198 rlineto
-184 0 rlineto
closepath
endchar } ND
/quoteright { 
0 296 hsbw
422 94 hstem
240 714 rmoveto
0 -164 rlineto
0 -121 -62 -78 -122 -16 rrcurveto
0 87 rlineto
57 6 23 33 2 55 rrcurveto
-82 0 rlineto
0 198 rlineto
184 0 rlineto
closepath
endchar } ND
/quotedblleft { 
0 519 hsbw
533 94 hstem
230 60 vstem
290 335 rmoveto
0 164 rlineto
0 121 60 76 124 18 rrcurveto
0 -87 rlineto
-58 -7 -23 -33 -1 -54 rrcurveto
82 0 rlineto
0 -198 rlineto
-184 0 rlineto
closepath
-244 0 rmoveto
0 164 rlineto
0 121 60 76 124 18 rrcurveto
0 -87 rlineto
-58 -7 -23 -33 -1 -54 rrcurveto
82 0 rlineto
0 -198 rlineto
-184 0 rlineto
closepath
endchar } ND
/quotedblright { 
0 519 hsbw
422 94 hstem
230 60 vstem
474 714 rmoveto
0 -164 rlineto
0 -121 -62 -78 -122 -16 rrcurveto
0 87 rlineto
57 6 23 33 2 55 rrcurveto
-82 0 rlineto
0 198 rlineto
184 0 rlineto
closepath
-244 0 rmoveto
0 -164 rlineto
0 -121 -62 -78 -122 -16 rrcurveto
0 87 rlineto
57 6 23 33 2 55 rrcurveto
-82 0 rlineto
0 198 rlineto
184 0 rlineto
closepath
endchar } ND
/bullet { 
0 500 hsbw
72 357 rmoveto
0 94 81 84 97 0 rrcurveto
94 0 85 -83 0 -95 rrcurveto
0 -95 -83 -84 -96 0 rrcurveto
-95 0 -83 82 0 97 rrcurveto
closepath
endchar } ND
/endash { 
0 500 hsbw
0 188 rmoveto
0 158 rlineto
500 0 rlineto
0 -158 rlineto
-500 0 rlineto
closepath
endchar } ND
/emdash { 
0 1000 hsbw
130 188 rmoveto
0 158 rlineto
740 0 rlineto
0 -158 rlineto
-740 0 rlineto
closepath
endchar } ND
/tilde { 
0 278 hsbw
-57 569 rmoveto
0 65 38 77 74 0 rrcurveto
32 0 39 -3 31 -21 rrcurveto
3 -2 4 -1 5 -1 rrcurveto
20 -8 12 -4 6 0 rrcurveto
22 0 16 11 7 32 rrcurveto
82 0 rlineto
-3 -80 -43 -59 -70 0 rrcurveto
-27 0 -30 6 -32 13 rrcurveto
-9 3 rlineto
-27 10 -19 5 -11 0 rrcurveto
-20 0 -17 -22 -4 -21 rrcurveto
-79 0 rlineto
closepath
endchar } ND
/trademark { 
0 1000 hsbw
512 20 hstem
376 86 vstem
586 258 vstem
376 714 rmoveto
0 -94 rlineto
-104 0 rlineto
0 -318 rlineto
-136 0 rlineto
0 318 rlineto
-104 0 rlineto
0 94 rlineto
344 0 rlineto
closepath
592 0 rmoveto
0 -412 rlineto
-124 0 rlineto
0 230 rlineto
-2 0 rlineto
-75 -230 rlineto
-104 0 rlineto
-75 230 rlineto
-2 0 rlineto
0 -230 rlineto
-124 0 rlineto
0 412 rlineto
172 0 rlineto
80 -239 rlineto
2 0 rlineto
80 239 rlineto
172 0 rlineto
closepath
endchar } ND
/scaron { 
0 574 hsbw
109 313 hstem
532 37 hstem
231 113 vstem
491 718 rmoveto
-122 -149 rlineto
-164 0 rlineto
-121 149 rlineto
147 0 rlineto
56 -76 rlineto
57 76 rlineto
147 0 rlineto
closepath
43 -350 rmoveto
-180 0 rlineto
-2 31 -31 23 -34 0 rrcurveto
-35 0 -25 -10 0 -30 rrcurveto
0 -40 39 6 53 -9 rrcurveto
136 -23 93 -27 0 -119 rrcurveto
0 -134 -138 -49 -126 0 rrcurveto
-120 0 -139 55 0 128 rrcurveto
189 0 rlineto
1 -37 30 -24 46 0 rrcurveto
32 0 35 13 0 27 rrcurveto
0 30 -17 9 -92 16 rrcurveto
-150 26 -62 47 0 90 rrcurveto
0 122 137 43 108 0 rrcurveto
115 0 130 -40 7 -124 rrcurveto
closepath
endchar } ND
/guilsinglright { 
0 241 hsbw
205 121 hstem
39 483 rmoveto
163 -129 rlineto
0 -177 rlineto
-163 -129 rlineto
0 157 rlineto
77 60 rlineto
-77 61 rlineto
0 157 rlineto
closepath
endchar } ND
/oe { 
0 981 hsbw
151 64 hstem
318 87 hstem
230 176 vstem
576 187 vstem
576 318 rmoveto
187 0 rlineto
-4 58 -31 29 -56 0 rrcurveto
-54 0 -41 -31 -1 -56 rrcurveto
closepath
-170 -59 rmoveto
0 62 -20 67 -67 0 rrcurveto
-68 0 -21 -67 0 -62 rrcurveto
0 -63 22 -65 67 0 rrcurveto
67 0 20 68 0 60 rrcurveto
closepath
360 -108 rmoveto
191 0 rlineto
-42 -111 -101 -53 -133 0 rrcurveto
-76 0 -64 18 -50 48 rrcurveto
-46 -47 -67 -19 -74 0 rrcurveto
-166 0 -106 100 0 172 rrcurveto
0 167 146 106 140 0 rrcurveto
72 0 67 -18 44 -45 rrcurveto
53 45 54 18 72 0 rrcurveto
181 0 99 -124 0 -193 rrcurveto
-384 0 rlineto
0 -63 41 -38 64 0 rrcurveto
37 0 24 10 24 27 rrcurveto
closepath
endchar } ND
/Ydieresis { 
0 667 hsbw
714 55 hstem
306 57 vstem
363 769 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
85 -769 rmoveto
0 263 rlineto
-251 451 rlineto
243 0 rlineto
119 -263 rlineto
124 263 rlineto
241 0 rlineto
-256 -451 rlineto
0 -263 rlineto
-220 0 rlineto
closepath
endchar } ND
/exclamdown { 
0 315 hsbw
291 59 hstem
54 -166 rmoveto
0 168 rlineto
56 289 rlineto
97 0 rlineto
55 -289 rlineto
0 -168 rlineto
-208 0 rlineto
closepath
208 714 rmoveto
0 -198 rlineto
-208 0 rlineto
0 198 rlineto
208 0 rlineto
closepath
endchar } ND
/cent { 
0 668 hsbw
207 115 hstem
245 72 vstem
369 53 vstem
317 135 rmoveto
0 252 rlineto
-52 -11 -20 -42 0 -70 rrcurveto
0 -70 18 -47 54 -12 rrcurveto
closepath
0 -244 rmoveto
0 96 rlineto
-153 0 -109 125 0 151 rrcurveto
0 144 116 125 146 0 rrcurveto
0 80 rlineto
52 0 rlineto
0 -80 rlineto
145 0 73 -73 16 -137 rrcurveto
-181 0 rlineto
-2 36 -18 22 -33 7 rrcurveto
0 -252 rlineto
36 18 18 19 3 35 rrcurveto
182 0 rlineto
-15 -122 -103 -98 -121 0 rrcurveto
0 -96 rlineto
-52 0 rlineto
closepath
endchar } ND
/sterling { 
0 668 hsbw
166 119 hstem
412 72 hstem
294 132 vstem
619 484 rmoveto
-193 0 rlineto
0 36 -23 53 -45 0 rrcurveto
-46 0 -18 -22 0 -38 rrcurveto
0 -19 8 -21 16 -32 rrcurveto
3 -6 3 -6 2 -5 rrcurveto
1 -6 2 -4 2 -2 rrcurveto
109 0 rlineto
0 -127 rlineto
-75 0 rlineto
2 -8 1 -7 0 -7 rrcurveto
0 -40 -23 -40 -34 -24 rrcurveto
2 -2 rlineto
12 6 14 3 16 0 rrcurveto
12 0 16 -2 20 -4 rrcurveto
4 -1 4 -1 5 -1 rrcurveto
6 0 6 -1 5 -3 rrcurveto
28 -8 21 -4 14 0 rrcurveto
32 0 19 25 20 36 rrcurveto
0 2 0 0 2 2 rrcurveto
79 -142 rlineto
-47 -52 -62 -29 -65 0 rrcurveto
-40 0 -29 8 -44 13 rrcurveto
-7 2 rlineto
-2 1 rlineto
-25 3 -18 4 -16 4 rrcurveto
-17 3 -15 1 -19 0 rrcurveto
-38 0 -31 -14 -36 -25 rrcurveto
-79 112 rlineto
70 48 40 38 0 67 rrcurveto
0 14 -2 13 -4 10 rrcurveto
-117 0 rlineto
0 127 rlineto
71 0 rlineto
-18 24 -12 35 0 36 rrcurveto
0 128 119 96 147 0 rrcurveto
165 0 94 -86 8 -161 rrcurveto
closepath
endchar } ND
/currency { 
0 668 hsbw
241 214 hstem
229 214 vstem
528 79 rmoveto
-59 60 rlineto
-31 -28 -44 -14 -58 0 rrcurveto
-58 0 -48 14 -30 26 rrcurveto
-60 -58 rlineto
-75 75 rlineto
60 58 rlineto
-22 29 -18 56 0 51 rrcurveto
0 54 18 53 22 28 rrcurveto
-60 57 rlineto
78 76 rlineto
57 -56 rlineto
25 21 57 18 54 0 rrcurveto
50 0 56 -18 27 -21 rrcurveto
58 56 rlineto
76 -75 rlineto
-58 -58 rlineto
22 -27 20 -55 0 -53 rrcurveto
0 -52 -18 -58 -24 -26 rrcurveto
58 -58 rlineto
-75 -75 rlineto
closepath
-192 376 rmoveto
-55 0 -52 -41 0 -66 rrcurveto
0 -67 52 -40 55 0 rrcurveto
54 0 53 40 0 67 rrcurveto
0 66 -53 41 -54 0 rrcurveto
closepath
endchar } ND
/yen { 
0 668 hsbw
229 86 hstem
222 224 vstem
235 0 rmoveto
0 107 rlineto
-161 0 rlineto
0 122 rlineto
161 0 rlineto
0 43 rlineto
-21 43 rlineto
-140 0 rlineto
0 122 rlineto
73 0 rlineto
-150 277 rlineto
225 0 rlineto
112 -277 rlineto
112 277 rlineto
225 0 rlineto
-150 -277 rlineto
73 0 rlineto
0 -122 rlineto
-140 0 rlineto
-21 -43 rlineto
0 -43 rlineto
161 0 rlineto
0 -122 rlineto
-161 0 rlineto
0 -107 rlineto
-198 0 rlineto
closepath
endchar } ND
/brokenbar { 
0 222 hsbw
161 250 hstem
35 -89 rmoveto
0 250 rlineto
153 0 rlineto
0 -250 rlineto
-153 0 rlineto
closepath
0 500 rmoveto
0 250 rlineto
153 0 rlineto
0 -250 rlineto
-153 0 rlineto
closepath
endchar } ND
/section { 
0 667 hsbw
208 322 hstem
293 118 vstem
201 326 rmoveto
0 -31 46 -21 78 -28 rrcurveto
12 -3 9 -3 6 -4 rrcurveto
7 -3 5 -2 3 -1 rrcurveto
2 0 2 -1 4 -2 rrcurveto
4 -2 5 -2 8 -2 rrcurveto
24 -10 10 -3 4 0 rrcurveto
18 0 18 14 0 19 rrcurveto
0 25 -8 7 -24 12 rrcurveto
-7 3 rlineto
-28 12 -39 16 -50 20 rrcurveto
-11 6 -9 3 -8 2 rrcurveto
-7 2 -4 2 -2 1 rrcurveto
-5 2 rlineto
-12 6 -11 3 -6 0 rrcurveto
-16 0 -18 -19 0 -18 rrcurveto
closepath
382 204 rmoveto
-172 0 rlineto
-4 38 -22 19 -39 0 rrcurveto
-28 0 -25 -16 0 -26 rrcurveto
0 -30 46 -18 73 -27 rrcurveto
7 -1 4 -2 3 -2 rrcurveto
5 -2 3 -1 3 -1 rrcurveto
8 -4 6 -2 4 -1 rrcurveto
96 -35 85 -41 0 -100 rrcurveto
0 -77 -45 -63 -61 -11 rrcurveto
33 -26 19 -39 0 -42 rrcurveto
0 -132 -126 -68 -123 0 rrcurveto
-135 0 -114 74 0 138 rrcurveto
170 0 rlineto
5 -46 28 -22 44 0 rrcurveto
29 0 32 16 0 28 rrcurveto
0 34 -19 14 -39 15 rrcurveto
-105 40 -198 44 0 142 rrcurveto
0 80 36 46 76 30 rrcurveto
-27 26 -13 31 0 38 rrcurveto
0 117 123 66 111 0 rrcurveto
140 0 100 -69 6 -132 rrcurveto
closepath
endchar } ND
/dieresis { 
0 278 hsbw
111 57 vstem
168 574 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
endchar } ND
/copyright { 
0 800 hsbw
100 48 309 96 566 48 hstem3
150 54 vstem
314 169 vstem
583 67 vstem
483 309 rmoveto
100 0 rlineto
-15 -106 -71 -55 -92 0 rrcurveto
-116 0 -85 93 0 115 rrcurveto
0 122 77 87 122 0 rrcurveto
93 0 67 -54 16 -101 rrcurveto
-96 0 rlineto
-8 34 -26 18 -43 0 rrcurveto
-58 0 -34 -40 0 -64 rrcurveto
0 -63 39 -44 52 0 rrcurveto
45 0 29 16 4 42 rrcurveto
closepath
-457 48 rmoveto
0 198 174 176 200 0 rrcurveto
198 0 176 -175 0 -199 rrcurveto
0 -201 -175 -173 -199 0 rrcurveto
-201 0 -173 174 0 200 rrcurveto
closepath
124 0 rmoveto
0 -138 112 -119 138 0 rrcurveto
134 0 116 120 0 137 rrcurveto
0 133 -114 124 -136 0 rrcurveto
-138 0 -112 -123 0 -134 rrcurveto
closepath
endchar } ND
/ordfeminine { 
0 367 hsbw
505 20 hstem
576 58 hstem
143 90 vstem
234 1 vstem
233 492 rmoveto
0 33 rlineto
-16 -7 -17 -5 -18 -3 rrcurveto
-26 -6 -17 -8 0 -20 rrcurveto
0 -20 16 -11 20 0 rrcurveto
34 0 24 15 0 32 rrcurveto
closepath
124 91 rmoveto
0 -145 rlineto
0 -24 7 -18 7 -13 rrcurveto
-132 0 rlineto
0 7 rlineto
-3 10 -1 8 0 6 rrcurveto
-1 0 rlineto
-26 -28 -36 -11 -46 0 rrcurveto
-70 0 -47 33 0 63 rrcurveto
0 78 67 15 82 9 rrcurveto
8 2 7 1 8 0 rrcurveto
28 0 24 6 0 23 rrcurveto
0 21 -18 8 -25 0 rrcurveto
-26 0 -21 -13 0 -20 rrcurveto
-120 0 rlineto
0 85 93 18 80 0 rrcurveto
79 0 82 -34 0 -87 rrcurveto
closepath
endchar } ND
/guillemotleft { 
0 444 hsbw
205 124 hstem
205 34 vstem
205 48 rmoveto
-163 129 rlineto
0 177 rlineto
163 129 rlineto
0 -154 rlineto
-78 -64 rlineto
78 -60 rlineto
0 -157 rlineto
closepath
197 0 rmoveto
-163 129 rlineto
0 177 rlineto
163 129 rlineto
0 -154 rlineto
-78 -64 rlineto
78 -60 rlineto
0 -157 rlineto
closepath
endchar } ND
/logicalnot { 
0 600 hsbw
553 450 rmoveto
0 -342 rlineto
-153 0 rlineto
0 189 rlineto
-352 0 rlineto
0 153 rlineto
505 0 rlineto
closepath
endchar } ND
/minus { 
0 600 hsbw
48 176 rmoveto
0 153 rlineto
505 0 rlineto
0 -153 rlineto
-505 0 rlineto
closepath
endchar } ND
/registered { 
0 800 hsbw
100 67 hstem
388 88 hstem
546 68 hstem
150 102 vstem
342 128 vstem
566 84 vstem
26 357 rmoveto
0 198 174 176 200 0 rrcurveto
198 0 176 -175 0 -199 rrcurveto
0 -201 -175 -173 -199 0 rrcurveto
-201 0 -173 174 0 200 rrcurveto
closepath
124 0 rmoveto
0 -138 112 -119 138 0 rrcurveto
134 0 116 120 0 137 rrcurveto
0 133 -114 124 -136 0 rrcurveto
-138 0 -112 -123 0 -134 rrcurveto
closepath
102 -190 rmoveto
0 379 rlineto
147 0 rlineto
113 0 54 -30 0 -89 rrcurveto
0 -70 -39 -31 -61 -2 rrcurveto
85 -157 rlineto
-97 0 rlineto
-76 151 rlineto
-36 0 rlineto
0 -151 rlineto
-90 0 rlineto
closepath
90 309 rmoveto
0 -88 rlineto
75 0 rlineto
35 0 18 16 0 33 rrcurveto
0 33 -32 6 -41 0 rrcurveto
-55 0 rlineto
closepath
endchar } ND
/macron { 
0 278 hsbw
-30 598 rmoveto
0 91 rlineto
339 0 rlineto
0 -91 rlineto
-339 0 rlineto
closepath
endchar } ND
/degree { 
0 400 hsbw
484 142 hstem
129 142 vstem
51 555 rmoveto
0 86 62 63 87 0 rrcurveto
86 0 63 -63 0 -86 rrcurveto
0 -87 -63 -62 -86 0 rrcurveto
-87 0 -62 62 0 87 rrcurveto
closepath
78 0 rmoveto
0 -40 32 -31 39 0 rrcurveto
40 0 31 31 0 40 rrcurveto
0 41 -31 30 -40 0 rrcurveto
-39 0 -32 -30 0 -41 rrcurveto
closepath
endchar } ND
/plusminus { 
0 600 hsbw
153 37 hstem
48 271 rmoveto
0 153 rlineto
176 0 rlineto
0 81 rlineto
153 0 rlineto
0 -81 rlineto
176 0 rlineto
0 -153 rlineto
-176 0 rlineto
0 -81 rlineto
-153 0 rlineto
0 81 rlineto
-176 0 rlineto
closepath
0 -271 rmoveto
0 153 rlineto
505 0 rlineto
0 -153 rlineto
-505 0 rlineto
closepath
endchar } ND
/twosuperior { 
0 400 hsbw
503 20 hstem
135 120 vstem
135 523 rmoveto
-122 0 rlineto
0 131 70 50 122 0 rrcurveto
120 0 66 -42 0 -89 rrcurveto
0 -88 -78 -43 -80 -32 rrcurveto
-32 -13 -10 -6 0 -6 rrcurveto
205 0 rlineto
0 -103 rlineto
-392 0 rlineto
0 98 67 50 100 53 rrcurveto
13 7 9 5 4 3 rrcurveto
3 2 rlineto
34 17 21 16 0 24 rrcurveto
0 30 -21 23 -30 0 rrcurveto
-48 0 -21 -37 0 -50 rrcurveto
closepath
endchar } ND
/threesuperior { 
0 400 hsbw
536 74 hstem
147 118 vstem
147 559 rmoveto
-124 0 rlineto
0 96 84 49 100 0 rrcurveto
110 0 72 -48 0 -70 rrcurveto
0 -44 -17 -23 -38 -14 rrcurveto
-3 -1 rlineto
0 -2 rlineto
46 -16 24 -29 0 -61 rrcurveto
0 -68 -63 -54 -133 0 rrcurveto
-138 0 -57 61 0 103 rrcurveto
124 0 rlineto
0 -45 23 -25 46 0 rrcurveto
33 0 29 17 0 32 rrcurveto
0 32 -28 9 -34 0 rrcurveto
-36 0 rlineto
0 78 rlineto
32 0 rlineto
34 0 32 11 0 22 rrcurveto
0 25 -24 16 -34 0 rrcurveto
-35 0 -25 -22 0 -29 rrcurveto
closepath
endchar } ND
/acute { 
0 278 hsbw
344 718 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
endchar } ND
/mu { 
0 648 hsbw
252 144 vstem
401 2 vstem
594 519 rmoveto
0 -519 rlineto
-191 0 rlineto
0 66 rlineto
-2 0 rlineto
-37 -51 -50 -26 -62 -2 rrcurveto
0 -155 rlineto
-198 0 rlineto
0 687 rlineto
198 0 rlineto
0 -272 rlineto
0 -70 11 -32 54 0 rrcurveto
45 0 34 43 0 57 rrcurveto
0 274 rlineto
198 0 rlineto
closepath
endchar } ND
/paragraph { 
0 620 hsbw
377 65 vstem
241 -174 rmoveto
0 539 rlineto
-144 0 -79 63 0 108 rrcurveto
0 131 119 47 127 0 rrcurveto
314 0 rlineto
0 -888 rlineto
-136 0 rlineto
0 785 rlineto
-65 0 rlineto
0 -785 rlineto
-136 0 rlineto
closepath
endchar } ND
/periodcentered { 
0 334 hsbw
47 237 rmoveto
0 66 53 54 67 0 rrcurveto
66 0 54 -54 0 -66 rrcurveto
0 -67 -54 -53 -66 0 rrcurveto
-67 0 -53 53 0 67 rrcurveto
closepath
endchar } ND
/cedilla { 
0 278 hsbw
-178 72 hstem
0 20 hstem
1 -204 rmoveto
21 46 rlineto
5 -1 8 -2 10 -4 rrcurveto
27 -8 21 -5 15 0 rrcurveto
29 0 23 12 0 25 rrcurveto
0 20 -19 15 -24 0 rrcurveto
-13 0 -13 -3 -13 -7 rrcurveto
-26 24 rlineto
56 92 rlineto
58 0 rlineto
-40 -62 rlineto
2 -2 rlineto
7 4 14 1 21 0 rrcurveto
54 0 46 -27 0 -62 rrcurveto
0 -52 -55 -32 -76 0 rrcurveto
-51 0 -30 8 -57 20 rrcurveto
closepath
endchar } ND
/onesuperior { 
0 400 hsbw
496 20 hstem
157 282 rmoveto
0 234 rlineto
-106 0 rlineto
0 87 rlineto
74 1 57 28 4 64 rrcurveto
107 0 rlineto
0 -414 rlineto
-136 0 rlineto
closepath
endchar } ND
/ordmasculine { 
0 378 hsbw
462 155 hstem
146 98 vstem
10 541 rmoveto
0 93 65 70 120 0 rrcurveto
120 0 65 -70 0 -93 rrcurveto
0 -95 -65 -71 -120 0 rrcurveto
-120 0 -65 71 0 95 rrcurveto
closepath
136 0 rmoveto
0 -46 10 -33 39 0 rrcurveto
38 0 11 33 0 46 rrcurveto
0 42 -11 34 -38 0 rrcurveto
-39 0 -10 -34 0 -42 rrcurveto
closepath
endchar } ND
/guillemotright { 
0 444 hsbw
205 121 hstem
205 34 vstem
42 483 rmoveto
163 -129 rlineto
0 -177 rlineto
-163 -129 rlineto
0 157 rlineto
78 60 rlineto
-78 61 rlineto
0 157 rlineto
closepath
197 0 rmoveto
163 -129 rlineto
0 -177 rlineto
-163 -129 rlineto
0 157 rlineto
78 60 rlineto
-78 61 rlineto
0 157 rlineto
closepath
endchar } ND
/onequarter { 
0 1000 hsbw
186 116 hstem
496 20 hstem
590 110 vstem
205 -26 rmoveto
388 748 rlineto
110 0 rlineto
-388 -748 rlineto
-110 0 rlineto
closepath
-31 308 rmoveto
0 234 rlineto
-106 0 rlineto
0 87 rlineto
74 1 57 28 4 64 rrcurveto
107 0 rlineto
0 -414 rlineto
-136 0 rlineto
closepath
526 -282 rmoveto
0 83 rlineto
-200 0 rlineto
0 113 rlineto
206 218 rlineto
130 0 rlineto
0 -228 rlineto
61 0 rlineto
0 -103 rlineto
-61 0 rlineto
0 -83 rlineto
-136 0 rlineto
closepath
0 186 rmoveto
0 116 rlineto
-110 -116 rlineto
110 0 rlineto
closepath
endchar } ND
/onehalf { 
0 1000 hsbw
103 225 hstem
496 20 hstem
310 239 vstem
671 120 vstem
205 -26 rmoveto
388 748 rlineto
110 0 rlineto
-389 -748 rlineto
-109 0 rlineto
closepath
727 26 rmoveto
-392 0 rlineto
0 98 67 50 100 53 rrcurveto
13 7 9 5 4 3 rrcurveto
3 2 rlineto
34 17 21 16 0 24 rrcurveto
0 30 -21 23 -30 0 rrcurveto
-48 0 -21 -37 0 -50 rrcurveto
-122 0 rlineto
0 131 70 50 122 0 rrcurveto
120 0 66 -42 0 -89 rrcurveto
0 -88 -78 -43 -80 -32 rrcurveto
-32 -13 -10 -6 0 -6 rrcurveto
205 0 rlineto
0 -103 rlineto
closepath
-758 282 rmoveto
0 234 rlineto
-106 0 rlineto
0 87 rlineto
60 0 72 24 3 69 rrcurveto
107 0 rlineto
0 -414 rlineto
-136 0 rlineto
closepath
endchar } ND
/threequarters { 
0 1000 hsbw
186 116 hstem
536 74 hstem
166 118 vstem
663 110 vstem
773 0 rmoveto
0 83 rlineto
-200 0 rlineto
0 113 rlineto
206 218 rlineto
130 0 rlineto
0 -228 rlineto
61 0 rlineto
0 -103 rlineto
-61 0 rlineto
0 -83 rlineto
-136 0 rlineto
closepath
-110 186 rmoveto
110 0 rlineto
0 116 rlineto
-110 -116 rlineto
closepath
-382 -212 rmoveto
388 748 rlineto
110 0 rlineto
-389 -748 rlineto
-109 0 rlineto
closepath
-115 585 rmoveto
-124 0 rlineto
0 96 84 49 100 0 rrcurveto
110 0 72 -48 0 -70 rrcurveto
0 -44 -17 -23 -38 -14 rrcurveto
-3 -1 rlineto
0 -2 rlineto
46 -16 24 -29 0 -61 rrcurveto
0 -68 -63 -54 -133 0 rrcurveto
-138 0 -57 61 0 103 rrcurveto
124 0 rlineto
0 -45 23 -25 46 0 rrcurveto
33 0 29 17 0 32 rrcurveto
0 32 -28 9 -34 0 rrcurveto
-36 0 rlineto
0 78 rlineto
32 0 rlineto
34 0 32 11 0 22 rrcurveto
0 25 -24 16 -34 0 rrcurveto
-35 0 -25 -22 0 -29 rrcurveto
closepath
endchar } ND
/questiondown { 
0 574 hsbw
-11 361 hstem
229 122 vstem
351 74 rmoveto
208 0 rlineto
-7 -164 -106 -91 -164 0 rrcurveto
-128 0 -139 76 0 133 rrcurveto
0 70 32 45 64 33 rrcurveto
11 5 rlineto
57 30 28 21 0 58 rrcurveto
186 0 rlineto
0 -95 -21 -62 -90 -38 rrcurveto
-38 -16 -15 -11 0 -33 rrcurveto
0 -28 22 -18 30 0 rrcurveto
54 0 16 29 0 56 rrcurveto
closepath
53 474 rmoveto
0 -198 rlineto
-208 0 rlineto
0 198 rlineto
208 0 rlineto
closepath
endchar } ND
/Agrave { 
0 722 hsbw
255 220 hstem
714 50 hstem
293 135 vstem
327 764 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
-34 -509 rmoveto
135 0 rlineto
-64 220 rlineto
-2 0 rlineto
-69 -220 rlineto
closepath
-301 -255 rmoveto
263 714 rlineto
215 0 rlineto
260 -714 rlineto
-228 0 rlineto
-30 102 rlineto
-227 0 rlineto
-32 -102 rlineto
-221 0 rlineto
closepath
endchar } ND
/Aacute { 
0 722 hsbw
255 220 hstem
714 50 hstem
293 135 vstem
566 913 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-273 -658 rmoveto
135 0 rlineto
-64 220 rlineto
-2 0 rlineto
-69 -220 rlineto
closepath
-301 -255 rmoveto
263 714 rlineto
215 0 rlineto
260 -714 rlineto
-228 0 rlineto
-30 102 rlineto
-227 0 rlineto
-32 -102 rlineto
-221 0 rlineto
closepath
endchar } ND
/Acircumflex { 
0 722 hsbw
255 220 hstem
714 50 hstem
293 135 vstem
158 764 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
135 -509 rmoveto
135 0 rlineto
-64 220 rlineto
-2 0 rlineto
-69 -220 rlineto
closepath
-301 -255 rmoveto
263 714 rlineto
215 0 rlineto
260 -714 rlineto
-228 0 rlineto
-30 102 rlineto
-227 0 rlineto
-32 -102 rlineto
-221 0 rlineto
closepath
endchar } ND
/Atilde { 
0 722 hsbw
255 220 hstem
714 56 hstem
293 135 vstem
165 764 rmoveto
0 65 38 77 74 0 rrcurveto
32 0 39 -3 31 -21 rrcurveto
3 -2 4 -1 5 -1 rrcurveto
20 -8 12 -4 6 0 rrcurveto
22 0 16 11 7 32 rrcurveto
82 0 rlineto
-3 -80 -43 -59 -70 0 rrcurveto
-27 0 -30 6 -32 13 rrcurveto
-9 3 rlineto
-27 10 -19 5 -11 0 rrcurveto
-20 0 -17 -22 -4 -21 rrcurveto
-79 0 rlineto
closepath
128 -509 rmoveto
135 0 rlineto
-64 220 rlineto
-2 0 rlineto
-69 -220 rlineto
closepath
-301 -255 rmoveto
263 714 rlineto
215 0 rlineto
260 -714 rlineto
-228 0 rlineto
-30 102 rlineto
-227 0 rlineto
-32 -102 rlineto
-221 0 rlineto
closepath
endchar } ND
/Adieresis { 
0 722 hsbw
255 220 hstem
714 55 hstem
333 57 vstem
390 769 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
130 -514 rmoveto
135 0 rlineto
-64 220 rlineto
-2 0 rlineto
-69 -220 rlineto
closepath
-301 -255 rmoveto
263 714 rlineto
215 0 rlineto
260 -714 rlineto
-228 0 rlineto
-30 102 rlineto
-227 0 rlineto
-32 -102 rlineto
-221 0 rlineto
closepath
endchar } ND
/Aring { 
0 722 hsbw
255 220 hstem
714 14 hstem
788 105 hstem
308 105 vstem
248 841 rmoveto
0 61 51 51 62 0 rrcurveto
61 0 51 -51 0 -61 rrcurveto
0 -62 -51 -51 -61 0 rrcurveto
-63 0 -50 50 0 63 rrcurveto
closepath
60 0 rmoveto
0 -28 25 -25 28 0 rrcurveto
28 0 24 26 0 27 rrcurveto
0 26 -26 26 -26 0 rrcurveto
-27 0 -26 -24 0 -28 rrcurveto
closepath
-15 -586 rmoveto
135 0 rlineto
-64 220 rlineto
-2 0 rlineto
-69 -220 rlineto
closepath
-301 -255 rmoveto
263 714 rlineto
215 0 rlineto
260 -714 rlineto
-228 0 rlineto
-30 102 rlineto
-227 0 rlineto
-32 -102 rlineto
-221 0 rlineto
closepath
endchar } ND
/AE { 
0 1056 hsbw
183 89 hstem
442 89 hstem
315 152 vstem
467 249 rmoveto
0 282 rlineto
-29 0 rlineto
-123 -282 rlineto
152 0 rlineto
closepath
-483 -249 rmoveto
325 714 rlineto
690 0 rlineto
0 -183 rlineto
-324 0 rlineto
0 -89 rlineto
302 0 rlineto
0 -170 rlineto
-302 0 rlineto
0 -89 rlineto
336 0 rlineto
0 -183 rlineto
-544 0 rlineto
0 105 rlineto
-211 0 rlineto
-43 -105 rlineto
-229 0 rlineto
closepath
endchar } ND
/Ccedilla { 
0 759 hsbw
-178 72 hstem
-59 42 hstem
280 168 hstem
256 252 vstem
508 280 rmoveto
216 0 rlineto
0 -164 -151 -133 -175 0 rrcurveto
-3 0 rlineto
-29 -45 rlineto
2 -2 rlineto
7 4 14 1 21 0 rrcurveto
54 0 46 -27 0 -62 rrcurveto
0 -52 -55 -32 -76 0 rrcurveto
-51 0 -30 8 -57 20 rrcurveto
21 46 rlineto
5 -1 8 -2 10 -4 rrcurveto
27 -8 21 -5 15 0 rrcurveto
29 0 23 12 0 25 rrcurveto
0 20 -19 15 -24 0 rrcurveto
-13 0 -13 -3 -13 -7 rrcurveto
-26 24 rlineto
48 79 rlineto
-187 22 -117 165 0 183 rrcurveto
0 210 151 164 211 0 rrcurveto
193 0 111 -102 19 -181 rrcurveto
-214 0 rlineto
-2 4 -1 4 0 6 rrcurveto
-9 46 -44 45 -59 0 rrcurveto
-100 0 -36 -105 0 -91 rrcurveto
0 -95 36 -101 100 0 rrcurveto
69 0 32 44 15 75 rrcurveto
closepath
endchar } ND
/Egrave { 
0 704 hsbw
183 89 hstem
442 89 hstem
714 50 hstem
318 764 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
-261 -764 rmoveto
0 714 rlineto
591 0 rlineto
0 -183 rlineto
-371 0 rlineto
0 -89 rlineto
337 0 rlineto
0 -170 rlineto
-337 0 rlineto
0 -89 rlineto
382 0 rlineto
0 -183 rlineto
-602 0 rlineto
closepath
endchar } ND
/Eacute { 
0 704 hsbw
183 89 hstem
442 89 hstem
714 50 hstem
557 913 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-500 -913 rmoveto
0 714 rlineto
591 0 rlineto
0 -183 rlineto
-371 0 rlineto
0 -89 rlineto
337 0 rlineto
0 -170 rlineto
-337 0 rlineto
0 -89 rlineto
382 0 rlineto
0 -183 rlineto
-602 0 rlineto
closepath
endchar } ND
/Ecircumflex { 
0 704 hsbw
183 89 hstem
442 89 hstem
714 50 hstem
296 113 vstem
149 764 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
-92 -764 rmoveto
0 714 rlineto
591 0 rlineto
0 -183 rlineto
-371 0 rlineto
0 -89 rlineto
337 0 rlineto
0 -170 rlineto
-337 0 rlineto
0 -89 rlineto
382 0 rlineto
0 -183 rlineto
-602 0 rlineto
closepath
endchar } ND
/Edieresis { 
0 704 hsbw
183 89 hstem
442 89 hstem
714 55 hstem
324 57 vstem
381 769 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-97 -769 rmoveto
0 714 rlineto
591 0 rlineto
0 -183 rlineto
-371 0 rlineto
0 -89 rlineto
337 0 rlineto
0 -170 rlineto
-337 0 rlineto
0 -89 rlineto
382 0 rlineto
0 -183 rlineto
-602 0 rlineto
closepath
endchar } ND
/Igrave { 
0 333 hsbw
714 50 hstem
133 764 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
-76 -764 rmoveto
0 714 rlineto
220 0 rlineto
0 -714 rlineto
-220 0 rlineto
closepath
endchar } ND
/Iacute { 
0 333 hsbw
714 50 hstem
372 913 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-315 -913 rmoveto
0 714 rlineto
220 0 rlineto
0 -714 rlineto
-220 0 rlineto
closepath
endchar } ND
/Icircumflex { 
0 333 hsbw
714 50 hstem
111 113 vstem
-36 764 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
93 -764 rmoveto
0 714 rlineto
220 0 rlineto
0 -714 rlineto
-220 0 rlineto
closepath
endchar } ND
/Idieresis { 
0 333 hsbw
714 55 hstem
139 57 vstem
196 769 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
88 -769 rmoveto
0 714 rlineto
220 0 rlineto
0 -714 rlineto
-220 0 rlineto
closepath
endchar } ND
/Eth { 
0 778 hsbw
183 127 hstem
432 99 hstem
413 107 vstem
57 0 rmoveto
0 310 rlineto
-56 0 rlineto
0 122 rlineto
56 0 rlineto
0 282 rlineto
361 0 rlineto
203 0 119 -168 0 -189 rrcurveto
0 -224 -123 -133 -242 0 rrcurveto
-318 0 rlineto
closepath
220 531 rmoveto
0 -99 rlineto
136 0 rlineto
0 -122 rlineto
-136 0 rlineto
0 -127 rlineto
86 0 rlineto
104 0 53 89 0 89 rrcurveto
0 81 -64 89 -109 0 rrcurveto
-70 0 rlineto
closepath
endchar } ND
/Ntilde { 
0 759 hsbw
714 50 hstem
266 227 vstem
184 764 rmoveto
0 65 38 77 74 0 rrcurveto
32 0 39 -3 31 -21 rrcurveto
3 -2 4 -1 5 -1 rrcurveto
20 -8 12 -4 6 0 rrcurveto
22 0 16 11 7 32 rrcurveto
82 0 rlineto
-3 -80 -43 -59 -70 0 rrcurveto
-27 0 -30 6 -32 13 rrcurveto
-9 3 rlineto
-27 10 -19 5 -11 0 rrcurveto
-20 0 -17 -22 -4 -21 rrcurveto
-79 0 rlineto
closepath
-126 -764 rmoveto
0 714 rlineto
225 0 rlineto
208 -382 rlineto
2 0 rlineto
0 382 rlineto
208 0 rlineto
0 -714 rlineto
-214 0 rlineto
-219 390 rlineto
-2 0 rlineto
0 -390 rlineto
-208 0 rlineto
closepath
endchar } ND
/Ograve { 
0 796 hsbw
161 392 hstem
731 33 hstem
256 284 vstem
364 764 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
-328 -407 rmoveto
0 205 154 169 208 0 rrcurveto
205 0 157 -168 0 -206 rrcurveto
0 -206 -156 -168 -206 0 rrcurveto
-210 0 -152 169 0 205 rrcurveto
closepath
220 0 rmoveto
0 -107 54 -89 88 0 rrcurveto
88 0 54 90 0 106 rrcurveto
0 103 -54 93 -88 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
closepath
endchar } ND
/Oacute { 
0 796 hsbw
161 392 hstem
731 33 hstem
256 284 vstem
603 913 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-567 -556 rmoveto
0 205 154 169 208 0 rrcurveto
205 0 157 -168 0 -206 rrcurveto
0 -206 -156 -168 -206 0 rrcurveto
-210 0 -152 169 0 205 rrcurveto
closepath
220 0 rmoveto
0 -107 54 -89 88 0 rrcurveto
88 0 54 90 0 106 rrcurveto
0 103 -54 93 -88 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
closepath
endchar } ND
/Ocircumflex { 
0 796 hsbw
161 392 hstem
731 33 hstem
256 284 vstem
195 764 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
-159 -407 rmoveto
0 205 154 169 208 0 rrcurveto
205 0 157 -168 0 -206 rrcurveto
0 -206 -156 -168 -206 0 rrcurveto
-210 0 -152 169 0 205 rrcurveto
closepath
220 0 rmoveto
0 -107 54 -89 88 0 rrcurveto
88 0 54 90 0 106 rrcurveto
0 103 -54 93 -88 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
closepath
endchar } ND
/Otilde { 
0 796 hsbw
161 392 hstem
731 33 hstem
256 284 vstem
202 764 rmoveto
0 65 38 77 74 0 rrcurveto
32 0 39 -3 31 -21 rrcurveto
3 -2 4 -1 5 -1 rrcurveto
20 -8 12 -4 6 0 rrcurveto
22 0 16 11 7 32 rrcurveto
82 0 rlineto
-3 -80 -43 -59 -70 0 rrcurveto
-27 0 -30 6 -32 13 rrcurveto
-9 3 rlineto
-27 10 -19 5 -11 0 rrcurveto
-20 0 -17 -22 -4 -21 rrcurveto
-79 0 rlineto
closepath
-166 -407 rmoveto
0 205 154 169 208 0 rrcurveto
205 0 157 -168 0 -206 rrcurveto
0 -206 -156 -168 -206 0 rrcurveto
-210 0 -152 169 0 205 rrcurveto
closepath
220 0 rmoveto
0 -107 54 -89 88 0 rrcurveto
88 0 54 90 0 106 rrcurveto
0 103 -54 93 -88 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
closepath
endchar } ND
/Odieresis { 
0 796 hsbw
161 392 hstem
731 38 hstem
370 57 vstem
427 769 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-164 -412 rmoveto
0 205 154 169 208 0 rrcurveto
205 0 157 -168 0 -206 rrcurveto
0 -206 -156 -168 -206 0 rrcurveto
-210 0 -152 169 0 205 rrcurveto
closepath
220 0 rmoveto
0 -107 54 -89 88 0 rrcurveto
88 0 54 90 0 106 rrcurveto
0 103 -54 93 -88 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
closepath
endchar } ND
/multiply { 
0 600 hsbw
159 4 rmoveto
-108 108 rlineto
141 141 rlineto
-141 141 rlineto
108 108 rlineto
141 -141 rlineto
141 142 rlineto
108 -108 rlineto
-141 -142 rlineto
141 -141 rlineto
-109 -108 rlineto
-140 141 rlineto
-141 -141 rlineto
closepath
endchar } ND
/Oslash { 
0 796 hsbw
161 392 hstem
256 284 vstem
271 258 rmoveto
226 249 rlineto
-28 32 -38 14 -33 0 rrcurveto
-89 0 -53 -91 0 -105 rrcurveto
0 -38 5 -33 10 -28 rrcurveto
closepath
256 192 rmoveto
-224 -247 rlineto
26 -30 35 -12 34 0 rrcurveto
88 0 54 90 0 106 rrcurveto
0 34 -5 31 -8 28 rrcurveto
closepath
187 295 rmoveto
46 -39 rlineto
-87 -95 rlineto
56 -66 31 -88 0 -100 rrcurveto
0 -206 -156 -168 -206 0 rrcurveto
-88 0 -79 26 -60 48 rrcurveto
-85 -94 rlineto
-45 42 rlineto
86 94 rlineto
-59 66 -32 90 0 102 rrcurveto
0 205 154 169 208 0 rrcurveto
91 0 77 -26 64 -52 rrcurveto
84 92 rlineto
closepath
endchar } ND
/Ugrave { 
0 759 hsbw
161 603 hstem
274 211 vstem
346 764 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
359 -50 rmoveto
0 -438 rlineto
0 -196 -109 -97 -216 0 rrcurveto
-218 0 -108 97 0 196 rrcurveto
0 438 rlineto
220 0 rlineto
0 -389 rlineto
0 -94 15 -70 92 0 rrcurveto
87 0 17 72 0 92 rrcurveto
0 389 rlineto
220 0 rlineto
closepath
endchar } ND
/Uacute { 
0 759 hsbw
161 603 hstem
274 211 vstem
585 913 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
120 -199 rmoveto
0 -438 rlineto
0 -196 -109 -97 -216 0 rrcurveto
-218 0 -108 97 0 196 rrcurveto
0 438 rlineto
220 0 rlineto
0 -389 rlineto
0 -94 15 -70 92 0 rrcurveto
87 0 17 72 0 92 rrcurveto
0 389 rlineto
220 0 rlineto
closepath
endchar } ND
/Ucircumflex { 
0 759 hsbw
714 50 hstem
274 211 vstem
177 764 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
528 -50 rmoveto
0 -438 rlineto
0 -196 -109 -97 -216 0 rrcurveto
-218 0 -108 97 0 196 rrcurveto
0 438 rlineto
220 0 rlineto
0 -389 rlineto
0 -94 15 -70 92 0 rrcurveto
87 0 17 72 0 92 rrcurveto
0 389 rlineto
220 0 rlineto
closepath
endchar } ND
/Udieresis { 
0 759 hsbw
714 55 hstem
352 57 vstem
409 769 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
523 -55 rmoveto
0 -438 rlineto
0 -196 -109 -97 -216 0 rrcurveto
-218 0 -108 97 0 196 rrcurveto
0 438 rlineto
220 0 rlineto
0 -389 rlineto
0 -94 15 -70 92 0 rrcurveto
87 0 17 72 0 92 rrcurveto
0 389 rlineto
220 0 rlineto
closepath
endchar } ND
/Yacute { 
0 667 hsbw
451 313 hstem
213 243 vstem
539 913 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-318 -913 rmoveto
0 263 rlineto
-251 451 rlineto
243 0 rlineto
119 -263 rlineto
124 263 rlineto
241 0 rlineto
-256 -451 rlineto
0 -263 rlineto
-220 0 rlineto
closepath
endchar } ND
/Thorn { 
0 704 hsbw
302 145 hstem
277 202 vstem
277 447 rmoveto
0 -145 rlineto
85 0 rlineto
60 0 57 11 0 65 rrcurveto
0 56 -49 13 -55 0 rrcurveto
-98 0 rlineto
closepath
-220 -447 rmoveto
0 714 rlineto
220 0 rlineto
0 -84 rlineto
148 0 rlineto
149 0 113 -116 0 -128 rrcurveto
0 -167 -110 -87 -170 0 rrcurveto
-130 0 rlineto
0 -132 rlineto
-220 0 rlineto
closepath
endchar } ND
/germandbls { 
0 648 hsbw
153 178 hstem
393 2 hstem
441 132 hstem
256 40 vstem
58 0 rmoveto
0 466 rlineto
0 164 96 101 168 0 rrcurveto
157 0 100 -81 0 -112 rrcurveto
0 -64 -34 -48 -45 -28 rrcurveto
-2 -2 -2 -1 -1 0 rrcurveto
0 -2 rlineto
47 -8 77 -79 0 -89 rrcurveto
0 -131 -105 -92 -124 0 rrcurveto
-40 0 -24 2 -30 5 rrcurveto
0 152 rlineto
14 -4 13 -2 14 0 rrcurveto
50 0 34 39 0 51 rrcurveto
0 72 -48 22 -77 0 rrcurveto
0 110 rlineto
64 0 35 11 0 56 rrcurveto
0 43 -23 22 -45 0 rrcurveto
-51 0 -20 -34 0 -56 rrcurveto
0 -483 rlineto
-198 0 rlineto
closepath
endchar } ND
/agrave { 
0 611 hsbw
104 311 hstem
532 37 hstem
225 145 vstem
373 2 vstem
272 569 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
98 -384 rmoveto
0 48 rlineto
-19 -10 -27 -8 -32 -7 rrcurveto
-49 -11 -18 -12 0 -33 rrcurveto
0 -26 27 -22 35 0 rrcurveto
46 0 37 29 0 52 rrcurveto
closepath
190 144 rmoveto
0 -213 rlineto
0 -48 4 -39 20 -29 rrcurveto
-202 0 rlineto
-5 12 -2 17 0 20 rrcurveto
-2 0 rlineto
-40 -46 -57 -16 -70 0 rrcurveto
-107 0 -76 53 0 104 rrcurveto
0 124 117 33 122 12 rrcurveto
60 6 48 -2 0 50 rrcurveto
0 32 -29 16 -36 0 rrcurveto
-55 0 -18 -24 -1 -29 rrcurveto
-184 0 rlineto
3 133 136 37 128 0 rrcurveto
154 0 92 -49 0 -154 rrcurveto
closepath
endchar } ND
/aacute { 
0 611 hsbw
104 311 hstem
532 37 hstem
225 145 vstem
373 2 vstem
511 718 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-141 -533 rmoveto
0 48 rlineto
-19 -10 -27 -8 -32 -7 rrcurveto
-49 -11 -18 -12 0 -33 rrcurveto
0 -26 27 -22 35 0 rrcurveto
46 0 37 29 0 52 rrcurveto
closepath
190 144 rmoveto
0 -213 rlineto
0 -48 4 -39 20 -29 rrcurveto
-202 0 rlineto
-5 12 -2 17 0 20 rrcurveto
-2 0 rlineto
-40 -46 -57 -16 -70 0 rrcurveto
-107 0 -76 53 0 104 rrcurveto
0 124 117 33 122 12 rrcurveto
60 6 48 -2 0 50 rrcurveto
0 32 -29 16 -36 0 rrcurveto
-55 0 -18 -24 -1 -29 rrcurveto
-184 0 rlineto
3 133 136 37 128 0 rrcurveto
154 0 92 -49 0 -154 rrcurveto
closepath
endchar } ND
/acircumflex { 
0 611 hsbw
104 311 hstem
532 37 hstem
225 145 vstem
373 2 vstem
103 569 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
267 -384 rmoveto
0 48 rlineto
-19 -10 -27 -8 -32 -7 rrcurveto
-49 -11 -18 -12 0 -33 rrcurveto
0 -26 27 -22 35 0 rrcurveto
46 0 37 29 0 52 rrcurveto
closepath
190 144 rmoveto
0 -213 rlineto
0 -48 4 -39 20 -29 rrcurveto
-202 0 rlineto
-5 12 -2 17 0 20 rrcurveto
-2 0 rlineto
-40 -46 -57 -16 -70 0 rrcurveto
-107 0 -76 53 0 104 rrcurveto
0 124 117 33 122 12 rrcurveto
60 6 48 -2 0 50 rrcurveto
0 32 -29 16 -36 0 rrcurveto
-55 0 -18 -24 -1 -29 rrcurveto
-184 0 rlineto
3 133 136 37 128 0 rrcurveto
154 0 92 -49 0 -154 rrcurveto
closepath
endchar } ND
/atilde { 
0 611 hsbw
104 311 hstem
532 37 hstem
225 145 vstem
373 2 vstem
110 569 rmoveto
0 65 38 77 74 0 rrcurveto
32 0 39 -3 31 -21 rrcurveto
3 -2 4 -1 5 -1 rrcurveto
20 -8 12 -4 6 0 rrcurveto
22 0 16 11 7 32 rrcurveto
82 0 rlineto
-3 -80 -43 -59 -70 0 rrcurveto
-27 0 -30 6 -32 13 rrcurveto
-9 3 rlineto
-27 10 -19 5 -11 0 rrcurveto
-20 0 -17 -22 -4 -21 rrcurveto
-79 0 rlineto
closepath
260 -384 rmoveto
0 48 rlineto
-19 -10 -27 -8 -32 -7 rrcurveto
-49 -11 -18 -12 0 -33 rrcurveto
0 -26 27 -22 35 0 rrcurveto
46 0 37 29 0 52 rrcurveto
closepath
190 144 rmoveto
0 -213 rlineto
0 -48 4 -39 20 -29 rrcurveto
-202 0 rlineto
-5 12 -2 17 0 20 rrcurveto
-2 0 rlineto
-40 -46 -57 -16 -70 0 rrcurveto
-107 0 -76 53 0 104 rrcurveto
0 124 117 33 122 12 rrcurveto
60 6 48 -2 0 50 rrcurveto
0 32 -29 16 -36 0 rrcurveto
-55 0 -18 -24 -1 -29 rrcurveto
-184 0 rlineto
3 133 136 37 128 0 rrcurveto
154 0 92 -49 0 -154 rrcurveto
closepath
endchar } ND
/adieresis { 
0 611 hsbw
104 311 hstem
532 42 hstem
278 57 vstem
373 2 vstem
335 574 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
262 -389 rmoveto
0 48 rlineto
-19 -10 -27 -8 -32 -7 rrcurveto
-49 -11 -18 -12 0 -33 rrcurveto
0 -26 27 -22 35 0 rrcurveto
46 0 37 29 0 52 rrcurveto
closepath
190 144 rmoveto
0 -213 rlineto
0 -48 4 -39 20 -29 rrcurveto
-202 0 rlineto
-5 12 -2 17 0 20 rrcurveto
-2 0 rlineto
-40 -46 -57 -16 -70 0 rrcurveto
-107 0 -76 53 0 104 rrcurveto
0 124 117 33 122 12 rrcurveto
60 6 48 -2 0 50 rrcurveto
0 32 -29 16 -36 0 rrcurveto
-55 0 -18 -24 -1 -29 rrcurveto
-184 0 rlineto
3 133 136 37 128 0 rrcurveto
154 0 92 -49 0 -154 rrcurveto
closepath
endchar } ND
/aring { 
0 611 hsbw
104 311 hstem
532 21 hstem
613 105 hstem
253 105 vstem
373 2 vstem
193 666 rmoveto
0 61 51 51 62 0 rrcurveto
61 0 51 -51 0 -61 rrcurveto
0 -62 -51 -51 -61 0 rrcurveto
-63 0 -50 50 0 63 rrcurveto
closepath
60 0 rmoveto
0 -28 25 -25 28 0 rrcurveto
28 0 24 26 0 27 rrcurveto
0 26 -26 26 -26 0 rrcurveto
-27 0 -26 -24 0 -28 rrcurveto
closepath
117 -481 rmoveto
0 48 rlineto
-19 -10 -27 -8 -32 -7 rrcurveto
-49 -11 -18 -12 0 -33 rrcurveto
0 -26 27 -22 35 0 rrcurveto
46 0 37 29 0 52 rrcurveto
closepath
190 144 rmoveto
0 -213 rlineto
0 -48 4 -39 20 -29 rrcurveto
-202 0 rlineto
-5 12 -2 17 0 20 rrcurveto
-2 0 rlineto
-40 -46 -57 -16 -70 0 rrcurveto
-107 0 -76 53 0 104 rrcurveto
0 124 117 33 122 12 rrcurveto
60 6 48 -2 0 50 rrcurveto
0 32 -29 16 -36 0 rrcurveto
-55 0 -18 -24 -1 -29 rrcurveto
-184 0 rlineto
3 133 136 37 128 0 rrcurveto
154 0 92 -49 0 -154 rrcurveto
closepath
endchar } ND
/ae { 
0 962 hsbw
151 64 hstem
318 87 hstem
234 155 vstem
559 176 vstem
559 318 rmoveto
176 0 rlineto
0 53 -35 34 -53 0 rrcurveto
-62 0 -26 -42 0 -45 rrcurveto
closepath
-170 -123 rmoveto
0 42 rlineto
-16 -9 -29 -10 -43 -10 rrcurveto
-43 -7 -24 -15 0 -38 rrcurveto
0 -24 27 -20 42 0 rrcurveto
54 0 32 39 0 52 rrcurveto
closepath
542 20 rmoveto
-372 0 rlineto
2 -66 33 -35 64 0 rrcurveto
29 0 31 18 15 19 rrcurveto
186 0 rlineto
-30 -105 -120 -59 -108 0 rrcurveto
-104 0 -44 17 -50 52 rrcurveto
-69 -58 -88 -11 -91 0 rrcurveto
-96 0 -87 55 0 105 rrcurveto
0 124 119 30 124 13 rrcurveto
67 7 47 4 0 42 rrcurveto
0 34 -32 14 -37 0 rrcurveto
-44 0 -30 -16 -5 -37 rrcurveto
-186 0 rlineto
0 131 139 39 120 0 rrcurveto
69 0 66 -15 47 -41 rrcurveto
40 38 56 18 70 0 rrcurveto
165 0 104 -136 0 -158 rrcurveto
0 -23 rlineto
closepath
endchar } ND
/ccedilla { 
0 611 hsbw
-178 72 hstem
207 115 hstem
230 159 vstem
394 207 rmoveto
189 0 rlineto
-18 -134 -103 -81 -138 -4 rrcurveto
-32 -50 rlineto
2 -2 rlineto
7 4 14 1 21 0 rrcurveto
54 0 46 -27 0 -62 rrcurveto
0 -52 -55 -32 -76 0 rrcurveto
-51 0 -30 8 -57 20 rrcurveto
21 46 rlineto
5 -1 8 -2 10 -4 rrcurveto
27 -8 21 -5 15 0 rrcurveto
29 0 23 12 0 25 rrcurveto
0 20 -19 15 -24 0 rrcurveto
-13 0 -13 -3 -13 -7 rrcurveto
-26 24 rlineto
51 83 rlineto
-136 19 -101 109 0 140 rrcurveto
0 153 130 120 151 0 rrcurveto
144 0 108 -73 12 -137 rrcurveto
-188 0 rlineto
-4 43 -26 23 -42 0 rrcurveto
-68 0 -19 -64 0 -65 rrcurveto
0 -65 19 -63 68 0 rrcurveto
41 0 32 33 4 43 rrcurveto
closepath
endchar } ND
/egrave { 
0 630 hsbw
151 64 hstem
318 87 hstem
532 37 hstem
222 189 vstem
281 569 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
-59 -251 rmoveto
189 0 rlineto
0 49 -37 38 -50 0 rrcurveto
-59 0 -34 -29 -9 -58 rrcurveto
closepath
379 -103 rmoveto
-380 0 rlineto
5 -62 36 -39 60 0 rrcurveto
34 0 34 13 15 24 rrcurveto
185 0 rlineto
-39 -108 -104 -56 -124 0 rrcurveto
-160 0 -131 108 0 163 rrcurveto
0 147 122 127 152 0 rrcurveto
201 0 94 -111 0 -206 rrcurveto
closepath
endchar } ND
/eacute { 
0 630 hsbw
151 64 hstem
318 87 hstem
532 37 hstem
222 189 vstem
520 718 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-298 -400 rmoveto
189 0 rlineto
0 49 -37 38 -50 0 rrcurveto
-59 0 -34 -29 -9 -58 rrcurveto
closepath
379 -103 rmoveto
-380 0 rlineto
5 -62 36 -39 60 0 rrcurveto
34 0 34 13 15 24 rrcurveto
185 0 rlineto
-39 -108 -104 -56 -124 0 rrcurveto
-160 0 -131 108 0 163 rrcurveto
0 147 122 127 152 0 rrcurveto
201 0 94 -111 0 -206 rrcurveto
closepath
endchar } ND
/ecircumflex { 
0 630 hsbw
151 64 hstem
318 87 hstem
532 37 hstem
222 189 vstem
112 569 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
110 -251 rmoveto
189 0 rlineto
0 49 -37 38 -50 0 rrcurveto
-59 0 -34 -29 -9 -58 rrcurveto
closepath
379 -103 rmoveto
-380 0 rlineto
5 -62 36 -39 60 0 rrcurveto
34 0 34 13 15 24 rrcurveto
185 0 rlineto
-39 -108 -104 -56 -124 0 rrcurveto
-160 0 -131 108 0 163 rrcurveto
0 147 122 127 152 0 rrcurveto
201 0 94 -111 0 -206 rrcurveto
closepath
endchar } ND
/edieresis { 
0 630 hsbw
151 64 hstem
318 87 hstem
532 42 hstem
287 57 vstem
344 574 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
105 -256 rmoveto
189 0 rlineto
0 49 -37 38 -50 0 rrcurveto
-59 0 -34 -29 -9 -58 rrcurveto
closepath
379 -103 rmoveto
-380 0 rlineto
5 -62 36 -39 60 0 rrcurveto
34 0 34 13 15 24 rrcurveto
185 0 rlineto
-39 -108 -104 -56 -124 0 rrcurveto
-160 0 -131 108 0 163 rrcurveto
0 147 122 127 152 0 rrcurveto
201 0 94 -111 0 -206 rrcurveto
closepath
endchar } ND
/igrave { 
0 315 hsbw
519 50 hstem
124 569 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
132 -569 rmoveto
-198 0 rlineto
0 519 rlineto
198 0 rlineto
0 -519 rlineto
closepath
endchar } ND
/iacute { 
0 315 hsbw
519 50 hstem
363 718 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
-107 -718 rmoveto
-198 0 rlineto
0 519 rlineto
198 0 rlineto
0 -519 rlineto
closepath
endchar } ND
/icircumflex { 
0 315 hsbw
519 50 hstem
102 113 vstem
-45 569 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
301 -569 rmoveto
-198 0 rlineto
0 519 rlineto
198 0 rlineto
0 -519 rlineto
closepath
endchar } ND
/idieresis { 
0 315 hsbw
519 55 hstem
130 57 vstem
187 574 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
296 -574 rmoveto
-198 0 rlineto
0 519 rlineto
198 0 rlineto
0 -519 rlineto
closepath
endchar } ND
/eth { 
0 630 hsbw
131 237 hstem
495 23 hstem
230 170 vstem
314 131 rmoveto
57 0 29 40 0 81 rrcurveto
0 77 -29 39 -57 0 rrcurveto
-56 0 -28 -39 0 -77 rrcurveto
0 -81 28 -40 56 0 rrcurveto
closepath
136 648 rmoveto
76 -77 rlineto
-99 -53 rlineto
109 -102 62 -109 0 -140 rrcurveto
0 -162 -100 -149 -184 0 rrcurveto
-180 0 -102 97 0 164 rrcurveto
0 142 118 105 119 0 rrcurveto
38 0 27 -2 30 -13 rrcurveto
2 2 rlineto
-24 34 -38 39 -26 16 rrcurveto
-100 -53 rlineto
-72 78 rlineto
81 44 rlineto
-8 10 -27 20 -22 14 rrcurveto
142 82 rlineto
2 -3 4 -2 4 -2 rrcurveto
9 -6 rlineto
25 -18 17 -11 9 -4 rrcurveto
108 59 rlineto
closepath
endchar } ND
/ntilde { 
0 648 hsbw
532 43 hstem
245 2 vstem
252 144 vstem
128 569 rmoveto
0 65 38 77 74 0 rrcurveto
32 0 39 -3 31 -21 rrcurveto
3 -2 4 -1 5 -1 rrcurveto
20 -8 12 -4 6 0 rrcurveto
22 0 16 11 7 32 rrcurveto
82 0 rlineto
-3 -80 -43 -59 -70 0 rrcurveto
-27 0 -30 6 -32 13 rrcurveto
-9 3 rlineto
-27 10 -19 5 -11 0 rrcurveto
-20 0 -17 -22 -4 -21 rrcurveto
-79 0 rlineto
closepath
-74 -569 rmoveto
0 519 rlineto
191 0 rlineto
0 -66 rlineto
2 0 rlineto
37 52 56 27 76 0 rrcurveto
100 0 78 -63 0 -113 rrcurveto
0 -356 rlineto
-198 0 rlineto
0 272 rlineto
0 69 -12 33 -53 0 rrcurveto
-51 0 -28 -43 0 -57 rrcurveto
0 -274 rlineto
-198 0 rlineto
closepath
endchar } ND
/ograve { 
0 630 hsbw
131 257 hstem
532 37 hstem
230 170 vstem
281 569 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
317 -309 rmoveto
0 -154 -118 -119 -165 0 rrcurveto
-167 0 -116 119 0 154 rrcurveto
0 150 117 122 166 0 rrcurveto
163 0 120 -122 0 -150 rrcurveto
closepath
-198 0 rmoveto
0 61 -17 67 -68 0 rrcurveto
-68 0 -17 -66 0 -62 rrcurveto
0 -61 17 -68 68 0 rrcurveto
68 0 17 69 0 60 rrcurveto
closepath
endchar } ND
/oacute { 
0 630 hsbw
131 257 hstem
532 37 hstem
230 170 vstem
520 718 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
78 -458 rmoveto
0 -154 -118 -119 -165 0 rrcurveto
-167 0 -116 119 0 154 rrcurveto
0 150 117 122 166 0 rrcurveto
163 0 120 -122 0 -150 rrcurveto
closepath
-198 0 rmoveto
0 61 -17 67 -68 0 rrcurveto
-68 0 -17 -66 0 -62 rrcurveto
0 -61 17 -68 68 0 rrcurveto
68 0 17 69 0 60 rrcurveto
closepath
endchar } ND
/ocircumflex { 
0 630 hsbw
131 257 hstem
532 37 hstem
230 170 vstem
112 569 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
486 -309 rmoveto
0 -154 -118 -119 -165 0 rrcurveto
-167 0 -116 119 0 154 rrcurveto
0 150 117 122 166 0 rrcurveto
163 0 120 -122 0 -150 rrcurveto
closepath
-198 0 rmoveto
0 61 -17 67 -68 0 rrcurveto
-68 0 -17 -66 0 -62 rrcurveto
0 -61 17 -68 68 0 rrcurveto
68 0 17 69 0 60 rrcurveto
closepath
endchar } ND
/otilde { 
0 630 hsbw
131 257 hstem
532 37 hstem
230 170 vstem
119 569 rmoveto
0 65 38 77 74 0 rrcurveto
32 0 39 -3 31 -21 rrcurveto
3 -2 4 -1 5 -1 rrcurveto
20 -8 12 -4 6 0 rrcurveto
22 0 16 11 7 32 rrcurveto
82 0 rlineto
-3 -80 -43 -59 -70 0 rrcurveto
-27 0 -30 6 -32 13 rrcurveto
-9 3 rlineto
-27 10 -19 5 -11 0 rrcurveto
-20 0 -17 -22 -4 -21 rrcurveto
-79 0 rlineto
closepath
479 -309 rmoveto
0 -154 -118 -119 -165 0 rrcurveto
-167 0 -116 119 0 154 rrcurveto
0 150 117 122 166 0 rrcurveto
163 0 120 -122 0 -150 rrcurveto
closepath
-198 0 rmoveto
0 61 -17 67 -68 0 rrcurveto
-68 0 -17 -66 0 -62 rrcurveto
0 -61 17 -68 68 0 rrcurveto
68 0 17 69 0 60 rrcurveto
closepath
endchar } ND
/odieresis { 
0 630 hsbw
131 257 hstem
532 42 hstem
287 57 vstem
344 574 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
481 -314 rmoveto
0 -154 -118 -119 -165 0 rrcurveto
-167 0 -116 119 0 154 rrcurveto
0 150 117 122 166 0 rrcurveto
163 0 120 -122 0 -150 rrcurveto
closepath
-198 0 rmoveto
0 61 -17 67 -68 0 rrcurveto
-68 0 -17 -66 0 -62 rrcurveto
0 -61 17 -68 68 0 rrcurveto
68 0 17 69 0 60 rrcurveto
closepath
endchar } ND
/divide { 
0 600 hsbw
147 29 hstem
329 29 hstem
48 176 rmoveto
0 153 rlineto
505 0 rlineto
0 -153 rlineto
-505 0 rlineto
closepath
253 182 rmoveto
-56 0 -45 45 0 56 rrcurveto
0 56 45 46 56 0 rrcurveto
56 0 45 -46 0 -56 rrcurveto
0 -56 -45 -45 -56 0 rrcurveto
closepath
0 -211 rmoveto
56 0 45 -45 0 -56 rrcurveto
0 -56 -45 -46 -56 0 rrcurveto
-56 0 -45 46 0 56 rrcurveto
0 56 45 45 56 0 rrcurveto
closepath
endchar } ND
/oslash { 
0 630 hsbw
131 257 hstem
230 170 vstem
395 314 rmoveto
-137 -159 rlineto
12 -16 19 -8 26 0 rrcurveto
68 0 17 69 0 60 rrcurveto
0 20 -2 18 -3 16 rrcurveto
closepath
-159 -112 rmoveto
138 160 rlineto
-13 17 -20 9 -26 0 rrcurveto
-68 0 -17 -66 0 -62 rrcurveto
0 -24 2 -20 4 -14 rrcurveto
closepath
298 344 rmoveto
35 -31 rlineto
-48 -56 rlineto
50 -50 27 -71 0 -78 rrcurveto
0 -154 -118 -119 -165 0 rrcurveto
-64 0 -56 14 -46 28 rrcurveto
-55 -64 rlineto
-36 33 rlineto
53 61 rlineto
-52 49 -27 72 0 80 rrcurveto
0 150 117 122 166 0 rrcurveto
66 0 57 -15 46 -29 rrcurveto
50 58 rlineto
closepath
endchar } ND
/ugrave { 
0 648 hsbw
519 50 hstem
252 144 vstem
401 2 vstem
290 569 rmoveto
-171 149 rlineto
208 0 rlineto
100 -149 rlineto
-137 0 rlineto
closepath
304 -50 rmoveto
0 -519 rlineto
-191 0 rlineto
0 66 rlineto
-2 0 rlineto
-38 -53 -56 -26 -75 0 rrcurveto
-100 0 -78 62 0 114 rrcurveto
0 356 rlineto
198 0 rlineto
0 -272 rlineto
0 -70 11 -32 54 0 rrcurveto
45 0 34 43 0 57 rrcurveto
0 274 rlineto
198 0 rlineto
closepath
endchar } ND
/uacute { 
0 648 hsbw
519 50 hstem
252 144 vstem
401 2 vstem
529 718 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
65 -199 rmoveto
0 -519 rlineto
-191 0 rlineto
0 66 rlineto
-2 0 rlineto
-38 -53 -56 -26 -75 0 rrcurveto
-100 0 -78 62 0 114 rrcurveto
0 356 rlineto
198 0 rlineto
0 -272 rlineto
0 -70 11 -32 54 0 rrcurveto
45 0 34 43 0 57 rrcurveto
0 274 rlineto
198 0 rlineto
closepath
endchar } ND
/ucircumflex { 
0 648 hsbw
519 50 hstem
252 144 vstem
401 2 vstem
121 569 rmoveto
121 149 rlineto
164 0 rlineto
122 -149 rlineto
-147 0 rlineto
-57 76 rlineto
-56 -76 rlineto
-147 0 rlineto
closepath
473 -50 rmoveto
0 -519 rlineto
-191 0 rlineto
0 66 rlineto
-2 0 rlineto
-38 -53 -56 -26 -75 0 rrcurveto
-100 0 -78 62 0 114 rrcurveto
0 356 rlineto
198 0 rlineto
0 -272 rlineto
0 -70 11 -32 54 0 rrcurveto
45 0 34 43 0 57 rrcurveto
0 274 rlineto
198 0 rlineto
closepath
endchar } ND
/udieresis { 
0 648 hsbw
519 55 hstem
296 57 vstem
401 2 vstem
353 574 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
468 -55 rmoveto
0 -519 rlineto
-191 0 rlineto
0 66 rlineto
-2 0 rlineto
-38 -53 -56 -26 -75 0 rrcurveto
-100 0 -78 62 0 114 rrcurveto
0 356 rlineto
198 0 rlineto
0 -272 rlineto
0 -70 11 -32 54 0 rrcurveto
45 0 34 43 0 57 rrcurveto
0 274 rlineto
198 0 rlineto
closepath
endchar } ND
/yacute { 
0 556 hsbw
-10 20 hstem
519 50 hstem
280 2 vstem
483 718 rmoveto
-172 -149 rlineto
-136 0 rlineto
99 149 rlineto
209 0 rlineto
closepath
88 -199 rmoveto
-172 -479 rlineto
-1 -2 -1 -2 0 -3 rrcurveto
-3 -9 rlineto
-54 -162 -43 -30 -172 0 rrcurveto
-90 0 rlineto
0 158 rlineto
46 0 rlineto
69 0 10 -1 0 45 rrcurveto
0 4 -3 11 -5 16 rrcurveto
-2 8 -3 8 -4 9 rrcurveto
-3 9 -3 9 -3 10 rrcurveto
-8 21 -16 42 -24 65 rrcurveto
-4 5 -2 6 -1 6 rrcurveto
-2 6 -2 6 -2 5 rrcurveto
-88 239 rlineto
212 0 rlineto
83 -298 rlineto
2 0 rlineto
84 298 rlineto
205 0 rlineto
closepath
endchar } ND
/thorn { 
0 648 hsbw
131 257 hstem
245 174 vstem
245 259 rmoveto
0 -63 22 -65 65 0 rrcurveto
63 0 24 67 0 61 rrcurveto
0 61 -23 68 -64 0 rrcurveto
-67 0 -20 -67 0 -62 rrcurveto
closepath
-191 -427 rmoveto
0 882 rlineto
198 0 rlineto
0 -252 rlineto
2 0 rlineto
30 44 53 26 59 0 rrcurveto
148 0 73 -137 0 -133 rrcurveto
0 -139 -71 -136 -150 0 rrcurveto
-59 0 -53 26 -30 44 rrcurveto
-2 0 rlineto
0 -225 rlineto
-198 0 rlineto
closepath
endchar } ND
/ydieresis { 
0 556 hsbw
-10 20 hstem
519 55 hstem
250 57 vstem
307 574 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
-227 0 rmoveto
0 130 rlineto
170 0 rlineto
0 -130 rlineto
-170 0 rlineto
closepath
491 -55 rmoveto
-172 -479 rlineto
-1 -2 -1 -2 0 -3 rrcurveto
-3 -9 rlineto
-54 -162 -43 -30 -172 0 rrcurveto
-90 0 rlineto
0 158 rlineto
46 0 rlineto
69 0 10 -1 0 45 rrcurveto
0 4 -3 11 -5 16 rrcurveto
-2 8 -3 8 -4 9 rrcurveto
-3 9 -3 9 -3 10 rrcurveto
-8 21 -16 42 -24 65 rrcurveto
-4 5 -2 6 -1 6 rrcurveto
-2 6 -2 6 -2 5 rrcurveto
-88 239 rlineto
212 0 rlineto
83 -298 rlineto
2 0 rlineto
84 298 rlineto
205 0 rlineto
closepath
endchar } ND
end
end
readonly put
noaccess put
dup/FontName get exch definefont pop
mark currentfile closefile
cleartomark
